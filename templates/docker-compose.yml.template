services:
  mysql:
    image: mysql:8.0.23
    container_name: mysql
    env_file:
      - .env
    volumes:
      - ${PROJECT_HOME}/volumes/mysql/init-giraffe.sql:/docker-entrypoint-initdb.d/init-giraffe.sql:ro
      - ${PROJECT_HOME}/volumes/mysql/data:/var/lib/mysql
      - /opt/giraffe-wash/volumes/mysql/conf.d:/etc/mysql/conf.d/
    ports:
      - "3306:3306"
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - giraffe-wash
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 15s
      timeout: 5s
      retries: 5

  redis:
    image: redis:6-alpine
    container_name: redis
    env_file:
      - .env
    ports:
      - "6379:6379"
    volumes:
      - ${PROJECT_HOME}/volumes/redis/data:/data
      - ${PROJECT_HOME}/volumes/redis/conf/redis.conf:/opt/redis/redis.conf:ro
    command: ["redis-server", "/opt/redis/redis.conf"]
    networks:
      - giraffe-wash
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", 'redis-cli -a "$REDIS_PASSWORD" ping']
      interval: 10s
      timeout: 3s
      retries: 3

  minio:
    image: minio/minio:RELEASE.2025-04-22T22-12-26Z
    container_name: minio
    env_file:
      - .env
    command: server /data --console-address ":9001"
    volumes:
      - ${PROJECT_HOME}/volumes/minio/data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      - giraffe-wash
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 15s
      timeout: 5s
      retries: 3

  nginx:
    image: nginx:1.28.0-alpine
    container_name: nginx
    environment:
      TZ: Asia/Shanghai
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ${PROJECT_HOME}/volumes/nginx/conf:/etc/nginx/conf.d:ro
      - ${PROJECT_HOME}/volumes/nginx/logs:/var/log/nginx
      - ${PROJECT_HOME}/volumes/nginx/certs:/etc/nginx/certs:ro
    networks:
      - giraffe-wash
    restart: unless-stopped

  giraffe-wash-java:
    image: giraffe-wash-java:latest
    container_name: giraffe-wash-java
    env_file:
      - .env
    ports:
      - "8080:8080"
    volumes:
      - ${PROJECT_HOME}/volumes/giraffe-wash-java/logs:/app/logs
    networks:
      - giraffe-wash
    restart: unless-stopped

  giraffe-wash-web:
    image: giraffe-wash-web:latest
    container_name: giraffe-wash-web
    ports:
      - "8081:80"
    networks:
      - giraffe-wash
    depends_on:
      giraffe-wash-java:
        condition: service_started
    restart: unless-stopped

networks:
  giraffe-wash:
