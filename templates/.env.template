# 该文件为所有容器的环境变量配置
# MySQL 容器配置
MYSQL_CONTAINER_NAME=mysql
MYSQL_ROOT_PASSWORD=
MYSQL_HOST=mysql
MYSQL_PORT=3306
MYSQL_USER=
MYSQL_PASSWORD=
MYSQL_DATABASE=
# redis 容器配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_CONTAINER_NAME=redis
REDIS_DATABASE=
REDIS_PASSWORD=
# minio 容器配置
MINIO_HOST=minio
MINIO_ROOT_USER=
MINIO_ROOT_PASSWORD=
# giraffe-wash 容器配置,如果giraffe-wash某个变量和中间件服务的变量名一致，则无需重复添加，直接复用中间件服务的配置
JAVA_OPTS="-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005"
# 公钥、密钥
PRIVATE_KEY=
PUBLIC_KEY=
MINIO_DOMAIN=
# 微信支付信息
WECHAT_MERCHANT_ID=
WECHAT_MERCHANT_SERIAL_NUMBER=
WECHAT_PRIVATE_KEY_PATH=
WECHAT_API_V3_KEY=
# 微信小程序配置
WECHAT_APP_ID=
WECHAT_SECRET=
# 数据库涉密信息保存加密
SECURITY_AES_KEY=
SECURITY_AES_IV=