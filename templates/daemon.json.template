{"data-root": "/var/lib/docker", "exec-opts": ["native.cgroupdriver=systemd"], "log-driver": "json-file", "log-opts": {"max-size": "100m", "max-file": "3"}, "storage-driver": "overlay2", "group": "docker", "registry-mirrors": ["https://registry.docker-cn.com", "https://mirror.ccs.tencentyun.com", "https://docker.1panel.live/", "https://82m9ar63.mirror.aliyuncs.com", "https://2a6bf1988cb6428c877f723ec7530dbc.mirror.swr.myhuaweicloud.com", "https://docker.m.daocloud.io", "https://hub-mirror.c.163.com", "https://mirror.baidubce.com", "https://your_preferred_mirror", "https://dockerhub.icu", "https://docker.registry.cyou", "https://docker-cf.registry.cyou", "https://dockercf.jsdelivr.fyi", "https://docker.jsdelivr.fyi", "https://dockertest.jsdelivr.fyi", "https://mirror.aliyuncs.com", "https://dockerproxy.com", "https://mirror.baidubce.com", "https://docker.m.daocloud.io", "https://docker.nju.edu.cn", "https://docker.mirrors.sjtug.sjtu.edu.cn", "https://docker.mirrors.ustc.edu.cn", "https://mirror.iscas.ac.cn", "https://docker.rainbond.cc", "https://docker.1ms.run", "https://docker.xuanyuan.me"], "live-restore": true, "metrics-addr": "127.0.0.1:9323", "experimental": false, "default-ulimits": {"nofile": {"Name": "nofile", "Hard": 65536, "Soft": 65536}}, "features": {"buildkit": true}}