[Unit]
Description=Docker Application Container Engine
Documentation=https://docs.docker.com
After=network-online.target firewalld.service
Wants=network-online.target

[Service]
Type=notify
Environment="PATH=$DOCKER_HOME:/bin:/sbin:/usr/bin:/usr/sbin"
ExecStart=$DOCKER_HOME/dockerd $DOCKER_OPTS
ExecStartPost=/sbin/iptables -P FORWARD DROP
ExecStartPost=/sbin/iptables -I FORWARD 1 -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT
ExecStartPost=/sbin/iptables -I FORWARD 2 -s **********/16 -j ACCEPT
ExecStartPost=/sbin/iptables -I FORWARD 3 -d **********/16 -j ACCEPT
ExecReload=/bin/kill -s HUP $MAINPID
LimitNOFILE=infinity
LimitNPROC=infinity
LimitCORE=infinity
TimeoutStartSec=0
Delegate=yes
KillMode=process
Restart=on-failure
StartLimitBurst=3
StartLimitInterval=60s

[Install]
WantedBy=multi-user.target
