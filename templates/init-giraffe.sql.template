CREATE DATABASE IF NOT EXISTS `giraffe-wash` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `giraffe-wash`;

create table app_user
(
    id              bigint auto_increment comment '用户主键ID'
        primary key,
    openid          varchar(64)                           not null comment '微信 openid',
    unionid         varchar(64)                           null comment '微信 unionid',
    phone           varchar(11)                           null comment '手机号',
    nickname        varchar(50)                           null comment '微信昵称',
    avatar_url      varchar(255)                          null comment '微信头像 URL',
    gender          tinyint     default 0                 null comment '性别 (0未知/1男/2女)',
    language        varchar(10) default 'zh_CN'           null comment '语言',
    city            varchar(20)                           null comment '城市',
    province        varchar(20)                           null comment '省份',
    country         varchar(20)                           null comment '国家',
    register_source tinyint     default 1                 not null comment '注册来源 (1小程序/2公众号/3APP/4H5/5PC)',
    register_time   datetime    default CURRENT_TIMESTAMP not null comment '注册时间',
    last_login_time datetime                              null comment '最后登录时间',
    role            varchar(32) default 'customer'        not null comment '角色编码',
    login_count     int         default 0                 not null comment '登录次数',
    status          tinyint     default 0                 not null comment '状态 (0正常/1禁用/2注销/3冻结)',
    device_info     json                                  null comment '设备信息',
    remark          varchar(200)                          null comment '备注',
    creator         bigint                                not null comment '创建者ID',
    create_time     datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater         bigint                                null comment '更新者ID',
    update_time     datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted         bit         default b'0'              not null comment '删除标记 (0正常/1已删除)',
    constraint uk_app_user_openid
        unique (openid),
    constraint uk_app_user_phone
        unique (phone),
    constraint uk_app_user_unionid
        unique (unionid)
)
    comment '微信用户信息表';

create index idx_app_user_register_source
    on app_user (register_source);

create index idx_app_user_register_time
    on app_user (register_time);

create index idx_app_user_role
    on app_user (role);

create index idx_app_user_status
    on app_user (status);

create index idx_create_time
    on app_user (create_time);

create index idx_deleted
    on app_user (deleted);

create index idx_update_time
    on app_user (update_time);

create table member_address
(
    id             bigint auto_increment comment '地址主键ID'
        primary key,
    member_id      bigint                             not null comment '关联会员ID',
    address_code   int                                not null comment '区域编码',
    detail_address varchar(200)                       not null comment '详细地址',
    contact_name   varchar(30)                        not null comment '联系人姓名',
    contact_phone  varchar(11)                        not null comment '联系电话',
    longitude      decimal(10, 7)                     null comment '经度',
    latitude       decimal(10, 7)                     null comment '纬度',
    is_default     bit      default b'0'              not null comment '是否默认地址（0否 1是）',
    creator        bigint                             not null comment '创建者',
    create_time    datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updater        bigint                             null comment '更新者',
    update_time    datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted        bit      default b'0'              not null comment '删除标记（0正常 / 1已删除）'
)
    comment '会员收发货地址表';

create index idx_address_default
    on member_address (member_id, is_default);

create index idx_address_member
    on member_address (member_id);

create index idx_create_time
    on member_address (create_time);

create index idx_deleted
    on member_address (deleted);

create index idx_update_time
    on member_address (update_time);

create table member_identity
(
    id             bigint auto_increment comment '会员主键ID'
        primary key,
    app_user_id    bigint                                   not null comment '关联app_user用户ID',
    member_no      varchar(20)                              not null comment '会员编号（M+时间戳+随机数）',
    level          tinyint                                  not null comment '会员等级（1普通/2银卡/3金卡/4钻石）',
    points         int            default 0                 not null comment '当前可用积分',
    total_points   int            default 0                 not null comment '累计获得积分',
    total_consumed decimal(10, 2) default 0.00              not null comment '累计消费金额',
    phone          varchar(11)                              not null comment '绑定手机号',
    real_name      varchar(30)                              null comment '实名认证姓名',
    id_card        varchar(18)                              null comment '身份证号（加密存储）',
    status         tinyint        default 0                 not null comment '状态（0正常/1冻结）',
    creator        bigint                                   not null comment '创建者',
    create_time    datetime       default CURRENT_TIMESTAMP not null comment '创建时间',
    updater        bigint                                   null comment '更新者',
    update_time    datetime       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted        tinyint(1)     default 0                 not null comment '删除标记(0正常/1已删除)',
    constraint uk_member_app_user
        unique (app_user_id),
    constraint uk_member_no
        unique (member_no),
    constraint uk_member_phone
        unique (phone)
)
    comment '会员身份管理表';

create index idx_deleted_create_time
    on member_identity (deleted, create_time);

create index idx_deleted_update_time
    on member_identity (deleted, update_time);

create index idx_level_deleted
    on member_identity (level, deleted);

create index idx_status_deleted
    on member_identity (status, deleted);

create table service_category
(
    id          bigint auto_increment comment '分类主键ID'
        primary key,
    parent_id   bigint   default 0                 not null comment '父分类ID（0为顶级）',
    name        varchar(50)                        not null comment '分类名称',
    code        varchar(20)                        not null comment '分类编码（唯一业务标识）',
    icon        varchar(255)                       null comment '分类图标URL',
    sort        int      default 0                 not null comment '排序权重（越大越靠前）',
    level       tinyint  default 1                 not null comment '分类层级（1-一级 2-二级...）',
    path        varchar(200)                       not null comment '分类路径（如：0,1,5）',
    status      tinyint  default 0                 not null comment '状态：0-正常 1-停用',
    description varchar(200)                       null comment '分类描述',
    creator     bigint                             not null comment '创建人ID',
    create_time datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updater     bigint                             null comment '更新人ID',
    update_time datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted     bit      default b'0'              not null comment '删除标记：0-正常 1-已删除',
    constraint uk_code
        unique (code) comment '分类编码唯一索引'
)
    comment '服务分类表（支持多级树形结构）';

create index idx_create_time
    on service_category (create_time);

create index idx_deleted
    on service_category (deleted);

create index idx_parent
    on service_category (parent_id)
    comment '父分类查询优化';

create index idx_path
    on service_category (path(50))
    comment '路径查询优化';

create index idx_sort
    on service_category (sort)
    comment '排序查询优化';

create index idx_status
    on service_category (status)
    comment '状态筛选优化';

create index idx_update_time
    on service_category (update_time);
