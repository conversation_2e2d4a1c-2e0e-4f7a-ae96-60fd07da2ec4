#!/bin/bash

_app_completions() {
    local cur prev words cword
    _init_completion -n : || return

    local cmd="${words[1]}"
    local subcmd="${words[2]}"
    local APP_BIN="$(which app)"

    # 获取补全数据
    local completion_data
    completion_data=$(APP_COMPLETION=1 "$APP_BIN" __completion_data 2>/dev/null)

    # 初始化变量容器
    local -a COMMAND_KEYS=()
    declare -A COMMAND_STATIC_SUB_DESC=()
    declare -A COMMAND_DYNAMIC_SUB_LIST=()

    # 安全加载
    eval "$(echo "$completion_data" | grep '^declare -a COMMAND_KEYS=')"
    eval "$(echo "$completion_data" | grep '^declare -A COMMAND_STATIC_SUB_DESC=')"
    eval "$(echo "$completion_data" | grep '^declare -A COMMAND_DYNAMIC_SUB_LIST=')"
    eval "$(echo "$completion_data" | grep '^declare -A CONTAINER_CONFIG=')"

    # 一级命令补全
    if [[ $cword -eq 1 ]]; then
        COMPREPLY=( $(compgen -W "${COMMAND_KEYS[*]}" -- "$cur") )
        return
    fi

    if [[ "$cmd" == "explain" && $cword -eq 2 ]]; then
        # 排除 explain 自身，避免重复
        local filtered_commands=()
        for command in "${COMMAND_KEYS[@]}"; do
            [[ "$command" != "explain" ]] && filtered_commands+=("$command")
        done
        COMPREPLY=( $(compgen -W "${filtered_commands[*]}" -- "$cur") )
        return
    fi

    # 二级命令为 start/restart/stop/status/debug 时，补全容器服务名
    if [[ "$cmd" =~ ^(start|restart|stop|status|update|debug|images)$ && $cword -eq 2 ]]; then
        local services
        services=$(docker compose -f $PROJECT_HOME/docker-compose.yml config --services 2>/dev/null)
        COMPREPLY=( $(compgen -W "$services" -- "$cur") )
        return
    fi

    # package 命令补全容器名
    if [[ "$cmd" =~ ^(package|upgrade)$ && $cword -eq 2 ]]; then
        COMPREPLY=( $(compgen -W "${!CONTAINER_CONFIG[@]}" -- "$cur") )
        return
    fi

    # 二级命令补全（静态 + 动态）
    if [[ $cword -eq 2 ]]; then
        local static=()
        local dynamic=()

        for key in "${!COMMAND_STATIC_SUB_DESC[@]}"; do
            [[ "$key" == "$cmd:"* ]] && static+=( "${key#*:}" )
        done

        [[ -n "${COMMAND_DYNAMIC_SUB_LIST[$cmd]}" ]] && dynamic=( ${COMMAND_DYNAMIC_SUB_LIST[$cmd]} )

        COMPREPLY=( $(compgen -W "${static[*]} ${dynamic[*]}" -- "$cur") )
        return
    fi

    if [[ "$cmd" == "build" && $cword -eq 3 ]]; then
        local service="$subcmd"
        local repo="${CONTAINER_CONFIG[$service]}"

        if [[ -n "$repo" ]]; then
            local branches
            branches=$(git ls-remote --heads "$repo" 2>/dev/null | awk '{print $2}' | sed 's|refs/heads/||')
            COMPREPLY=( $(compgen -W "$branches" -- "$cur") )
        fi
        return
    fi

    # 第三级参数的特殊处理（如 update、package）
    # if [[ "$cmd" =~ ^(update|package)$ && $cword -eq 3 ]]; then
    #     return
    # fi
}

complete -F _app_completions app