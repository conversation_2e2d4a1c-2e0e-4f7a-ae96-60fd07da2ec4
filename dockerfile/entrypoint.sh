#!/bin/bash

CURRENT_TIME=$(date "+%Y-%m-%d %H:%M:%S")
mkdir -p /app/config

if [ ! -f /app/config/application.yml ]; then
    echo "[$CURRENT_TIME] User configuration not found. Copying default configuration..."
    mkdir -p /app/default/config
    if [ -f /app/default/config/application.yml ]; then
        cp /app/default/config/application.yml /app/config/
        echo "[$CURRENT_TIME] Default configuration copied to /app/config/application.yml"
    else
        echo "[$CURRENT_TIME] WARNING: Default configuration not found at /app/default/config/application.yml"
        echo "[$CURRENT_TIME] Starting application without configuration"
    fi
else
    echo "[$CURRENT_TIME] Using existing user configuration at /app/config/application.yml"
fi

CONFIG_LOCATION="file:/app/config/application.yml"

echo "[$CURRENT_TIME] Starting Giraffe Wash application with config: $CONFIG_LOCATION"

exec java $JAVA_OPTS  -jar /app/giraffe-wash.jar --spring.config.location=$CONFIG_LOCATION "$@"