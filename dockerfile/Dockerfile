FROM maven:3.9.6-eclipse-temurin-17 AS builder

WORKDIR /build

# 先拷贝 pom.xml 独立缓存依赖（可选优化）
COPY pom.xml ./

# 再拷贝源码
COPY src ./src
COPY dockerfile ./dockerfile

# 编译打包
RUN mvn clean package -DskipTests

# 运行阶段
FROM eclipse-temurin:17-jre

WORKDIR /app

COPY --from=builder /build/target/*.jar giraffe-wash.jar
COPY --from=builder /build/src/main/resources/application.yml default/config/application.yml
COPY dockerfile/entrypoint.sh /entrypoint.sh

RUN mkdir -p /app/config \
    && chmod +x /entrypoint.sh

ENTRYPOINT ["/entrypoint.sh"]
