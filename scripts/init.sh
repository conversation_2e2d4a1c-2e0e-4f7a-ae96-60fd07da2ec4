#!/bin/bash

# source config file
source "$(find ../ -name config.env)" >/dev/null 2>&1


# 项目配置
APP_BIN="/usr/bin/app"
# 定义一个关联数组，用于配置容器名称、项目仓库地址和 Dockerfile 路径
# key: 容器名称
# value: 项目仓库地址, Dockerfile 路径
declare -A CONTAINER_CONFIG=(
    ["giraffe-wash-java"]="*************:sunqiankun369440/giraffe-wash.git"
)

# 日志级别
LOG_LEVEL_INFO=1
LOG_LEVEL_WARN=2
LOG_LEVEL_ERROR=3
LOG_LEVEL_DEBUG=4

# 设置日志级别（默认为INFO）
LOG_LEVEL=$LOG_LEVEL_INFO

# 颜色定义
COLOR_TIMESTAMP="\033[38;5;242m"  # 浅灰色
COLOR_INFO="\033[38;5;122m"       # 绿色
COLOR_WARN="\033[38;5;150m"       # 黄色
COLOR_ERROR="\033[38;5;205m"      # 红色
COLOR_DEBUG="\033[38;5;75m"       # 青色
COLOR_RESET="\033[0m"             # 重置颜色

echoGrays()   { echo -e $'\e[38;5;249m'"$1"$'\e[0m'; }
echoLightGrays() { echo -e $'\e[38;5;243m'"$1"$'\e[0m'; }
# 命令注册顺序（决定 help 输出顺序）
COMMAND_KEYS=( init install uninstall build debug images update start restart stop status package upgrade log vim explain help )

# 命令说明）
declare -A COMMAND_DESC=(
    [init]="初始化环境"
    [install]="安装 Docker 和 Docker Compose"
    [uninstall]="卸载 Docker 和 Docker Compose"
    [build]="构建镜像"
    [debug]="调试容器"
    [images]="查看服务镜像列表"
    [update]="更新容器"
    [start]="启动容器"
    [restart]="重启容器"
    [stop]="停止容器"
    [status]="显示容器状态"
    [package]="打包镜像"
    [upgrade]="升级镜像"
    [explain]="显示命令说明"
    [log]="查看日志"
    [vim]="修改 app 命令配置"
    [help]="显示此帮助信息"
)

# 命令子参数说明
declare -A COMMAND_STATIC_SUB_DESC=(
    [init:all]="一键初始化"
    [init:delete]="仅删除环境,不删除docker以及容器"
    [init:force]="强制重新初始化"
    [init:sql]="渲染 SQL 初始化文件"
    [init:compose]="渲染 compose 初始化文件"
    [init:ssl]="渲染 ssl 证书"
)

# 动态子命令生成
services=$(printf "%s " "${!CONTAINER_CONFIG[@]}")
declare -A COMMAND_DYNAMIC_SUB_LIST=(
    [build]="$services"
)

# 自动补全命令补全数据
if [[ "$1" == "__completion_data" && -n "$APP_COMPLETION" ]]; then
    declare -p COMMAND_KEYS
    declare -p COMMAND_STATIC_SUB_DESC
    declare -A COMMAND_DYNAMIC_SUB_LIST
    declare -p CONTAINER_CONFIG

    # 动态生成每个命令的子命令列表（仅包含名称，说明 info 可省）
    for cmd in "${COMMAND_KEYS[@]}"; do
        subcmds=$(generate_dynamic_subcommands "$cmd" 2>/dev/null)
        [[ -n "$subcmds" ]] && COMMAND_DYNAMIC_SUB_LIST["$cmd"]="$subcmds"
    done

    declare -p COMMAND_DYNAMIC_SUB_LIST
    exit 0
fi

# 美观输出帮助说明
USAGE() {
  echo "Usage: $(basename "$0") [command] [options]"
  echo

  local main_indent=2       # 主命令缩进空格数
  local main_desc_start=26  # 主命令描述起始列（相对于行首）

  local sub_indent=4        # 子命令缩进空格数
  local symbol_width=2      # 子命令符号宽度（├─/└─）

  for cmd in "${COMMAND_KEYS[@]}"; do
    local desc="${COMMAND_DESC[$cmd]}"
    local cmd_len=${#cmd}
    local padding=$((main_desc_start - main_indent - cmd_len))
    (( padding < 2 )) && padding=2

    # 主命令打印（缩进 + 命令名 + padding + 描述）
    printf "%*s%s%*s%s\n" "$main_indent" "" "$cmd" "$padding" "" "$desc"

    # 子命令收集
    local -a sub_names=()
    local -a sub_descs=()

    # 静态子命令
    for key in "${!COMMAND_STATIC_SUB_DESC[@]}"; do
      [[ "$key" == "$cmd:"* ]] || continue
      local subcmd="${key#*:}"
      sub_names+=( "$subcmd" )
      sub_descs+=( "${COMMAND_STATIC_SUB_DESC[$key]}" )
    done

    # 动态子命令（无描述）
    local dynamic_subs="${COMMAND_DYNAMIC_SUB_LIST[$cmd]}"
    if [[ -n "$dynamic_subs" ]]; then
      for subcmd in $dynamic_subs; do
        sub_names+=( "$subcmd" )
        sub_descs+=( "" )
      done
    fi

    local count=${#sub_names[@]}
    if (( count > 0 )); then
      # 计算最大子命令名长度
      local max_sub_len=0
      for name in "${sub_names[@]}"; do
        (( ${#name} > max_sub_len )) && max_sub_len=${#name}
      done
      (( max_sub_len < 12 )) && max_sub_len=12

      # 计算子命令描述起点（相对于行首）
      local sub_desc_start=$((sub_indent + symbol_width + max_sub_len + 7))

      for ((i=0; i < count; i++)); do
        local prefix="├─"
        (( i == count-1 )) && prefix="└─"
        local name="${sub_names[i]}"
        local desc="${sub_descs[i]}"

        if [[ -n "$desc" ]]; then
          # 子命令名后补足空格对齐描述列
          local name_padding=$((sub_desc_start - sub_indent - symbol_width - ${#name}))
          (( name_padding < 2 )) && name_padding=2

          printf "%*s%s %s%*s%s\n" \
            "$sub_indent" "" "$prefix" "$name" "$name_padding" "" "$desc"
        else
          printf "%*s%s %s\n" "$sub_indent" "" "$prefix" "$name"
        fi
      done
    fi
  done
}

# 日志函数
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date +"%H:%M:%S")
    
    # 根据日志级别决定是否显示
    if [ $level -lt $LOG_LEVEL ]; then
        return
    fi
    
    # 设置日志级别的颜色
    local level_color=""
    case $level in
        $LOG_LEVEL_INFO)
            level_color=$COLOR_INFO
            level_text="INFO"
            ;;
        $LOG_LEVEL_WARN)
            level_color=$COLOR_WARN
            level_text="WARN"
            ;;
        $LOG_LEVEL_ERROR)
            level_color=$COLOR_ERROR
            level_text="ERROR"
            ;;
        $LOG_LEVEL_DEBUG)
            level_color=$COLOR_DEBUG
            level_text="DEBUG"
            ;;
        *)
            level_color=$COLOR_RESET
            level_text="UNKNOWN"
            ;;
    esac
    
    echo -e "${COLOR_TIMESTAMP}[${timestamp}]${COLOR_RESET} ${level_color}[${level_text}]${COLOR_RESET} ${message}"
}

# 日志函数
log_info() {
    log_message $LOG_LEVEL_INFO "$1"
}

log_warn() {
    log_message $LOG_LEVEL_WARN "$1"
}

log_error() {
    log_message $LOG_LEVEL_ERROR "$1"
}

log_debug() {
    log_message $LOG_LEVEL_DEBUG "$1"
}

log_load() {
    BLOCKS=(' ' '▏' '▎' '▍' '▌' '▋' '▊' '▉' '█')
    LOADING_ANIMATION=('⠿' '⠾' '⠽' '⠼' '⠻' '⠺' '⠷' '⠹')
    local message=$1
    local progress=$2
    local is_failed=$3
    local timestamp=$(date +"%H:%M:%S")

    progress=$(( progress < 0 ? 0 : progress > 100 ? 100 : progress ))

    local nanos=$(date +%s%N)
    local anim_index=$(( (nanos / 125000000) % ${#LOADING_ANIMATION[@]} ))
    local anim_char="${LOADING_ANIMATION[$anim_index]}"

    local bar_width=40
    local total_blocks=$(( bar_width * 8 ))

    local filled_blocks=$(( progress * total_blocks / 100 ))
    local full_chars=$(( filled_blocks / 8 ))
    local partial_char_index=$(( filled_blocks % 8 ))

    local progress_bar=""
    if [ $full_chars -gt 0 ]; then
        progress_bar+="$(printf '%0.s█' $(seq 1 $full_chars))"
    fi
    if [ $partial_char_index -gt 0 ]; then
        progress_bar+="${BLOCKS[$partial_char_index]}"
    fi
    local empty_chars=$(( bar_width - full_chars - (partial_char_index > 0 ? 1 : 0) ))
    if [ $empty_chars -gt 0 ]; then
        progress_bar+="$(printf '%0.s ' $(seq 1 $empty_chars))"
    fi

    local log_prefix="${COLOR_TIMESTAMP}[${timestamp}]${COLOR_RESET} ${COLOR_INFO}[INFO]${COLOR_RESET}"
    local line="${log_prefix} ${COLOR_INFO}${anim_char}${COLOR_RESET}${message} ${progress_bar} ${progress}%%"

    if [[ "$line" == "$_last_line" ]]; then
        return
    fi
    _last_line="$line"

    printf "\r$line"

    if [ "$progress" = "100" ] || [ "$is_failed" = "1" ]; then
        echo ""
        _last_line=""
    fi
}

# 进度条
run_with_progress() {
    local message="$1"
    shift
    local command=("$@")
    local progress=0
    local status=1
    local anim_pid

    (
        while true; do
            if [ $progress -lt 99 ]; then
                progress=$((progress + 1))
            fi
            log_load "$message" $progress
            sleep 0.08
        done
    ) &
    anim_pid=$!

    trap "kill $anim_pid 2>/dev/null" EXIT

    "${command[@]}"
    status=$?

    kill "$anim_pid" 2>/dev/null
    wait "$anim_pid" 2>/dev/null

    trap - EXIT
    if [ $status -eq 0 ]; then
        log_load "$message" 100 0
    else
        log_load "$message" 99 1
    fi

    return $status
}

# 检查日志文件是否存在
log_file_check() {
    if [ ! -f /var/log/giraffe-wash.log ]; then
        sudo touch /var/log/giraffe-wash.log
        sudo chmod 666 /var/log/giraffe-wash.log
    fi
}

# 日志输出到文件 /var/log/giraffe-wash.log
log_to_file() {
    local log_file="/var/log/giraffe-wash.log"
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$log_file"
}

# 初始化代理
proxy_init() {    
    if [ -n "$PROXY_IP" ]; then
        export http_proxy="http://$PROXY_IP" 
        export https_proxy="http://$PROXY_IP"
        log_info "Proxy initialized successfully."
    fi
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 参数解析
parse_arguments() {

    local service=""
    case "$1" in
        init)
            args="${2:-}"
            if [ "$args" = force ]; then
                log_warn "Prepare to reinitialize the environment..."
                environment_remove
            elif [ "$args" = delete ]; then
                log_warn "Prepare to delete the environment..."
                environment_remove
                exit 0
            elif [ "$args" = sql ]; then
                generate_sql_init_file
                exit 0
            elif [ "$args" = compose ]; then
                generate_compose_init_file
                exit 0
            elif [ "$args" = ssl ]; then
                generate_ssl_cert
                exit 0
            elif [ "$args" = all ]; then
                generate_ssl_cert
                compose_file_check
                generate_compose_init_file
                exit 0
            fi
            if environment_init; then
                log_info "Environment initialized successfully."
                log_info "------------------------"
                log_info "PROJECT_HOME: $PROJECT_HOME"
                log_info "COMMAND: app , please run 'app -h' to see the usage."
                log_info "------------------------"
            else
                log_error "Environment initialization failed."
                exit 1
            fi
            ;;
        install)
            software_install
            if [ $? -ne 0 ]; then
                log_error "Failed to install docker and compose."
                exit 1
            else
                log_info "Installed docker and docker compose successfully."
            fi
            ;;
        uninstall)
            software_uninstall
            if [ $? -ne 0 ]; then
                log_error "Failed uninstall docker and compose."
                exit 1
            fi
            shift
            log_info "Uninstall docker and docker compose successfully."
            ;;
        build)
            args="${2:-all}"
            local branch=${3:-master}
            if [ $args = local ]; then
                docker exec -it builder bash script/build.sh
                args="giraffe-wash-java"
            fi
            dangling=$(docker images -f "dangling=true" -q)
            if [[ -n "$dangling" ]]; then
              docker rmi $dangling
            else
              echo "没有悬挂镜像，无需清理。"
            fi
            build_docker_images $args $branch
            ;;
        debug)
            args="${2:-}"
            if [ -z "$args" ]; then
                log_error "Please input service name."
                exit 1
            fi
            log_info "Debug $args service"
            docker run --rm -it --net container:$args --pid container:$args nicolaka/netshoot:latest /bin/sh
            ;;
        images)
            args="${2:-}"
            docker images --filter=reference="$args*" --format 'table {{.Repository}}:{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}'
            ;;
        update)
            args="${2:-}"
            verify_docker_installation
            log_info "Recreate $args container"
            docker compose -f $PROJECT_HOME/docker-compose.yml up -d $args --force-recreate
            if [ $? -ne 0 ]; then
                log_error "Failed to recreate $args container."
                log_to_file "Failed to recreate $args container."
                exit 1
            else
                log_info "Recreate $args container successfully."
                log_to_file "Recreate $args container successfully."
            fi
            ;;
        start)
            verify_docker_installation
            args="${2:-}"
            start_log() {
                if [ -n "$args" ]; then
                    log_to_file "Start $args"
                else
                    log_to_file "Start all service"
                fi
            }
            if [ -f $PROJECT_HOME/docker-compose.yml ]; then
                check_service_images
                docker compose -f $PROJECT_HOME/docker-compose.yml up -d $args
                start_log
            else
                log_warn "docker-compose.yml file is not exist, initing..."
                log_info "If you first run this command, it will init the docker-compose.yml file."
                generate_ssl_cert
                compose_file_check
                generate_compose_init_file
                if [ $? -ne 0 ]; then
                    log_error "$PROJECT_HOME/docker-compose.yml file is not exist, exit."
                    exit 1
                else
                    check_service_images
                    docker compose -f $PROJECT_HOME/docker-compose.yml up -d $args
                    start_log
                fi
            fi
            ;;
        restart)
            args="${2:-}"
            verify_docker_installation
            restart_log() {
                if [ -n "$args" ]; then
                    log_to_file "Restart $args"
                else
                    log_to_file "Restart all service"
                fi
            }
            docker compose -f $PROJECT_HOME/docker-compose.yml restart $args
            restart_log
            ;;
        stop)
            verify_docker_installation
            args="${2:-}"
            stop_log() {
                if [ -n "$args" ]; then
                    log_to_file "Stop $args"
                else
                    log_to_file "Stop all service"
                fi
            }
            docker compose -f $PROJECT_HOME/docker-compose.yml down $args
            stop_log
            ;;
        status)
            verify_docker_installation
            args="${2:-}"
            docker compose -f $PROJECT_HOME/docker-compose.yml ps $args
            ;;
        package)            
            args="${2:-}"
            container_images_package $args
            ;;
        upgrade)
            args="${2:-}"
            if [ -z "$args" ]; then
                log_error "Please input package name."
                exit 1
            fi
            download_and_clean_package_files $args
            unzip_upgrade_package $args
            log_to_file "Upgrade $args service"
            ;;
        log)
            tail -n 20 /var/log/giraffe-wash.log
            ;;
        vim)
            if command_exists vim; then
                vim $APP_BIN
            else
                vi $APP_BIN
            fi
            log_to_file "Open $APP_BIN with vim"
            ;;
        explain)
            explain_command "$@"
            ;;
        help|*)
            USAGE
            exit 0
            ;;
    esac
}

# 命令说明
explain_command() {
    if [[ "$1" == "explain" ]]; then
        if [[ -z "$2" ]]; then
            echo -e "${COLOR_INFO}📘 可用命令说明：${COLOR_RESET}\n"
            for key in "${COMMAND_KEYS[@]}"; do
                printf "  ${COLOR_DEBUG}%-12s${COLOR_RESET} %b\n" " $key" "${COMMAND_DESC[$key]}"
            done
            echo -e "\n👉 使用 ${COLOR_WARN}app explain <命令>${COLOR_RESET} 查看某个命令的详细说明"
            exit 0
        fi

        local cmd="$2"
        local subcmd="$3"

        echo -e "${COLOR_INFO}🧩 $cmd 说明:${COLOR_RESET}\t\t${COMMAND_DESC[$cmd]:-${COLOR_WARN}暂无说明${COLOR_RESET}}"

        # 打印静态子参数说明
        local found_static_subdesc=false
        for key in "${!COMMAND_STATIC_SUB_DESC[@]}"; do
            if [[ "$key" == "$cmd:"* ]]; then
                if ! $found_static_subdesc; then
                    echo -e "\n${COLOR_INFO}🔹 子参数说明:${COLOR_RESET}"
                    found_static_subdesc=true
                fi
                local sub="${key#"$cmd:"}"
                printf "  ${COLOR_DEBUG}%-21s${COLOR_RESET} %s\n" " $sub" "${COMMAND_STATIC_SUB_DESC[$key]}"
            fi
        done

        # 打印动态子参数列表（如服务名）
        if [[ -n "${COMMAND_DYNAMIC_SUB_LIST[$cmd]}" ]]; then
            echo -e "\n${COLOR_INFO}📦 可用子命令:${COLOR_RESET}"
            for svc in ${COMMAND_DYNAMIC_SUB_LIST[$cmd]}; do
                printf "  ${COLOR_WARN}%s${COLOR_RESET}\n" "$svc"
            done
        fi

        # 特别情况：构建/更新/打包展示仓库地址
        if [[ "$cmd" =~ ^(build|package)$ && ${#CONTAINER_CONFIG[@]} -gt 0 ]]; then
            echo -e "\n${COLOR_INFO}🔗 服务与仓库地址:${COLOR_RESET}"
            for svc in "${!CONTAINER_CONFIG[@]}"; do
                printf "  ${COLOR_WARN}%-21s${COLOR_RESET} %s\n" "$svc" "${CONTAINER_CONFIG[$svc]}"
            done
        fi

        exit 0
    fi
}

# app 命令自动补全
command_app_complete() {
    mv $PROJECT_HOME/config/bash_completion_app /etc/bash_completion.d/app
    chmod +x /etc/bash_completion.d/app
    source /etc/bash_completion.d/app
}

# 生成配置文件
generate_template() {
    find $(find .. -type d -name templates) -type f -name "*.template" | while read -r template_file; do
        local relative_path="${template_file#$(find .. -type d -name templates)/}"
        local target_path="$PROJECT_HOME/config/$(basename "${relative_path%.template}")"

        if [ -f "$target_path" ]; then
            log_info "File $target_path already exists, skip."
        else
            mkdir -p "$(dirname "$target_path")"
            if [ "$(basename "$template_file")" = "bash_completion_app.template" ]; then
                cp $template_file $target_path
                sed -i "2i PROJECT_HOME=$PROJECT_HOME" $target_path
            elif [ "$(basename "$template_file")" = "nginx_default.conf.template" ]; then
                cp $template_file $target_path
            else
                envsubst < "$template_file" > "$target_path"
                log_info "Created $target_path from template."
            fi

            if [[ "$target_path" == *.env ]]; then
                chmod 666 "$target_path"
                log_info "Set permission 666 for $target_path"
            fi
        fi
    done
}

# 配置环境变量
environment_init() {
    log_file_check
    if [ "$(id -u)" -ne 0 ]; then
        log_error "Please run this script with root privileges"
        exit 1
    fi
    log_info "Check environment..."
    
    # 创建项目家目录
    if [ ! -d "$PROJECT_HOME" ]; then
        sudo mkdir -p $PROJECT_HOME
        log_info "Directory $PROJECT_HOME is not exist, create it."
    else
        log_info "Directory $PROJECT_HOME is exist, skip it."
    fi

    # 创建 app 命令
    if [ ! -f "$PROJECT_HOME/app" ]; then
        sudo cp "$0" $PROJECT_HOME/app
        SHELL_CONFIG_FILE=$(find ../ -name "config.env")
        insert_block=$(sed '/^# service_config/Q' $SHELL_CONFIG_FILE)
        awk -v block="$insert_block" '
            { print }
            $0 ~ /^# 项目配置$/ {
                print block
            }            
            ' $PROJECT_HOME/app > $PROJECT_HOME/app.tmp && mv $PROJECT_HOME/app.tmp $PROJECT_HOME/app
        sed -i '1,2!{/^# 项目配置/,$!d}' $PROJECT_HOME/app
        log_info "File $PROJECT_HOME/app is not exist, copy it."
    else
        log_info "File $PROJECT_HOME/app is exist, skip it."
    fi
    sudo chmod 777 $PROJECT_HOME/app

    # 创建 app 命令符号链接
    if [ ! -L "$APP_BIN" ]; then
        sudo ln -s $PROJECT_HOME/app $APP_BIN
        log_info "Symbol link $APP_BIN is not exist, create it."
    else
        log_info "Symbol link $APP_BIN is exist, skip it."
    fi

    generate_template

    # 配置 app 命令自动补全
    command_app_complete
    log_info "App command completion configuration completed."

    # 配置家目录权限
    if [ -n "$SUDO_USER" ]; then
        sudo chown -R "$SUDO_USER":"$SUDO_USER" "$PROJECT_HOME"
        log_info "Set $PROJECT_HOME directory ownership to $SUDO_USER"
    else
        log_error "Not found sudo user, cannot set $PROJECT_HOME directory ownership."
    fi
    log_to_file "Environment initialization completed."
}

# 删除环境
environment_remove() {
    if [ "$(id -u)" -ne 0 ]; then
        log_error "Please run this script with root privileges"
        exit 1
    fi
    log_warn "Remove environment..."
    # 删除项目家目录
    if [ -d "$PROJECT_HOME" ]; then
        sudo rm -rf $PROJECT_HOME
        log_warn "Directory $PROJECT_HOME is exist, remove it."
    else
        log_info "Directory $PROJECT_HOME is not exist, skip it."
        exit 0
    fi
    # 删除 app 命令符号链接
    if [ -L "$APP_BIN" ]; then
        sudo rm -f $APP_BIN
        log_warn "Symbol link $APP_BIN is exist, remove it."
    else
        log_info "Symbol link $APP_BIN is not exist, skip it."
    fi
    # 删除 app 命令自动补全
    if [ -f "/etc/bash_completion.d/app" ]; then
        sudo rm -f /etc/bash_completion.d/app
        log_warn "File /etc/bash_completion.d/app is exist, remove it."
    else
        log_info "File /etc/bash_completion.d/app is not exist, skip it."
    fi
    log_to_file "Environment remove completed."
}

# 获取主机架构
detect_architecture() {
    log_info "Detecting system architecture..."
    ARCH=$(uname -m)
    case $ARCH in
        x86_64|amd64)
            ARCH="x86_64"
            log_info "Detected architecture: $ARCH"
            ;;
        aarch64|arm64)
            ARCH="aarch64"
            log_info "Detected architecture: $ARCH"
            ;;
        armv7l|armv8l)
            ARCH="armhf"
            log_info "Detected architecture: $ARCH"
            ;;
        *)
            log_error "Unsupported architecture: $ARCH"
            return 1
            ;;
    esac
    return 0
}

# 获取最新Docker版本
get_latest_docker_version() {
    log_info "Getting the latest Docker version..."
    local API_URL="https://api.github.com/repos/moby/moby/releases/latest"
    local RESPONSE=$(curl -s "$API_URL")
    local TAG=$(echo "$RESPONSE" | grep -oP '"tag_name": "\K[^\"]+' | sed 's/v//')
    
    if [ -n "$TAG" ]; then
        log_info "Version obtained from API: $TAG"
        DOCKER_LATEST_VERSION=$TAG
        return 0
    else
        log_error "Failed to get the latest Docker version"
        return 1
    fi
}

# 配置Docker用户和符号链接函数
configure_docker_user() {
    log_info "Adding Docker symbolic link and configuring user groups..."
    ln -s $DOCKER_HOME/docker /usr/bin/docker
    if ! getent group docker >/dev/null; then
        sudo groupadd docker
    fi
    
    local CURRENT_USER=${SUDO_USER:-$USER}
    if [ "$CURRENT_USER" != "root" ]; then
        if ! id -nG "$CURRENT_USER" | grep -qw docker; then
            sudo usermod -aG docker "$CURRENT_USER"
        fi
    fi
    log_info "Symbolic link and group configuration completed."
}

# 下载并解压Docker文件函数，增加错误处理
download_and_extract_docker() {
    DOCKER_LATEST_VERSION=$1
    ARCH=$2
    log_info "Downloading Docker version $DOCKER_LATEST_VERSION for $ARCH..."
    local OFFICIAL_URL="https://download.docker.com/linux/static/stable/$ARCH/docker-$DOCKER_LATEST_VERSION.tgz"
    if run_with_progress "" curl -s -o /tmp/docker.tgz --retry 3 --retry-delay 5 "$OFFICIAL_URL";
    then
        log_info "Download successful from $OFFICIAL_URL"
        sudo mkdir -p $DOCKER_HOME
        if ! sudo tar -xzf /tmp/docker.tgz -C $DOCKER_HOME --strip-components 1;
        then
            log_error "Extraction failed"
            return 1
        fi
        sudo chmod +x $DOCKER_HOME/*
        rm /tmp/docker.tgz
        log_info "Extraction completed: $OFFICIAL_URL"
        return 0
    else
        log_error "Download failed, please check your network connection"
        log_error "Download failed from $OFFICIAL_URL"
        return 1
    fi
}

# 下载Docker bash completion脚本函数，增加错误处理
download_docker_bash_completion() {
    log_info "Downloading Docker bash completion..."
    local DOCKER_VERSION=$(docker --version | awk '{print $3}' | sed 's/,//')

    if [ -z "$DOCKER_VERSION" ]; then
        log_error "Docker version is not provided for completion install"
        return 1
    fi

    local COMPLETION_URL="https://raw.githubusercontent.com/docker/cli/v$DOCKER_VERSION/contrib/completion/bash/docker"

    log_info "Downloading docker completion script for version $DOCKER_VERSION..."
    if run_with_progress "" curl -fsSL --retry 3 --retry-delay 5 "$COMPLETION_URL" -o /tmp/docker_bash_completion; then
        sudo cp /tmp/docker_bash_completion /etc/bash_completion.d/docker
        sudo chmod 666 /etc/bash_completion.d/docker
        source /etc/bash_completion.d/docker
        log_info "Docker bash completion installed successfully to /etc/bash_completion.d/docker"
        return 0
    else
        log_error "Failed to download Docker bash completion script from $COMPLETION_URL"
        return 1
    fi
}

# 配置systemd服务函数，增加错误处理
configure_systemd() {
    local VERSION=$1
    log_info "Configuring systemd service..."
    
    # 创建systemd服务文件
    if [ -f "$DOCKER_SERVICE_FILE" ]; then
        log_info "Docker service file $DOCKER_SERVICE_FILE is exist, clean it..."
        sudo rm -f "$DOCKER_SERVICE_FILE" "$DOCKER_SERVICE_FILE.bak"
    fi
    sed -i '/^ExecStart=/ s|$| \$DOCKER_OPTS|' $PROJECT_HOME/config/docker.service
    sed -i '/^ExecReload=/ s|$| \$MAINPID|' $PROJECT_HOME/config/docker.service

    ln -s $PROJECT_HOME/config/docker.service $DOCKER_SERVICE_FILE


    local DOCKER_DIR="/etc/docker"
    local DAEMON_FILE="$DOCKER_DIR/daemon.json"

    if [ ! -d "$DOCKER_DIR" ]; then
        log_info "Docker directory $DOCKER_DIR is not exist, create it..."
        sudo mkdir -p "$DOCKER_DIR"
    fi

    if [ -f "$DAEMON_FILE" ]; then
        log_info "Docker daemon.json file is exist, clean it..."
        sudo rm -f $DAEMON_FILE
    fi
    ln -s $PROJECT_HOME/config/daemon.json $DAEMON_FILE

    log_info "systemd file is created: $SERVICE_FILE"
    configure_docker_user
    # 重载systemd并启动服务
    if ! sudo systemctl daemon-reload; then
        log_error "Failed to reload systemd daemon"
        return 1
    fi
    if ! sudo systemctl enable docker >/dev/null 2>&1; then
        log_error "Failed to enable docker service"
        return 1
    fi
    if ! sudo systemctl start docker; then
        log_error "Failed to start docker service"
        return 1
    fi
    
    # 检查服务状态
    log_info "Checking Docker Status..."
    if sudo systemctl status docker | grep -q "active (running)"; then
        log_info "Docker run success"
    else
        log_error "Docker run failed, please check the log: sudo journalctl -xe"
        return 1
    fi
    return 0
}

# 验证 docker 安装
verify_docker_installation() {
    log_info "Verify Docker installation..."
    if $DOCKER_HOME/docker version >/dev/null 2>&1; then
        log_info "Docker verify success"
        log_info "------------------------"
        log_info "Version: $($DOCKER_HOME/docker version | grep "Version" | head -1 | awk '{print $2}')"
        log_info "------------------------"
        return 0
    else
        log_error "Docker verify failed, please check your docker installation."
        exit 1
    fi
}

# 安装 docker
docker_install() {
    log_info "Docker auto install..."
    
    get_latest_docker_version
    if [ $? -ne 0 ]; then
        return 1
    fi
    
    if ! download_and_extract_docker "$DOCKER_LATEST_VERSION" "$ARCH"; then
        return 1
    fi


    if ! configure_systemd "$VERSION"; then
        return 1
    fi

    if ! verify_docker_installation; then
        return 1
    fi
    
    log_info "Docker install and config success"

    if ! download_docker_bash_completion; then
        return 1
    fi
}

# 获取最新的 docker compose 版本
get_latest_compose_version() {
    log_info "Get latest version of Docker Compose..."
    local API_URL="https://api.github.com/repos/docker/compose/releases/latest"
    
    if command_exists jq ; then
        COMPOSE_VERSION=$(curl -s $API_URL | jq -r .tag_name | sed 's/v//')
    else
        COMPOSE_VERSION=$(curl -s $API_URL | grep -oP '"tag_name": "\Kv?[0-9]+\.[0-9]+\.[0-9]+')
        COMPOSE_VERSION=${COMPOSE_VERSION#v}
    fi
    
    if [ -z "$COMPOSE_VERSION" ]; then
        log_error "Failed to get the latest Docker Compose version."
        return 1
    fi
    
    log_info "Latest version of Docker Compose: $COMPOSE_VERSION"
}

# 下载 docker compose
download_and_extract_compose() {
    COMPOSE_VERSION=$1
    ARCH=$2
    log_info "Downloading Docker Compose version $COMPOSE_VERSION for $ARCH..."
    local OFFICIAL_URL="https://github.com/docker/compose/releases/download/v${COMPOSE_VERSION}/docker-compose-linux-${ARCH}"
    sudo mkdir -p $COMPOSE_HOME
    if run_with_progress "" wget -q -t 3 --timeout=120 -O $COMPOSE_HOME/docker-compose "$OFFICIAL_URL";
    then
        log_info "Download successful from $OFFICIAL_URL"
    else
        log_error "Download failed, please check your network connection"
        log_error "Download failed from $OFFICIAL_URL"
        return 1
    fi
    log_info "Downloading Docker Compose from official source success."
    
    sudo chmod +x $COMPOSE_HOME/docker-compose
    
    if [ "$(id -u)" -eq 0 ]; then
        sudo mkdir -p ${COMPOSE_BIN_PATH}
        sudo cp $COMPOSE_HOME/docker-compose ${COMPOSE_BIN_PATH}
        log_info "All users have installed Docker Compose plugin."
    fi
    
    log_info "Docker Compose install success."
}

# 验证 docker compose
verify_compose_installation() {
    log_info "Verify Docker Compose installation..."
    
    if docker compose version >/dev/null 2>&1; then
        log_info "Docker Compose verify success."
        log_info "------------------------"
        log_info "Version: $(docker compose version | awk '{print $NF}')"
        log_info "------------------------"
        return 0
    else
        log_error "Docker Compose verify failed, please check the log: docker compose version"
        return 1
    fi
}

# 安装 docker compose 
compose_install() {
    get_latest_compose_version
    if [ $? -ne 0 ]; then
        return 1
    fi
    if ! download_and_extract_compose "$COMPOSE_VERSION" "$ARCH"; then
        return 1
    fi
    if ! verify_compose_installation; then
        return 1
    fi
    log_info "Docker Compose install and config success"
    return 0
}

# 软件安装
software_install() {
    if [ "$(id -u)" -ne 0 ]; then
        log_error "Please run this script with root privileges"
        return 1
    fi
    log_info "It will install docker and docker compose."
    
    if ! detect_architecture; then
        return 1
    fi

    if ! command_exists docker; then
        log_info "Not found docker command, start install docker..."
        if docker_install; then
            log_to_file "Docker install and config success"
        else
            log_to_file "Docker install and config failed"
            return 1
        fi
    else
        log_info "Found docker command, skip install."
    fi
    if ! docker compose version >/dev/null 2>&1; then
        log_info "Not found docker compose command, start install docker compose..."
        if compose_install; then
            log_to_file "Docker Compose install and config success"
            return 0
        else
            log_to_file "Docker Compose install and config failed"
            return 1
        fi
    else
        log_info "Found docker compose command, skip install."
    fi
}

# 软件卸载
software_uninstall() {
    if [ "$(id -u)" -ne 0 ]; then
        log_error "Please run this script with root privileges"
        return 1
    fi
    log_info "It will Uninstall docker and docker compose..."
    if docker compose version >/dev/null 2>&1; then
        log_info "Found docker compose command, start uninstall..."
        docker compose version
        sudo rm -fr $COMPOSE_BIN_PATH
    fi
    if command_exists docker; then
        log_info "Found docker command, start uninstall..."
        docker system prune -a --volumes --force > /dev/null 2>&1
        docker rmi $(docker images -q) --force > /dev/null 2>&1
        sudo rm -fr $DOCKER_HOME
        sudo rm -f /usr/bin/docker
        sudo systemctl disable docker.service --now
        sudo rm -f $DOCKER_SERVICE_FILE        
        log_info "Docker uninstall success."
    else
        log_info "Not found docker command, skip uninstall."
    fi
    log_info "Verify Docker uninstall..."
    hash -r
    log_info "Hash table cleared."
    if command_exists docker; then
        log_error "Docker uninstall verify failed, please check the log: docker version"
        return 1
    else
        log_info "Docker uninstall verify success."
        return 0
    fi
}

# 构建服务的容器镜像
build_docker_images() {
    log_info "Build docker images..."
    local CONTAINER_NAME=$1
    local BRANCH=$2
    TEMP_DIR=$(mktemp -d)
    if [ $? -ne 0 ]; then
        log_error "Failed to create temporary directory"
        exit 1
    fi

    build_container() {
        local value="${CONTAINER_CONFIG[$CONTAINER_NAME]}"
        local REPO_URL=$(echo "$value" | cut -d ' ' -f 1)
        local branch=${2}
        log_info "Container name: $CONTAINER_NAME"
        log_info "Repository URL: $REPO_URL"
        log_info "Repository Branch: $branch"
        local HOST=$(echo "$REPO_URL" | sed -n 's/.*@\(.*\):.*/\1/p')
        if [[ -n "$HOST" ]]; then
            mkdir -p ~/.ssh
            ssh-keyscan -H "$HOST" >> ~/.ssh/known_hosts 2>/dev/null
        fi
        if ! run_with_progress " Cloning repository..." git clone -q --depth=1 -b $branch $REPO_URL $TEMP_DIR; then
            log_error "Failed to clone repository $REPO_URL. Please check your script execution permissions. You do not need sudo permissions as a normal user."
            log_info "Please check if you have permission to clone the repository: $REPO_URL"
            exit 1
        fi
        cd $TEMP_DIR    
        local GIT_COMMIT=$(git rev-parse --short=8 HEAD)
        log_info "Build docker images with git commit: $(git rev-parse HEAD)"
        log_info "Build docker images with name: $CONTAINER_NAME"
        log_info "Build docker images with version: $GIT_COMMIT"
        if docker images | awk -v name="${CONTAINER_NAME%:*}" -v tag="${GIT_COMMIT#*:}" '$1 == name && $2 == tag {print; exit 1}' | grep -q .; then
            log_warn "Docker image $CONTAINER_NAME:$GIT_COMMIT already exists, skip build."
            log_info "If you want to build, please delete the existing image first."
            return 0
        fi

        if docker build --no-cache -t $CONTAINER_NAME:$GIT_COMMIT -f dockerfile/Dockerfile .; then
            docker tag $CONTAINER_NAME:$GIT_COMMIT $CONTAINER_NAME:latest
            log_info "Build docker images success."
            log_to_file "Docker images $CONTAINER_NAME:$GIT_COMMIT build completed."
            rm -fr $TEMP_DIR
            return 0
        else
            log_error "Build docker images failed."
            return 1
        fi
    }

    if [[ " ${!CONTAINER_CONFIG[@]} " =~ " $1 " ]]; then
        build_container $CONTAINER_NAME $BRANCH
    else
        if [ "$1" == "all" ]; then
            for CONTAINER_NAME in "${!CONTAINER_CONFIG[@]}"; do            
                build_container $CONTAINER_NAME $BRANCH
            done
            return 0
        fi
        log_error "The container name $1 is not in the configuration list, please check."
        exit 1
    fi
}

# 生成 sql 初始化文件
generate_sql_init_file() {
    log_warn "This options will overwrite the existing sql init file."
    log_info "Generate sql init file..."
    mapfile -t SQL_FILE < <(find $PROJECT_HOME/config -name "*.sql*")
    local TARGET_SQL_PATH=$PROJECT_HOME/volumes/mysql
    if [ ! -d $TARGET_SQL_PATH ];then
        mkdir -p $TARGET_SQL_PATH
        log_info "Create $TARGET_SQL_PATH directory success."
    fi
    for file in "${SQL_FILE[@]}"; do
        local original_path=${file}
        local target_path=${TARGET_SQL_PATH}/$(basename $file)
        if [ -f $original_path ]; then
            cp $original_path $target_path >/dev/null 2>&1
            if [ $? -ne 0 ]; then
                log_error "Generate sql file failed, file: $file"
                log_error "Please check directory $TARGET_SQL_PATH permission."
                exit 1
            else
                log_info "Generate sql file success, file: $file"
            fi
            log_info "Create sql file $(basename $file) success."
        fi
    done
    log_to_file "Sql init file generate completed."
}

# 生成 compose 文件
generate_compose_init_file() {
    log_warn "This options will overwrite the existing compose init file."
    log_info "Generate compose init file..."
    local COMPOSE_FILE=$(find $PROJECT_HOME/config -name "docker-compose.yml")
    cp $COMPOSE_FILE $PROJECT_HOME/docker-compose.yml >/dev/null 2>&1
    if [ $? -ne 0 ]; then
        log_error "Generate compose file failed, file: $COMPOSE_FILE"
        log_error "Please check directory $PROJECT_HOME permission."
        exit 1
    else
        log_info "Create compose file $(basename $COMPOSE_FILE) success."
    fi
    log_to_file "Compose file generate completed."
}

# 生成自签ssl证书
generate_ssl_cert() {
    log_info "Generate ssl cert..."
    if [ ! -d ${PROJECT_HOME}/volumes/nginx/certs ];then
        mkdir -p ${PROJECT_HOME}/volumes/nginx/certs
        log_info "Create ${PROJECT_HOME}/volumes/nginx/certs directory success."
    fi
    if [ ! -f ${PROJECT_HOME}/volumes/nginx/certs/server.key ];then
        openssl req -x509 -nodes -days 3650 -newkey rsa:2048 \
        -keyout ${PROJECT_HOME}/volumes/nginx/certs/server.key \
        -out ${PROJECT_HOME}/volumes/nginx/certs/server.crt \
        -subj "/C=CN/ST=Shanghai/L=Shanghai/O=Giraffe-wash/CN=localhost" > /dev/null 2>&1
    fi
    log_info "Create ${PROJECT_HOME}/volumes/nginx/certs/server.key success."
    log_info "Create ${PROJECT_HOME}/volumes/nginx/certs/server.crt success."
}

# 检查关于docker-compose服务相关的配置文件是否存在
compose_file_check() {
    # docker-compose.yml 必须放在字典的最后一行
    declare -A FILE_ORIGINAL_PATH_DICT=(
        [".env"]="$PROJECT_HOME/config/.env"
        ["init-giraffe.sql"]="$PROJECT_HOME/config/init-giraffe.sql"
        ["timezone.cnf.template"]="$PROJECT_HOME/config/timezone.cnf.template"
        ["redis.conf"]="$PROJECT_HOME/config/redis.conf"
        ["nginx_default.conf"]="$PROJECT_HOME/config/nginx_default.conf"
    )

    declare -A FILE_TARGET_PATH_DICT=(
        [".env"]="$PROJECT_HOME/.env"
        ["init-giraffe.sql"]="$PROJECT_HOME/volumes/mysql/init-giraffe.sql"
        ["timezone.cnf.template"]="$PROJECT_HOME/volumes/mysql/conf.d/timezone.cnf.template"
        ["redis.conf"]="$PROJECT_HOME/volumes/redis/conf/redis.conf"
        ["nginx_default.conf"]="$PROJECT_HOME/volumes/nginx/conf/nginx_default.conf"
    )

    log_info "Check compose required file..."
    for file in "${!FILE_ORIGINAL_PATH_DICT[@]}"; do
        local original_path=${FILE_ORIGINAL_PATH_DICT[$file]}
        local target_path=${FILE_TARGET_PATH_DICT[$file]}
        if [ -f $original_path ]; then
            log_info "File $file is exist, skip check."
        else
            log_error "$file file is not exist, please check."
        fi

        if [ ! -f $target_path ]; then
            log_warn "File $file is not exist, create it to $target_path."
            mkdir -p "$(dirname "$target_path")"
            cp $original_path $target_path
        else
            log_error "$file file is not exist, please check."
            return 1
        fi
    done
}

# 检查docker-compose.yml文件中的镜像是否存在
check_service_images() {
    mapfile -t IMAGE_LIST < <(grep -E '^\s*[^#].*image:.*giraffe-wash' $PROJECT_HOME/docker-compose.yml | awk '{print $NF}')
    for image in "${IMAGE_LIST[@]}"; do
        image_name=$(echo $image | cut -d ':' -f 1)
        if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$image$"; then
            log_info "Service $image_name container images is exists, image name: $image"
        else
            log_error "Service $image_name container images is not exists, image name: $image"
            exit 1
        fi
    done
}

# 打包容器镜像
container_images_package() {
    local input="$1"  # 参数可能是镜像名或镜像名:tag
    local repo tag is_exact=0

    if [[ -n "$input" ]]; then
        if [[ "$input" == *:* ]]; then
            # 明确指定镜像名:tag → 强制打包
            repo="${input%%:*}"
            tag="${input##*:}"
            is_exact=1
            log_info "Start packaging specific image: $repo:$tag..."
        else
            # 只指定镜像名 → 匹配所有非latest的tag，与latest ID一致时打包
            repo="$input"
            log_info "Start packaging images for repo [$repo], only if tag matches latest..."
        fi
    else
        log_info "Start packaging all giraffe-wash* images with matching latest ID..."
    fi

    local output_dir="${PROJECT_HOME}/packages"
    mkdir -p "$output_dir"

    # 获取所有镜像（限制 giraffe-wash 系列）
    mapfile -t IMAGE_LIST < <(docker images --format '{{.Repository}}:{{.Tag}}|{{.ID}}' | grep '^giraffe-wash')

    if [[ ${#IMAGE_LIST[@]} -eq 0 ]]; then
        log_warn "No matching giraffe-wash images found. Skipping packaging."
        return 0
    fi

    declare -A latest_id_map
    local packed_count=0

    # 先收集所有 repo 的 latest 镜像 ID
    for item in "${IMAGE_LIST[@]}"; do
        IFS='|' read -r full_name image_id <<< "$item"
        repo_name="${full_name%%:*}"
        tag_name="${full_name##*:}"
        if [[ "$tag_name" == "latest" ]]; then
            latest_id_map["$repo_name"]="$image_id"
        fi
    done

    for item in "${IMAGE_LIST[@]}"; do
        IFS='|' read -r full_name image_id <<< "$item"
        image_repo="${full_name%%:*}"
        image_tag="${full_name##*:}"

        [[ "$image_tag" == "latest" ]] && continue  # 不打包 latest 本身

        # 如果传入具体镜像名:tag，直接打包
        if [[ "$is_exact" -eq 1 ]]; then
            if [[ "$image_repo" == "$repo" && "$image_tag" == "$tag" ]]; then
                local base_name="${image_repo//\//_}-${image_tag}"
                local tar_file="${output_dir}/${base_name}.tar"
                local tar_gz_file="${tar_file}.gz"

                log_info "打包镜像 [$image_repo:$image_tag] 到 $tar_gz_file..."
                docker save "$image_repo:$image_tag" -o "$tar_file"
                gzip -f "$tar_file"
                ((packed_count++))
                break  # 找到目标镜像就退出
            fi
        else
            # 如果传的是 repo 或不传，则只打包与 latest ID 相同的镜像
            if [[ -n "$repo" && "$image_repo" != "$repo" ]]; then
                continue
            fi

            latest_id="${latest_id_map[$image_repo]}"
            if [[ "$image_id" == "$latest_id" ]]; then
                local base_name="${image_repo//\//_}-${image_tag}"
                local tar_file="${output_dir}/${base_name}.tar"
                local tar_gz_file="${tar_file}.gz"

                log_info "[$image_repo:$image_tag] 与 latest ID 相同，打包到 $tar_gz_file..."
                docker save "$image_repo:$image_tag" -o "$tar_file"
                gzip -f "$tar_file"
                ((packed_count++))
            else
                log_info "[$image_repo:$image_tag] 与 latest ID 不同，跳过。"
            fi
        fi
    done

    log_info "Container images packaging completed. Total packed: $packed_count."
    log_to_file "Container images packaging completed. Total packed: $packed_count."
}

# 解压镜像包并升级
unzip_upgrade_package() {
    # 检查是否提供了镜像名称参数
    if [ -z "$1" ]; then
        log_info "错误: 请提供镜像名称作为参数"
        exit 1
    fi
    
    local IMAGE_NAME="$1"
    local TARGET_TAG="latest"
    
    # 创建release目录（如果不存在）
    mkdir -p "${PROJECT_HOME}/packages/release"
    
    # 查找匹配的镜像压缩包
    log_info "正在查找 ${IMAGE_NAME} 相关的镜像压缩包..."
    local PACKAGE_FILES=($(find "${PROJECT_HOME}/packages" -maxdepth 1 -type f -name "${IMAGE_NAME}-*.tar.gz"))
    
    # 检查匹配结果
    case ${#PACKAGE_FILES[@]} in
        0)
            log_info "错误: 未找到 ${IMAGE_NAME} 相关的镜像压缩包"
            exit 1
            ;;
        1)
            IMAGE_PACKAGE=$(basename "${PACKAGE_FILES[0]}")
            log_info "找到镜像压缩包: ${IMAGE_PACKAGE}"
            ;;
        *)
            log_info "错误: 找到多个匹配的镜像压缩包:"
            for file in "${PACKAGE_FILES[@]}"; do
                log_info "  - $(basename "$file")"
            done
            exit 1
            ;;
    esac
    
    # 从文件名提取原始标签
    local ORIGINAL_TAG=$(echo "$IMAGE_PACKAGE" | sed -E "s/${IMAGE_NAME}-([^-]+)\.tar\.gz/\1/")
    
    # 检查压缩包是否存在（双重验证）
    if [ ! -f "${PROJECT_HOME}/packages/${IMAGE_PACKAGE}" ]; then
        log_info "错误: 找不到 ${PROJECT_HOME}/packages/${IMAGE_PACKAGE}"
        exit 1
    fi
    
    # 加载镜像
    log_info "正在加载镜像..."
    docker load -i "${PROJECT_HOME}/packages/${IMAGE_PACKAGE}"
    if [ $? -ne 0 ]; then
        log_info "错误: 加载镜像失败"
        exit 1
    fi
    
    # 为镜像添加新标签
    log_info "正在为镜像添加标签..."
    docker tag "${IMAGE_NAME}:${ORIGINAL_TAG}" "${IMAGE_NAME}:${TARGET_TAG}"
    if [ $? -ne 0 ]; then
        log_info "错误: 添加标签失败"
        exit 1
    fi
    
    # 移动解压后的镜像到release目录
    mv "${PROJECT_HOME}/packages/${IMAGE_PACKAGE}" "${PROJECT_HOME}/packages/release/"
    if [ $? -ne 0 ]; then
        log_info "警告: 移动镜像文件失败，可能已存在或权限不足"
    else
        log_info "镜像文件已移动至: ${PROJECT_HOME}/packages/release/${IMAGE_PACKAGE}"
    fi
    
    # 显示操作结果
    log_info "原始镜像: ${IMAGE_NAME}:${ORIGINAL_TAG}"
    docker compose -f $PROJECT_HOME/docker-compose.yml up -d $IMAGE_NAME --force-recreate
    log_info "服务已重启: ${IMAGE_NAME}"
}

download_and_clean_package_files() {
    local base_url="http://**************:18080"           # 如：http://localhost:8080
    local remote_dir="/"         # 如：/
    local keyword="$1"
    local download_dir="${PROJECT_HOME}/packages"

    mkdir -p "$download_dir"

    echo "📄 即将下载的文件列表（包含 $keyword）："
    local files
    files=$(curl -s -H 'Accept: application/json' "${base_url}${remote_dir}" | \
    jq -r ".subItems[] | select(.isDir == false) | select(.name | contains(\"$keyword\")) | .name")

    if [ -z "$files" ]; then
    echo "没有找到符合条件的文件"
    return 0
    fi

    echo "$files"

    local delete_params=()
    local file

    for file in $files; do
    echo "⬇️ 下载文件: $file"
    curl -s -o "$download_dir/$file" "$base_url/$file?download"

    if [ $? -eq 0 ]; then
        echo "✅ 下载成功: $file"
        delete_params+=("--data-urlencode" "name=$file")
    else
        echo "❌ 下载失败: $file"
    fi
    done

    if [ ${#delete_params[@]} -gt 0 ]; then
    echo "🗑️ 批量删除文件: ${files//$'\n'/, }"
    curl -s -X POST "${delete_params[@]}" "$base_url/?delete"
    else
    echo "没有文件需要删除"
    fi
}

# 程序入口
main(){
    proxy_init
    if [ $# -eq 0 ]; then
        USAGE
        exit 0
    fi
    parse_arguments "$@"
}

main "$@"
