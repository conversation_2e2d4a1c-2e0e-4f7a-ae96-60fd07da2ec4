#!/bin/bash
CONTAINER_NAME="giraffe-wash-java"
cd giraffe-wash
git pull origin master
export GIT_COMMIT=$(git rev-parse --short=8 HEAD)
echo "GIT_COMMIT=$GIT_COMMIT" > $HOME/script/.git_commit
if docker images | awk -v name="${CONTAINER_NAME%:*}" -v tag="${GIT_COMMIT#*:}" '$1 == name && $2 == tag {print; exit 1}' | grep -q .; then
    echo "Docker image $CONTAINER_NAME:$GIT_COMMIT already exists, skip build."
    exit 0
fi
mvn clean package -DskipTests

cd -
docker build --no-cache -t $CONTAINER_NAME:$GIT_COMMIT -f dockerfile/Dockerfile .