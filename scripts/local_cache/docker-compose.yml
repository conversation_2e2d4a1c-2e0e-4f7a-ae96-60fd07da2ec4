services:
  maven:
    image: maven:3.9.6-eclipse-temurin-17
    container_name: builder
    working_dir: /root
    tty: true
    stdin_open: true
    volumes:
      - /opt/maven/giraffe-wash:/root/giraffe-wash
      - /opt/maven/dockerfile:/root/dockerfile
      - /opt/maven/script:/root/script
      - /opt/maven/.m2:/root/.m2
      - /var/run/docker.sock:/var/run/docker.sock
      - $HOME/.ssh:/root/.ssh:ro
      - /etc/ssh/ssh_config:/etc/ssh/ssh_config:ro
      - /usr/bin/docker:/usr/bin/docker:ro
    command: tail -f /dev/null