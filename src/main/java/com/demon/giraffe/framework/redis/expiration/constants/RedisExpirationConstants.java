package com.demon.giraffe.framework.redis.expiration.constants;

/**
 * Redis过期处理常量类
 * 定义各种Redis key前缀和相关常量
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class RedisExpirationConstants {

    private RedisExpirationConstants() {
        // 私有构造函数，防止实例化
    }

    /**
     * Redis key前缀基础部分
     */
    public static final String BASE_PREFIX = "giraffe";

    /**
     * 过期处理key前缀
     */
    public static final String EXPIRATION_PREFIX = BASE_PREFIX + ":expire";

    /**
     * 支付相关过期key前缀
     */
    public static class Payment {
        public static final String PREFIX = EXPIRATION_PREFIX + ":payment";
        public static final String ORDER_PAYMENT = PREFIX + ":order";
        public static final String REFUND = PREFIX + ":refund";
    }

    /**
     * 用户相关过期key前缀
     */
    public static class User {
        public static final String PREFIX = EXPIRATION_PREFIX + ":user";
        public static final String SESSION = PREFIX + ":session";
        public static final String LOGIN_TOKEN = PREFIX + ":token";
        public static final String VERIFICATION_CODE = PREFIX + ":code";
    }

    /**
     * 营销相关过期key前缀
     */
    public static class Marketing {
        public static final String PREFIX = EXPIRATION_PREFIX + ":marketing";
        public static final String COUPON = PREFIX + ":coupon";
        public static final String PROMOTION = PREFIX + ":promotion";
        public static final String ACTIVITY = PREFIX + ":activity";
    }

    /**
     * 订单相关过期key前缀
     */
    public static class Order {
        public static final String PREFIX = EXPIRATION_PREFIX + ":order";
        public static final String RESERVATION = PREFIX + ":reservation";
        public static final String PICKUP = PREFIX + ":pickup";
        public static final String DELIVERY = PREFIX + ":delivery";
    }

    /**
     * 缓存相关过期key前缀
     */
    public static class Cache {
        public static final String PREFIX = EXPIRATION_PREFIX + ":cache";
        public static final String TEMP_DATA = PREFIX + ":temp";
        public static final String LOCK = PREFIX + ":lock";
    }

    /**
     * 处理器优先级常量
     */
    public static class Priority {
        public static final int HIGHEST = 1;
        public static final int HIGH = 10;
        public static final int NORMAL = 50;
        public static final int LOW = 100;
        public static final int LOWEST = 200;
    }

    /**
     * 默认配置常量
     */
    public static class Defaults {
        public static final int DEFAULT_PRIORITY = Priority.NORMAL;
        public static final String KEY_SEPARATOR = ":";
        public static final int MAX_RETRY_TIMES = 3;
        public static final long RETRY_DELAY_MS = 1000L;
    }
}
