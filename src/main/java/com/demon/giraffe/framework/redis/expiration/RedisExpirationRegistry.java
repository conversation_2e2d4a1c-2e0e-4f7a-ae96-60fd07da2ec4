package com.demon.giraffe.framework.redis.expiration;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Redis过期处理器注册中心
 * 管理所有的Redis key过期处理器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class RedisExpirationRegistry {

    /**
     * key前缀 -> 处理器列表的映射
     * 使用ConcurrentHashMap保证线程安全
     */
    private final Map<String, List<RedisExpirationHandler>> handlerMap = new ConcurrentHashMap<>();

    /**
     * 所有处理器的列表（按优先级排序）
     */
    private final List<RedisExpirationHandler> allHandlers = new CopyOnWriteArrayList<>();

    /**
     * 注册Redis过期处理器
     *
     * @param handler 处理器实例
     */
    public void registerHandler(RedisExpirationHandler handler) {
        if (handler == null) {
            log.warn("尝试注册空的Redis过期处理器");
            return;
        }

        String keyPrefix = handler.getKeyPrefix();
        if (keyPrefix == null || keyPrefix.trim().isEmpty()) {
            log.warn("Redis过期处理器的key前缀不能为空: {}", handler.getClass().getName());
            return;
        }

        // 添加到前缀映射中
        handlerMap.computeIfAbsent(keyPrefix, k -> new CopyOnWriteArrayList<>()).add(handler);

        // 添加到全局列表中（按优先级排序）
        insertHandlerByPriority(handler);

        log.info("注册Redis过期处理器: {} -> {}", keyPrefix, handler.getClass().getSimpleName());
    }

    /**
     * 根据优先级插入处理器
     *
     * @param handler 处理器
     */
    private void insertHandlerByPriority(RedisExpirationHandler handler) {
        int priority = handler.getPriority();
        int insertIndex = 0;

        for (int i = 0; i < allHandlers.size(); i++) {
            if (allHandlers.get(i).getPriority() > priority) {
                insertIndex = i;
                break;
            }
            insertIndex = i + 1;
        }

        allHandlers.add(insertIndex, handler);
    }

    /**
     * 获取指定key前缀的处理器列表
     *
     * @param keyPrefix key前缀
     * @return 处理器列表
     */
    public List<RedisExpirationHandler> getHandlersByPrefix(String keyPrefix) {
        return handlerMap.getOrDefault(keyPrefix, List.of());
    }

    /**
     * 查找能够处理指定Redis key的处理器
     *
     * @param redisKey Redis key
     * @return 匹配的处理器，如果没有找到返回null
     */
    public RedisExpirationHandler findHandler(String redisKey) {
        if (redisKey == null || redisKey.trim().isEmpty()) {
            return null;
        }

        // 按优先级顺序查找第一个支持的处理器
        for (RedisExpirationHandler handler : allHandlers) {
            if (handler.supports(redisKey)) {
                return handler;
            }
        }

        return null;
    }

    /**
     * 查找所有能够处理指定Redis key的处理器
     *
     * @param redisKey Redis key
     * @return 匹配的处理器列表
     */
    public List<RedisExpirationHandler> findAllHandlers(String redisKey) {
        if (redisKey == null || redisKey.trim().isEmpty()) {
            return List.of();
        }

        return allHandlers.stream()
                .filter(handler -> handler.supports(redisKey))
                .toList();
    }

    /**
     * 获取所有已注册的处理器
     *
     * @return 处理器列表
     */
    public List<RedisExpirationHandler> getAllHandlers() {
        return List.copyOf(allHandlers);
    }

    /**
     * 获取已注册的处理器数量
     *
     * @return 处理器数量
     */
    public int getHandlerCount() {
        return allHandlers.size();
    }

    /**
     * 获取所有已注册的key前缀
     *
     * @return key前缀集合
     */
    public java.util.Set<String> getRegisteredPrefixes() {
        return handlerMap.keySet();
    }
}
