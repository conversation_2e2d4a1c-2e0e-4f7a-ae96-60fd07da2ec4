package com.demon.giraffe.framework.redis.expiration;

import lombok.extern.slf4j.Slf4j;

/**
 * Redis过期处理器抽象基类
 * 提供通用的实现和工具方法，简化具体处理器的开发
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public abstract class AbstractRedisExpirationHandler implements RedisExpirationHandler {

    /**
     * 处理过期事件的模板方法
     * 提供统一的异常处理和日志记录
     *
     * @param expiredKey    过期的Redis key
     * @param extractedData 从key中提取的数据
     */
    @Override
    public final void handleExpiration(String expiredKey, String extractedData) {
        log.info("开始处理Redis key过期：{} -> {} (处理器: {})", 
                expiredKey, extractedData, this.getClass().getSimpleName());
        
        try {
            // 前置处理
            beforeHandle(expiredKey, extractedData);
            
            // 核心处理逻辑
            doHandle(expiredKey, extractedData);
            
            // 后置处理
            afterHandle(expiredKey, extractedData);
            
            log.info("Redis key过期处理完成：{}", expiredKey);
            
        } catch (Exception e) {
            log.error("Redis key过期处理失败，key：{}，数据：{}", expiredKey, extractedData, e);
            
            // 异常处理
            handleException(expiredKey, extractedData, e);
            
            // 重新抛出异常
            throw e;
        }
    }

    /**
     * 核心处理逻辑
     * 子类必须实现此方法来定义具体的过期处理逻辑
     *
     * @param expiredKey    过期的Redis key
     * @param extractedData 从key中提取的数据
     */
    protected abstract void doHandle(String expiredKey, String extractedData);

    /**
     * 前置处理
     * 在核心处理逻辑之前执行，可用于参数验证、权限检查等
     *
     * @param expiredKey    过期的Redis key
     * @param extractedData 从key中提取的数据
     */
    protected void beforeHandle(String expiredKey, String extractedData) {
        // 默认实现：参数验证
        if (expiredKey == null || expiredKey.trim().isEmpty()) {
            throw new IllegalArgumentException("过期的Redis key不能为空");
        }
        
        if (extractedData == null || extractedData.trim().isEmpty()) {
            throw new IllegalArgumentException("从Redis key中提取的数据不能为空");
        }
    }

    /**
     * 后置处理
     * 在核心处理逻辑之后执行，可用于清理资源、发送通知等
     *
     * @param expiredKey    过期的Redis key
     * @param extractedData 从key中提取的数据
     */
    protected void afterHandle(String expiredKey, String extractedData) {
        // 默认空实现，子类可以重写
    }

    /**
     * 异常处理
     * 当处理过程中发生异常时调用，可用于异常记录、告警等
     *
     * @param expiredKey    过期的Redis key
     * @param extractedData 从key中提取的数据
     * @param exception     发生的异常
     */
    protected void handleException(String expiredKey, String extractedData, Exception exception) {
        // 默认空实现，子类可以重写
        // 可以在这里添加：
        // 1. 异常告警
        // 2. 失败重试
        // 3. 死信队列
        // 4. 监控指标
    }

    /**
     * 从Redis key中提取业务数据的默认实现
     * 假设key格式为：prefix:suffix，返回最后一个冒号后的部分
     *
     * @param redisKey Redis key
     * @return 提取的业务数据
     */
    @Override
    public String extractDataFromKey(String redisKey) {
        if (redisKey == null || !redisKey.startsWith(getKeyPrefix())) {
            return null;
        }
        
        // 查找最后一个冒号的位置
        int lastColonIndex = redisKey.lastIndexOf(':');
        if (lastColonIndex >= 0 && lastColonIndex < redisKey.length() - 1) {
            return redisKey.substring(lastColonIndex + 1);
        }
        
        return null;
    }

    /**
     * 默认优先级
     *
     * @return 优先级（数值越小优先级越高）
     */
    @Override
    public int getPriority() {
        return 100; // 默认优先级
    }

    /**
     * 获取处理器名称
     * 用于日志和监控
     *
     * @return 处理器名称
     */
    protected String getHandlerName() {
        return this.getClass().getSimpleName();
    }
}
