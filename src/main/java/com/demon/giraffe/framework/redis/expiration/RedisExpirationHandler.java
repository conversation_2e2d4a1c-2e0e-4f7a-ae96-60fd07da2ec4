package com.demon.giraffe.framework.redis.expiration;

/**
 * Redis key过期处理器接口
 * 定义处理Redis key过期事件的标准接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface RedisExpirationHandler {

    /**
     * 获取处理器支持的key前缀
     * 用于匹配需要处理的Redis key
     *
     * @return key前缀
     */
    String getKeyPrefix();

    /**
     * 处理Redis key过期事件
     *
     * @param expiredKey 过期的Redis key
     * @param extractedData 从key中提取的数据（如订单号、用户ID等）
     */
    void handleExpiration(String expiredKey, String extractedData);

    /**
     * 从Redis key中提取业务数据
     * 例如从 "giraffe:payment:expire:ORDER123" 中提取 "ORDER123"
     *
     * @param redisKey Redis key
     * @return 提取的业务数据
     */
    default String extractDataFromKey(String redisKey) {
        if (redisKey == null || !redisKey.startsWith(getKeyPrefix())) {
            return null;
        }
        
        String[] parts = redisKey.split(":");
        if (parts.length > 0) {
            return parts[parts.length - 1]; // 返回最后一部分作为业务数据
        }
        
        return null;
    }

    /**
     * 获取处理器的优先级
     * 数值越小优先级越高
     *
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }

    /**
     * 判断是否支持处理指定的Redis key
     *
     * @param redisKey Redis key
     * @return 是否支持
     */
    default boolean supports(String redisKey) {
        return redisKey != null && redisKey.startsWith(getKeyPrefix());
    }
}
