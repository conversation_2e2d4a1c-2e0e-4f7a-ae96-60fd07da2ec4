package com.demon.giraffe.framework.redis.config;

import com.demon.giraffe.modules.config.Constants;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableCaching
public class CacheConfig {

    public static class CacheExpiration {
        public static final Duration SHORT = Duration.ofMinutes(5);    // 5分钟
        public static final Duration MEDIUM = Duration.ofMinutes(30);  // 30分钟
        public static final Duration LONG = Duration.ofHours(2);       // 2小时
        public static final Duration VERY_LONG = Duration.ofHours(24); // 24小时
        public static final Duration PERMANENT = Duration.ZERO;        // 永久缓存(不设置过期时间)
    }

//    @Bean
//    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
//        // 创建并配置ObjectMapper
//        ObjectMapper om = new ObjectMapper();
//        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
//        om.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
//        JavaTimeModule javaTimeModule = new JavaTimeModule();
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//        // 添加自定义序列化器
//        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(formatter));
//        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(formatter));
//        om.registerModule(javaTimeModule);
//        om.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
//        Jackson2JsonRedisSerializer<Object> serializer = new Jackson2JsonRedisSerializer<>(om, Object.class);
//        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
//                .computePrefixWith(cacheName -> "giraffe:cache:" + cacheName+":")
//                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
//                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(serializer))
//                .disableCachingNullValues()
//                .entryTtl(CacheExpiration.MEDIUM) // 默认30分钟过期
//                ;
////                ; // 统一缓存前缀
//
//        // 配置特定缓存的过期时间
//        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
//
//        // 用户相关缓存
//        cacheConfigurations.put(Constants.CacheNames.APP_USER, config.entryTtl(CacheExpiration.LONG));
//        cacheConfigurations.put(Constants.CacheNames.APP_USER_BY_PHONE, config.entryTtl(CacheExpiration.LONG));
//        cacheConfigurations.put(Constants.CacheNames.APP_USER_BY_WECHAT, config.entryTtl(CacheExpiration.LONG));
//
//        // 会员相关缓存
//        cacheConfigurations.put(Constants.CacheNames.MEMBER, config.entryTtl(CacheExpiration.MEDIUM));
//        cacheConfigurations.put(Constants.CacheNames.MEMBER_BY_USER_ID, config.entryTtl(CacheExpiration.MEDIUM));
//        cacheConfigurations.put(Constants.CacheNames.MEMBER_ADDRESS, config.entryTtl(CacheExpiration.SHORT));
//        cacheConfigurations.put(Constants.CacheNames.MEMBER_DEFAULT_ADDRESS, config.entryTtl(CacheExpiration.SHORT));
//
//        // 投资人相关缓存
//        cacheConfigurations.put(Constants.CacheNames.REGION_INVESTOR, config.entryTtl(CacheExpiration.LONG));
//        cacheConfigurations.put(Constants.CacheNames.REGION_INVESTOR_BY_USER_ID, config.entryTtl(CacheExpiration.LONG));
//        cacheConfigurations.put(Constants.CacheNames.REGION_INVESTOR_BY_ADDRESS, config.entryTtl(CacheExpiration.VERY_LONG));
//
//        // 工厂相关缓存
//        cacheConfigurations.put(Constants.CacheNames.FACTORY_INFO, config.entryTtl(CacheExpiration.LONG));
//        cacheConfigurations.put(Constants.CacheNames.FACTORY_BY_ADDRESS, config.entryTtl(CacheExpiration.VERY_LONG));
//
//        // 智能柜相关缓存
//        cacheConfigurations.put(Constants.CacheNames.SMART_CABINET, config.entryTtl(CacheExpiration.SHORT));
//        cacheConfigurations.put(Constants.CacheNames.CABINET_CELLS, config.entryTtl(CacheExpiration.SHORT));
//        cacheConfigurations.put(Constants.CacheNames.CABINET_BY_ADDRESS, config.entryTtl(CacheExpiration.MEDIUM));
//        // 永久缓存
//        cacheConfigurations.put(Constants.CacheNames.SERVICE_ITEM,
//                config.entryTtl(CacheExpiration.PERMANENT));
//
//        cacheConfigurations.put(Constants.CacheNames.SERVICE_ITEM_BY_REGION,
//                config.entryTtl(CacheExpiration.PERMANENT));
//
//        cacheConfigurations.put(Constants.CacheNames.SERVICE_ITEM_BY_CATEGORY,
//                config.entryTtl(CacheExpiration.PERMANENT));
//
//        cacheConfigurations.put(Constants.CacheNames.SERVICE_ITEM_NO_REGION,
//                config.entryTtl(CacheExpiration.PERMANENT));
//
//        cacheConfigurations.put(Constants.CacheNames.SERVICE_ITEM_BY_CATEGORY_AND_REGION,
//                config.entryTtl(CacheExpiration.PERMANENT));
//
//        cacheConfigurations.put(Constants.CacheNames.SERVICE_ITEM_BATCH,
//                config.entryTtl(CacheExpiration.PERMANENT));
//        return RedisCacheManager.builder(connectionFactory)
//                .cacheDefaults(config)
//                .withInitialCacheConfigurations(cacheConfigurations)
//                .transactionAware() // 启用事务感知
//                .build();
//    }

}