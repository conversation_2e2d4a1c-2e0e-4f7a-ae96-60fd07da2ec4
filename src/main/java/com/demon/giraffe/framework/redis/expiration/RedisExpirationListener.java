package com.demon.giraffe.framework.redis.expiration;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

/**
 * 通用Redis key过期事件监听器
 * 基于注册中心模式，支持多种类型的过期处理器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class RedisExpirationListener extends KeyExpirationEventMessageListener {

    private final RedisExpirationRegistry registry;

    public RedisExpirationListener(RedisMessageListenerContainer listenerContainer,
                                 RedisExpirationRegistry registry) {
        super(listenerContainer);
        this.registry = registry;
    }

    /**
     * 处理Redis key过期事件
     *
     * @param message 过期消息
     * @param pattern 匹配模式
     */
    @Override
    public void onMessage(Message message, byte[] pattern) {
        try {
            String expiredKey = message.toString();
            log.debug("Redis key过期事件：{}", expiredKey);

            // 查找能够处理该key的处理器
            RedisExpirationHandler handler = registry.findHandler(expiredKey);
            if (handler != null) {
                handleKeyExpiration(expiredKey, handler);
            } else {
                log.debug("未找到处理器处理过期key：{}", expiredKey);
            }
        } catch (Exception e) {
            log.error("处理Redis key过期事件失败", e);
        }
    }

    /**
     * 使用指定处理器处理key过期事件
     *
     * @param expiredKey 过期的Redis key
     * @param handler    处理器
     */
    private void handleKeyExpiration(String expiredKey, RedisExpirationHandler handler) {
        try {
            // 从key中提取业务数据
            String extractedData = handler.extractDataFromKey(expiredKey);
            if (extractedData == null) {
                log.warn("无法从过期key中提取业务数据：{}", expiredKey);
                return;
            }

            log.info("处理Redis key过期：{} -> {} (处理器: {})", 
                    expiredKey, extractedData, handler.getClass().getSimpleName());

            // 调用处理器处理过期事件
            handler.handleExpiration(expiredKey, extractedData);

            log.info("Redis key过期处理完成：{}", expiredKey);

        } catch (Exception e) {
            log.error("处理Redis key过期失败，key：{}，处理器：{}", 
                    expiredKey, handler.getClass().getSimpleName(), e);
        }
    }
}
