package com.demon.giraffe.framework.redis.service;



import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Redis 服务接口，提供各种 Redis 操作方法。
 * 该接口包含对 Redis 中 String、Hash 等数据类型的操作。
 *

 */
public interface RedisService {
	
	/**
	 * 保存 String 类型数据。
	 *
	 * @param key   String - 要存储的键。
	 * @param value V - 要存储的值。
	 * @param <V>   值的类型。
	 */
	<V> void setValue(String key, V value);

	/**
	 * 设置redis值仅当key不存在是
	 * @param key 键
	 * @param value 值
	 * @param timeout 过期时间
	 * @param unit 过期单位
	 * @return 设置是否成功
	 */
	Boolean setIfAbsent(String key, Object value, long timeout, TimeUnit unit);
	
	/**
	 * 读取 String 类型数据。
	 *
	 * @param key    String - 要获取值的键。
	 * @param vClass Class<V> - 数据类型，用于避免获取基本数据类型时出错。
	 * @param <V>    返回值的类型。
	 * @return V - 获取到的值，如果不存在则返回 null。
	 */
	<V> V getValue(String key, Class<V> vClass);
	
	/**
	 * 删除指定的键。
	 *
	 * @param key String - 要删除的键。
	 * @return boolean - 如果删除成功返回 true，删除失败返回 false。
	 */
	boolean deleteKey(String key);
	
	/**
	 * 批量删除指定的键。
	 *
	 * @param keys List<String> - 要删除的键集合。
	 * @return Long - 返回删除的键的数量。
	 */
	Long deleteKeys(List<String> keys);
	
	/**
	 * 普通缓存放入。
	 *
	 * @param key   String - 键。
	 * @param value Object - 值。
	 * @return boolean - 成功返回 true，失败返回 false。
	 */
	boolean set(String key, Object value);
	
	/**
	 * 普通缓存放入并设置过期时间。
	 *
	 * @param key   String - 键。
	 * @param value Object - 值。
	 * @param time  long - 过期时间（秒）。如果 time 大于 0，则设置过期时间；如果小于等于 0，则设置为无限期。
	 * @return boolean - 成功返回 true，失败返回 false。
	 */
	boolean set(String key, Object value, long time);
	
	/**
	 * 获取键的过期时间。
	 * 如果存在 key 且设置了过期时间，返回该过期时间；
	 * 如果存在 key 但没有设置过期时间，返回 -1；
	 * 如果不存在 key，返回 -2。
	 *
	 * @param key String - 键。
	 * @return long - 过期时间，单位为秒。
	 */
	long getExpire(String key);
	
	/**
	 * 判断某个键是否存在。
	 *
	 * @param key String - 键。
	 * @return Boolean - 存在返回 true，不存在返回 false。
	 */
	Boolean exist(String key);
	
	/**
	 * 获取哈希表中的一个字段的值。
	 *
	 * @param key   String - 哈希表的键。
	 * @param field String - 字段名。
	 * @return Object - 字段的值。
	 */
	Object hGet(String key, String field);
	
	/**
	 * 获取哈希表中的所有字段和值。
	 *
	 * @param key String - 哈希表的键。
	 * @return Map<Object, Object> - 哈希表中的所有键值对。
	 */
	Map<Object, Object> hGetAll(String key);
	
	/**
	 * 批量获取哈希表中的多个字段的值。
	 *
	 * @param key    String - 哈希表的键。
	 * @param fields Collection<Object> - 字段集合。
	 * @return List<Object> - 字段值的列表。
	 */
	List<Object> hMultiGet(String key, Collection<Object> fields);
	
	/**
	 * 设置哈希表中的字段值。
	 *
	 * @param key     String - 哈希表的键。
	 * @param hashKey String - 字段名。
	 * @param value   String - 字段值。
	 */
	void hPut(String key, String hashKey, String value);
	
	/**
	 * 批量设置哈希表中的字段值。
	 *
	 * @param key  String - 哈希表的键。
	 * @param maps Map<String, Object> - 要设置的字段及对应值的集合。
	 */
	void hPutAll(String key, Map<String, Object> maps);
	
	/**
	 * 如果字段不存在，则设置哈希表中的字段值。
	 *
	 * @param key     String - 哈希表的键。
	 * @param hashKey String - 字段名。
	 * @param value   String - 字段值。
	 * @return Boolean - 如果字段不存在并成功设置返回 true，否则返回 false。
	 */
	Boolean hPutIfAbsent(String key, String hashKey, String value);
	
	/**
	 * 删除哈希表中的一个或多个字段。
	 *
	 * @param key    String - 哈希表的键。
	 * @param fields Object... - 要删除的字段。
	 * @return Long - 返回删除的字段数量。
	 */
	Long hDelete(String key, Object... fields);
	
	/**
	 * 判断哈希表中是否有指定的字段。
	 *
	 * @param key   String - 哈希表的键。
	 * @param field String - 字段名。
	 * @return boolean - 存在返回 true，不存在返回 false。
	 */
	boolean hExists(String key, String field);
	
	/**
	 * 为哈希表中的指定字段值加上增量。
	 *
	 * @param key       String - 哈希表的键。
	 * @param field     Object - 字段名。
	 * @param increment long - 增量值。
	 * @return Long - 返回增量后的值。
	 */
	Long hIncrBy(String key, Object field, long increment);
	
	/**
	 * 为哈希表中的指定字段值加上浮点数增量。
	 *
	 * @param key   String - 哈希表的键。
	 * @param field Object - 字段名。
	 * @param delta double - 浮点数增量。
	 * @return Double - 返回增量后的值。
	 */
	Double hIncrByFloat(String key, Object field, double delta);
	
	/**
	 * 获取哈希表中的所有字段名。
	 *
	 * @param key String - 哈希表的键。
	 * @return Set<Object> - 字段名的集合。
	 */
	Set<Object> hKeys(String key);
	
	/**
	 * 获取哈希表中的字段数量。
	 *
	 * @param key String - 哈希表的键。
	 * @return Long - 字段数量。
	 */
	Long hSize(String key);
	
	/**
	 * 获取哈希表中的所有字段值。
	 *
	 * @param key String - 哈希表的键。
	 * @return List<Object> - 字段值的列表。
	 */
	List<Object> hValues(String key);
	

	/**
	 * 设置键的过期时间。
	 *
	 * @param key      String - 键。
	 * @param timeout  long - 过期时间。
	 * @param timeUnit TimeUnit - 时间单位。
	 * @return boolean - 设置成功返回 true，失败返回 false。
	 */
	boolean expire(String key, long timeout, TimeUnit timeUnit);


	Long getGenerateId(String key);

	/**
	 * 通过 URL 发起 GET 请求，获取 JSON 字符串结果
	 *
	 * @param url 请求地址
	 * @return 响应内容
	 */
	 String doGetJson(String url);

}
