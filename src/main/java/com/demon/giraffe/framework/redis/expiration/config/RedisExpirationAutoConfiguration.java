package com.demon.giraffe.framework.redis.expiration.config;

import com.demon.giraffe.framework.redis.expiration.RedisExpirationHandler;
import com.demon.giraffe.framework.redis.expiration.RedisExpirationRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Redis过期处理器自动配置类
 * 在Spring容器启动完成后，自动发现并注册所有的Redis过期处理器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class RedisExpirationAutoConfiguration {

    @Autowired
    private RedisExpirationRegistry registry;

    /**
     * 在Spring容器启动完成后，自动发现并注册所有的Redis过期处理器
     */
    @EventListener(ContextRefreshedEvent.class)
    public void onApplicationEvent(ContextRefreshedEvent event) {
        ApplicationContext applicationContext = event.getApplicationContext();
        
        // 查找所有实现了RedisExpirationHandler接口的Bean
        Map<String, RedisExpirationHandler> handlerBeans = applicationContext.getBeansOfType(RedisExpirationHandler.class);
        
        log.info("发现 {} 个Redis过期处理器", handlerBeans.size());
        
        // 注册所有处理器
        for (Map.Entry<String, RedisExpirationHandler> entry : handlerBeans.entrySet()) {
            RedisExpirationHandler handler = entry.getValue();
            try {
                registry.registerHandler(handler);
                log.info("Redis过期处理器注册成功：{} -> {} (优先级: {})", 
                        handler.getKeyPrefix(), 
                        handler.getClass().getSimpleName(),
                        handler.getPriority());
            } catch (Exception e) {
                log.error("注册Redis过期处理器失败：{}", entry.getKey(), e);
            }
        }
        
        // 输出注册摘要
        log.info("Redis过期处理器注册完成，共注册 {} 个处理器", registry.getHandlerCount());
        log.info("已注册的key前缀：{}", registry.getRegisteredPrefixes());
    }
}
