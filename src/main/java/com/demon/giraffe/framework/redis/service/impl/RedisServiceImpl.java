package com.demon.giraffe.framework.redis.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.demon.giraffe.framework.redis.util.RedisUtils;
import org.springframework.data.redis.core.RedisTemplate;
import com.demon.giraffe.framework.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**

 */
@Slf4j
@Component
public class RedisServiceImpl implements RedisService {


    private final RedisTemplate<String, Object> redisTemplate;
    private final RestTemplate restTemplate;

    public RedisServiceImpl(RedisTemplate<String, Object> redisTemplate, RestTemplate restTemplate) {
        this.redisTemplate = redisTemplate;
        this.restTemplate = restTemplate;
    }

    @Override
    public <V> void setValue(String key, V value) {
        redisTemplate.opsForValue().set(key, value);
    }


    @Override
    public Boolean setIfAbsent(String key, Object value, long timeout, TimeUnit unit) {
        return redisTemplate.opsForValue().setIfAbsent(key, value, timeout, unit);
    }

    /**
     * @param key key
     * @return 获取key对应的 string
     **/
    @Override
    public <V> V getValue(String key, Class<V> vClass) {
        Object o = redisTemplate.opsForValue().get(key);
        return transType(o, vClass);
    }

    @Override
    public boolean deleteKey(String key) {
        if (StringUtils.isBlank(key)) {
            return true;
        }
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            return Boolean.TRUE.equals(redisTemplate.delete(key));
        }
        return true;
    }


    @Override
    public Long deleteKeys(List<String> keys) {
        return redisTemplate.delete(keys);
    }


    /**
     * @param o      对象
     * @param vClass 对应类型
     * @return 返回具体得数据

     * 类型为null 返回为null 下层需要判空
     **/
    @SuppressWarnings({"unchecked"})
    private <V> V transType(Object o, Class<V> vClass) {
        if (o == null || vClass == null) {
            return null;
        }

        // 类型一致，直接转换
        if (vClass.isAssignableFrom(o.getClass())) {
            return (V) o;
        }

        // 数字类型的自动转换
        if (o instanceof Number) {
            Number number = (Number) o;
            if (vClass == Long.class) {
                return (V) Long.valueOf(number.longValue());
            } else if (vClass == Integer.class) {
                return (V) Integer.valueOf(number.intValue());
            } else if (vClass == Double.class) {
                return (V) Double.valueOf(number.doubleValue());
            } else if (vClass == Float.class) {
                return (V) Float.valueOf(number.floatValue());
            } else if (vClass == Short.class) {
                return (V) Short.valueOf(number.shortValue());
            } else if (vClass == Byte.class) {
                return (V) Byte.valueOf(number.byteValue());
            }
        }

        // 字符串转数字（Redis可能存储为String的情况）
        if (o instanceof String && Number.class.isAssignableFrom(vClass)) {
            String s = (String) o;
            try {
                if (vClass == Long.class) {
                    return (V) Long.valueOf(s);
                } else if (vClass == Integer.class) {
                    return (V) Integer.valueOf(s);
                } else if (vClass == Double.class) {
                    return (V) Double.valueOf(s);
                } else if (vClass == Float.class) {
                    return (V) Float.valueOf(s);
                } else if (vClass == Short.class) {
                    return (V) Short.valueOf(s);
                } else if (vClass == Byte.class) {
                    return (V) Byte.valueOf(s);
                }
            } catch (NumberFormatException e) {
                return null; // 非法数字字符串，返回null
            }
        }

        // 其他情况不支持转换
        return null;
    }


    @Override
    public boolean set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            log.error("redis设置值操作出现异常，键：{}，值：{}", key, value, e);
            return false;
        }
    }

    @Override
    public boolean set(String key, Object value, long time) {
        try {
            if (time > 0) {
                redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            log.error("redis操作出现异常", e);
            return false;
        }
    }

    /**
     * 获取过期时间，如果存在key且设置了过期时间，就返回这个过期时间
     * 如果存在key但没有设置过期时间，返回-1
     * 如果不存在key，返回-2
     *
     * @param key 键值
     * @return 时间值，单位秒
     */
    @Override
    public long getExpire(String key) {
        if (StringUtils.isBlank(key)) {
            return -1;
        }
        // 判断是否存在该键
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            Long expire = redisTemplate.getExpire(key);
            return Objects.isNull(expire) ? -1 : expire;
        }
        return -2;
    }

    @Override
    public Boolean exist(String key) {
        if (CharSequenceUtil.isBlank(key)) {
            return false;
        }
        Boolean flag;
        try {
            flag = redisTemplate.hasKey(key);
            return flag;
        } catch (Exception e) {
            log.error("redis操作出现异常", e);
            return false;
        }
    }


    /* -------------------hash相关操作------------------------- */

    @Override
    public Object hGet(String key, String field) {
        return redisTemplate.opsForHash().get(key, field);
    }

    @Override
    public Map<Object, Object> hGetAll(String key) {
        return redisTemplate.opsForHash().entries(key);
    }


    @Override
    public List<Object> hMultiGet(String key, Collection<Object> fields) {
        return redisTemplate.opsForHash().multiGet(key, fields);
    }

    @Override
    public void hPut(String key, String hashKey, String value) {
        redisTemplate.opsForHash().put(key, hashKey, value);
    }


    @Override
    public void hPutAll(String key, Map<String, Object> maps) {
        redisTemplate.opsForHash().putAll(key, maps);
    }

    @Override
    public Boolean hPutIfAbsent(String key, String hashKey, String value) {
        return redisTemplate.opsForHash().putIfAbsent(key, hashKey, value);
    }

    @Override
    public Long hDelete(String key, Object... fields) {
        return redisTemplate.opsForHash().delete(key, fields);
    }

    @Override
    public boolean hExists(String key, String field) {
        return redisTemplate.opsForHash().hasKey(key, field);
    }

    @Override
    public Long hIncrBy(String key, Object field, long increment) {
        return redisTemplate.opsForHash().increment(key, field, increment);
    }

    @Override
    public Double hIncrByFloat(String key, Object field, double delta) {
        return redisTemplate.opsForHash().increment(key, field, delta);
    }

    @Override
    public Set<Object> hKeys(String key) {
        return redisTemplate.opsForHash().keys(key);
    }

    @Override
    public Long hSize(String key) {
        return redisTemplate.opsForHash().size(key);
    }

    @Override
    public List<Object> hValues(String key) {
        return redisTemplate.opsForHash().values(key);
    }


    @Override
    public boolean expire(String key, long timeout, TimeUnit timeUnit) {
        return Boolean.TRUE.equals(redisTemplate.expire(key, timeout, TimeUnit.SECONDS));
    }

    private static final String PREFIX = "GENERATE_ID";
    /**
     * 获取对应业务自增序列
     *
     * @param key 键值
     * @return 响应键结果
     */
    public Long getGenerateId(String key) {
        String cacheKey = RedisUtils.redisAssembleKey(PREFIX, key);
        Long generateId;

        // 判断Redis中是否存在这个自增序列
        if (Boolean.FALSE.equals(redisTemplate.hasKey(cacheKey))) {
            generateId = redisTemplate.opsForValue().increment(cacheKey);
        } else {
            Object value = redisTemplate.opsForValue().get(cacheKey);

            // 处理不同类型的返回值
            if (value == null) {
                generateId = 1L;
                redisTemplate.opsForValue().set(cacheKey, generateId.toString());
            } else if (value instanceof String) {
                String strValue = (String) value;
                if (!NumberUtil.isNumber(strValue)) {
                    generateId = 1L;
                    redisTemplate.opsForValue().set(cacheKey, generateId.toString());
                } else {
                    generateId = Long.valueOf(strValue);
                    if (generateId >= Integer.MAX_VALUE) {
                        generateId = 1L;
                        redisTemplate.opsForValue().set(cacheKey, generateId.toString());
                    } else {
                        generateId = redisTemplate.opsForValue().increment(cacheKey);
                    }
                }
            } else if (value instanceof Number) {
                generateId = ((Number) value).longValue();
                if (generateId >= Integer.MAX_VALUE) {
                    generateId = 1L;
                    redisTemplate.opsForValue().set(cacheKey, generateId.toString());
                } else {
                    generateId = redisTemplate.opsForValue().increment(cacheKey);
                }
            } else {
                generateId = 1L;
                redisTemplate.opsForValue().set(cacheKey, generateId.toString());
            }
        }
        return generateId;
    }
    /**
     * 通过 URL 发起 GET 请求，返回 JSON 字符串
     *
     * @param url 请求地址
     * @return 响应内容（可能为 null）
     */
    public String doGetJson(String url) {
        try {
            log.info("发起GET请求：{}", url);
            String result = restTemplate.getForObject(url, String.class);
            log.debug("GET响应内容：{}", result);
            return result;
        } catch (Exception e) {
            log.error("GET请求失败，URL：{}", url, e);
            return null;
        }
    }


}
