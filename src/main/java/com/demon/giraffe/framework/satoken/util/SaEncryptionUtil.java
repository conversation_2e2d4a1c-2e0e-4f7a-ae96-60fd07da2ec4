package com.demon.giraffe.framework.satoken.util;

import cn.dev33.satoken.secure.SaSecureUtil;
import com.demon.giraffe.common.exception.exception.BusinessException;


public class SaEncryptionUtil {
    /**
     * RSA私钥解密
     *
     * @param privateKey 私钥
     * @param content    已加密内容
     * @return 解密后内容
     */
    public static String rsaDecryptByPrivate(String privateKey, String content) {
        try {
            return SaSecureUtil.rsaDecryptByPrivate(privateKey, content);
        } catch (Exception e) {
            throw new BusinessException("PASSWORD_IS_ERROR");
        }
    }

    /**
     * RSA公钥加密
     *
     * @param publicKey 公钥
     * @param content   内容
     * @return 加密后内容
     */
    public static String rsaEncryptByPublic(String publicKey, String content) {
        try {
            return SaSecureUtil.rsaEncryptByPublic(publicKey, content);
        } catch (Exception e) {
            throw new BusinessException("PASSWORD_IS_ERROR");
        }
    }
}
