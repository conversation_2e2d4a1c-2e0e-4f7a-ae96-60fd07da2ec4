package com.demon.giraffe.framework.satoken.util;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.exception.NotWebContextException;
import cn.dev33.satoken.secure.SaSecureUtil;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import com.demon.giraffe.common.constants.UserConstants;
import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.common.exception.exception.UserLoginException;
import com.demon.giraffe.modules.user.model.po.UserPo;
import com.demon.giraffe.modules.user.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

import static com.demon.giraffe.common.constants.UserConstants.*;

@Slf4j
@Component
public class SaTokenUtil {
    public static final String NULL_CHAR = "null";
    private static final Long SYSTEM_USER_ID_LONG = 0L;

    @Resource
    private UserRepository userRepository;

    private static UserRepository repository;

    @PostConstruct
    public void init() {
        repository = userRepository;
    }

    /**
     * 获取当前请求的token
     *
     * @return 当前用户的token字符串
     * @throws UserLoginException 当无法获取token时抛出
     */
    public static String getCurrentToken() {
        return Optional.ofNullable(SaHolder.getRequest())
                .map(request -> request.getHeader(UserConstants.TOKEN))
                .filter(token -> !StringUtils.isEmpty(token) && !NULL_CHAR.equalsIgnoreCase(token))
                .orElseThrow(() -> new UserLoginException("无法获取当前用户Token"));
    }

    /**
     * Get JWT from request header
     */
    public static JWT getJwt() {
        String token = getCurrentToken();
        try {
            return JWTUtil.parseToken(token);
        } catch (Exception e) {
            log.error("Token解析失败");
            throw new UserLoginException("Token解析异常");
        }
    }

    /**
     * Get login ID from JWT
     */
    public static Long getLoginId() {
        try {
            String loginId = Optional.ofNullable(getJwt().getPayload(USER_SESSION_LOGIN_ID))
                    .map(Object::toString)
                    .orElse(null);
            if (Objects.isNull(loginId)) {
                return null;
            }
            return Long.valueOf(loginId);
        } catch (NotWebContextException e) {
            log.error("获取用户登录id失败,非web上下文无法获取HttpServletRequest");
            return null;
        } catch (Exception e) {
            log.error("获取用户登录id失败,未知异常", e);
            return null;
        }
    }

    /**
     * Get openid from JWT
     */
    public static String getOpenid() {
        try {
            return Optional.ofNullable(StpUtil.getTokenSession())
                    .map(session -> session.getString(USER_SESSION_OPENID))
                    .orElseThrow(() -> new UserLoginException("OpenID不存在"));
        } catch (Exception e) {
            log.error("获取OpenID失败");
            throw new UserLoginException("获取用户信息失败");
        }
    }

    /**
     * Get expiration time from JWT
     */
    public static String getExpirationTime() {
        return Optional.ofNullable(getJwt().getPayload(USER_SESSION_EXPIRATION))
                .map(Object::toString)
                .orElseThrow(() -> new UserLoginException("过期时间不存在"));
    }

    /**
     * Get user info (may be null if token doesn't exist)
     */
    public static UserPo getUserInfo() {
        return repository.getById(getLoginId());
    }

    /**
     * Get current login user ID as Long
     */
    public static Long getLoginIdAsLong() {
        try {
            return Optional.ofNullable(getLoginId())
                    .map(Long::valueOf)
                    .orElse(SYSTEM_USER_ID_LONG);
        } catch (NumberFormatException e) {
            log.warn("登录ID格式错误，使用默认系统用户", e);
            return SYSTEM_USER_ID_LONG;
        }
    }

    /**
     * Get user info (throws exception if user not logged in)
     */
    public static UserPo getUserPo() {
        Long loginId = getLoginId();
        if (Objects.isNull(loginId)) {
            throw new UserLoginException("用户未登录");
        }
        return Optional.ofNullable(repository.getById(getLoginId()))
                .orElseThrow(() -> new BusinessException("用户不存在"));
    }

    /**
     * Encrypt string using SHA-256
     */
    public static String encrypt(String s) {
        if (StringUtils.isBlank(s)) {
            throw new IllegalArgumentException("加密内容不能为空");
        }
        return SaSecureUtil.sha256(s);
    }

    /**
     * Verify password
     */
    public static void verify(String raw, String encrypt) {
        if (!verifyAndReturn(raw, encrypt)) {
            throw new BusinessException("密码错误");
        }
    }

    /**
     * Verify password and return boolean result
     */
    public static boolean verifyAndReturn(String raw, String encrypt) {
        if (StringUtils.isAnyBlank(raw, encrypt)) {
            return false;
        }
        return encrypt.equals(SaSecureUtil.sha256(raw));
    }

    /**
     * Get JWT from specific token (mainly for WebSocket)
     */
    public static JWT getJwt(String token) {
        if (StringUtils.isEmpty(token)) {
            throw new UserLoginException("登录令牌为空");
        }
        try {
            return JWTUtil.parseToken(token);
        } catch (Exception e) {
            log.error("Token解析失败");
            throw new UserLoginException("Token解析异常");
        }
    }

    /**
     * Get login ID from specific token
     */
    public static String getLoginId(String token) {
        JWT jwt = StringUtils.isBlank(token) ? getJwt() : getJwt(token);
        return Optional.ofNullable(jwt.getPayload(USER_SESSION_LOGIN_ID))
                .map(Object::toString)
                .orElseThrow(() -> new UserLoginException("登录ID不存在"));
    }

    /**
     * 获取当前登录用户的完整token信息（包含header、payload和signature）
     *
     * @return 完整的token字符串
     * @throws UserLoginException 当用户未登录或无法获取token时抛出
     */
    public static String getFullToken() {
        try {
            return StpUtil.getTokenValue();
        } catch (Exception e) {
            log.error("获取完整Token失败");
            throw new UserLoginException("获取用户Token失败");
        }
    }

    /**
     * 安全地获取当前token（不抛出异常）
     *
     * @return 当前token，如果获取失败则返回null
     */
    public static String getTokenSafely() {
        try {
            return getCurrentToken();
        } catch (Exception e) {
            log.debug("安全获取token失败", e);
            return null;
        }
    }

    /**
     * 根据用户ID失效该用户的所有token（强制下线）
     * @param userId 用户ID
     */
    public static void invalidateTokenByUserId(Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }

        try {
            // 1. 先检查用户是否已登录
            if (StpUtil.isLogin(userId)) {
                // 2. 强制注销该用户（会使其所有token失效）
                StpUtil.kickout(userId);
                log.info("已强制用户[{}]下线，所有token失效", userId);
            } else {
                log.debug("用户[{}]当前未登录，无需失效token", userId);
            }
        } catch (Exception e) {
            log.error("失效用户[{}]token失败", userId, e);
            throw new BusinessException("强制用户下线失败");
        }
    }
}