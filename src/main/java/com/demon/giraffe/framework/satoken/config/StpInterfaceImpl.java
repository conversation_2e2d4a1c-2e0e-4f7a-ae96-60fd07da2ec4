package com.demon.giraffe.framework.satoken.config;

import cn.dev33.satoken.stp.StpInterface;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import com.demon.giraffe.modules.user.model.po.UserPo;
import com.demon.giraffe.modules.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class StpInterfaceImpl implements StpInterface {


    private final UserService userService;

    public StpInterfaceImpl(UserService userService) {
        this.userService = userService;
    }

    // 构造函数，注入 UserPermissionService 用于获取用户权限



    /**
     * 重写sa-token用户权限
     * 获取当前用户所有权限
     * 后续可以优化入redis
     *
     * @param loginId   用户id，服务端口生成id
     * @param loginType - 登录类型
     * @return 权限name列表
     **/
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        return new ArrayList<>();
    }

    @Override
    public List<String> getRoleList(Object loginId, String loginType) {

        Long id = null;

        // 判断 loginId 的类型并进行转换，确保可以作为用户id使用
        if (loginId instanceof String) {
            // 将 String 类型的 loginId 转换为 Integer 类型
            id = Long.parseLong((String) loginId);
        } else if (loginId instanceof Integer integer) {
            // 如果已经是 Integer 类型，直接使用
            id = integer.longValue();
        } else {
            // 如果 loginId 既不是 String 也不是 Integer，返回空列表
            return Collections.emptyList();
        }
        // 获取用户信息
        UserPo userInfo = userService.getUserInfo(id);
        if(userInfo == null){
            log.warn("获取权限时,用户不存在");
            return Collections.emptyList();
        }
        UserRole role = userInfo.getRole();
        return List.of(role.name());
    }
}