package com.demon.giraffe.framework.satoken.config;


import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpLogic;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.jwt.StpLogicJwtForSimple;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.demon.giraffe.framework.satoken.util.SaTokenUtil;
import com.demon.giraffe.common.constants.UserConstants;
import com.demon.giraffe.common.exception.exception.UserLoginException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Configuration
public class SaTokenConfig implements WebMvcConfigurer {

    @Bean
    public StpLogic getStpLogicJwt() {
        return new StpLogicJwtForSimple();
    }

    private static List<String> WHITE_LIST = List.of(
            "/swagger-ui.html",
            "/swagger-ui/**",
            "/v3/api-docs/**",
            "/doc.html",
            "/webjars/**",
            "/favicon.ico",
            "/test/**",//测试接口
            "/user/auth/login",//登录接口
            "/user/auth/wechat/login", //微信登录接口
            "/user/auth/simple/login/*", //测试登录接口
            "/user/auth/qrCode", //生成登陆二维码
            "/user/auth/poll/*",
            "/service/item/detail",//详情
//            游客支持接口
            "/service/item/by-region",
            "/service/item/by-category",
            "/service/category/list",
            "/common/banner-config/home",
//          配置接口
            "/config/global/get/*",
            "/config/global/list",
            "/config/global/home-service/get",
//           评价
            "/review/item/*",
//          获取活动
            "/config/activity/current",
//           下单情况
            "/order/recent",
            "/favicon.ico",
            "*.gif",
            "*.jpg",
            "*.png",
            "*.js",
            "*.css"
    );


    /**
     * 定义拦截器，用于业务层更细粒度控制
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new SaInterceptor(
                handle -> {
                    SaRouter
                            .match("/**")
                            .notMatch(WHITE_LIST)
                            .check(() -> {
                                Long loginId = SaTokenUtil.getLoginId();
                                String openid = SaTokenUtil.getOpenid();
//                                LocalDateTime expirationTime = LocalDateTimeUtil.parse(SaTokenUtil.getExpirationTime());
//
//                                if (LocalDateTime.now().isAfter(expirationTime)) {
//                                    log.info("用户已过期 loginId={}, openid={}", loginId, openid);
//                                    throw new UserLoginException("认证过期");
//                                }

                                if (!StpUtil.isLogin(loginId)) {
                                    log.info("用户未登录 loginId={}, openid={}", loginId, openid);
                                    throw new UserLoginException("未获取到用户");
                                }
                            });
                }));
    }
}
