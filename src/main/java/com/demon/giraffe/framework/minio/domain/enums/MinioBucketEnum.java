package com.demon.giraffe.framework.minio.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * @className MinioBucketEnum
 * @description minio存储桶类型枚举
 */
@Getter
@Schema(name = "MinioBucketEnum", description = "minio存储桶类型枚举")
public enum MinioBucketEnum implements IEnum<Byte> {
    // 核心业务桶
    @Schema(description = "用户头像")
    USER_AVATAR((byte) 0, "user-avatar", "用户头像", true),
    
    @Schema(description = "身份证照片")
    ID_CARD((byte) 1, "id-card", "身份证照片", false),
    
    @Schema(description = "商品图片")
    PRODUCT_IMAGE((byte) 2, "product-image", "商品图片", true),
    
    @Schema(description = "订单附件")
    ORDER_ATTACHMENT((byte) 3, "order-attach", "订单附件", true),

    // 系统功能桶
    @Schema(description = "用户反馈图片")
    FEEDBACK_IMAGE((byte) 10, "feedback-img", "用户反馈图片", true),
    
    @Schema(description = "广告素材")
    SYSTEM_ADVERTISEMENT((byte) 11, "sys-ads", "广告素材", true),
    
    @Schema(description = "小程序素材")
    WECHAT_MINI_PROGRAM((byte) 12, "wx-mini-program", "小程序素材", true),

    // 临时存储桶
    @Schema(description = "临时上传文件")
    TEMP_UPLOAD((byte) 20, "temp-upload", "临时上传文件", true),
    
    @Schema(description = "导入导出文件")
    IMPORT_EXPORT((byte) 21, "import-export", "导入导出文件", false);

    @Schema(description = "ID")
    @EnumValue
    private final Byte id;

    @Schema(description = "编码")
    private final String code;

    @Schema(description = "描述信息")
    private final String description;

    @Schema(description = "是否自动创建桶")
    private final boolean autoCreateFlag;

    MinioBucketEnum(byte id, String code, String description, boolean autoCreateFlag) {
        this.id = id;
        this.code = code;
        this.description = description;
        this.autoCreateFlag = autoCreateFlag;
    }

    @Override
    public Byte getValue() {
        return getId();
    }
}