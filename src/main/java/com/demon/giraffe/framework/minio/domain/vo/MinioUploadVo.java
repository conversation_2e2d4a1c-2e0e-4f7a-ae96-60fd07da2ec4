package com.demon.giraffe.framework.minio.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**

 * @date 2023/7/18 15:28
 * @description 上传文件响应展示信息
 */
@Data
public class MinioUploadVo implements Serializable {

    private static final long serialVersionUID = -5505959905587905505L;


    @Schema(description = "存储的文件名", example = "test.jpg")
    private String fileName;

    @Schema(description = "文件类型", example = "image/jpeg")
    private String contentType;

    @Schema(description = "代理访问路径", example = "/api-storage/bucket/test.jpg")
    private String proxyUrl;

    @Schema(description = "直接访问路径", example = "http://minio:9000/bucket/test.jpg")
    private String realUrl;

    @Schema(description = "文件大小(字节)", example = "1024")
    private long fileSize;

    @Schema(description = "文件后缀", example = "jpg")
    private String suffix;

    @Schema(description = "文件MD5值", example = "d41d8cd98f00b204e9800998ecf8427e")
    private String md5;
}
