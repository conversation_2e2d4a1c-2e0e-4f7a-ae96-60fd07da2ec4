package com.demon.giraffe.framework.minio.util;


import com.demon.giraffe.common.exception.exception.MinioException;
import io.minio.*;
import io.minio.messages.Bucket;
import io.minio.messages.Item;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @date 2023/7/18 15:18
 * @description minio存储桶操作
 */
@Slf4j
@Component
@SuppressWarnings("unused")
public class MinioBucketUtil {

    private final MinioClient minioClient;

    /**
     * 存储桶名称
     */
    private static final String BUCKET_NAME = "${bucket_name}";

    /**
     * 自定义策略，后续支持持久链接
     */
    private static final AtomicReference<String> CUSTOM_POLICY = new AtomicReference<>("{\n" +
            "    \"Version\": \"2012-10-17\",\n" +
            "    \"Statement\": [\n" +
            "        {\n" +
            "            \"Effect\": \"Allow\",\n" +
            "            \"Principal\": {\n" +
            "                \"AWS\": [\n" +
            "                    \"*\"\n" +
            "                ]\n" +
            "            },\n" +
            "            \"Action\": [\n" +
            "                \"s3:GetBucketLocation\",\n" +
            "                \"s3:ListBucket\",\n" +
            "                \"s3:ListBucketMultipartUploads\"\n" +
            "            ],\n" +
            "            \"Resource\": [\n" +
            "                \"arn:aws:s3:::" + BUCKET_NAME + "\"\n" +
            "            ]\n" +
            "        },\n" +
            "        {\n" +
            "            \"Effect\": \"Allow\",\n" +
            "            \"Principal\": {\n" +
            "                \"AWS\": [\n" +
            "                    \"*\"\n" +
            "                ]\n" +
            "            },\n" +
            "            \"Action\": [\n" +
            "                \"s3:AbortMultipartUpload\",\n" +
            "                \"s3:DeleteObject\",\n" +
            "                \"s3:GetObject\",\n" +
            "                \"s3:ListMultipartUploadParts\",\n" +
            "                \"s3:PutObject\"\n" +
            "            ],\n" +
            "            \"Resource\": [\n" +
            "                \"arn:aws:s3:::" + BUCKET_NAME + "/*\"\n" +
            "            ]\n" +
            "        }\n" +
            "    ]\n" +
            "}");

    public MinioBucketUtil(MinioClient minioClient) {
        this.minioClient = minioClient;
    }

    /**
     * 根据名称创建bucket桶
     *
     * @param bucketName bucket名称
     */
    @SneakyThrows
    public void createBucket(String bucketName) {
        if (!existBucket(bucketName)) {
            // 不存在时，创建新的存储桶
            MakeBucketArgs makeBucketArgs = MakeBucketArgs.builder()
                    .bucket(bucketName)
                    .build();
            minioClient.makeBucket(makeBucketArgs);
            log.debug("minio服务器创建存储桶:【" + bucketName + "】成功");
            SetBucketPolicyArgs policyArgs = SetBucketPolicyArgs.builder().bucket(bucketName).config(CUSTOM_POLICY.get().replace(BUCKET_NAME, bucketName)).build();
            minioClient.setBucketPolicy(policyArgs);
        }
        existBucket(bucketName);
    }

    @SneakyThrows
    public boolean existBucket(String bucketName) {
        BucketExistsArgs args = BucketExistsArgs.builder().bucket(bucketName).build();
        return minioClient.bucketExists(args);
    }

    /**
     * 列出所有存储桶名称
     *
     * @return 名称集合
     */
    @SneakyThrows
    public List<String> listBucketNames() {
        // 所有存储桶
        List<Bucket> bucketList = minioClient.listBuckets();
        return bucketList.stream().map(Bucket::name).collect(Collectors.toList());
    }

    /**
     * 删除存储桶
     *
     * @param bucketName 存储桶名称
     * @return 移除成功标识
     */
    @SneakyThrows
    public boolean removeBucket(String bucketName) {
        Iterable<Result<Item>> listObjects = listObjects(bucketName);
        // 不存在该名称的存储桶
        if (Objects.isNull(listObjects)) {
            return true;
        }
        // 获取存储桶中所有对象，当其中存在对象，则不能删除
        for (Result<Item> resultItem : listObjects) {
            Item item = resultItem.get();
            if (item.size() > 0) {
                throw new MinioException("存储桶【" + bucketName + "】中存在文件项，不能删除");
            }
        }
        // 设置存储桶删除参数
        RemoveBucketArgs removeBucketArgs = RemoveBucketArgs.builder().bucket(bucketName).build();
        // 删除存储桶
        minioClient.removeBucket(removeBucketArgs);
        // 判断存储桶是否存在
        return !existBucket(bucketName);
    }

    /**
     * 列出存储桶中所有对象名称
     *
     * @param bucketName 存储桶名称
     * @return 存储桶中对象名称
     */
    @SneakyThrows
    public List<String> listObjectNames(String bucketName) {
        List<String> listObjectNames = new LinkedList<>();
        if (existBucket(bucketName)) {
            Iterable<Result<Item>> listObjects = listObjects(bucketName);
            for (Result<Item> resultItem : listObjects) {
                Item item = resultItem.get();
                listObjectNames.add(item.objectName());
            }
        }
        return listObjectNames;
    }

    /**
     * 列出存储桶中所有对象
     *
     * @param bucketName 存储桶名称
     * @return 存储桶所有对象
     */
    @SneakyThrows
    public Iterable<Result<Item>> listObjects(String bucketName) {
        if (!existBucket(bucketName)) {
            return null;
        }
        // 存储桶对象集合参数
        ListObjectsArgs listObjectsArgs = ListObjectsArgs.builder().bucket(bucketName).build();
        return minioClient.listObjects(listObjectsArgs);
    }
}
