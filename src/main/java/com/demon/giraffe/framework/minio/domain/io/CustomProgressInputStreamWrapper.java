package com.demon.giraffe.framework.minio.domain.io;

import cn.hutool.cache.impl.TimedCache;

import java.io.IOException;
import java.io.InputStream;

public class CustomProgressInputStreamWrapper extends InputStream {

    private final InputStream innerStream;
    private long totalBytesRead = 0;
    private double percent = 0.0d;
    private final long totalSize;
    private final String fileName;
    /**
     * 键超时时间
     */
    private static final long EXPIRE_TIME = 60 * 60 * 1000;
    private static TimedCache<String, String> cache = new TimedCache<>(EXPIRE_TIME);

    /**
     *
     * @param innerStream 文件流
     * @param totalSize 文件大小 byte
     * @param fileName 文件名 用于获取进度
     */
    public CustomProgressInputStreamWrapper(InputStream innerStream, long totalSize, String fileName) {
        this.innerStream = innerStream;
        this.totalSize = totalSize;
        this.fileName = fileName;
    }

    @Override
    public int read() throws IOException {
        int data = innerStream.read();
        if (data != -1) {
            totalBytesRead++;
            updateProgress();
        }
        return data;
    }

    @Override
    public int read(byte[] b, int off, int len) throws IOException {
        int bytesRead = innerStream.read(b, off, len);
        if (bytesRead != -1) {
            totalBytesRead += bytesRead;
            updateProgress();
        }
        return bytesRead;
    }

    private void updateProgress() {
        if (totalSize > 0) {
            double percentNow = (totalBytesRead * 100.0) / totalSize;
            // 每百分之5更新一下
            if(percentNow - percent > 5 || percentNow == 100) {
                percent = percentNow;
                String format = String.format("%.2f", percent);
                cache.put(fileName, format);
            }
        }
    }

    /**
     * 获取文件上传进度
     * @return
     */
    public static String getPercent(String fileName){
        return cache.get(fileName);
    }


}
