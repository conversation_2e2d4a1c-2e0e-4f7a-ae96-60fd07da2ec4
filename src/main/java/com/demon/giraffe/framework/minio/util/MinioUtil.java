package com.demon.giraffe.framework.minio.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import com.demon.giraffe.framework.minio.domain.vo.MinioUploadVo;
import com.demon.giraffe.common.exception.exception.MinioException;
import io.minio.*;
import io.minio.http.Method;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;


import com.demon.giraffe.framework.minio.properties.MinioProperties;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * MinIO对象存储操作工具类
 */
@Slf4j
@Component
@Schema(name = "MinioUtil", description = "提供MinIO对象存储的上传、下载、删除等操作")
public class MinioUtil {

    private static final String BUCKET_NOT_FOUND = "名称为：%s的存储桶不存在";
    private static final String OBJECT_URL_PROXY_KEY = "/api-storage";

    private final MinioProperties minioProperties;
    private final MinioBucketUtil minioBucketUtil;
    private final MinioClient minioClient;

    public MinioUtil(MinioProperties minioProperties,
                     MinioBucketUtil minioBucketUtil,
                     MinioClient minioClient) {
        this.minioProperties = minioProperties;
        this.minioBucketUtil = minioBucketUtil;
        this.minioClient = minioClient;
    }

    @Operation(summary = "文件上传", description = "通过MultipartFile上传文件到指定存储桶")
    @SneakyThrows
    public boolean putObject(
            @Parameter(description = "存储桶名称", required = true, example = "user-avatars") String bucketName,
            @Parameter(description = "上传文件", required = true) MultipartFile multipartFile,
            @Parameter(description = "存储对象名", example = "avatar.jpg") String fileName,
            @Parameter(description = "文件MIME类型", example = "image/jpeg") String fileType) {
        validateBucket(bucketName);
        return putObject(bucketName, multipartFile.getInputStream(), fileName, fileType);
    }

    @Operation(summary = "流式文件上传", description = "通过输入流上传文件到指定存储桶")
    public boolean putObject(
            @Parameter(description = "存储桶名称", required = true) String bucketName,
            @Parameter(description = "文件输入流", required = true) InputStream inputStream,
            @Parameter(description = "存储对象名") String fileName,
            @Parameter(description = "文件MIME类型") String fileType) {
        validateBucket(bucketName);
        PutObjectArgs putObjectArgs = PutObjectArgs.builder()
                .bucket(bucketName)
                .object(fileName)
                .stream(inputStream, -1, 1024 * 1024 * 5L)
                .contentType(StringUtils.isEmpty(fileType) ? "application/octet-stream" : fileType)
                .build();
        try {
            minioClient.putObject(putObjectArgs);
        } catch (Exception e) {
            log.error("上传流文件失败");
            throw new MinioException("上传流文件失败");
        }
        StatObjectResponse statObject = statObject(bucketName, fileName);
        return statObject != null && statObject.size() > 0;
    }

    @Operation(summary = "上传文件并返回详情", description = "上传文件后返回包含访问URL的VO对象")
    @SneakyThrows
    public MinioUploadVo upload(
            @Parameter(description = "存储桶名称", required = true) String bucketName,
            @Parameter(description = "上传文件", required = true) MultipartFile multipartFile,
            @Parameter(description = "存储对象名", required = true) @NonNull String objectName) {
        String suffix = getObjectSuffix(multipartFile.getOriginalFilename());
        if (!putObject(bucketName, multipartFile, objectName, multipartFile.getContentType())) {
            throw new MinioException("上传文件失败");
        }
        return getUploadMinioVo(bucketName, objectName, suffix, multipartFile.getContentType());
    }

    @Operation(summary = "生成存储对象名", description = "根据规则生成MinIO存储对象名称")
    @Schema(description = """
            文件名生成规则:
            1. 当isSaveOriginalFileName=true时返回原文件名
            2. 否则生成随机文件名(基于MD5或时间戳)""")
    public String getObjectName(
            @Parameter(description = "原始文件名", required = true, example = "document.pdf") String fileName,
            @Parameter(description = "文件MD5值", example = "d41d8cd98f00b204e9800998ecf8427e") String fileMd5,
            @Parameter(description = "是否保留原文件名", example = "false") Boolean isSaveOriginalFileName) {
        validateFileName(fileName);
        DecimalFormat decimalFormat = new DecimalFormat("0000");
        if (fileName.contains(".") && Boolean.FALSE.equals(isSaveOriginalFileName)) {
            return (StringUtils.isBlank(fileMd5) ? System.currentTimeMillis() : fileMd5)
                    + decimalFormat.format(RandomUtil.randomInt(1000))
                    + "." + getObjectSuffix(fileName);
        }
        return fileName;
    }

    @Operation(summary = "获取文件后缀", description = "从文件名中提取后缀（最后一个点后的部分）")
    public String getObjectSuffix(
            @Parameter(description = "文件名", required = true, example = "report.pdf") String fileName) {
        validateFileName(fileName);
        return fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".") + 1) : "";
    }

    @Operation(summary = "批量上传文件", description = "批量上传文件并返回结果列表")
    public List<MinioUploadVo> batchUpload(
            @Parameter(description = "存储桶名称", required = true) String bucketName,
            @Parameter(description = "文件列表", required = true) List<MultipartFile> fileList) {
        validateBucket(bucketName);
        if (CollUtil.isEmpty(fileList)) {
            return Collections.emptyList();
        }
        return fileList.stream()
                .map(file -> upload(bucketName, file, getObjectName(file.getOriginalFilename(), null, Boolean.FALSE)))
                .collect(Collectors.toList());
    }

    @Operation(summary = "下载文件", description = "通过HTTP响应流下载文件")
    @SneakyThrows
    public void downloadFile(
            @Parameter(description = "存储桶名称", required = true) String bucketName,
            @Parameter(description = "存储对象名", required = true) String objectName,
            @Parameter(hidden = true) HttpServletResponse response) {
        validateBucket(bucketName);
        response.reset();
        response.setContentType("application/octet-stream");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(objectName, StandardCharsets.UTF_8));
        try (InputStream inputStream = getObjectInputStream(bucketName, objectName);
             ServletOutputStream outputStream = response.getOutputStream()) {
            byte[] bytes = new byte[1024];
            int len;
            while ((len = inputStream.read(bytes)) != -1) {
                outputStream.write(bytes, 0, len);
            }
            outputStream.flush();
        }
    }

    @Operation(summary = "删除文件", description = "删除指定存储桶中的文件对象")
    public boolean removeObject(
            @Parameter(description = "存储桶名称", required = true) String bucketName,
            @Parameter(description = "存储对象名", required = true) String objectName) {
        validateBucket(bucketName);
        try {
            minioClient.removeObject(RemoveObjectArgs.builder().bucket(bucketName).object(objectName).build());
            return minioClient.statObject(StatObjectArgs.builder().bucket(bucketName).object(objectName).build()) == null;
        } catch (Exception e) {
            log.error("删除文件失败", e);
            return false;
        }
    }

    @Operation(summary = "获取文件临时URL", description = "生成7天有效期的临时访问链接")
    @SneakyThrows
    public String getObjectTempUrl(
            @Parameter(description = "存储桶名称", required = true) String bucketName,
            @Parameter(description = "存储对象名", required = true) String objectName) {
        validateBucket(bucketName);
        return minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                .method(Method.GET)
                .bucket(bucketName)
                .object(objectName)
                .expiry(7, TimeUnit.DAYS)
                .build());
    }

    // 非公开方法保持不变
    public MinioUploadVo getUploadMinioVo(String bucketName, String name, String suffix, String fileType) {
        StatObjectResponse statObjectResponse = statObject(bucketName, name);
        if (statObjectResponse == null || statObjectResponse.size() == 0) return null;

        MinioUploadVo uploadVo = new MinioUploadVo();
        uploadVo.setFileName(name);
        uploadVo.setContentType(fileType);
        uploadVo.setProxyUrl(OBJECT_URL_PROXY_KEY + "/" + bucketName + "/" + name);
        uploadVo.setRealUrl(minioProperties.getEndpoint() + "/" + bucketName + "/" + name);
        uploadVo.setFileSize(statObjectResponse.size());
        uploadVo.setSuffix(suffix);
        return uploadVo;
    }

    @SneakyThrows
    private InputStream getObjectInputStream(String bucketName, String objectName) {
        return minioClient.getObject(GetObjectArgs.builder()
                .bucket(bucketName)
                .object(objectName)
                .build());
    }

    @SneakyThrows
    private StatObjectResponse statObject(String bucketName, String objectName) {
        return minioClient.statObject(StatObjectArgs.builder()
                .bucket(bucketName)
                .object(objectName)
                .build());
    }

    private void validateBucket(String bucketName) {
        if (!minioBucketUtil.existBucket(bucketName)) {
            throw new MinioException(String.format(BUCKET_NOT_FOUND, bucketName));
        }
    }

    private void validateFileName(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            throw new MinioException("文件名不能为空");
        }
    }
}