package com.demon.giraffe.framework.swagger.config;

import com.demon.giraffe.framework.swagger.domain.enums.SwaggerGroupEnum;
import io.swagger.v3.oas.models.info.Info;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.type.AnnotationMetadata;


// 根据枚举生成多个 @Bean，需手动写每个 Bean 方法（不能用 List）
// GroupedOpenApi[]也不可以

public class DynamicGroupedOpenApiRegistrar implements ImportBeanDefinitionRegistrar {

    @Override
    public void registerBeanDefinitions(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry registry) {
        for (SwaggerGroupEnum module : SwaggerGroupEnum.values()) {
            String beanName = module.getGroupName().replaceAll("\\s+", "") + "GroupedOpenApi";

            BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(GroupedOpenApi.class,
                    () -> GroupedOpenApi.builder()
                            .group(module.getGroupName())
                            .packagesToScan(module.getBasePackage())
                            .pathsToMatch(module.getPathPattern())
                            .addOpenApiCustomizer(openApi -> openApi.info(new Info()
                                    .title(module.getTitle())
                                    .description(module.getDescription())
                                    .version(module.getVersion())
                                    .contact(module.getContact())
                                    .license(module.getLicense())))
                            .build()
            );

            registry.registerBeanDefinition(beanName, builder.getBeanDefinition());
        }
    }
}
