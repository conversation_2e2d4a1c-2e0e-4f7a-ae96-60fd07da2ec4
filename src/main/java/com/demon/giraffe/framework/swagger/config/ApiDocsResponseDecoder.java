package com.demon.giraffe.framework.swagger.config;


import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.nio.charset.StandardCharsets;

@Slf4j
@RestControllerAdvice
public class ApiDocsResponseDecoder implements ResponseBodyAdvice<Object> {

    @Override
    public boolean supports(MethodParameter returnType,
                            Class<? extends HttpMessageConverter<?>> converterType) {
        // 只处理/v3/api-docs的响应
        return returnType.getDeclaringClass().getName().contains("OpenApiWebMvcResource");
    }

    @Override
    public Object beforeBodyWrite(Object body,
                                  MethodParameter returnType,
                                  MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request,
                                  ServerHttpResponse response) {

        if (body instanceof byte[]) {
            byte[] responseBytes = (byte[]) body;

            String json = new String(responseBytes, StandardCharsets.UTF_8);
            //针对swagger进行修改
            return json; // 原样返回即可
        }
        return body;
    }
}