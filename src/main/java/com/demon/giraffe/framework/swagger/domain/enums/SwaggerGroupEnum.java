package com.demon.giraffe.framework.swagger.domain.enums;

import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.License;
import lombok.Getter;

@Getter
public enum SwaggerGroupEnum {

    // 示例：一个分组对应一个模块，含标题、描述、版本、扫描包路径、URL路径匹配规则
    TEST("测试接口", "测试模块", "测试接口", "com.demon.giraffe.test", "/test/**"),
    ADMIN_USER("用户权限接口", "系统中用户权限相关接口", "用户权限接口", "com.demon.giraffe.modules.user.controller", "/user/**"),
    ORDER("订单接口", "订单管理相关接口", "订单接口", "com.demon.giraffe.modules.order.controller", "/order/**"),
    SERVICE("服务管理接口", "服务项目及定价相关接口", "服务管理接口", "com.demon.giraffe.modules.service.controller", "/service/**"),
    DELIVERY("配送接口", "配送员、揽投件相关接口", "配送接口", "com.demon.giraffe.modules.delivery.controller", "/delivery/**"),
    CABINET("智能柜接口", "智能柜格口及库存管理接口", "智能柜接口", "com.demon.giraffe.modules.cabinet.controller", "/cabinet/**"),
    DEVICE("设备接口", "设备注册与监控相关接口", "设备接口", "com.demon.giraffe.modules.device.controller", "/device/**"),
    FACTORY("洗护工厂接口", "洗护工厂及流水线相关接口", "洗护工厂接口", "com.demon.giraffe.modules.laundry.controller", "/laundry/**"),
    PAYMENT("支付接口", "支付、账单及退款接口", "支付接口", "com.demon.giraffe.modules.payment.controller", "/payment/**"),
    MESSAGE("消息中心接口", "系统通知、推送等消息接口", "消息中心接口", "com.demon.giraffe.modules.message.controller", "/message/**"),
    MARKETING("营销活动接口", "优惠券、促销活动相关接口", "营销活动接口", "com.demon.giraffe.modules.marketing.controller", "/marketing/**"),
    PROMOTION("推广系统接口", "邀请注册、分享相关接口", "推广系统接口", "com.demon.giraffe.modules.promotion.controller", "/promotion/**"),
    QR("二维码管理接口", "生成、扫描、解析二维码相关接口", "二维码系统接口", "com.demon.giraffe.modules.qr.controller", "/qr-code/**"),
    SETTLEMENT("结算接口", "合伙人分润结算接口", "结算接口", "com.demon.giraffe.modules.settlement.controller", "/settlement/**"),
    EVALUATION("评价系统接口", "订单及用户评价相关接口", "评价系统接口", "com.demon.giraffe.modules.evaluation.controller", "/evaluation/**"),
    REVIEW("用户评价接口", "用户评价查询和管理相关接口", "用户评价接口", "com.demon.giraffe.modules.review.controller", "/review/**"),
    TICKET("工单接口", "客服工单处理相关接口", "工单接口", "com.demon.giraffe.modules.ticket.controller", "/ticket/**"),
    COMMON("公共接口", "公共接口", "公共接口", "com.demon.giraffe.modules.common.controller", "/common/**"),
    CONFIG("展示配置", "展示配置接口", "展示配置接口", "com.demon.giraffe.modules.config.controller", "/config/**"),
    CART("购物车管理", "购物车接口", "会员购物车相关接口", "com.demon.giraffe.modules.cart.controller", "/cart/**")

    ;
    private static final String ONE_VERSION = "1.0.0";

    private final String title;
    private final String description;
    private final String version;
    private final String groupName;
    private final String basePackage;
    private final String pathPattern;

    SwaggerGroupEnum(String title, String description, String groupName, String basePackage, String pathPattern) {
        this.title = title;
        this.description = description;
        this.version = ONE_VERSION;
        this.groupName = groupName;
        this.basePackage = basePackage;
        this.pathPattern = pathPattern;
    }

    public Contact getContact() {
        return new Contact()
                .name("Demon")
                .email("<EMAIL>")
                .url("https://www.giraffe.com");
    }

    public License getLicense() {
        return new License()
                .name("Apache License 2.0")
                .url("https://www.apache.org/licenses/LICENSE-2.0.html");
    }
}
