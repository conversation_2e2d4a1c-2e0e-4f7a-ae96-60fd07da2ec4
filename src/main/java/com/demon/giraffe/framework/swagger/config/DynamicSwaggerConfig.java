package com.demon.giraffe.framework.swagger.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.*;

@Configuration
@Import(DynamicGroupedOpenApiRegistrar.class)
public class DynamicSwaggerConfig {
    /**
     * 默认 OpenAPI 信息（用于未分组接口）
     */
    @Bean
    public OpenAPI defaultOpenApi() {
        return new OpenAPI()
                .info(new Info()
                        .title("Giraffe 洗护系统 API 总览")
                        .description("全局默认描述")
                        .version("1.0.0")
                );
    }
}
