package com.demon.giraffe.framework.mybatis.typehandler;

import com.demon.giraffe.common.util.GsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * [mybatis] String 和 ArrayList<String> 的 TypeHandler
 * 使用 Gson 处理 JSON 序列化与反序列化
 *
 * <AUTHOR>
 */
@Component
public class StringArrayListTypeHandler extends BaseTypeHandler<List<String>> {

	private static final Type LIST_TYPE = new com.google.gson.reflect.TypeToken<List<String>>() {}.getType();

	@Override
	public void setNonNullParameter(PreparedStatement preparedStatement, int i,
									List<String> strings, JdbcType jdbcType) throws SQLException {
		if (strings == null) {
			preparedStatement.setNull(i, Types.VARCHAR);
		} else {
			preparedStatement.setString(i, GsonUtil.toJson(strings));
		}
	}

	@Override
	public List<String> getNullableResult(ResultSet resultSet, String columnName) throws SQLException {
		return parseStrToList(resultSet.getString(columnName));
	}

	@Override
	public List<String> getNullableResult(ResultSet resultSet, int columnIndex) throws SQLException {
		return parseStrToList(resultSet.getString(columnIndex));
	}

	@Override
	public List<String> getNullableResult(CallableStatement callableStatement, int columnIndex)
			throws SQLException {
		return parseStrToList(callableStatement.getString(columnIndex));
	}

	private List<String> parseStrToList(String json) {
		if (StringUtils.isBlank(json)) {
			return new ArrayList<>();
		}
		return GsonUtil.fromJson(json, LIST_TYPE);
	}
}
