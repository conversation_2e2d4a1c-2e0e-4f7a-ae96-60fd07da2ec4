package com.demon.giraffe.framework.mybatis.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;

import static com.demon.giraffe.framework.satoken.util.SaTokenUtil.getLoginIdAsLong;

@Slf4j
@Configuration
public class MyBatisPlusConfig implements MetaObjectHandler {

    // 配置类 MyBatisPlusConfig 实现了 MetaObjectHandler，本自动填充 create_time、update_time、creator、updater 等字段。
    //自动填充只适用于使用 MyBatis Plus 的插入/更新方法，比如 insert(entity)、updateById(entity)
    // XML 中手写的 SQL 不适用
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor plusInterceptor = new MybatisPlusInterceptor();
        plusInterceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return plusInterceptor;
    }

    @Override
    public void insertFill(MetaObject metaObject) {
        Long loginId = getLoginIdAsLong();
        // 使用驼峰命名与实体类一致
        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "creator", Long.class, loginId);
        this.strictInsertFill(metaObject, "updater", Long.class, loginId);
        this.strictInsertFill(metaObject, "deleted", Boolean.class, false);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        Long loginId = getLoginIdAsLong();
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
        this.strictUpdateFill(metaObject, "updater", Long.class, loginId);
    }

}