package com.demon.giraffe.framework.mybatis.typehandler;

import com.demon.giraffe.modules.common.model.entity.BannerSingleDetailConfig;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.springframework.stereotype.Component;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;


@Component
public class BannerSingleDetailListTypeHandler extends BaseTypeHandler<List<BannerSingleDetailConfig>> {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final TypeReference<List<BannerSingleDetailConfig>> typeReference =
            new TypeReference<List<BannerSingleDetailConfig>>() {};

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i,
                                    List<BannerSingleDetailConfig> parameter,
                                    JdbcType jdbcType) throws SQLException {
        try {
            ps.setString(i, objectMapper.writeValueAsString(parameter));
        } catch (Exception e) {
            throw new RuntimeException("Failed to serialize BannerSingleDetailConfig list to JSON", e);
        }
    }

    @Override
    public List<BannerSingleDetailConfig> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return parseJson(json);
    }

    @Override
    public List<BannerSingleDetailConfig> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return parseJson(json);
    }

    @Override
    public List<BannerSingleDetailConfig> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return parseJson(json);
    }

    private List<BannerSingleDetailConfig> parseJson(String json) {
        if (json == null || json.isEmpty()) {
            return new ArrayList<>();
        }
        try {
            return objectMapper.readValue(json, typeReference);
        } catch (Exception e) {
            throw new RuntimeException("Failed to parse BannerSingleDetailConfig list JSON: " + json, e);
        }
    }
}