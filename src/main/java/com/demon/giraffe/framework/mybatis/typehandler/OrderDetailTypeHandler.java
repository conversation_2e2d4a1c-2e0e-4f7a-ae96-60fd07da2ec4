package com.demon.giraffe.framework.mybatis.typehandler;

import com.demon.giraffe.modules.order.model.entity.OrderDetailEntity;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.*;

@MappedTypes(OrderDetailEntity.class)
public class OrderDetailTypeHandler extends BaseTypeHandler<OrderDetailEntity> {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, OrderDetailEntity parameter, JdbcType jdbcType) throws SQLException {
        try {
            ps.setString(i, objectMapper.writeValueAsString(parameter));
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to serialize OrderDetailEntity to JSON", e);
        }
    }

    @Override
    public OrderDetailEntity getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return parseJson(json);
    }

    @Override
    public OrderDetailEntity getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return parseJson(json);
    }

    @Override
    public OrderDetailEntity getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return parseJson(json);
    }

    private OrderDetailEntity parseJson(String json) {
        if (json == null || json.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(json, OrderDetailEntity.class);
        } catch (Exception e) {
            throw new RuntimeException("Failed to parse OrderDetailEntity JSON: " + json, e);
        }
    }
}