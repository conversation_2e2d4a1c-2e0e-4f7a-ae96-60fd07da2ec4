package com.demon.giraffe.framework.mybatis.typehandler;

import com.demon.giraffe.common.util.GsonUtil;
import com.google.gson.reflect.TypeToken;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import org.springframework.stereotype.Component;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

@Component
public class IntegerArrayListTypeHandler extends BaseTypeHandler<List<Integer>> {

    private static final com.google.gson.reflect.TypeToken<List<Integer>> TYPE_TOKEN = new com.google.gson.reflect.TypeToken<List<Integer>>() {
    };

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<Integer> parameter, JdbcType jdbcType) throws SQLException {
        if (parameter == null) {
            ps.setNull(i, Types.VARCHAR);
        } else {
            ps.setString(i, GsonUtil.toJson(parameter));
        }
    }

    @Override
    public List<Integer> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return json == null ? new ArrayList<>() : GsonUtil.fromJson(json, TYPE_TOKEN.getType());
    }

    @Override
    public List<Integer> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return json == null ? new ArrayList<>() : GsonUtil.fromJson(json, TYPE_TOKEN.getType());
    }

    @Override
    public List<Integer> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return json == null ? new ArrayList<>() : GsonUtil.fromJson(json, TYPE_TOKEN.getType());
    }
}