package com.demon.giraffe.framework.mybatis.typehandler;


import com.demon.giraffe.common.util.GsonUtil;
import com.demon.giraffe.modules.review.model.enums.ReviewTagEnum;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.*;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * [mybatis] String 和 ArrayList<ReviewTagEnum> 的 TypeHandler
 */
@Component
public class ReviewTagEnumListTypeHandler extends BaseTypeHandler<List<ReviewTagEnum>> {

    private static final Type LIST_TYPE = new TypeToken<List<ReviewTagEnum>>() {}.getType();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<ReviewTagEnum> parameter, JdbcType jdbcType) throws SQLException {
        if (parameter == null) {
            ps.setNull(i, Types.VARCHAR);
        } else {
            ps.setString(i, GsonUtil.toJson(parameter));
        }
    }

    @Override
    public List<ReviewTagEnum> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return parseJsonToList(rs.getString(columnName));
    }

    @Override
    public List<ReviewTagEnum> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return parseJsonToList(rs.getString(columnIndex));
    }

    @Override
    public List<ReviewTagEnum> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return parseJsonToList(cs.getString(columnIndex));
    }

    private List<ReviewTagEnum> parseJsonToList(String json) {
        if (StringUtils.isBlank(json)) {
            return new ArrayList<>();
        }
        return GsonUtil.fromJson(json, LIST_TYPE);
    }
}
