package com.demon.giraffe.framework.mybatis.typehandler;

import com.demon.giraffe.modules.service.model.entity.ServiceDetailConfig;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.*;

@MappedTypes(ServiceDetailConfig.class)
public class ServiceDetailConfigTypeHandler extends BaseTypeHandler<ServiceDetailConfig> {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, ServiceDetailConfig parameter, JdbcType jdbcType) throws SQLException {
        try {
            ps.setString(i, objectMapper.writeValueAsString(parameter));
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to serialize ServiceDetailConfig to JSON", e);
        }
    }

    @Override
    public ServiceDetailConfig getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return parseJson(json);
    }

    @Override
    public ServiceDetailConfig getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return parseJson(json);
    }

    @Override
    public ServiceDetailConfig getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return parseJson(json);
    }

    private ServiceDetailConfig parseJson(String json) {
        if (json == null || json.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(json, ServiceDetailConfig.class);
        } catch (Exception e) {
            throw new RuntimeException("Failed to parse ServiceDetailConfig JSON: " + json, e);
        }
    }
}