package com.demon.giraffe.framework.mybatis.typehandler;

import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.common.util.GsonUtil;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * [mybatis] String 和 ArrayList<CountyEnum> 的 TypeHandler
 */
@Component
public class CountyEnumListTypeHandler extends BaseTypeHandler<List<CountyEnum>> {

    private static final Type LIST_TYPE = new TypeToken<List<CountyEnum>>() {}.getType();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<CountyEnum> parameter, JdbcType jdbcType) throws SQLException {
        if (parameter == null) {
            ps.setNull(i, Types.VARCHAR);
        } else {
            ps.setString(i, GsonUtil.toJson(parameter));
        }
    }

    @Override
    public List<CountyEnum> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return parseJsonToList(rs.getString(columnName));
    }

    @Override
    public List<CountyEnum> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return parseJsonToList(rs.getString(columnIndex));
    }

    @Override
    public List<CountyEnum> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return parseJsonToList(cs.getString(columnIndex));
    }

    private List<CountyEnum> parseJsonToList(String json) {
        if (StringUtils.isBlank(json)) {
            return new ArrayList<>();
        }
        return GsonUtil.fromJson(json, LIST_TYPE);
    }
}