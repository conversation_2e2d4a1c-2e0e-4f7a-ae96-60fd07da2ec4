package com.demon.giraffe.common.domain;


import com.demon.giraffe.common.exception.enums.BaseResultCodeEnum;
import com.demon.giraffe.common.exception.enums.inter.ExceptionInterface;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * @className ResultBean
 * @description 响应结果定义
 */
@Data
@Schema(name = "resultBean", description = "响应结果")
public class ResultBean<T> implements Serializable {

    private static final long serialVersionUID = -2450894967260832681L;
    /**
     * 响应码
     */
    @Schema(description = "响应状态码", example = "200")
    private Integer code;

    /**
     * 响应信息
     */
    @Schema(description = "响应信息")
    private String message;

    /**
     * 响应数据
     */
    @Schema(description = "响应数据")
    private T data;

    public static <T> ResultBean<T> of(ExceptionInterface<?> codeEnum, String message, T data) {
        ResultBean<T> resultBean = new ResultBean<>();
        resultBean.setCode(codeEnum.getCode());
        resultBean.setMessage(message);
        resultBean.setData(data);
        return resultBean;
    }

    public static <T> ResultBean<T> of(ExceptionInterface<?> codeEnum, T data) {
        ResultBean<T> resultBean = new ResultBean<>();
        resultBean.setCode(codeEnum.getCode());
        resultBean.setMessage(codeEnum.getKey());
        resultBean.setData(data);
        return resultBean;
    }

    public static <T> ResultBean<T> success() {
        return of(BaseResultCodeEnum.SUCCESS, BaseResultCodeEnum.SUCCESS.getKey(), null);
    }

    public static <T> ResultBean<T> success(T data) {
        return of(BaseResultCodeEnum.SUCCESS, BaseResultCodeEnum.SUCCESS.getKey(), data);
    }

    public static <T> ResultBean<T> success(String message, T data) {
        return of(BaseResultCodeEnum.SUCCESS, message, data);
    }

    public static <T> ResultBean<T> fail() {
        return of(BaseResultCodeEnum.FAIL, BaseResultCodeEnum.FAIL.getKey(), null);
    }

    public static <T> ResultBean<T> fail(String message) {
        return of(BaseResultCodeEnum.FAIL, message, null);
    }

    public static <T> ResultBean<T> fail(ExceptionInterface<? extends Enum<?>> resultCodeEnum, String message, T data) {
        if (Objects.isNull(resultCodeEnum)) {
            resultCodeEnum = BaseResultCodeEnum.FAIL;
        }
        return of(resultCodeEnum, message, data);
    }

    public static <T> ResultBean<T> fail(ExceptionInterface<? extends Enum<?>> resultCodeEnum) {
        if (Objects.isNull(resultCodeEnum)) {
            resultCodeEnum = BaseResultCodeEnum.FAIL;
        }
        return of(resultCodeEnum, null);
    }

}
