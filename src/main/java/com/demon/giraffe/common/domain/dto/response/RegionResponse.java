package com.demon.giraffe.common.domain.dto.response;

import com.demon.giraffe.common.domain.enums.CountyEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 统一区域请求DTO
 * 用于统一处理省份、城市、区县的入参
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "统一区域请求")
public class RegionResponse {

    @Schema(description = "省份名称")
    private String province;

    @Schema(description = "城市名称")
    private String city;

    @Schema(description = "区县名称")
    private String district;

    public RegionResponse(CountyEnum countyEnum) {
        if (countyEnum == null) {
            return;
        }
        this.province = countyEnum.getCityEnum().getProvinceEum().getName();
        this.city = countyEnum.getCityEnum().getName();
        this.district = countyEnum.getName();
    }
}
