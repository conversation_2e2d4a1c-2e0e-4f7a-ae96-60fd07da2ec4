package com.demon.giraffe.common.domain.dto.request;

import com.demon.giraffe.common.domain.enums.CountyEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 统一区域请求DTO
 * 用于统一处理省份、城市、区县的入参
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "统一区域请求")
public class RegionRequest {

    @Schema(description = "省份名称", example = "广东省", required = true)
    @NotBlank(message = "省份不能为空")
    private String province;

    @Schema(description = "城市名称", example = "深圳市", required = true)
    @NotBlank(message = "城市不能为空")
    private String city;

    @Schema(description = "区县名称", example = "南山区", required = true)
    @NotBlank(message = "区县不能为空")
    private String district;

    @Schema(hidden = true)
    private CountyEnum addressCode;

    public RegionRequest(@NotNull(message = "区域编码不能为空") CountyEnum countyEnum) {
        province = countyEnum.getCityEnum().getProvinceEum().getDescription();
        city = countyEnum.getCityEnum().getName();
        district = countyEnum.getName();
        addressCode = countyEnum;

    }

    /**
     * 获取完整地址描述
     */
    @Schema(hidden = true)
    public String getFullAddress() {
        return province + city + district;
    }

    /**
     * 验证区域信息是否完整
     */
    @Schema(hidden = true)
    public boolean isValid() {
        return province != null && !province.trim().isEmpty() &&
                city != null && !city.trim().isEmpty() &&
                district != null && !district.trim().isEmpty();
    }

}
