package com.demon.giraffe.common.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "省枚举")
public enum ProvinceEnum implements IEnum<Integer> {
    P11(11, "北京市"),
    P12(12, "天津市"),
    P13(13, "河北省"),
    P14(14, "山西省"),
    P15(15, "内蒙古自治区"),
    P21(21, "辽宁省"),
    <PERSON>22(22, "吉林省"),
    P23(23, "黑龙江省"),
    P31(31, "上海市"),
    P32(32, "江苏省"),
    P33(33, "浙江省"),
    P34(34, "安徽省"),
    P35(35, "福建省"),
    P36(36, "江西省"),
    P37(37, "山东省"),
    P41(41, "河南省"),
    P42(42, "湖北省"),
    P43(43, "湖南省"),
    P44(44, "广东省"),
    P45(45, "广西壮族自治区"),
    P46(46, "海南省"),
    P50(50, "重庆市"),
    P51(51, "四川省"),
    P52(52, "贵州省"),
    P53(53, "云南省"),
    P54(54, "西藏自治区"),
    P61(61, "陕西省"),
    P62(62, "甘肃省"),
    P63(63, "青海省"),
    P64(64, "宁夏回族自治区"),
    P65(65, "新疆维吾尔自治区"),
    ;

    @EnumValue
    private final Integer code;
    private final String description;
    private final String name;

    ProvinceEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
        this.description = name;
    }

    @Override
    public Integer getValue() {
        return code;
    }

    public static ProvinceEnum of(Integer code) {
        for (ProvinceEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
