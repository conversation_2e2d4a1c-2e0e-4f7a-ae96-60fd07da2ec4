package com.demon.giraffe.common.domain.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * <AUTHOR>
 * GB/T 2260 行政区划列表 的地区
 */
@Getter
@Schema(description = "区县数量")
public enum CountyEnum implements IEnum<Integer> {

    C110101(110101, CityEnum.C1101, "东城区"),
    C110102(110102, CityEnum.C1101, "西城区"),
    C110105(110105, CityEnum.C1101, "朝阳区"),
    C110106(110106, CityEnum.C1101, "丰台区"),
    C110107(110107, CityEnum.C1101, "石景山区"),
    C110108(110108, CityEnum.C1101, "海淀区"),
    C110109(110109, CityEnum.C1101, "门头沟区"),
    C110111(110111, CityEnum.C1101, "房山区"),
    C110112(110112, CityEnum.C1101, "通州区"),
    C110113(110113, CityEnum.C1101, "顺义区"),
    C110114(110114, CityEnum.C1101, "昌平区"),
    C110115(110115, CityEnum.C1101, "大兴区"),
    C110116(110116, CityEnum.C1101, "怀柔区"),
    C110117(110117, CityEnum.C1101, "平谷区"),
    C110118(110118, CityEnum.C1101, "密云区"),
    C110119(110119, CityEnum.C1101, "延庆区"),
    C120101(120101, CityEnum.C1201, "和平区"),
    C120102(120102, CityEnum.C1201, "河东区"),
    C120103(120103, CityEnum.C1201, "河西区"),
    C120104(120104, CityEnum.C1201, "南开区"),
    C120105(120105, CityEnum.C1201, "河北区"),
    C120106(120106, CityEnum.C1201, "红桥区"),
    C120110(120110, CityEnum.C1201, "东丽区"),
    C120111(120111, CityEnum.C1201, "西青区"),
    C120112(120112, CityEnum.C1201, "津南区"),
    C120113(120113, CityEnum.C1201, "北辰区"),
    C120114(120114, CityEnum.C1201, "武清区"),
    C120115(120115, CityEnum.C1201, "宝坻区"),
    C120116(120116, CityEnum.C1201, "滨海新区"),
    C120117(120117, CityEnum.C1201, "宁河区"),
    C120118(120118, CityEnum.C1201, "静海区"),
    C120119(120119, CityEnum.C1201, "蓟州区"),
    C310101(310101, CityEnum.C3101, "黄浦区"),
    C310104(310104, CityEnum.C3101, "徐汇区"),
    C310105(310105, CityEnum.C3101, "长宁区"),
    C310106(310106, CityEnum.C3101, "静安区"),
    C310107(310107, CityEnum.C3101, "普陀区"),
    C310109(310109, CityEnum.C3101, "虹口区"),
    C310110(310110, CityEnum.C3101, "杨浦区"),
    C310112(310112, CityEnum.C3101, "闵行区"),
    C310113(310113, CityEnum.C3101, "宝山区"),
    C310114(310114, CityEnum.C3101, "嘉定区"),
    C310115(310115, CityEnum.C3101, "浦东新区"),
    C310116(310116, CityEnum.C3101, "金山区"),
    C310117(310117, CityEnum.C3101, "松江区"),
    C310118(310118, CityEnum.C3101, "青浦区"),
    C310120(310120, CityEnum.C3101, "奉贤区"),
    C310151(310151, CityEnum.C3101, "崇明区"),
    C320102(320102, CityEnum.C3201, "玄武区"),
    C320104(320104, CityEnum.C3201, "秦淮区"),
    C320105(320105, CityEnum.C3201, "建邺区"),
    C320106(320106, CityEnum.C3201, "鼓楼区"),
    C320111(320111, CityEnum.C3201, "浦口区"),
    C320113(320113, CityEnum.C3201, "栖霞区"),
    C320114(320114, CityEnum.C3201, "雨花台区"),
    C320115(320115, CityEnum.C3201, "江宁区"),
    C320116(320116, CityEnum.C3201, "六合区"),
    C320117(320117, CityEnum.C3201, "溧水区"),
    C320118(320118, CityEnum.C3201, "高淳区"),
    C320505(320505, CityEnum.C3205, "虎丘区"),
    C320506(320506, CityEnum.C3205, "吴中区"),
    C320507(320507, CityEnum.C3205, "相城区"),
    C320508(320508, CityEnum.C3205, "姑苏区"),
    C320509(320509, CityEnum.C3205, "吴江区"),
    C320576(320576, CityEnum.C3205, "苏州工业园区"),
    C320581(320581, CityEnum.C3205, "常熟市"),
    C320582(320582, CityEnum.C3205, "张家港市"),
    C320583(320583, CityEnum.C3205, "昆山市"),
    C320585(320585, CityEnum.C3205, "太仓市"),
    C330102(330102, CityEnum.C3301, "上城区"),
    C330105(330105, CityEnum.C3301, "拱墅区"),
    C330106(330106, CityEnum.C3301, "西湖区"),
    C330108(330108, CityEnum.C3301, "滨江区"),
    C330109(330109, CityEnum.C3301, "萧山区"),
    C330110(330110, CityEnum.C3301, "余杭区"),
    C330111(330111, CityEnum.C3301, "富阳区"),
    C330112(330112, CityEnum.C3301, "临安区"),
    C330113(330113, CityEnum.C3301, "临平区"),
    C330114(330114, CityEnum.C3301, "钱塘区"),
    C330122(330122, CityEnum.C3301, "桐庐县"),
    C330127(330127, CityEnum.C3301, "淳安县"),
    C330182(330182, CityEnum.C3301, "建德市"),
    C330203(330203, CityEnum.C3302, "海曙区"),
    C330205(330205, CityEnum.C3302, "江北区"),
    C330206(330206, CityEnum.C3302, "北仑区"),
    C330211(330211, CityEnum.C3302, "镇海区"),
    C330212(330212, CityEnum.C3302, "鄞州区"),
    C330213(330213, CityEnum.C3302, "奉化区"),
    C330225(330225, CityEnum.C3302, "象山县"),
    C330226(330226, CityEnum.C3302, "宁海县"),
    C330281(330281, CityEnum.C3302, "余姚市"),
    C330282(330282, CityEnum.C3302, "慈溪市"),
    C350203(350203, CityEnum.C3502, "思明区"),
    C350205(350205, CityEnum.C3502, "海沧区"),
    C350206(350206, CityEnum.C3502, "湖里区"),
    C350211(350211, CityEnum.C3502, "集美区"),
    C350212(350212, CityEnum.C3502, "同安区"),
    C350213(350213, CityEnum.C3502, "翔安区"),
    C370102(370102, CityEnum.C3701, "历下区"),
    C370103(370103, CityEnum.C3701, "市中区"),
    C370104(370104, CityEnum.C3701, "槐荫区"),
    C370105(370105, CityEnum.C3701, "天桥区"),
    C370112(370112, CityEnum.C3701, "历城区"),
    C370113(370113, CityEnum.C3701, "长清区"),
    C370114(370114, CityEnum.C3701, "章丘区"),
    C370115(370115, CityEnum.C3701, "济阳区"),
    C370116(370116, CityEnum.C3701, "莱芜区"),
    C370117(370117, CityEnum.C3701, "钢城区"),
    C370124(370124, CityEnum.C3701, "平阴县"),
    C370126(370126, CityEnum.C3701, "商河县"),
    C370176(370176, CityEnum.C3701, "济南高新技术产业开发区"),
    C370202(370202, CityEnum.C3702, "市南区"),
    C370203(370203, CityEnum.C3702, "市北区"),
    C370211(370211, CityEnum.C3702, "黄岛区"),
    C370212(370212, CityEnum.C3702, "崂山区"),
    C370213(370213, CityEnum.C3702, "李沧区"),
    C370214(370214, CityEnum.C3702, "城阳区"),
    C370215(370215, CityEnum.C3702, "即墨区"),
    C370281(370281, CityEnum.C3702, "胶州市"),
    C370283(370283, CityEnum.C3702, "平度市"),
    C370285(370285, CityEnum.C3702, "莱西市"),
    C410102(410102, CityEnum.C4101, "中原区"),
    C410103(410103, CityEnum.C4101, "二七区"),
    C410104(410104, CityEnum.C4101, "管城回族区"),
    C410105(410105, CityEnum.C4101, "金水区"),
    C410106(410106, CityEnum.C4101, "上街区"),
    C410108(410108, CityEnum.C4101, "惠济区"),
    C410122(410122, CityEnum.C4101, "中牟县"),
    C410171(410171, CityEnum.C4101, "郑州经济技术开发区"),
    C410172(410172, CityEnum.C4101, "郑州高新技术产业开发区"),
    C410173(410173, CityEnum.C4101, "郑州航空港经济综合实验区"),
    C410181(410181, CityEnum.C4101, "巩义市"),
    C410182(410182, CityEnum.C4101, "荥阳市"),
    C410183(410183, CityEnum.C4101, "新密市"),
    C410184(410184, CityEnum.C4101, "新郑市"),
    C410185(410185, CityEnum.C4101, "登封市"),
    C420102(420102, CityEnum.C4201, "江岸区"),
    C420103(420103, CityEnum.C4201, "江汉区"),
    C420104(420104, CityEnum.C4201, "硚口区"),
    C420105(420105, CityEnum.C4201, "汉阳区"),
    C420106(420106, CityEnum.C4201, "武昌区"),
    C420107(420107, CityEnum.C4201, "青山区"),
    C420111(420111, CityEnum.C4201, "洪山区"),
    C420112(420112, CityEnum.C4201, "东西湖区"),
    C420113(420113, CityEnum.C4201, "汉南区"),
    C420114(420114, CityEnum.C4201, "蔡甸区"),
    C420115(420115, CityEnum.C4201, "江夏区"),
    C420116(420116, CityEnum.C4201, "黄陂区"),
    C420117(420117, CityEnum.C4201, "新洲区"),
    C430102(430102, CityEnum.C4301, "芙蓉区"),
    C430103(430103, CityEnum.C4301, "天心区"),
    C430104(430104, CityEnum.C4301, "岳麓区"),
    C430105(430105, CityEnum.C4301, "开福区"),
    C430111(430111, CityEnum.C4301, "雨花区"),
    C430112(430112, CityEnum.C4301, "望城区"),
    C430121(430121, CityEnum.C4301, "长沙县"),
    C430181(430181, CityEnum.C4301, "浏阳市"),
    C430182(430182, CityEnum.C4301, "宁乡市"),
    C440103(440103, CityEnum.C4401, "荔湾区"),
    C440104(440104, CityEnum.C4401, "越秀区"),
    C440105(440105, CityEnum.C4401, "海珠区"),
    C440106(440106, CityEnum.C4401, "天河区"),
    C440111(440111, CityEnum.C4401, "白云区"),
    C440112(440112, CityEnum.C4401, "黄埔区"),
    C440113(440113, CityEnum.C4401, "番禺区"),
    C440114(440114, CityEnum.C4401, "花都区"),
    C440115(440115, CityEnum.C4401, "南沙区"),
    C440117(440117, CityEnum.C4401, "从化区"),
    C440118(440118, CityEnum.C4401, "增城区"),
    C440303(440303, CityEnum.C4403, "罗湖区"),
    C440304(440304, CityEnum.C4403, "福田区"),
    C440305(440305, CityEnum.C4403, "南山区"),
    C440306(440306, CityEnum.C4403, "宝安区"),
    C440307(440307, CityEnum.C4403, "龙岗区"),
    C440308(440308, CityEnum.C4403, "盐田区"),
    C440309(440309, CityEnum.C4403, "龙华区"),
    C440310(440310, CityEnum.C4403, "坪山区"),
    C440311(440311, CityEnum.C4403, "光明区"),
    C440604(440604, CityEnum.C4406, "禅城区"),
    C440605(440605, CityEnum.C4406, "南海区"),
    C440606(440606, CityEnum.C4406, "顺德区"),
    C440607(440607, CityEnum.C4406, "三水区"),
    C440608(440608, CityEnum.C4406, "高明区"),
    C441900(441900, CityEnum.C4419, "东莞市"),
    //    成都高新区 不是一级或二级行政区，它是一个“国家级高新技术产业开发区”，属于 功能区、管理区、经济区 的范畴，不具有独立的行政区代码。
    C510104(510104, CityEnum.C5101, "锦江区"),
    C510105(510105, CityEnum.C5101, "青羊区"),
    C510106(510106, CityEnum.C5101, "金牛区"),
    C510107(510107, CityEnum.C5101, "武侯区"),
    C510108(510108, CityEnum.C5101, "成华区"),
    C510112(510112, CityEnum.C5101, "龙泉驿区"),
    C510113(510113, CityEnum.C5101, "青白江区"),
    C510114(510114, CityEnum.C5101, "新都区"),
    C510115(510115, CityEnum.C5101, "温江区"),
    C510116(510116, CityEnum.C5101, "双流区"),
    C510117(510117, CityEnum.C5101, "郫都区"),
    C510118(510118, CityEnum.C5101, "新津区"),
    C510121(510121, CityEnum.C5101, "金堂县"),
    C510129(510129, CityEnum.C5101, "大邑县"),
    C510131(510131, CityEnum.C5101, "蒲江县"),
    C510181(510181, CityEnum.C5101, "都江堰市"),
    C510182(510182, CityEnum.C5101, "彭州市"),
    C510183(510183, CityEnum.C5101, "邛崃市"),
    C510184(510184, CityEnum.C5101, "崇州市"),
    C510185(510185, CityEnum.C5101, "简阳市"),
    C610102(610102, CityEnum.C6101, "新城区"),
    C610103(610103, CityEnum.C6101, "碑林区"),
    C610104(610104, CityEnum.C6101, "莲湖区"),
    C610111(610111, CityEnum.C6101, "灞桥区"),
    C610112(610112, CityEnum.C6101, "未央区"),
    C610113(610113, CityEnum.C6101, "雁塔区"),
    C610114(610114, CityEnum.C6101, "阎良区"),
    C610115(610115, CityEnum.C6101, "临潼区"),
    C610116(610116, CityEnum.C6101, "长安区"),
    C610117(610117, CityEnum.C6101, "高陵区"),
    C610118(610118, CityEnum.C6101, "鄠邑区"),
    C610122(610122, CityEnum.C6101, "蓝田县"),


    ;

    private final Integer code;
    private final String description;
    private final String name;
    private final CityEnum cityEnum;


    /**
     * 根据编码获取枚举值
     *
     * @param code 区县编码
     * @return 对应的枚举值
     * @throws IllegalArgumentException 如果找不到对应的枚举值
     */
    public static CountyEnum fromCode(Integer code) {
        if (code == null) {
            throw new IllegalArgumentException("区县编码不能为null");
        }

        for (CountyEnum county : values()) {
            if (county.code.equals(code)) {
                return county;
            }
        }

        throw new IllegalArgumentException("改地区未开发支持:" + code);
    }

    CountyEnum(Integer code, CityEnum cityEnum, String name) {
        this.cityEnum = cityEnum;
        this.code = code;
        this.description = cityEnum.getDescription() + "-" + name;
        this.name = name;
    }

    @Override
    public Integer getValue() {
        return code;
    }

}