package com.demon.giraffe.common.domain;

import com.demon.giraffe.modules.user.model.dto.query.MemberAddressQueryRequest;
import com.demon.giraffe.modules.user.model.dto.query.MemberIdentityQueryRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.util.Assert;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.util.Objects;

/**
 * 分页检索条件基类
 *
 * @param <T> 查询条件类型，必须是可序列化的
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Schema(name = "BasePageQuery", description = "分页查询基础参数")
public class BasePageQuery<T extends Serializable> implements Serializable {

    private static final long serialVersionUID = 7779338682042686609L;
    private static final int DEFAULT_PAGE = 1;
    private static final int DEFAULT_PAGE_SIZE = 10;
    private static final int MAX_PAGE_SIZE = 100;

    @Schema(
            description = "当前页码（从1开始）",
            example = "1",
            defaultValue = "1"
    )
    @Min(value = 1, message = "页码不能小于1")
    private Integer page = DEFAULT_PAGE;

    @Schema(
            description = "每页记录数",
            example = "10",
            defaultValue = "10"
    )
    @Min(value = 1, message = "每页记录数不能小于1")
    @Max(value = MAX_PAGE_SIZE, message = "每页记录数不能超过100")
    private Integer perPage = DEFAULT_PAGE_SIZE;

    @Schema(
            description = "数据起始位置（内部计算使用）",
            accessMode = Schema.AccessMode.READ_ONLY,
            hidden = true
    )
    private Integer pageStart;

    @Schema(
            description = "动态查询条件",
            oneOf = {
                    MemberAddressQueryRequest.class,
                    MemberIdentityQueryRequest.class
            }
    )
    private T query;

    /**
     * 初始化分页参数并计算起始位置
     *
     * @throws IllegalArgumentException 如果分页参数无效
     */
    public void init() {


        // 使用默认值如果参数为null
        int currentPage = Objects.requireNonNullElse(page, DEFAULT_PAGE);
        int pageSize = Objects.requireNonNullElse(perPage, DEFAULT_PAGE_SIZE);

        // 参数校验
        Assert.isTrue(currentPage >= 1, "页码必须大于等于1");
        Assert.isTrue(pageSize >= 1 && pageSize <= MAX_PAGE_SIZE,
                "每页记录数必须在1到100之间");

        this.pageStart = (currentPage - 1) * pageSize;
    }

    /**
     * 获取计算后的起始位置
     *
     * @return 数据起始位置
     * @throws IllegalStateException 如果尚未调用init()方法
     */
    @Schema(hidden = true)
    public Integer getPageStart() {
        if (pageStart == null) {
            throw new IllegalStateException("请先调用init()方法初始化分页参数");
        }
        return pageStart;
    }
}