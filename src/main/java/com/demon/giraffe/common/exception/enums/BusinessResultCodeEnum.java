package com.demon.giraffe.common.exception.enums;


import com.demon.giraffe.common.exception.enums.inter.ExceptionInterface;
import lombok.Getter;

/**


 * 状态码合并 防止code 重复


 *
 */
@Getter
public enum BusinessResultCodeEnum implements ExceptionInterface<BusinessResultCodeEnum> {
    SUCCESS(200, "请求成功"),
    /**
     * 自定义异常
     * 10001 凭证过期
     * 10002 凭证已使用
     * 10003 设备已移除
     * 10004 创建设备异常
     * 10005 绑定设备异常
     * 10006 移除设备异常
     * 10007 token异常
     * 10008 屏体绑定异常
     * 10009 远程调用异常
     * 10010 未定义业务异常
     * 10011 设备信息为空
     * 10011 默认上云异常
     */
    TOKEN_EXPIRE(10001, "TOKEN_EXPIRE"),
    TOKEN_USED(10002, "TOKEN_USED"),
    DEVICE_REMOVED(10003, "DEVICE_REMOVED"),
    CREATE_DEVICE_EXCEPTION(10004, "CREATE_DEVICE_EXCEPTION"),
    BIND_DEVICE_EXCEPTION(10005, "BIND_DEVICE_EXCEPTION"),
    REMOVE_DEVICE_EXCEPTION(10006, "REMOVE_DEVICE_EXCEPTION"),
    TOKEN_EXCEPTION(10007, "TOKEN_EXCEPTION"),
    SCREEN_BINDING_EXCEPTION(10008, "SCREEN_BINDING_EXCEPTION"),
    APP_EXCEPTION(10009, "远程调用异常"),
    BUSINESS_EXCEPTION(10010, "BUSINESS_EXCEPTION"),
    DEVICE_INFO_IS_NULL(10011, "DEVICE_INFO_IS_NULL"),
    DEFAULT_DEVICE_EXCEPTION(10012, "默认上云异常"),
    DEVICE_GROUP_REMOVED_EXCEPTION(10013, "设备组被移除"),
    CAMERA_NAME_EXCEPTION(10014, null),
    INVALID_ID_KEY(10015,null),
    UNKNOWN_PROTOCOL_TYPE(10016,null),

    //ACU35、播放盒的公用异常状态 200XX
    REGISTER_PARAM_IS_NULL(20000, "设备上云时,请求参数为空"),
    REGISTER_DEVICE_NAME_IS_NULL(20001, "设备上云时，设备名称为空"),
    DEVICE_REGISTER_RECORD_IS_NULL(20002, "设备上云记录获取失败"),

    DEVICE_REGISTER_NOT_OPERATE_PERMISSIONS(20003,"设备上云，指定用户没有显示屏组操作权限"),
    DEVICE_REGISTER_FAIL(20004, "设备上云失败，保存数据出现异常"),
    UPLOAD_TYPE_MISMATCH(20005, "上传文件类型不匹配"),
    FILE_NAMING_EXCEPTION(20006, "文件命名异常"),
    FILE_MD5_MISMATCH(20007, "文件md5不匹配"),
    EXIST_OTHER_SCREEN_USE_SEND_CARD(20008, "存在显示屏使用发送卡上云"),
    SCREEN_SEND_CARD_IS_EMPTY(20009,"显示屏下不存在要上云的发送卡"),

    //播放盒单独异常状态 300XX
    DEVICE_UNIQUE_ID_IS_NULL(30004,"设备上云，唯一编码为空"),
    SCREEN_NAME_IS_NULL(30005,"设备上云，唯一识别码为空"),

    /**
     * 400XX
     * 具体的BUSINESS_EXCEPTION
     * 业务异常
     *
     *
     *  40006 设备版本不支持
     *  40007 设备离线指令下发失败
     *  40032 用户没有权限
     */
    NOT_LOGIN(40001, "BusinessResultCodeEnum.NOT_LOGIN"),
    PARAM_EXCEPTION(40003, "BusinessResultCodeEnum.PARAM_EXCEPTION"),
    PARAM_VERIFY_EXCEPTION(40004, "BusinessResultCodeEnum.PARAM_VERIFY_EXCEPTION"),
    OPERATE_EXCEPTION(40005, "BusinessResultCodeEnum.OPERATE_EXCEPTION"),
    DEVICE_VERSION_NOT_SUPPORTED(40006, "DEVICE_VERSION_NOT_SUPPORTED"),
    SCREEN_BODY_OFFLINE_PUBLISH_COMMAND_FAIL(40007, "SCREEN_BODY_OFFLINE_PUBLISH_COMMAND_FAIL"),
    EXCEPTION_DUPLICATE_RULE(40031, "BUSINESS_EXCEPTION_DUPLICATE_RULE"),

    NO_OPERATION_PERMISSION(40032, "NO_OPERATION_PERMISSION"),

    FAIL_TO_REGISTER(60001, "fail_to_register"),

    /**
     * Json 异常 110XX
     */
    JSON_EXCEPTION(11000, "json exception"),
    JSON_SERIALIZATION_EXCEPTION(11001, "JsonUtil serialization failed"),
    JSON_DESERIALIZATION_EXCEPTION(11002, "JsonUtil deserialization failed"),

    ;

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 国际化信息的key
     */
    private final String key;


    BusinessResultCodeEnum(Integer code, String key) {
        this.code = code;
        this.key = key;
    }
    @Override
    public Integer getCode() {
        return code;
    }
    @Override
    public String getKey() {
        return key;
    }

    @Override
    public BusinessResultCodeEnum getT() {
        return this;
    }
}
