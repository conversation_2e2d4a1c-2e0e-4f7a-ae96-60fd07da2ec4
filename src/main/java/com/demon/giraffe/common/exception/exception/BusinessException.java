package com.demon.giraffe.common.exception.exception;


import com.demon.giraffe.common.exception.enums.inter.ExceptionInterface;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**


 * @date 2023/6/21 10:16
 * @description 自定义异常 —— 业务异常
 */
@NoArgsConstructor
public class BusinessException extends RuntimeException {
	private static final long serialVersionUID = -1708097317609308820L;
	
	@Getter
    private ExceptionInterface<? extends Enum<?>> resultCodeEnum;
	/**
	 * 异常信息
	 */
	private String message;

	/**
	 * 异常参数
	 */
	@Getter
    private Object[] params;

	public BusinessException(String message) {
		this.resultCodeEnum = null;
		this.message = message;
	}

	public BusinessException(String message, Object... params) {
		this.message = message;
		this.params = params;
	}
	
	public BusinessException(ExceptionInterface<?> resultCodeEnum, String message) {
		this.message = message;
		this.resultCodeEnum = resultCodeEnum;
	}
	public BusinessException(ExceptionInterface<?> resultCodeEnum) {
		this.message = resultCodeEnum.getKey();
		this.resultCodeEnum = resultCodeEnum;
	}
	
	@Override
	public String getMessage() {
		return message;
	}

}
