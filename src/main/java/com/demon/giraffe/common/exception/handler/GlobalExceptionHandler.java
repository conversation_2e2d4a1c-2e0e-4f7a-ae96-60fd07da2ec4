package com.demon.giraffe.common.exception.handler;


import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotRoleException;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.common.exception.enums.BaseResultCodeEnum;
import com.demon.giraffe.common.exception.enums.BusinessResultCodeEnum;
import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.common.exception.exception.UserLoginException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;


import java.util.Objects;

/**
 * @date 2023/6/20 18:20
 * @description 全局异常处理
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {

    public GlobalExceptionHandler() {

    }
    /**
     * 空指针异常处理
     */
    @ResponseBody
    @ExceptionHandler(NullPointerException.class)
    public ResultBean<Object> handleNullPointerException(HttpServletRequest request, NullPointerException e) {
        log.error("发生空指针异常! 请求URI: {}", request.getRequestURI(), e);

        // 生产环境返回简化信息，开发环境返回详细堆栈
        String errorMsg = "系统内部错误";
        if (Objects.equals(System.getProperty("spring.profiles.active"), "dev")) {
            errorMsg = String.format("空指针异常: %s", e.getMessage());
        }

        return ResultBean.fail(errorMsg);
    }


    /**
     * Sa-Token 未登录异常处理
     */
    @ResponseBody
    @ExceptionHandler(NotLoginException.class)
    public ResultBean<Object> handleNotLoginException(HttpServletRequest request, NotLoginException e) {
        log.warn("用户未登录, 请求URI: {}, 原因: {}", request.getRequestURI(), e.getMessage());
        // 可以自定义异常类或复用 UserLoginException 提示未登录
        return ResultBean.fail(BaseResultCodeEnum.UN_AUTHORITATIVE, "用户未登录或登录已失效", null);
    }

    /**
     * Sa-Token 无角色权限异常处理
     */
    @ResponseBody
    @ExceptionHandler(NotRoleException.class)
    public ResultBean<Object> handleNotRoleException(HttpServletRequest request, NotRoleException e) {
        log.warn("用户无访问权限, 请求URI: {}, 原因:\n}", request.getRequestURI(), e);
        return ResultBean.fail(BaseResultCodeEnum.FORBIDDEN, "无访问权限：" + e.getRole(), null);
    }

    /**
     * Throw异常处理
     * Sc
     *
     * @param request   请求request
     * @param throwable 抛出的Throw异常
     * @return 响应结果
     */
    @ExceptionHandler(value = Throwable.class)
    @ResponseBody
    public ResultBean<Object> dealThrowable(HttpServletRequest request, Throwable throwable) {
        log.error("发生异常!请求URI:{}", request.getRequestURI(), throwable);
        BaseResultCodeEnum resultCode = BaseResultCodeEnum.UN_KNOWN;
        return ResultBean.fail(resultCode, resultCode.getKey(), null);
    }

    /**
     * 登录异常处理
     *
     * @param request   请求
     * @param exception 登录异常
     * @return 响应结果
     */
    @ResponseBody
    @ExceptionHandler(value = UserLoginException.class)
    public ResultBean<Object> dealLoginException(HttpServletRequest request, UserLoginException exception) {
        log.error("获取token,请求URI:{}", request.getRequestURI(), exception);
        BaseResultCodeEnum resultCode = BaseResultCodeEnum.FORBIDDEN;
        return ResultBean.fail(resultCode, exception.getMessage(), null);

    }

    /**
     * 登录异常处理
     *
     * @param request   请求
     * @param exception 登录异常
     * @return 响应结果
     */
    @ResponseBody
    @ExceptionHandler(value = BusinessException.class)
    public ResultBean<Object> dealBusinessException(HttpServletRequest request, BusinessException exception) {
        log.error("业务异常,请求URI:{}", request.getRequestURI(), exception);
        // 参数异常
        if (Objects.nonNull(exception.getResultCodeEnum())) {
            return ResultBean.fail(exception.getResultCodeEnum(), exception.getMessage(), null);
        }

        return ResultBean.fail(BusinessResultCodeEnum.BUSINESS_EXCEPTION, exception.getMessage(), null);
    }

    /**
     * 未知异常处理
     *
     * @param request   请求
     * @param exception 未知异常
     * @return 响应结果
     */
    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public ResultBean<Object> dealException(HttpServletRequest request, Exception exception) {
        log.error("发生异常!请求URI:{}", request.getRequestURI(), exception);
        BaseResultCodeEnum resultCode = BaseResultCodeEnum.UN_KNOWN;
        return ResultBean.fail(resultCode, resultCode.getKey(), null);
    }


    /**
     * 统一处理spring注解校验
     *
     * @param exception 参数异常
     * @return 参数校验错误结果描述
     */
    @ResponseBody
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResultBean<String> myMethodArgumentNotValidException(HttpServletRequest request, MethodArgumentNotValidException exception) {
        BindingResult result = exception.getBindingResult();
        FieldError fieldError = result.getFieldError();
        if (Objects.nonNull(fieldError)) {
            log.error("参数校验异常,请求URI:{},参数检验异常信息：{}", request.getRequestURI(), fieldError.getDefaultMessage(), exception);
            return ResultBean.fail(BaseResultCodeEnum.BAD_REQUEST, fieldError.getDefaultMessage(), null);
        } else {
            log.error("参数校验异常,请求URI:{}", request.getRequestURI(), exception);
        }
        return ResultBean.fail(BaseResultCodeEnum.FAIL);
    }

    /**
     * Multipart 请求缺少必要部分（如文件）异常处理
     *
     * @param request   请求
     * @param exception 缺失文件异常
     * @return 响应结果
     */
    @ResponseBody
    @ExceptionHandler(value = org.springframework.web.multipart.support.MissingServletRequestPartException.class)
    public ResultBean<Object> handleMissingServletRequestPartException(HttpServletRequest request,
                                                                       org.springframework.web.multipart.support.MissingServletRequestPartException exception) {
        log.error("文件上传参数缺失异常, 请求URI: {}, 缺失字段: {}", request.getRequestURI(), exception.getRequestPartName(), exception);
        return ResultBean.fail(BaseResultCodeEnum.BAD_REQUEST, "缺少必要的上传文件参数: " + exception.getRequestPartName(), null);
    }


    /**
     * 参数类型不匹配异常（如：期望 Long，传了 "sort"）
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseBody
    public ResultBean<Object> handleMethodArgumentTypeMismatchException(HttpServletRequest request,
                                                                        MethodArgumentTypeMismatchException ex) {
        String paramName = ex.getName();
        String paramType = ex.getRequiredType() != null ? ex.getRequiredType().getSimpleName() : "未知类型";
        String message = String.format("参数类型错误：参数 [%s] 应为 [%s] 类型", paramName, paramType);
        log.warn("参数类型不匹配, 请求URI: {}, 错误信息: {}", request.getRequestURI(), message, ex);
        return ResultBean.fail(BaseResultCodeEnum.BAD_REQUEST, message, null);
    }

    /**
     * 处理 IllegalArgumentException 异常，返回异常消息
     */
    @ResponseBody
    @ExceptionHandler(IllegalArgumentException.class)
    public ResultBean<Object> handleIllegalArgumentException(HttpServletRequest request, IllegalArgumentException e) {
        log.error("非法参数异常，请求URI: {}, 错误信息: {}", request.getRequestURI(), e.getMessage(), e);
        // 返回异常消息，code 这里用 UN_KNOWN 的 code 10000（你根据实际code调整）
        BaseResultCodeEnum resultCode = BaseResultCodeEnum.UN_KNOWN;
        return ResultBean.fail(resultCode, e.getMessage(), null);
    }

}
