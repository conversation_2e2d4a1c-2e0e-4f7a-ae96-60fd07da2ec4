package com.demon.giraffe.common.exception.enums;


import com.demon.giraffe.common.exception.enums.inter.ExceptionInterface;
import lombok.Getter;

/**

 * @className BusinessResultCodeEnum
 * @description 通用响应码枚举
 */
@Getter
public enum BaseResultCodeEnum implements ExceptionInterface<BaseResultCodeEnum> {

    SUCCESS(200, "SUCCESS"),
    FAIL(500, "FAIL"),

    BAD_REQUEST(400, "BAD_REQUEST"),
    UN_AUTHORITATIVE(401, "UN_AUTHORITATIVE"),
    FORBIDDEN(403, "FORBIDDEN"),
    NOT_FOUND(404, "NOT_FOUND"),
    METHOD_DISABLE(405, "METHOD_DISABLE"),
    TIME_OUT(408, "TIME_OUT"),

    SERVICE_UN_AVAILABLE(503, "SERVICE_UN_AVAILABLE"),
    GATEWAY_TIME_OUT(504, "GATEWAY_TIME_OUT"),
    UN_KNOWN(10000, "UN_KNOWN");
    /**
     * 编码
     */
    private final Integer code;

    /**
     * 国际化信息的key
     */
    private final String key;


    BaseResultCodeEnum(Integer code, String key) {
        this.code = code;
        this.key = key;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public BaseResultCodeEnum getT() {
        return this;
    }
}
