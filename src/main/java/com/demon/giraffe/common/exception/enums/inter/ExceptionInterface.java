package com.demon.giraffe.common.exception.enums.inter;

/**

 * @description 异常接口
 * @date 30/11/2023  上午10:33
 */
public interface ExceptionInterface<T extends Enum<T>> {

    /**
     * 获取业务码
     *
     * @return 业务码

     **/
    Integer getCode();

    /**
     * 获取国际化信息的key
     *
     * @return 国际化信息的key

     **/
    String getKey();

    /**
     * 当前枚举类型
     *
     * @return 当前异常枚举类型

     **/
    T getT();
}
