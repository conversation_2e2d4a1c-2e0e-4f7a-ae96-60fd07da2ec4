package com.demon.giraffe.common.aspect;

import com.demon.giraffe.common.domain.dto.request.RegionRequest;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.common.service.RegionProcessingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 区域处理切面
 * 自动处理包含区域信息的请求参数
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class RegionProcessingAspect {

    private final RegionProcessingService regionProcessingService;

    @Around("@annotation(com.demon.giraffe.common.annotation.ProcessRegion)")
    public Object processRegion(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        
        // 处理方法参数中的区域信息
        for (Object arg : args) {
            if (arg != null) {
                processRegionInObject(arg);
            }
        }
        
        return joinPoint.proceed(args);
    }

    private final Set<Object> visitedObjects = Collections.newSetFromMap(new IdentityHashMap<>());

    private void processRegionInObject(Object obj) {
        if (obj == null || visitedObjects.contains(obj)) {
            return;
        }
        visitedObjects.add(obj);

        // 特殊处理集合类型
        if (obj instanceof Collection<?> collection) {
            for (Object item : collection) {
                processRegionInObject(item);
            }
            return;
        }

        // 特殊处理数组类型
        if (obj.getClass().isArray()) {
            Object[] array = (Object[]) obj;
            for (Object item : array) {
                processRegionInObject(item);
            }
            return;
        }

        Class<?> clazz = obj.getClass();

        // 跳过Java内置类型和第三方库类型
        if (clazz.getName().startsWith("java.") ||
            clazz.getName().startsWith("javax.") ||
            clazz.getName().startsWith("org.springframework.") ||
            isPrimitiveOrWrapper(clazz)) {
            return;
        }

        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            try {
                field.setAccessible(true);
                Object fieldValue = field.get(obj);

                // 处理RegionRequest类型的字段
                if (fieldValue instanceof RegionRequest regionRequest) {
                    if (regionRequest.isValid() && regionRequest.getAddressCode() == null) {
                        CountyEnum countyEnum = regionProcessingService.validateAndParseRegion(regionRequest);
                        regionRequest.setAddressCode(countyEnum);
                    }
                }

                // 递归处理嵌套对象
                if (fieldValue != null && !isPrimitiveOrWrapper(field.getType()) &&
                        !field.getType().equals(String.class)) {
                    processRegionInObject(fieldValue);
                }

            } catch (Exception e) {
                log.warn("处理字段 {} 时发生异常: {}", field.getName(), e.getMessage());
                // 不抛出异常，继续处理其他字段
            }
        }
    }

    /**
     * 判断是否是基本类型或包装类型
     */
    private boolean isPrimitiveOrWrapper(Class<?> type) {
        return type.isPrimitive() || 
               type.equals(Boolean.class) || type.equals(Integer.class) || 
               type.equals(Long.class) || type.equals(Double.class) || 
               type.equals(Float.class) || type.equals(Byte.class) || 
               type.equals(Short.class) || type.equals(Character.class);
    }
}
