package com.demon.giraffe.common.service;

import com.demon.giraffe.common.domain.dto.request.RegionRequest;
import com.demon.giraffe.common.domain.enums.CountyEnum;

/**
 * 区域处理服务接口
 * 统一处理省份、城市、区县的入参验证和转换
 */
public interface RegionProcessingService {

    /**
     * 验证并解析区域信息
     * @param regionRequest 区域请求
     * @return 解析后的区域编码
     * @throws IllegalArgumentException 当区域信息无效时抛出
     */
    CountyEnum validateAndParseRegion(RegionRequest regionRequest);

    /**
     * 验证并解析区域信息（直接传入省市区）
     * @param province 省份
     * @param city 城市
     * @param district 区县
     * @return 解析后的区域编码
     * @throws IllegalArgumentException 当区域信息无效时抛出
     */
    CountyEnum validateAndParseRegion(String province, String city, String district);

    /**
     * 检查区域是否在服务范围内
     * @param countyEnum 区域编码
     * @return 是否在服务范围内
     */
    boolean isRegionSupported(CountyEnum countyEnum);

    /**
     * 获取区域的完整描述
     * @param countyEnum 区域编码
     * @return 完整描述（省-市-区）
     */
    String getRegionDescription(CountyEnum countyEnum);

    /**
     * 从CountyEnum中提取省市区信息
     * @param countyEnum 区域编码
     * @return 区域请求对象
     */
    RegionRequest extractRegionInfo(CountyEnum countyEnum);
}
