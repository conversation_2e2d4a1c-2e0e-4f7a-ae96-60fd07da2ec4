package com.demon.giraffe.common.service.impl;

import com.demon.giraffe.common.domain.dto.request.RegionRequest;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.common.service.RegionProcessingService;
import com.demon.giraffe.modules.common.service.impl.LocationServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 区域处理服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RegionProcessingServiceImpl implements RegionProcessingService {

    private final LocationServiceImpl locationService;

    @Override
    public CountyEnum validateAndParseRegion(RegionRequest regionRequest) {
        if (regionRequest == null) {
            throw new IllegalArgumentException("区域信息不能为空");
        }

        return validateAndParseRegion(
                regionRequest.getProvince(),
                regionRequest.getCity(),
                regionRequest.getDistrict()
        );
    }

    @Override
    public CountyEnum validateAndParseRegion(String province, String city, String district) {
        // 1. 基础验证
        validateRegionParams(province, city, district);

        // 2. 标准化处理
        String normalizedProvince = normalizeRegionName(province);
        String normalizedCity = normalizeRegionName(city);
        String normalizedDistrict = normalizeRegionName(district);

        try {
            // 3. 解析区域编码
            CountyEnum countyEnum = locationService.resolveCountyCode(
                    normalizedProvince, 
                    normalizedCity, 
                    normalizedDistrict
            );

            // 4. 验证区域是否在服务范围内
            if (!isRegionSupported(countyEnum)) {
                throw new IllegalArgumentException(
                        String.format("区域[%s-%s-%s]暂未开放服务", 
                                normalizedProvince, normalizedCity, normalizedDistrict)
                );
            }

            log.info("区域解析成功：{}-{}-{} -> {}", 
                    normalizedProvince, normalizedCity, normalizedDistrict, countyEnum.getDescription());

            return countyEnum;

        } catch (Exception e) {
            log.error("区域解析失败：{}-{}-{}", normalizedProvince, normalizedCity, normalizedDistrict, e);
            throw new IllegalArgumentException(
                    String.format("区域[%s-%s-%s]解析失败：%s", 
                            normalizedProvince, normalizedCity, normalizedDistrict, e.getMessage())
            );
        }
    }

    @Override
    public boolean isRegionSupported(CountyEnum countyEnum) {
        if (countyEnum == null) {
            return false;
        }

        // 这里可以根据业务需求添加更多的区域支持判断逻辑
        // 例如：检查是否有配送员、是否有柜子等
        try {
            // 基础验证：能够正常获取区域信息就认为支持
            return countyEnum.getCityEnum() != null && 
                   countyEnum.getCityEnum().getProvinceEum() != null;
        } catch (Exception e) {
            log.warn("检查区域支持状态失败：{}", countyEnum, e);
            return false;
        }
    }

    @Override
    public String getRegionDescription(CountyEnum countyEnum) {
        if (countyEnum == null) {
            return "未知区域";
        }

        try {
            return countyEnum.getDescription();
        } catch (Exception e) {
            log.warn("获取区域描述失败：{}", countyEnum, e);
            return "区域信息异常";
        }
    }

    @Override
    public RegionRequest extractRegionInfo(CountyEnum countyEnum) {
        if (countyEnum == null) {
            return null;
        }
        return RegionRequest.builder()
                .province(countyEnum.getCityEnum().getProvinceEum().getName())
                .city(countyEnum.getCityEnum().getName())
                .district(countyEnum.getName())
                .addressCode(countyEnum)
                .build();
    }

    /**
     * 验证区域参数
     */
    private void validateRegionParams(String province, String city, String district) {
        if (!StringUtils.hasText(province)) {
            throw new IllegalArgumentException("省份不能为空");
        }
        if (!StringUtils.hasText(city)) {
            throw new IllegalArgumentException("城市不能为空");
        }
        if (!StringUtils.hasText(district)) {
            throw new IllegalArgumentException("区县不能为空");
        }

        // 长度验证
        if (province.length() > 20) {
            throw new IllegalArgumentException("省份名称长度不能超过20个字符");
        }
        if (city.length() > 20) {
            throw new IllegalArgumentException("城市名称长度不能超过20个字符");
        }
        if (district.length() > 20) {
            throw new IllegalArgumentException("区县名称长度不能超过20个字符");
        }
    }

    /**
     * 标准化区域名称
     */
    private String normalizeRegionName(String regionName) {
        if (!StringUtils.hasText(regionName)) {
            return regionName;
        }

        String normalized = regionName.trim();

        // 移除常见的后缀，然后再加回标准后缀
        // 省份处理
        if (normalized.endsWith("省") || normalized.endsWith("市") || 
            normalized.endsWith("自治区") || normalized.endsWith("特别行政区")) {
            // 保持原样
        } else {
            // 尝试添加常见后缀
            if (isProvinceName(normalized)) {
                if (!normalized.endsWith("省") && !normalized.endsWith("市") && 
                    !normalized.endsWith("自治区")) {
                    // 根据常见省份名称添加后缀
                    if (normalized.contains("北京") || normalized.contains("天津") || 
                        normalized.contains("上海") || normalized.contains("重庆")) {
                        normalized += "市";
                    } else if (normalized.contains("内蒙古") || normalized.contains("广西") || 
                              normalized.contains("西藏") || normalized.contains("宁夏") || 
                              normalized.contains("新疆")) {
                        normalized += "自治区";
                    } else {
                        normalized += "省";
                    }
                }
            }
        }

        return normalized;
    }

    /**
     * 判断是否是省份名称
     */
    private boolean isProvinceName(String name) {
        // 这里可以根据实际需求完善省份名称的判断逻辑
        String[] provinces = {
                "北京", "天津", "河北", "山西", "内蒙古", "辽宁", "吉林", "黑龙江",
                "上海", "江苏", "浙江", "安徽", "福建", "江西", "山东", "河南",
                "湖北", "湖南", "广东", "广西", "海南", "重庆", "四川", "贵州",
                "云南", "西藏", "陕西", "甘肃", "青海", "宁夏", "新疆"
        };

        for (String province : provinces) {
            if (name.contains(province)) {
                return true;
            }
        }
        return false;
    }
}
