package com.demon.giraffe.common.constants;

/**


 * @date 2023/7/21 14:38
 * @description 字符串常量
 */
public class StringConstants {

    private StringConstants() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 默认设备参数值
     */
    public static final String DEFAULT_DEVICE_PARAM_VALUE = "---";

    /**
     * 逗号符号
     */
    public static final String COMMA_CHARACTER = ",";


    /**
     * 英文逗号符号
     */
    public static final String CHINESE_COMMA_CHARACTER = "，";

    /**
     * 顿号符号
     */
    public static final String CAESURA_SIGN_CHARACTER = "、";

    /**
     * 小于符号
     */
    public static final String LESS_THAN_SYMBOL = "<";

    /**
     * 减号
     */
    public static final String MINUS_SIGN_SYMBOL = "-";

    /**
     * 冒号
     */
    public static final String COLON_SYMBOL = ":";

    /**
     * 冒号
     */
    public static final String RES_HEADER_FILE_NAME = "File-Name";

    /**
     * 空格符
     */
    public static final String SPACE_CHARACTER = " ";

    /**
     * 下划线
     */
    public static final String UNDER_LINE = "_";
    /**
     * 天
     */
    public static final String DAY = "d";
    /**
     * 时
     */
    public static final String HOUR = "h";
    /**
     * 分
     */
    public static final String MINUTE = "m";
    /**
     * 秒
     */
    public static final String SECOND = "s";
    /**
     * 英文短横线
     */
    public static final String ENGLISH_SHOT_LINE = "-";
}
