package com.demon.giraffe.common.constants;

/**
 * 用户模块常量
 */
public class UserConstants {

    /**
     * 前端携带的 token key
     */
    public static final String TOKEN = "token";

    /**
     * Redis 用户缓存前缀
     */
    public static final String REDIS_USER = "user:";

    /**
     * 登录校验失败提示
     */
    public static final String PLEASE_LOGIN = "PLEASE_LOGIN";

    public static final String USER_EXIST = "USER_EXIST";
    public static final String EMAIL_EXIST = "EMAIL_EXIST";
    public static final String EMAIL_NOT_EXIST = "EMAIL_NOT_EXIST";
    public static final String LINK_INVALID = "CHANGE_PASSWORD_LINK_INVALID";

    // ========== Sa-Token 会话字段 ==========
    public static final String USER_SESSION_LOGIN_ID = "loginId";
    public static final String USER_SESSION_OPENID = "openid";
    public static final String USER_SESSION_INFO = "userInfo";
    public static final String USER_SESSION_EXPIRATION = "expirationTime";
}
