package com.demon.giraffe.common.constants;

import java.time.format.DateTimeFormatter;

/**
 * @description 时间格式化常量信息
 */
public class DateFormatConstants {

    private DateFormatConstants() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 日期格式化字符串，格式化到月
     */
    public static final String NORMAL_MONTH_PATTERN = "yyyy-MM";

    /**
     * 日期格式化字符串，格式化到日
     */
    public static final String NORMAL_DATE_PATTERN = "yyyy-MM-dd";

    /**
     * 日期格式化字符串，格式化到日
     */
    public static final String NORMAL_DAY_PATTERN = "MM-dd";

    /**
     * 时间格式化字符串
     */
    public static final String NORMAL_TIME_PATTERN = "HH:mm:ss";

    /**
     * 时间格式化字符串
     */
    public static final String NORMAL_TIME_EXCLUDE_SECOND_PATTERN = "HH:mm";

    /**
     * 日期 + 时间格式化字符串
     */
    public static final String NORMAL_DATETIME_EXCLUDE_SECOND_PATTERN = "yyyy-MM-dd HH:mm";

    /**
     * 日期 + 时间格式化字符串
     */
    public static final String NORMAL_DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * 简化时间格式字符串 月份+日期 小时：分钟
     */
    public static final String SIMPLE_DATETIME_PATTERN = "MM-dd HH:mm";

    /**
     * 日期格式化，格式化到月
     */
    public static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormatter.ofPattern(NORMAL_MONTH_PATTERN);

    /**
     * 日期格式化，格式化到日(不要年份)
     */
    public static final DateTimeFormatter DAY_FORMATTER = DateTimeFormatter.ofPattern(NORMAL_DAY_PATTERN);

    /**
     * 日期格式化：格式化到日期yyyy-MM-dd
     */
    public static final DateTimeFormatter NORMAL_DATE_FORMATTER = DateTimeFormatter.ofPattern(NORMAL_DATE_PATTERN);

    /**
     * 日期格式化，格式化时间HH:mm:ss
     */
    public static final DateTimeFormatter NORMAL_TIME_FORMATTER = DateTimeFormatter.ofPattern(NORMAL_TIME_PATTERN);

    /**
     * 日期格式化，格式化时间HH:mm
     */
    public static final DateTimeFormatter NORMAL_TIME_EXCLUDE_SECOND_FORMATTER = DateTimeFormatter.ofPattern(NORMAL_TIME_EXCLUDE_SECOND_PATTERN);

    /**
     * 日期格式化，格式化时间yyyy-MM-dd HH:mm:ss
     */
    public static final DateTimeFormatter NORMAL_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(NORMAL_DATETIME_PATTERN);
}
