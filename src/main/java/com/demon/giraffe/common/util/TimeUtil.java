package com.demon.giraffe.common.util;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 时间工具类
 * <AUTHOR>
 * @date 29/6/2025
 */
public class TimeUtil {
    // 默认时区（东八区）
    public static final ZoneOffset DEFAULT_ZONE = ZoneOffset.of("+8");
    // 常用时间格式
    private static final DateTimeFormatter DEFAULT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 获取当前时间的秒级时间戳（10位）
     */
    public static long currentTimestamp() {
        return LocalDateTime.now().toEpochSecond(DEFAULT_ZONE);
    }

    /**
     * 获取当前时间的毫秒级时间戳（13位）
     */
    public static long currentTimestampMillis() {
        return LocalDateTime.now().atZone(DEFAULT_ZONE).toInstant().toEpochMilli();
    }

    /**
     * 将LocalDateTime转换为秒级时间戳
     */
    public static long toTimestamp(LocalDateTime dateTime) {
        return dateTime.toEpochSecond(DEFAULT_ZONE);
    }

    /**
     * 将LocalDateTime转换为毫秒级时间戳
     */
    public static long toTimestampMillis(LocalDateTime dateTime) {
        return dateTime.atZone(DEFAULT_ZONE).toInstant().toEpochMilli();
    }

//    /**
//     * 将秒级时间戳转换为LocalDateTime
//     */
//    public static LocalDateTime fromTimestamp(long timestamp) {
//        return LocalDateTime.ofEpochSecond(timestamp, 0, DEFAULT_ZONE);
//    }

    /**
     * 将毫秒级时间戳转换为LocalDateTime
     */
    public static LocalDateTime fromTimestampMillis(long timestampMillis) {
        return Instant.ofEpochMilli(timestampMillis).atZone(DEFAULT_ZONE).toLocalDateTime();
    }

    /**
     * 将Date转换为LocalDateTime
     */
    public static LocalDateTime fromDate(Date date) {
        return date.toInstant().atZone(DEFAULT_ZONE).toLocalDateTime();
    }

    /**
     * 将LocalDateTime转换为Date
     */
    public static Date toDate(LocalDateTime dateTime) {
        return Date.from(dateTime.atZone(DEFAULT_ZONE).toInstant());
    }

    /**
     * 格式化LocalDateTime为字符串
     */
    public static String format(LocalDateTime dateTime) {
        return dateTime.format(DEFAULT_FORMATTER);
    }

    /**
     * 格式化LocalDateTime为字符串（自定义格式）
     */
    public static String format(LocalDateTime dateTime, String pattern) {
        return dateTime.format(DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 解析字符串为LocalDateTime
     */
    public static LocalDateTime parse(String dateTimeStr) {
        return LocalDateTime.parse(dateTimeStr, DEFAULT_FORMATTER);
    }

    /**
     * 解析字符串为LocalDateTime（自定义格式）
     */
    public static LocalDateTime parse(String dateTimeStr, String pattern) {
        return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 计算两个时间点之间的秒数差
     */
    public static long betweenSeconds(LocalDateTime start, LocalDateTime end) {
        return Duration.between(start, end).getSeconds();
    }

    /**
     * 计算两个时间点之间的天数差
     */
    public static long betweenDays(LocalDateTime start, LocalDateTime end) {
        return Duration.between(start, end).toDays();
    }

    /**
     * 增加秒数
     */
    public static LocalDateTime plusSeconds(LocalDateTime dateTime, long seconds) {
        return dateTime.plusSeconds(seconds);
    }

    /**
     * 增加天数
     */
    public static LocalDateTime plusDays(LocalDateTime dateTime, long days) {
        return dateTime.plusDays(days);
    }


    /**
     * 判断给定的 LocalDateTime 是否已过期（早于当前时间）
     */
    public static boolean isExpired(LocalDateTime dateTime) {
        return dateTime.isBefore(LocalDateTime.now());
    }

    /**
     * 判断给定的毫秒时间戳是否已过期（早于当前时间）
     */
    public static boolean isExpiredMillis(long timestampMillis) {
        return timestampMillis < currentTimestampMillis();
    }


}