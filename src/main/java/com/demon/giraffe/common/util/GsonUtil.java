package com.demon.giraffe.common.util;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;

/**
 * JSON工具类(基于Gson实现)
 */
public class GsonUtil {

    private static final Gson GSON = new GsonBuilder()
            .setDateFormat("yyyy-MM-dd HH:mm:ss")
            .disableHtmlEscaping()
            .create();

    /**
     * 对象转JSON字符串
     * @param object 任意对象
     * @return JSON字符串
     */
    public static String toJson(Object object) {
        return GSON.toJson(object);
    }

    /**
     * JSON字符串转对象
     * @param json JSON字符串
     * @param clazz 目标类
     * @param <T> 泛型类型
     * @return 目标对象
     * @throws JsonSyntaxException 如果JSON格式不合法
     */
    public static <T> T fromJson(String json, Class<T> clazz) throws JsonSyntaxException {
        return GSON.fromJson(json, clazz);
    }

    /**
     * JSON字符串转对象(支持复杂类型)
     * @param json JSON字符串
     * @param type 目标类型(TypeToken获取)
     * @param <T> 泛型类型
     * @return 目标对象
     * @throws JsonSyntaxException 如果JSON格式不合法
     */
    public static <T> T fromJson(String json, Type type) throws JsonSyntaxException {
        return GSON.fromJson(json, type);
    }

    /**
     * JSON字符串转List
     * @param json JSON字符串
     * @param clazz List元素类
     * @param <T> 泛型类型
     * @return List对象
     * @throws JsonSyntaxException 如果JSON格式不合法
     */
    public static <T> List<T> toList(String json, Class<T> clazz) throws JsonSyntaxException {
        return GSON.fromJson(json, TypeToken.getParameterized(List.class, clazz).getType());
    }

    /**
     * JSON字符串转Map
     * @param json JSON字符串
     * @param keyClass Key类
     * @param valueClass Value类
     * @param <K> Key泛型类型
     * @param <V> Value泛型类型
     * @return Map对象
     * @throws JsonSyntaxException 如果JSON格式不合法
     */
    public static <K, V> Map<K, V> toMap(String json, Class<K> keyClass, Class<V> valueClass) throws JsonSyntaxException {
        return GSON.fromJson(json, TypeToken.getParameterized(Map.class, keyClass, valueClass).getType());
    }

    /**
     * 对象深度拷贝
     * @param object 原始对象
     * @param clazz 目标类
     * @param <T> 泛型类型
     * @return 拷贝后的新对象
     */
    public static <T> T deepCopy(T object, Class<T> clazz) {
        return fromJson(toJson(object), clazz);
    }

    /**
     * 对象深度拷贝(支持复杂类型)
     * @param object 原始对象
     * @param type 目标类型(TypeToken获取)
     * @param <T> 泛型类型
     * @return 拷贝后的新对象
     */
    public static <T> T deepCopy(T object, Type type) {
        return fromJson(toJson(object), type);
    }
}