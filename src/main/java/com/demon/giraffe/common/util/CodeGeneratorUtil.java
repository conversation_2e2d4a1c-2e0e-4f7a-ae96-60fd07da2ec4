package com.demon.giraffe.common.util;

import com.demon.giraffe.framework.redis.service.RedisService;
import com.demon.giraffe.modules.marketing.model.enums.CouponTypeEnum;
import com.demon.giraffe.modules.marketing.model.enums.DiscountTypeEnum;
import com.demon.giraffe.modules.order.model.enums.DeliveryTypeEnum;
import com.demon.giraffe.modules.qr.model.enums.QRBusinessType;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 编码生成工具类（V2.0）
 * 所有顺序编号统一为4位数字（0001-9999）
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CodeGeneratorUtil {

    private final RedisService redisService;

    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd");

    // ================= Redis Key 常量 =================
    private static final String REDIS_PREFIX = "code:v2";
    private static final String PARTNER_PREFIX = REDIS_PREFIX + ":partner:";
    private static final String CABINET_PREFIX = REDIS_PREFIX + ":cabinet:";
    private static final String SLOT_PREFIX = REDIS_PREFIX + ":slot:";
    private static final String COURIER_PREFIX = REDIS_PREFIX + ":courier:";
    private static final String STORE_PREFIX = REDIS_PREFIX + ":factory:";
    private static final String LAUNDRY_PREFIX = REDIS_PREFIX + ":laundry:";
    private static final String ORDER_PREFIX = REDIS_PREFIX + ":order:";
    private static final String DIRECTOR_PREFIX = REDIS_PREFIX + ":director:";

    // ================= 编码生成方法 =================

    /**
     * 生成合伙人编码（P-区县code-0001）
     */
    public String generatePartnerCode(CountyEnum countyEnum) {
        String key = PARTNER_PREFIX + countyEnum.getValue();
        long seq = redisService.getGenerateId(key);
        return String.format("P-%d-%04d", countyEnum.getValue(), seq);
    }

    /**
     * 生成智能柜编码（CAB-区县code-0001）
     */
    public String generateCabinetCode(CountyEnum countyEnum) {
        String key = CABINET_PREFIX + countyEnum.getValue();
        long seq = redisService.getGenerateId(key);
        return String.format("CAB-%d-%04d", countyEnum.getValue(), seq);
    }

    /**
     * 生成柜格编码（S-区县code-柜编号-类型编号）
     * 示例：S-110101-0001-A01
     */
    public String generateSlotCode(CountyEnum countyEnum, int cabinetSeq, String slotType, int slotIndex) {
        return String.format("S-%d-%04d-%s%02d",
                countyEnum.getValue(), cabinetSeq, slotType, slotIndex);
    }

    /**
     * 生成取送员编码（C-区县code-0001）
     */
    public String generateCourierCode(CountyEnum countyEnum) {
        String key = COURIER_PREFIX + countyEnum.getValue();
        long seq = redisService.getGenerateId(key);
        return String.format("C-%d-%04d", countyEnum.getValue(), seq);
    }

    /**
     * 生成实体店编码（F-区县code-0001）
     */
    public String generateFactoryCode(CountyEnum countyEnum) {
        String key = STORE_PREFIX + countyEnum.getValue();
        long seq = redisService.getGenerateId(key);
        return String.format("F-%d-%04d", countyEnum.getValue(), seq);
    }

    /**
     * 生成洗护工编码（L-工厂编号-工种-0001）
     * 示例：L-1001-QW-0001
     */
    public String generateLaundryWorkerCode(Long factoryId, String workType) {
        String key = LAUNDRY_PREFIX + factoryId + ":" + workType;
        long seq = redisService.getGenerateId(key);
        return String.format("L-%04d-%s-%04d", factoryId, workType, seq);
    }



    /**
     * 生成业务信息丰富的复杂订单编码
     *
     * 编码格式说明：
     * | 段位       | 示例值                      | 含义说明 |
     * | -------- | ------------------------ | ---- |
     * | O1       | 订单类型（固定开头+类型编号）          |      |
     * | 20250725 | 日期                       |      |
     * | C / N    | 是否使用优惠券（Coupon / None）   |      |
     * | P / S    | 是否精洗（Premium / Standard） |      |
     * | U / N    | 是否加急（Urgent / Normal）    |      |
     * | D / C    | 配送方式（Door / Cabinet）     |      |
     * | 003EFD   | Redis分布式序列号（6位16进制）      |      |
     *
     * 示例：O120250725CPUD003EFD
     * 解读：订单类型O1，2025年7月25日，使用优惠券，精洗，加急，上门配送，序列号003EFD
     *
     * @param couponId 优惠券ID（非空即代表使用了）
     * @param isPremium 是否精洗
     * @param isUrgent 是否加急
     * @param deliveryType 配送方式
     * @return 业务信息丰富的订单编码
     */
    public String generateOrderCode(
            Long couponId,          // 优惠券ID（非空即代表使用了）
            Boolean isPremium,      // 是否精洗
            Boolean isUrgent,       // 是否加急
            DeliveryTypeEnum deliveryType // 配送方式
    ) {
        // 1. 订单类型前缀（O1表示普通订单）
        String orderTypePrefix = "O1";

        // 2. 日期部分
        String date = DATE_FORMAT.format(LocalDate.now());

        // 3. 业务特征编码
        String couponFlag = couponId != null ? "C" : "N";         // Coupon / None
        String premiumFlag = Boolean.TRUE.equals(isPremium) ? "P" : "S"; // Premium / Standard
        String urgentFlag = Boolean.TRUE.equals(isUrgent) ? "U" : "N";   // Urgent / Normal
        String deliveryFlag = deliveryType == DeliveryTypeEnum.DOOR_TO_DOOR ? "D" : "C"; // Door / Cabinet

        // 4. 生成唯一序列号（6位16进制）
        String key = ORDER_PREFIX + date;
        long seq = redisService.getGenerateId(key);
        String uniqueCode = String.format("%06X", seq); // 16进制表示，更紧凑

        // 5. 组装完整订单号
        String orderCode = orderTypePrefix + date + couponFlag + premiumFlag + urgentFlag + deliveryFlag + uniqueCode;

        log.info("生成复杂订单编码：{}，业务特征：优惠券[{}]，精洗[{}]，加急[{}]，配送[{}]",
                orderCode, couponFlag, premiumFlag, urgentFlag, deliveryFlag);

        return orderCode;
    }

    /**
     * 解析复杂订单编码中的业务信息
     *
     * @param orderCode 订单编码
     * @return 业务信息解析结果
     */
    public OrderCodeInfo parseOrderCode(String orderCode) {
        if (orderCode == null || orderCode.length() < 20) {
            throw new IllegalArgumentException("订单编码格式不正确：" + orderCode);
        }

        try {
            OrderCodeInfo info = new OrderCodeInfo();

            // 解析各个段位
            info.setOrderType(orderCode.substring(0, 2));           // O1
            info.setDate(orderCode.substring(2, 10));               // 20250725
            info.setCouponFlag(orderCode.substring(10, 11));        // C/N
            info.setPremiumFlag(orderCode.substring(11, 12));       // P/S
            info.setUrgentFlag(orderCode.substring(12, 13));        // U/N
            info.setDeliveryFlag(orderCode.substring(13, 14));      // D/C
            info.setUniqueCode(orderCode.substring(14));            // 003EFD

            // 转换为业务含义
            info.setHasCoupon("C".equals(info.getCouponFlag()));
            info.setIsPremium("P".equals(info.getPremiumFlag()));
            info.setIsUrgent("U".equals(info.getUrgentFlag()));
            info.setIsDoorToDoor("D".equals(info.getDeliveryFlag()));

            return info;
        } catch (Exception e) {
            throw new IllegalArgumentException("解析订单编码失败：" + orderCode, e);
        }
    }

    /**
     * 生成退款编码（RyyyyMMdd-0001）
     */
    public String generateRefundCode() {
        String today = DATE_FORMAT.format(LocalDate.now());
        String key = REDIS_PREFIX + ":refund:" + today;
        long seq = redisService.getGenerateId(key);
        return String.format("R%s-%04d", today, seq);
    }

    /**
     * 生成支付编码（PAYyyyyMMdd-0001）
     */
    public String generatePaymentCode() {
        String today = DATE_FORMAT.format(LocalDate.now());
        String key = REDIS_PREFIX + ":payment:" + today;
        long seq = redisService.getGenerateId(key);
        return String.format("PAY%s-%04d", today, seq);
    }


    /**
     * 生成不区分区县的二维码业务ID（纯数字long类型）
     * 结构：业务类型(2位) + 日期(8位) + 序列号(4位) = 14位数字
     * 示例：LOGIN(03) + 20230629 + 0001 = 03202306290001
     */
    public long generateQrBusinessId(QRBusinessType businessType) {
        String date = DATE_FORMAT.format(LocalDate.now());
        String redisKey = String.format("qr:id:%s:%s",
                businessType.getService(),
                date);

        long sequence = redisService.getGenerateId(redisKey);
        return Long.parseLong(
                String.format("%02d%s%04d",
                        businessType.ordinal() + 1, // 业务类型转2位数字
                        date,                      // 日期8位
                        sequence)                  // 序列号4位
        );
    }

    /**
     * 生成厂长编码（D-工厂ID-0001）
     */
    public String generateFactoryDirectorCode(Long factoryId) {
        String key = DIRECTOR_PREFIX + factoryId;
        long seq = redisService.getGenerateId(key);
        return String.format("D-%04d-%04d", factoryId, seq);
    }

    // ================= User模块专用编码生成 =================

    private static final String WORKER_PREFIX = REDIS_PREFIX + ":worker:";
    private static final String DELIVERY_PREFIX = REDIS_PREFIX + ":delivery:";

    /**
     * 生成工厂员工编码（W-工厂ID-0001）
     */
    public String generateFactoryWorkerCode(Long factoryId) {
        String key = WORKER_PREFIX + factoryId;
        long seq = redisService.getGenerateId(key);
        return String.format("W-%04d-%04d", factoryId, seq);
    }

    /**
     * 生成配送员编码（DW-区县code-0001）
     */
    public String generateDeliveryWorkerCode(CountyEnum countyEnum) {
        String key = DELIVERY_PREFIX + countyEnum.getValue();
        long seq = redisService.getGenerateId(key);
        return String.format("DW-%d-%04d", countyEnum.getValue(), seq);
    }


    private static final DateTimeFormatter MONTH_FORMAT = DateTimeFormatter.ofPattern("yyyyMM");
    private static final String REDIS_KEY_PREFIX = "user:code:seq:";

    /**
     * 生成用户唯一编码（U + yyyyMM + 6位Redis自增序列）
     * 示例：U202406000001
     * Redis存储结构：
     * key: "user:code:seq:202406"
     * value: 自增序号
     */
    public String generateUserCode() {
        String month = LocalDateTime.now().format(MONTH_FORMAT);
        String redisKey = REDIS_KEY_PREFIX + month;

        // 获取Redis自增序列（原子操作）
        long sequence = redisService.getGenerateId(redisKey);
        return String.format("U%s%06d", month, sequence);
    }


    /**
     * 会员编码生成规则（M + 用户ID + 4位随机校验码）
     * 示例：M12345678901234
     * 特点：
     * 1. 固定前缀"M"标识会员
     * 2. 使用用户ID保证唯一性
     * 3. 4位随机数防止枚举攻击
     */
    public String generateMemberCode(Long userId) {
        // 参数校验
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("用户ID必须为正整数");
        }

        // 生成4位随机校验码（1000-9999）
        int randomCode = ThreadLocalRandom.current().nextInt(1000, 10000);

        return String.format("M%d%04d", userId, randomCode);
    }

    /**
     * 生成优惠券编码（格式：C + 年月日 + 类型代码 + 优惠方式代码 + 6位随机数）
     * 示例：C202407011216123456
     * 结构说明：
     * - C: 优惠券标识前缀
     * - 20240701: 年月日(8位)
     * - 1: 类型代码(1位，满减券1/折扣券2)
     * - 2: 优惠方式代码(1位，金额1/比例2)
     * - 123456: 6位随机数
     * 总长度：1 + 8 + 1 + 1 + 6 = 17位
     */
    public String generateCouponCode(CouponTypeEnum type, DiscountTypeEnum discountType) {
        // 参数校验
        if (type == null || discountType == null) {
            throw new IllegalArgumentException("优惠券类型和优惠方式不能为空");
        }

        // 获取当前日期(年月日)
        String dateStr = DATE_FORMAT.format(LocalDate.now());

        // 生成6位随机数(100000-999999)
        int randomNum = ThreadLocalRandom.current().nextInt(100000, 1000000);

        // 组合各部分
        return String.format("C%s%d%d%06d",
                dateStr,          // 年月日(8位)
                type.getCode(),   // 类型代码(1位)
                discountType.getCode(), // 优惠方式代码(1位)
                randomNum         // 6位随机数
        );
    }

    /**
     * 生成订单任务编号（规则：OT+年月日+6位序列）
     * 示例：OT20230716000001
     */
    public String generateOrderTaskCode() {
        String today = DATE_FORMAT.format(LocalDate.now());
        String key = REDIS_PREFIX + ":task:" + today;
        long seq = redisService.getGenerateId(key);
        return String.format("OT%s%06d", today, seq);
    }

    /**
     * 生成活动编码（规则：ACT+年月日+3位序列）
     * 示例：ACT20240725001
     */
    public String generateActivityCode() {
        String today = DATE_FORMAT.format(LocalDate.now());
        String key = REDIS_PREFIX + ":activity:" + today;
        long seq = redisService.getGenerateId(key);
        return String.format("ACT%s%03d", today, seq);
    }

    /**
     * 订单编码信息解析结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderCodeInfo {
        private String orderType;      // 订单类型
        private String date;           // 日期
        private String couponFlag;     // 优惠券标识
        private String premiumFlag;    // 精洗标识
        private String urgentFlag;     // 加急标识
        private String deliveryFlag;   // 配送标识
        private String uniqueCode;     // 唯一码

        // 业务含义
        private Boolean hasCoupon;     // 是否使用优惠券
        private Boolean isPremium;     // 是否精洗
        private Boolean isUrgent;      // 是否加急
        private Boolean isDoorToDoor;  // 是否上门配送

        /**
         * 获取业务特征描述
         */
        public String getBusinessDescription() {
            StringBuilder desc = new StringBuilder();
            desc.append(hasCoupon ? "使用优惠券" : "无优惠券");
            desc.append(", ").append(isPremium ? "精洗" : "标准洗");
            desc.append(", ").append(isUrgent ? "加急" : "普通");
            desc.append(", ").append(isDoorToDoor ? "上门配送" : "柜子配送");
            return desc.toString();
        }
    }
}