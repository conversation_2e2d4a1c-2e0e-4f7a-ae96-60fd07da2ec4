package com.demon.giraffe.common.util;

import com.demon.giraffe.common.domain.enums.CountyEnum;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class CountySupportUtil {

    /**
     * 成都服务范围：CountyEnum 中所有属于 CityEnum.C5101（成都市）的县级行政区划
     */
    private static final List<CountyEnum> SUPPORTED_COUNTIES = Arrays.asList(
            CountyEnum.C510104, // 锦江区
            CountyEnum.C510105, // 青羊区
            CountyEnum.C510106, // 金牛区
            CountyEnum.C510107, // 武侯区
            CountyEnum.C510108, // 成华区
            CountyEnum.C510112, // 龙泉驿区
            CountyEnum.C510113, // 青白江区
            CountyEnum.C510114, // 新都区
            CountyEnum.C510115, // 温江区
            CountyEnum.C510116, // 双流区
            CountyEnum.C510117, // 郫都区
            CountyEnum.C510118, // 新津区
            CountyEnum.C510121, // 金堂县
            CountyEnum.C510129, // 大邑县
            CountyEnum.C510131, // 蒲江县
            CountyEnum.C510181, // 都江堰市
            CountyEnum.C510182, // 彭州市
            CountyEnum.C510183, // 邛崃市
            CountyEnum.C510184, // 崇州市
            CountyEnum.C510185  // 简阳市
    );

    /**
     * 检查一个 CountyEnum 是否在服务区域内
     */
    public static boolean isSupported(CountyEnum county) {
        return SUPPORTED_COUNTIES.contains(county);
    }

    /**
     * 批量检查：只要存在一个不在支持列表中就返回 false
     */
    public static boolean allSupported(List<CountyEnum> counties) {
        if (counties == null || counties.isEmpty()) {
            return false;
        }
        return counties.stream().allMatch(SUPPORTED_COUNTIES::contains);
    }

    /**
     * 获取所有支持区域的行政区编码
     */
    public static List<Integer> getSupportedCountyCodes() {
        return SUPPORTED_COUNTIES.stream()
                .map(CountyEnum::getCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }



    /**
     * 返回完整支持列表
     */
    public static List<CountyEnum> getSupportedCounties() {
        return SUPPORTED_COUNTIES;
    }
}
