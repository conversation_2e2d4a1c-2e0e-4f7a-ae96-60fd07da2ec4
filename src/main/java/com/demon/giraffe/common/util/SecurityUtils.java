package com.demon.giraffe.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

/**
 * 加解密工具类
 */
@Slf4j
@Component
public class SecurityUtils {

    @Value("${security.aes.key}")
    private String aesKey;

    @Value("${security.aes.iv}")
    private String aesIv;

    private static final String AES_ALGORITHM = "AES/CBC/PKCS5Padding";

    /**
     * AES加密数据
     * @param data 待加密数据
     * @return 加密后的Base64字符串
     */
    public String encryptData(String data) {
        try {
            Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
            SecretKeySpec keySpec = new SecretKeySpec(aesKey.getBytes(StandardCharsets.UTF_8), "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(aesIv.getBytes(StandardCharsets.UTF_8));
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
            byte[] encrypted = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.encodeBase64String(encrypted);
        } catch (Exception e) {
            log.error("数据加密失败");
            throw new RuntimeException("数据加密失败", e);
        }
    }

    /**
     * AES解密数据
     * @param encryptedData 加密的Base64字符串
     * @return 解密后的原始数据
     */
    public String decryptData(String encryptedData) {
        try {
            Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
            SecretKeySpec keySpec = new SecretKeySpec(aesKey.getBytes(StandardCharsets.UTF_8), "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(aesIv.getBytes(StandardCharsets.UTF_8));
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            byte[] decoded = Base64.decodeBase64(encryptedData);
            byte[] decrypted = cipher.doFinal(decoded);
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("数据解密失败");
            throw new RuntimeException("数据解密失败", e);
        }
    }
}