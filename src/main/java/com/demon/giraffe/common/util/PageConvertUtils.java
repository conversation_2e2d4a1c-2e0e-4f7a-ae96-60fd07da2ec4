package com.demon.giraffe.common.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分页数据转换工具类
 */
public class PageConvertUtils {

    /**
     * 分页对象类型转换
     * @param sourcePage 源分页对象
     * @param mapper 数据转换函数
     * @return 转换后的分页对象
     */
    public static <S, T> Page<T> convert(Page<S> sourcePage, Function<S, T> mapper) {
        Page<T> targetPage = new Page<>();
        targetPage.setCurrent(sourcePage.getCurrent());
        targetPage.setSize(sourcePage.getSize());
        targetPage.setTotal(sourcePage.getTotal());
        targetPage.setRecords(sourcePage.getRecords()
                .stream()
                .map(mapper)
                .collect(Collectors.toList()));
        return targetPage;
    }

    /**
     * 分页对象简单包装
     * @param sourcePage 源分页对象
     * @param records 目标记录列表
     * @return 包装后的分页对象
     */
    public static <T> Page<T> wrap(Page<?> sourcePage, List<T> records) {
        Page<T> targetPage = new Page<>();
        targetPage.setCurrent(sourcePage.getCurrent());
        targetPage.setSize(sourcePage.getSize());
        targetPage.setTotal(sourcePage.getTotal());
        targetPage.setRecords(records);
        return targetPage;
    }
}