package com.demon.giraffe.common.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 金额计算工具类（与数据库decimal(10,2)精度保持一致）
 */
public class MoneyCalculator {

    // 默认精度（小数点后2位）
    private static final int DEFAULT_SCALE = 2;
    // 默认舍入模式（四舍五入）
    private static final RoundingMode DEFAULT_ROUNDING_MODE = RoundingMode.HALF_UP;

    /**
     * 加法（a + b）
     */
    public static BigDecimal add(BigDecimal a, BigDecimal b) {
        if (a == null) a = BigDecimal.ZERO;
        if (b == null) b = BigDecimal.ZERO;
        return a.add(b).setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 减法（a - b）
     */
    public static BigDecimal subtract(BigDecimal a, BigDecimal b) {
        if (a == null) a = BigDecimal.ZERO;
        if (b == null) b = BigDecimal.ZERO;
        return a.subtract(b).setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 乘法（a × b）
     */
    public static BigDecimal multiply(BigDecimal a, BigDecimal b) {
        if (a == null) a = BigDecimal.ZERO;
        if (b == null) b = BigDecimal.ZERO;
        return a.multiply(b).setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 除法（a ÷ b）
     */
    public static BigDecimal divide(BigDecimal a, BigDecimal b) {
        if (a == null) a = BigDecimal.ZERO;
        if (b == null || b.compareTo(BigDecimal.ZERO) == 0) {
            throw new ArithmeticException("Division by zero");
        }
        return a.divide(b, DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 比较两个金额（a > b 返回1，a == b 返回0，a < b 返回-1）
     */
    public static int compare(BigDecimal a, BigDecimal b) {
        if (a == null) a = BigDecimal.ZERO;
        if (b == null) b = BigDecimal.ZERO;
        return a.compareTo(b);
    }

    /**
     * 判断金额是否为零
     */
    public static boolean isZero(BigDecimal amount) {
        return amount == null || amount.compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * 安全转换字符串为BigDecimal（保留2位小数）
     */
    public static BigDecimal safeParse(String amount) {
        try {
            return new BigDecimal(amount).setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
        } catch (Exception e) {
            return BigDecimal.ZERO.setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
        }
    }

    /**
     * 安全转换数字为BigDecimal（保留2位小数）
     */
    public static BigDecimal valueOf(Number number) {
        if (number == null) {
            return BigDecimal.ZERO.setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
        }
        return new BigDecimal(number.toString()).setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }
}