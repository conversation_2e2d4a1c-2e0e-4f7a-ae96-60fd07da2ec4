package com.demon.giraffe.common.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.WxMaConfig;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 微信小程序服务配置
 * 注意：微信支付相关配置已移至 WechatPayConfiguration
 */
@Configuration
@RequiredArgsConstructor
public class WxServiceConfiguration {
    private final WxConfig wxConfig;

    @Bean
    public WxMaConfig wxMaConfig() {
        WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
        config.setAppid(wxConfig.getAppid());
        config.setSecret(wxConfig.getSecret());
        // 可选配置
        if (StringUtils.isNotBlank(wxConfig.getToken())) {
            config.setToken(wxConfig.getToken());
        }
//        if (StringUtils.isNotBlank(wxConfig.getAesKey())) {
//            config.setAesKey(wxConfig.getAesKey());
//        }

        return config;
    }

    @Bean
    public WxMaService wxMaService(WxMaConfig wxMaConfig) {
        WxMaService service = new WxMaServiceImpl();
        service.setWxMaConfig(wxMaConfig);
        return service;
    }
}