package com.demon.giraffe.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 微信小程序 & 微信支付 配置属性类
 * 从 application.yml 或 application.properties 中以 wechat.* 形式读取配置项
 */
@Data
@Component
@ConfigurationProperties(prefix = "wechat")
public class WxConfig {

    /**
     * 小程序 AppId
     */
    private String appid;

    /**
     * 小程序密钥
     */
    private String secret;

    /**
     * 微信服务器配置中的 token
     */
    private String token;

    /**
     * 微信支付商户号（MchID）
     */
    private String merchantId;

    /**
     * 商户证书序列号（40位十六进制字符串）
     */
    private String merchantSerialNumber;

    /**
     * 商户私钥文件路径（.pem 文件）
     */
    private String privateKeyPath;

    /**
     * API v3 密钥（32位字符串）
     */
    private String apiV3Key;

    /**
     * API v2 密钥（32位字符串）
     */
    private String apiV2Key;

    /**
     * 商户证书（.p12 文件）路径，适用于 V2 接口
     */
    private String keyPath;

    /**
     * 微信平台公钥文件路径（.pem）—适用于解密敏感信息（可选）
     */
    private String publicKeyPath;

    /**
     * 微信平台证书的公钥序列号（可选）
     */
    private String publicKeyId;

    /**
     * 微信平台证书的公钥文件路径（.pem）—适用于解密敏感信息（可选）
     */
    private String privateCertPath;
}
