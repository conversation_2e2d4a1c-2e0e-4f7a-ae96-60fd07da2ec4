package com.demon.giraffe.common.annotation;

import java.lang.annotation.*;

/**
 * 区域处理注解
 * 标记需要自动处理区域信息的方法
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ProcessRegion {
    
    /**
     * 是否验证区域支持
     */
    boolean validateSupported() default true;
    
    /**
     * 处理失败时的行为
     */
    FailureAction onFailure() default FailureAction.THROW_EXCEPTION;
    
    enum FailureAction {
        THROW_EXCEPTION,  // 抛出异常
        LOG_WARNING,      // 记录警告日志
        IGNORE           // 忽略错误
    }
}
