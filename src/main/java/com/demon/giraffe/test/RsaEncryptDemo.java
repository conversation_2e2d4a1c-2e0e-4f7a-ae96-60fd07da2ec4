package com.demon.giraffe.test;

import com.demon.giraffe.framework.satoken.util.SaEncryptionUtil;

public class RsaEncryptDemo {

    private final static String privateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJJw9mf+EivYMJfHSFgu2VFVFwM1LkvEGfTTZADNpfq09UrnIC9y8vDVE7QrOFJqAIm5W79VYMl3YREZNV5Hh/rIwIW6naW3zKck2MFPb7dscr5bsHVRYrmHCspQNA0z/oca4Q+6jin0F5CMYn09l6qVs59fijePFebFTX98YUJRAgMBAAECgYBLFLGxFbm5Uwlu+BSmabawFYFL+rs2y19HgA5YvxdnV9ofwiplSXk3cve8OyCQWJrYAfoBOkP6Z0OUIamUIfacHWEM9gUtZe8hi8aobVDMeJ9Wj2/TXyYlYiSDM+hM8+MaXpqVWp6Cte3MkOW13Vr2CUM5V6HHiVg5pX4zhL29ZQJBANSlAg9liLHCg3cWji9vchtUy+RagYhjmEo6k2sfOLrBjS8Pg3Yb/qFM2NseoX1o9avZNiIQqEE+LctcxgfYN1cCQQCwTHhkdk9r1pgp4gOs2TXwWg7or4VGOxfwvX4LN6Q0+R7/H//OJFcopAKaXlARFolsbh7EayDiMMdubuo1X5KXAkBHGZkzvrZTAX/lnMQT9kaH/vVhuCVhjjOIdkd+ZOUaBMeJOb2yCzA7jK9hrL9AaIzkVe90um8XyYuUWMPaMaBnAkBdygDOaUZtNzeyKGQOI2wXlsGRtjavmyZsU1Lnws0fxVxhG3PqBdk8bKxZ1UzBGn03awoBMoDG9G5UaLs+c763AkEAta6JT/4M9di7g/LnSQVTMIb3+4z7aiv41fOag2cdFmmiNqodrvh3s3dhFb9XfSC8dl75XD8CJkYpuHdmt+413A==";
    private final static String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCScPZn/hIr2DCXx0hYLtlRVRcDNS5LxBn002QAzaX6tPVK5yAvcvLw1RO0KzhSagCJuVu/VWDJd2ERGTVeR4f6yMCFup2lt8ynJNjBT2+3bHK+W7B1UWK5hwrKUDQNM/6HGuEPuo4p9BeQjGJ9PZeqlbOfX4o3jxXmxU1/fGFCUQIDAQAB";

    public static void main(String[] args) throws Exception {
        String password = "admin";

        for (int i = 1; i <= 3; i++) {
            String s1 = SaEncryptionUtil.rsaEncryptByPublic(publicKey, password);
            System.out.println("第" + i + "次加密：");
            System.out.println(s1);

            String s2 = SaEncryptionUtil.rsaDecryptByPrivate(privateKey, s1);
            System.out.println("第" + i + "次解密：");
            System.out.println(s2);

            System.out.println("是否解密成功: " + password.equals(s2));
        }
    }


}
