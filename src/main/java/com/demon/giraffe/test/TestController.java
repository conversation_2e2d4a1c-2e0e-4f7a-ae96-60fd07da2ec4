package com.demon.giraffe.test;

import com.demon.giraffe.framework.redis.service.RedisService;
import com.demon.giraffe.common.domain.ResultBean;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/test")
@Tag(name = "系统测试接口", description = "包含Redis/MySQL等组件测试")
public class TestController {

    @Resource
    private RedisService redisService;

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Operation(summary = "Redis读写测试")
    @GetMapping("/redis")
    public ResultBean<String> testRedis(
            @RequestParam String key,
            @RequestParam(defaultValue = "default_value") String value) {

        redisService.set(key, value);
        log.info("Redis写入成功 - Key: {}, Value: {}", key, value);

        String cachedValue = redisService.getValue(key, String.class);
        log.info("Redis读取结果 - Key: {}, Value: {}", key, cachedValue);

        return ResultBean.success("操作成功，读取值: " + cachedValue);
    }

    @Operation(summary = "MySQL读写测试")
    @PostMapping("/mysql")
    public ResultBean<List<Map<String, Object>>> testMySQL(
            @RequestParam String name,
            @RequestParam int age) {

        String tableName = "test_user";
        try {
            createTestUserTable();
            String insertSql = "INSERT INTO " + tableName + " (name, age) VALUES (?, ?)";
            jdbcTemplate.update(insertSql, name, age);
            log.info("MySQL写入成功 - Name: {}, Age: {}", name, age);

            String querySql = "SELECT * FROM " + tableName + " WHERE name = ?";
            List<Map<String, Object>> users = jdbcTemplate.queryForList(querySql, name);
            log.info("MySQL查询结果: {}", users);

            return ResultBean.success(users);
        } finally {
            dropTable(tableName);
        }
    }

    @Operation(summary = "综合组件测试")
    @GetMapping("/all")
    public ResultBean<String> testAllComponents() {
        log.info("==== 开始执行综合测试 ====");

        // Redis测试
        redisService.set("test_key", "hello_redis");
        String redisValue = redisService.getValue("test_key", String.class);
        log.info("Redis测试结果: {}", redisValue);

        // MySQL测试
        String tableName = "test_temp";
        try {
            createTempTable();
            jdbcTemplate.update("INSERT INTO " + tableName + " (data) VALUES (?)", "test_data");
            String dbData = jdbcTemplate.queryForObject("SELECT data FROM " + tableName + " LIMIT 1", String.class);
            log.info("MySQL测试结果: {}", dbData);

            return ResultBean.success("Redis值: " + redisValue + ", MySQL值: " + dbData);
        } finally {
            dropTable(tableName);
        }
    }

    // ---------------------
    // 表操作私有方法
    // ---------------------
    private void createTestUserTable() {
        String createSql = "CREATE TABLE IF NOT EXISTS test_user (" +
                "id INT AUTO_INCREMENT PRIMARY KEY," +
                "name VARCHAR(50) NOT NULL," +
                "age INT DEFAULT NULL" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        jdbcTemplate.execute(createSql);
        log.info("已创建 test_user 表（如不存在）");
    }

    private void createTempTable() {
        String createSql = "CREATE TABLE IF NOT EXISTS test_temp (" +
                "id INT AUTO_INCREMENT PRIMARY KEY," +
                "data VARCHAR(255)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        jdbcTemplate.execute(createSql);
        log.info("已创建 test_temp 表（如不存在）");
    }

    private void dropTable(String tableName) {
        jdbcTemplate.execute("DROP TABLE IF EXISTS " + tableName);
        log.info("已删除测试表: {}", tableName);
    }
}
