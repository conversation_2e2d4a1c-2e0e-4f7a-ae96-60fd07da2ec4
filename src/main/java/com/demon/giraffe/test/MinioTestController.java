package com.demon.giraffe.test;

import com.demon.giraffe.framework.minio.domain.vo.MinioUploadVo;
import com.demon.giraffe.framework.minio.util.MinioUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/test/minio")
@Tag(name = "MinIO测试接口")
@RequiredArgsConstructor
public class MinioTestController {

    private final MinioUtil minioUtil;

    @PostMapping("/upload")
    @Operation(summary = "上传单文件")
    public MinioUploadVo upload(@RequestParam("bucket") String bucket,
                                @RequestParam("file") MultipartFile file) {
        String objectName = minioUtil.getObjectName(file.getOriginalFilename(), null, false);
        return minioUtil.upload(bucket, file, objectName);
    }

    @PostMapping("/uploadBatch")
    @Operation(summary = "批量上传文件")
    public List<MinioUploadVo> uploadBatch(@RequestParam("bucket") String bucket,
                                           @RequestParam("files") List<MultipartFile> files) {
        return minioUtil.batchUpload(bucket, files);
    }

    @GetMapping("/download")
    @Operation(summary = "下载文件")
    public void download(@RequestParam("bucket") String bucket,
                         @RequestParam("objectName") String objectName,
                         HttpServletResponse response) {
        minioUtil.downloadFile(bucket, objectName, response);
    }

    @GetMapping("/url")
    @Operation(summary = "获取临时URL")
    public String getTempUrl(@RequestParam("bucket") String bucket,
                             @RequestParam("objectName") String objectName) {
        return minioUtil.getObjectTempUrl(bucket, objectName);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除文件")
    public boolean delete(@RequestParam("bucket") String bucket,
                          @RequestParam("objectName") String objectName) {
        return minioUtil.removeObject(bucket, objectName);
    }
}
