package com.demon.giraffe;

import com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ConfigurableApplicationContext;

@Slf4j
@SpringBootApplication
//@EnableCaching
public class GiraffeWashApplication {
	public static void main(String[] args) {
		ConfigurableApplicationContext context = SpringApplication.run(GiraffeWashApplication.class, args);
		MybatisPlusProperties properties = context.getBean(MybatisPlusProperties.class);
		log.info("MyBatis Plus配置: {}", properties.getConfiguration().getDefaultEnumTypeHandler());
	}
}
