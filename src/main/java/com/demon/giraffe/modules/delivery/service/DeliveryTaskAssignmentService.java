package com.demon.giraffe.modules.delivery.service;

import com.demon.giraffe.modules.delivery.model.dto.request.CourierAssignmentRequest;
import com.demon.giraffe.modules.delivery.model.dto.response.CourierAssignmentResponse;
import com.demon.giraffe.modules.user.model.dto.response.DeliveryWorkerResponse;

import java.util.List;

/**
 * 配送任务分配服务接口
 * 负责快递员的智能分配和任务管理
 */
public interface DeliveryTaskAssignmentService {

    /**
     * 自动分配取件快递员
     * 根据订单配送地址和快递员位置、状态等因素智能分配
     * 
     * @param orderId 订单ID
     * @return 分配的快递员ID，如果没有可用快递员则返回null
     */
    Long assignPickupCourier(Long orderId);

    /**
     * 自动分配送货快递员
     * 根据工厂位置和配送地址智能分配
     * 
     * @param orderId 订单ID
     * @param factoryId 工厂ID
     * @return 分配的快递员ID，如果没有可用快递员则返回null
     */
    Long assignDeliveryCourier(Long orderId, Long factoryId);

    /**
     * 手动分配快递员
     * 
     * @param request 分配请求
     * @return 分配结果
     */
    CourierAssignmentResponse manualAssignCourier(CourierAssignmentRequest request);

    /**
     * 检查快递员是否可用
     * 
     * @param courierId 快递员ID
     * @return 是否可用
     */
    Boolean isCourierAvailable(Long courierId);

    /**
     * 获取指定区域的可用快递员列表
     * 
     * @param regionCode 区域代码
     * @param taskType 任务类型：1-取件 2-送件
     * @return 可用快递员列表
     */
    List<DeliveryWorkerResponse> getAvailableCouriers(String regionCode, Integer taskType);

    /**
     * 获取快递员当前任务负载
     * 
     * @param courierId 快递员ID
     * @return 当前任务数量
     */
    Integer getCourierTaskLoad(Long courierId);

    /**
     * 计算快递员到指定地址的距离
     * 
     * @param courierId 快递员ID
     * @param targetLongitude 目标经度
     * @param targetLatitude 目标纬度
     * @return 距离（公里）
     */
    Double calculateDistanceToCourier(Long courierId, Double targetLongitude, Double targetLatitude);

    /**
     * 通知快递员任务分配
     * 
     * @param courierId 快递员ID
     * @param taskId 任务ID
     * @return 是否通知成功
     */
    Boolean notifyCourierAssignment(Long courierId, Long taskId);

    /**
     * 快递员接受任务
     * 
     * @param courierId 快递员ID
     * @param taskId 任务ID
     * @return 是否接受成功
     */
    Boolean courierAcceptTask(Long courierId, Long taskId);

    /**
     * 快递员拒绝任务
     * 
     * @param courierId 快递员ID
     * @param taskId 任务ID
     * @param reason 拒绝原因
     * @return 是否拒绝成功
     */
    Boolean courierRejectTask(Long courierId, Long taskId, String reason);

    /**
     * 重新分配被拒绝的任务
     * 
     * @param taskId 任务ID
     * @return 新分配的快递员ID
     */
    Long reassignRejectedTask(Long taskId);

    /**
     * 获取快递员的任务历史
     * 
     * @param courierId 快递员ID
     * @param days 查询天数
     * @return 任务历史列表
     */
    List<CourierAssignmentResponse> getCourierTaskHistory(Long courierId, Integer days);

    /**
     * 更新快递员位置
     * 
     * @param courierId 快递员ID
     * @param longitude 经度
     * @param latitude 纬度
     * @return 是否更新成功
     */
    Boolean updateCourierLocation(Long courierId, Double longitude, Double latitude);

    /**
     * 获取快递员实时位置
     * 
     * @param courierId 快递员ID
     * @return 位置信息 [经度, 纬度]
     */
    Double[] getCourierLocation(Long courierId);

    /**
     * 批量分配任务
     * 用于处理积压的未分配任务
     * 
     * @param regionCode 区域代码
     * @param maxTasks 最大处理任务数
     * @return 成功分配的任务数量
     */
    Integer batchAssignTasks(String regionCode, Integer maxTasks);

    /**
     * 获取区域内的任务分配统计
     * 
     * @param regionCode 区域代码
     * @return 分配统计信息
     */
    CourierAssignmentResponse getRegionAssignmentStats(String regionCode);

    /**
     * 优化快递员路线
     * 根据快递员当前任务列表优化配送路线
     * 
     * @param courierId 快递员ID
     * @return 优化后的路线信息
     */
    Object optimizeCourierRoute(Long courierId);

    /**
     * 检查任务是否超时
     * 检查分配给快递员但长时间未处理的任务
     * 
     * @param timeoutMinutes 超时分钟数
     * @return 超时任务列表
     */
    List<Long> checkTimeoutTasks(Integer timeoutMinutes);

    /**
     * 处理超时任务
     * 重新分配或标记异常
     * 
     * @param taskIds 超时任务ID列表
     * @return 处理结果
     */
    Boolean handleTimeoutTasks(List<Long> taskIds);
}
