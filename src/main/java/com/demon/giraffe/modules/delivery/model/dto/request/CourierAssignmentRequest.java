package com.demon.giraffe.modules.delivery.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 快递员分配请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "快递员分配请求")
public class CourierAssignmentRequest {

    @NotNull(message = "任务ID不能为空")
    @Schema(description = "任务ID", required = true)
    private Long taskId;

    @Schema(description = "快递员ID（手动分配时必填）")
    private Long courierId;

    @NotNull(message = "任务类型不能为空")
    @Schema(description = "任务类型：1-取件 2-送件", required = true)
    private Integer taskType;

    @Schema(description = "区域代码")
    private String regionCode;

    @Schema(description = "目标地址经度")
    private Double targetLongitude;

    @Schema(description = "目标地址纬度")
    private Double targetLatitude;

    @Schema(description = "目标地址")
    private String targetAddress;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "是否加急")
    private Boolean isUrgent;

    @Schema(description = "预计开始时间")
    private LocalDateTime estimatedStartTime;

    @Schema(description = "预计完成时间")
    private LocalDateTime estimatedEndTime;

    @Schema(description = "优先级：1-低 2-中 3-高")
    private Integer priority;

    @Schema(description = "分配方式：1-自动分配 2-手动分配")
    private Integer assignmentType;

    @Schema(description = "分配原因")
    private String assignmentReason;

    @Size(max = 500, message = "备注长度不能超过500字符")
    @Schema(description = "备注")
    private String notes;

    @Schema(description = "操作员ID")
    private Long operatorId;

    @Schema(description = "是否强制分配（忽略快递员状态检查）")
    private Boolean forceAssign ;

    @Schema(description = "最大分配距离（公里）")
    private Double maxAssignDistance;

    @Schema(description = "期望的快递员技能要求")
    private String requiredSkills;

    @Schema(description = "是否允许跨区域分配")
    private Boolean allowCrossRegion ;
}
