package com.demon.giraffe.modules.delivery.model.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 快递员分配响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "快递员分配响应")
public class CourierAssignmentResponse {

    @Schema(description = "分配ID")
    private Long assignmentId;

    @Schema(description = "任务ID")
    private Long taskId;

    @Schema(description = "任务编号")
    private String taskNo;

    @Schema(description = "快递员ID")
    private Long courierId;

    @Schema(description = "快递员姓名")
    private String courierName;

    @Schema(description = "快递员电话")
    private String courierPhone;

    @Schema(description = "快递员工号")
    private String courierNo;

    @Schema(description = "任务类型")
    private Integer taskType;

    @Schema(description = "任务类型描述")
    private String taskTypeDesc;

    @Schema(description = "分配状态：1-已分配 2-已接受 3-已拒绝 4-已取消")
    private Integer assignmentStatus;

    @Schema(description = "分配状态描述")
    private String assignmentStatusDesc;

    @Schema(description = "分配时间")
    private LocalDateTime assignmentTime;

    @Schema(description = "接受时间")
    private LocalDateTime acceptTime;

    @Schema(description = "拒绝时间")
    private LocalDateTime rejectTime;

    @Schema(description = "拒绝原因")
    private String rejectReason;

    @Schema(description = "分配方式")
    private Integer assignmentType;

    @Schema(description = "分配方式描述")
    private String assignmentTypeDesc;

    @Schema(description = "分配原因")
    private String assignmentReason;

    @Schema(description = "目标地址")
    private String targetAddress;

    @Schema(description = "目标地址经度")
    private Double targetLongitude;

    @Schema(description = "目标地址纬度")
    private Double targetLatitude;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "是否加急")
    private Boolean isUrgent;

    @Schema(description = "优先级")
    private Integer priority;

    @Schema(description = "优先级描述")
    private String priorityDesc;

    @Schema(description = "预计开始时间")
    private LocalDateTime estimatedStartTime;

    @Schema(description = "预计完成时间")
    private LocalDateTime estimatedEndTime;

    @Schema(description = "实际开始时间")
    private LocalDateTime actualStartTime;

    @Schema(description = "实际完成时间")
    private LocalDateTime actualEndTime;

    @Schema(description = "快递员当前位置经度")
    private Double courierLongitude;

    @Schema(description = "快递员当前位置纬度")
    private Double courierLatitude;

    @Schema(description = "快递员到目标地址的距离（公里）")
    private Double distanceToTarget;

    @Schema(description = "预计到达时间（分钟）")
    private Integer estimatedArrivalMinutes;

    @Schema(description = "快递员当前状态")
    private Integer courierStatus;

    @Schema(description = "快递员当前状态描述")
    private String courierStatusDesc;

    @Schema(description = "快递员当前任务负载")
    private Integer courierTaskLoad;

    @Schema(description = "快递员评分")
    private Double courierRating;

    @Schema(description = "分配评分（系统计算的匹配度）")
    private Double assignmentScore;

    @Schema(description = "备注")
    private String notes;

    @Schema(description = "操作员ID")
    private Long operatorId;

    @Schema(description = "操作员姓名")
    private String operatorName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否成功分配")
    private Boolean isSuccessfullyAssigned;

    @Schema(description = "是否已完成")
    private Boolean isCompleted;

    @Schema(description = "是否超时")
    private Boolean isOvertime;

    @Schema(description = "超时分钟数")
    private Integer overtimeMinutes;

    // 订单相关信息
    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "会员姓名")
    private String memberName;

    @Schema(description = "订单金额")
    private String orderAmount;

    @Schema(description = "衣物件数")
    private Integer itemCount;

    // 统计信息
    @Schema(description = "分配尝试次数")
    private Integer assignmentAttempts;

    @Schema(description = "分配历史")
    private List<AssignmentHistory> assignmentHistory;

    /**
     * 分配历史记录
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "分配历史记录")
    public static class AssignmentHistory {

        @Schema(description = "快递员ID")
        private Long courierId;

        @Schema(description = "快递员姓名")
        private String courierName;

        @Schema(description = "分配时间")
        private LocalDateTime assignmentTime;

        @Schema(description = "响应时间")
        private LocalDateTime responseTime;

        @Schema(description = "响应结果：1-接受 2-拒绝 3-超时")
        private Integer responseResult;

        @Schema(description = "响应结果描述")
        private String responseResultDesc;

        @Schema(description = "拒绝原因")
        private String rejectReason;

        @Schema(description = "分配评分")
        private Double assignmentScore;
    }
}
