package com.demon.giraffe.modules.delivery.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 取送任务明细表（P1核心业务表）
 * 对应数据库表：delivery_task_detail
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("delivery_task_detail")
public class DeliveryTaskDetailPo extends BasePo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;


    /** 关联任务ID，必填 */
    @NotNull(message = "任务ID不能为空")
    @TableField("task_id")
    @Schema(description = "关联任务ID")
    private Long taskId;

    /** 关联订单ID，必填 */
    @NotNull(message = "订单ID不能为空")
    @TableField("order_id")
    @Schema(description = "关联订单ID")
    private Long orderId;

    /** 执行顺序，默认1 */
    @NotNull(message = "执行顺序不能为空")
    @Min(value = 1, message = "执行顺序最小为1")
    @TableField("sequence")
    @Schema(description = "执行顺序")
    private Integer sequence;

    /** 地址类型：1-取件地址 2-送件地址，必填 */
    @NotNull(message = "地址类型不能为空")
    @TableField("address_type")
    @Schema(description = "地址类型：1-取件地址 2-送件地址")
    private Integer addressType;

    /** 关联地址ID，必填 */
    @NotNull(message = "地址ID不能为空")
    @TableField("address_id")
    @Schema(description = "关联地址ID（避免数据冗余）")
    private Long addressId;

    /** 地址快照（JSON格式） */
    @TableField(value = "address_snapshot", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    @Schema(description = "地址快照（JSON格式）")
    private Object addressSnapshot;

    /** 联系人，必填 */
    @NotBlank(message = "联系人不能为空")
    @Size(max = 30, message = "联系人长度不能超过30")
    @TableField("contact_name")
    @Schema(description = "联系人")
    private String contactName;

    /** 联系电话（支持国际号码），必填 */
    @NotBlank(message = "联系电话不能为空")
    @Size(max = 20, message = "联系电话长度不能超过20")
    @TableField("contact_phone")
    @Schema(description = "联系电话（支持国际号码）")
    private String contactPhone;

    /** 备用联系电话 */
    @Size(max = 20, message = "备用联系电话长度不能超过20")
    @TableField("contact_secondary")
    @Schema(description = "备用联系电话")
    private String contactSecondary;

    /** 预计到达时间（系统计算） */
    @TableField("estimated_time")
    @Schema(description = "预计到达时间（系统计算）")
    private LocalDateTime estimatedTime;

    /** 取送员调整时间 */
    @TableField("adjusted_time")
    @Schema(description = "取送员调整时间")
    private LocalDateTime adjustedTime;

    /** 实际到达时间 */
    @TableField("arrival_time")
    @Schema(description = "实际到达时间")
    private LocalDateTime arrivalTime;

    /** 完成时间 */
    @TableField("completion_time")
    @Schema(description = "完成时间")
    private LocalDateTime completionTime;

    /** 状态：0-待执行 1-前往中 2-已到达 3-已完成 4-异常，默认0 */
    @NotNull(message = "状态不能为空")
    @TableField("status")
    @Schema(description = "状态：0-待执行 1-前往中 2-已到达 3-已完成 4-异常")
    private Integer status;

    /** 子状态：1-客户拒收 2-联系不上... */
    @TableField("sub_status")
    @Schema(description = "子状态：1-客户拒收 2-联系不上...")
    private Integer subStatus;

    /** 异常类型编码 */
    @Size(max = 20, message = "异常类型编码长度不能超过20")
    @TableField("exception_code")
    @Schema(description = "异常类型编码")
    private String exceptionCode;

    /** 异常原因详情 */
    @Size(max = 200, message = "异常原因详情长度不能超过200")
    @TableField("exception_reason")
    @Schema(description = "异常原因详情")
    private String exceptionReason;

    /** 6位动态验证码 */
    @Size(max = 10, message = "验证码长度不能超过10")
    @TableField("verification_code")
    @Schema(description = "6位动态验证码")
    private String verificationCode;

    /** 验证码过期时间 */
    @TableField("code_expire_time")
    @Schema(description = "验证码过期时间")
    private LocalDateTime codeExpireTime;

    /** 执行位置经度 */
    @DecimalMin(value = "-180.0", message = "经度最小值为-180")
    @DecimalMax(value = "180.0", message = "经度最大值为180")
    @TableField("longitude")
    @Schema(description = "执行位置经度")
    private BigDecimal longitude;

    /** 执行位置纬度 */
    @DecimalMin(value = "-90.0", message = "纬度最小值为-90")
    @DecimalMax(value = "90.0", message = "纬度最大值为90")
    @TableField("latitude")
    @Schema(description = "执行位置纬度")
    private BigDecimal latitude;

    /** 定位精度（米） */
    @DecimalMin(value = "0.0", message = "定位精度不能小于0")
    @Digits(integer = 3, fraction = 2, message = "定位精度格式错误")
    @TableField("location_accuracy")
    @Schema(description = "定位精度（米）")
    private BigDecimal locationAccuracy;

    /** 执行凭证照片（[{url:\"\",type:1}]）JSON格式 */
    @TableField(value = "images", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    @Schema(description = "执行凭证照片（[{url:\"\",type:1}]）")
    private Object images;

    /** 电子签名URL */
    @Size(max = 255, message = "电子签名URL长度不能超过255")
    @TableField("signature")
    @Schema(description = "电子签名URL")
    private String signature;
}
