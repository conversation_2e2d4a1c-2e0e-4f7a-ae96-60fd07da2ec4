package com.demon.giraffe.modules.delivery.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 取送任务主表（P1核心业务表）
 * 对应数据库表：delivery_task
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("delivery_task")
public class DeliveryTaskPo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 任务编号（规则：DT+年月日+6位序列），必填且唯一 */
    @NotBlank(message = "任务编号不能为空")
    @Size(max = 32, message = "任务编号长度不能超过32")
    @TableField("task_no")
    @Schema(description = "任务编号（规则：DT+年月日+6位序列）")
    private String taskNo;

    /** 取送员ID，必填 */
    @NotNull(message = "取送员ID不能为空")
    @TableField("courier_id")
    @Schema(description = "取送员ID")
    private Long courierId;

    /** 任务类型：1-取件 2-送件 3-取送一体，必填 */
    @NotNull(message = "任务类型不能为空")
    @TableField("task_type")
    @Schema(description = "任务类型：1-取件 2-送件 3-取送一体")
    private Integer taskType;

    /** 任务执行日期，必填 */
    @NotNull(message = "任务执行日期不能为空")
    @TableField("task_date")
    @Schema(description = "任务执行日期")
    private LocalDate taskDate;

    /** 预计开始时间（系统计算） */
    @TableField("estimated_start_time")
    @Schema(description = "预计开始时间（系统计算）")
    private LocalDateTime estimatedStartTime;

    /** 预计结束时间（系统计算） */
    @TableField("estimated_end_time")
    @Schema(description = "预计结束时间（系统计算）")
    private LocalDateTime estimatedEndTime;

    /** 取送员调整时间 */
    @TableField("courier_adjust_time")
    @Schema(description = "取送员调整时间")
    private LocalDateTime courierAdjustTime;

    /** 实际开始时间 */
    @TableField("actual_start_time")
    @Schema(description = "实际开始时间")
    private LocalDateTime actualStartTime;

    /** 实际完成时间 */
    @TableField("actual_end_time")
    @Schema(description = "实际完成时间")
    private LocalDateTime actualEndTime;

    /** 状态：0-待接单 1-已接单 2-进行中 3-已完成 4-异常 5-已取消，默认0 */
    @NotNull(message = "任务状态不能为空")
    @TableField("status")
    @Schema(description = "状态：0-待接单 1-已接单 2-进行中 3-已完成 4-异常 5-已取消")
    private Integer status;

    /** 异常类型编码 */
    @Size(max = 20, message = "异常类型编码长度不能超过20")
    @TableField("exception_code")
    @Schema(description = "异常类型编码")
    private String exceptionCode;

    /** 总订单数，默认0 */
    @NotNull(message = "总订单数不能为空")
    @Min(value = 0, message = "总订单数不能小于0")
    @TableField("total_orders")
    @Schema(description = "总订单数")
    private Integer totalOrders;

    /** 已完成订单数，默认0 */
    @NotNull(message = "已完成订单数不能为空")
    @Min(value = 0, message = "已完成订单数不能小于0")
    @TableField("completed_orders")
    @Schema(description = "已完成订单数")
    private Integer completedOrders;

    /** 加急订单数，默认0 */
    @Min(value = 0, message = "加急订单数不能小于0")
    @TableField("urgent_orders")
    @Schema(description = "加急订单数")
    private Integer urgentOrders;

    /** 路线信息（含坐标点、距离、预估时长），JSON格式 */
    @TableField(value = "route_info", typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    @Schema(description = "路线信息（含坐标点、距离、预估时长）")
    private Object routeInfo;

    /** 总距离（公里） */
    @DecimalMin(value = "0.00", inclusive = true, message = "总距离不能小于0")
    @Digits(integer = 6, fraction = 2, message = "总距离格式错误")
    @TableField("distance")
    @Schema(description = "总距离（公里）")
    private BigDecimal distance;

    /** 预估时长（分钟） */
    @Min(value = 0, message = "预估时长不能小于0")
    @TableField("estimated_duration")
    @Schema(description = "预估时长（分钟）")
    private Integer estimatedDuration;
    
    /** 乐观锁版本号，默认0 */
    @NotNull(message = "版本号不能为空")
    @TableField("version")
    @Version
    @Schema(description = "乐观锁版本号")
    private Integer version;
}
