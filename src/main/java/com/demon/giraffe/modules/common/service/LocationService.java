package com.demon.giraffe.modules.common.service;

import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.modules.common.model.entity.DistanceWrapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 地理位置服务接口
 */
public interface LocationService {

    /**
     * 将地址信息转换为经纬度
     *
     * @param province      省份
     * @param city          城市
     * @param district      区/县
     * @param detailAddress 详细地址
     * @return 包含经纬度的Map，key为longitude和latitude         map key:   LONGITUDE_KEY LATITUDE_KEY
     * @throws Exception 当地址解析失败时抛出
     */
    Map<String, BigDecimal> convertAddressToCoordinate(String province, String city,
                                                       String district, String detailAddress) throws Exception;


    /**
     * 获取区县的地域编码(国标)
     *
     * @param province 省份名称
     * @param city     城市名称
     * @param district 区县名称
     * @return 区县的国标编码(对应CountyEnum中的code)
     * @throws Exception 当查询失败时抛出
     */
    Integer getDistrictAddressCode(String province, String city, String district) throws Exception;

    /**
     * 解析区县编码到CountyEnum
     *
     * @param province  省份名称
     * @param city      城市名称
     * @param district  区县名称
     */
    CountyEnum resolveCountyCode(String province, String city, String district);


    /**
     * 计算两点之间的距离（米）
     * @param lon1 点1经度
     * @param lat1 点1纬度
     * @param lon2 点2经度
     * @param lat2 点2纬度
     * @return 距离（米）
     */
    double calculateDistance(BigDecimal lon1, BigDecimal lat1,
                             BigDecimal lon2, BigDecimal lat2);

    /**
     * 计算多个位置与单个位置的距离，并过滤排序
     *
     * @param centerLon   中心点经度
     * @param centerLat   中心点纬度
     * @param points      位置列表（每个位置是包含longitude和latitude的Map）
     * @param maxDistance 最大距离限制（米），null表示无限制
     * @return 排序后的位置列表（按距离升序）
     */
    <T> List<DistanceWrapper<T>> calculateDistancesAndFilterGeneric(
            BigDecimal centerLon, BigDecimal centerLat,
            List<T> points,
            Function<T, BigDecimal> lonExtractor,
            Function<T, BigDecimal> latExtractor,
            Double maxDistance);
}