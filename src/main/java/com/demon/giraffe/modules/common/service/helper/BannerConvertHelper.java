package com.demon.giraffe.modules.common.service.helper;

import com.demon.giraffe.modules.common.model.entity.BannerSingleDetailConfig;
import com.demon.giraffe.modules.common.model.vo.CommonBannerVo;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 轮播图转换工具类
 * 排序规则：sort值越大越靠前（默认降序）
 */
public class BannerConvertHelper {

    /**
     * 将配置列表转换为VO列表（默认降序排序，sort值越大越靠前）
     * @param configs 原始配置列表
     * @return 排序后的VO列表
     */
    public static List<CommonBannerVo> convertWithSort(List<BannerSingleDetailConfig> configs) {
        return convertWithSort(configs, false); // 默认降序
    }

    /**
     * 将配置列表转换为VO列表（带排序控制）
     * @param configs 原始配置列表
     * @param ascending 是否升序排序（true=升序=数字小的在前，false=降序=数字大的在前）
     * @return 排序后的VO列表
     */
    public static List<CommonBannerVo> convertWithSort(List<BannerSingleDetailConfig> configs, boolean ascending) {
        if (configs == null) {
            return null;
        }

        Comparator<BannerSingleDetailConfig> comparator = Comparator.comparingInt(
                BannerSingleDetailConfig::getSort);

        // 默认降序（数字大的在前）
        if (!ascending) {
            comparator = comparator.reversed();
        }

        return configs.stream()
                .sorted(comparator)
                .map(CommonBannerVo::from)
                .collect(Collectors.toList());
    }

    /**
     * 将配置列表转换为VO列表（自定义排序）
     * @param configs 原始配置列表
     * @param comparator 自定义排序比较器
     * @return 排序后的VO列表
     */
    public static List<CommonBannerVo> convertWithSort(
            List<BannerSingleDetailConfig> configs,
            Comparator<BannerSingleDetailConfig> comparator) {
        if (configs == null) {
            return null;
        }

        return configs.stream()
                .sorted(comparator)
                .map(CommonBannerVo::from)
                .collect(Collectors.toList());
    }

    /**
     * 获取默认排序比较器（数字大的在前）
     * @return 默认降序比较器
     */
    public static Comparator<BannerSingleDetailConfig> getDefaultComparator() {
        return Comparator.comparingInt(BannerSingleDetailConfig::getSort).reversed();
    }

}