package com.demon.giraffe.modules.common.model.vo;

import com.demon.giraffe.common.util.TimeUtil;
import com.demon.giraffe.modules.common.model.entity.BannerSingleDetailConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 公用轮播图视图对象
 * 不包含排序字段，排序由外部处理
 */
@Data
@Builder
@Schema(description = "公用轮播图视图对象")
public class CommonBannerVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "单张图片地址", example = "https://cdn.example.com/banner1.jpg")
    private String imageUrl;

    @Schema(description = "点击图片跳转的地址", example = "https://example.com/promo")
    private String redirectUrl;

    @Schema(description = "图片生效开始时间戳（毫秒）", example = "1743465600000")
    private Long startTime;

    @Schema(description = "图片生效截止时间戳（毫秒）", example = "1746057599000")
    private Long endTime;

    @Schema(description = "图片放映时间（毫秒），用于前端展示轮播时长", example = "3000")
    private Integer duration;

    /**
     * 从BannerSingleDetailConfig转换为CommonBannerVo
     *
     * @param config 单个轮播图明细配置
     * @return 公用轮播图视图对象
     */
    public static CommonBannerVo from(BannerSingleDetailConfig config) {
        if (config == null) {
            return null;
        }
        return CommonBannerVo.builder()
                .imageUrl(config.getImageUrl())
                .redirectUrl(config.getRedirectUrl())
                .startTime(config.getStartTime())
                .endTime(config.getEndTime())
                .duration(config.getDuration())
                .build();
    }
}