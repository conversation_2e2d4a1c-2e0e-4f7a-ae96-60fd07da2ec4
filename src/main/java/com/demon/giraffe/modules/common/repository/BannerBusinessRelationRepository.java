package com.demon.giraffe.modules.common.repository;

import com.demon.giraffe.modules.common.model.entity.BannerBusinessInfo;
import com.demon.giraffe.modules.common.model.po.BannerBusinessRelationPo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 轮播图业务关系仓库接口
 */
@Repository
public interface BannerBusinessRelationRepository {

    /**
     * 保存首页轮播图关联关系
     * @param bannerId 轮播图ID
     */
    @Operation(summary = "保存首页轮播图关联", description = "建立首页与轮播图的关联关系")
    void saveHomeBannerId(
            @Parameter(description = "轮播图ID", required = true, example = "1")
            Long bannerId);

    /**
     * 保存服务轮播图关联关系
     * @param serviceId 服务ID
     * @param bannerId 轮播图ID
     */
    @Operation(summary = "保存服务轮播图关联", description = "建立服务与轮播图的关联关系")
    void saveServiceBannerId(
            @Parameter(description = "服务ID", required = true, example = "1001")
            Long serviceId,
            @Parameter(description = "轮播图ID", required = true, example = "1")
            Long bannerId);

    /**
     * 获取首页轮播图ID
     * @return 轮播图ID
     */
    @Operation(summary = "获取首页轮播图ID", description = "查询当前关联到首页的轮播图ID")
    Long getHomeBannerId();

    /**
     * 获取服务轮播图ID
     * @param serviceId 服务ID
     * @return 轮播图ID
     */
    @Operation(summary = "获取服务轮播图ID", description = "查询指定服务关联的轮播图ID")
    Long getServiceBannerId(
            @Parameter(description = "服务ID", required = true, example = "1001")
            Long serviceId);

    /**
     * 根据轮播图ID删除关联关系
     * @param bannerId 轮播图ID
     */
    @Operation(summary = "删除轮播图关联", description = "根据轮播图ID删除所有关联关系")
    void deleteByBannerId(
            @Parameter(description = "轮播图ID", required = true, example = "1")
            Long bannerId);

    /**
     * 根据轮播图ID获取所有业务关联关系
     * @param bannerId 轮播图ID
     * @return 业务关联关系列表（一个轮播图可以绑定多个业务）
     */
    @Operation(summary = "获取轮播图业务关联关系",
            description = "查询轮播图关联的所有业务关系(一个轮播图可绑定多个业务)")
    List<BannerBusinessRelationPo> getBannerRelations(
            @Parameter(description = "轮播图ID", required = true, example = "1")
            Long bannerId);

    /**
     * 批量查询服务与轮播图的映射关系
     * @param serviceIds 服务ID列表
     * @return Map<服务ID, 轮播图ID>
     */
    Map<Long, Long> batchGetServiceBannerIds(List<Long> serviceIds);
}