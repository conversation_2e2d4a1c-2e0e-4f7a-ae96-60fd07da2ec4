package com.demon.giraffe.modules.common.model.dto.request;

import com.demon.giraffe.modules.common.model.enums.BannerTypeEnum;
import com.demon.giraffe.modules.service.model.enums.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 轮播图配置查询请求参数
 */
@Data
@Schema(description = "轮播图配置查询请求参数")
public class BannerConfigQueryRequest implements Serializable {

    @Schema(description = "轮播图类型")
    private BannerTypeEnum type;

    @Schema(description = "轮播图状态")
    private CommonStatusEnum status;

    @Schema(description = "轮播图标题关键词")
    private String title;
}