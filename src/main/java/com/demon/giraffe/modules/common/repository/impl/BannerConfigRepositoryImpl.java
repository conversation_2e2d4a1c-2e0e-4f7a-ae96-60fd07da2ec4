package com.demon.giraffe.modules.common.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.modules.common.mapper.BannerConfigMapper;
import com.demon.giraffe.modules.common.model.po.BannerConfigPo;
import com.demon.giraffe.modules.common.repository.BannerConfigRepository;
import com.demon.giraffe.modules.service.model.enums.CommonStatusEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class BannerConfigRepositoryImpl implements BannerConfigRepository {

    private final BannerConfigMapper mapper;

    @Override
    @Transactional
    public BannerConfigPo save(BannerConfigPo po) {
        int result = mapper.insert(po);
        if (result <= 0) {
            throw new BusinessException("轮播图保存失败");
        }
        return po;
    }

    @Override
    public boolean update(BannerConfigPo po) {
        return mapper.updateById(po) > 0;
    }

    @Override
    public boolean delete(Long id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public BannerConfigPo getById(Long id) {
        return mapper.selectById(id);
    }

    @Override
    public List<BannerConfigPo> listAllValidBanners() {
        LambdaQueryWrapper<BannerConfigPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BannerConfigPo::getStatus, CommonStatusEnum.NORMAL)
                .orderByDesc(BannerConfigPo::getCreateTime);
        return mapper.selectList(wrapper);
    }

    @Override
    public Map<Long, BannerConfigPo> batchGetByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<BannerConfigPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BannerConfigPo::getId, ids);

        return mapper.selectList(wrapper).stream()
                .collect(Collectors.toMap(
                        BannerConfigPo::getId,
                        po -> po,
                        (existing, replacement) -> existing
                ));
    }
}