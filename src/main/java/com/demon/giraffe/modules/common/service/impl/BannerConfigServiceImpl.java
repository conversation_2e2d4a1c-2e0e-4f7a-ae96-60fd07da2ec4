package com.demon.giraffe.modules.common.service.impl;

import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.modules.common.model.dto.request.BannerConfigRequest;
import com.demon.giraffe.modules.common.model.dto.response.BannerConfigResponse;
import com.demon.giraffe.modules.common.model.entity.BannerSingleDetailConfig;
import com.demon.giraffe.modules.common.model.po.BannerBusinessRelationPo;
import com.demon.giraffe.modules.common.model.po.BannerConfigPo;
import com.demon.giraffe.modules.common.model.vo.CommonBannerVo;
import com.demon.giraffe.modules.common.repository.BannerBusinessRelationRepository;
import com.demon.giraffe.modules.common.repository.BannerConfigRepository;

import com.demon.giraffe.modules.common.service.BannerConfigService;
import com.demon.giraffe.modules.common.service.helper.BannerConvertHelper;
import com.demon.giraffe.modules.service.model.enums.CommonStatusEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.demon.giraffe.modules.common.service.helper.BannerConfigConvertHelper.convertToPo;
import static com.demon.giraffe.modules.common.service.helper.BannerConfigConvertHelper.convertToResponse;

/**
 * 轮播图配置服务实现类
 */
@Service
@RequiredArgsConstructor
public class BannerConfigServiceImpl implements BannerConfigService {

    private final BannerConfigRepository bannerConfigRepository;
    private final BannerBusinessRelationRepository bannerBusinessRelationRepository;

    @Transactional
    @Override
    public boolean createBannerConfig(BannerConfigRequest request) {
        BannerConfigPo po = convertToPo(request);
        Long id = bannerConfigRepository.save(po).getId();
        switch (request.getType()) {
            case HOME -> bannerBusinessRelationRepository.saveHomeBannerId(id);
            case SERVICE -> bannerBusinessRelationRepository.saveServiceBannerId(
                    request.getServiceItemInfo().getServiceItemId(), id);
            default -> throw new IllegalArgumentException("不支持的轮播图类型: " + request.getType());
        }
        return true;
    }

    @Transactional
    @Override
    public boolean updateBannerConfig(Long id, BannerConfigRequest request) {
        BannerConfigPo po = convertToPo(request);
        po.setId(id);
        bannerConfigRepository.update(po);

        switch (request.getType()) {
            case HOME -> bannerBusinessRelationRepository.saveHomeBannerId(id);

            case SERVICE -> bannerBusinessRelationRepository.saveServiceBannerId(
                    request.getServiceItemInfo().getServiceItemId(), id);
            default -> throw new IllegalArgumentException("不支持的轮播图类型: " + request.getType());
        }
        return true;
    }

    @Transactional
    @Override
    public boolean deleteBannerConfig(Long id) {
        bannerConfigRepository.delete(id);
        bannerBusinessRelationRepository.deleteByBannerId(id);
        return true;
    }

    @Override
    public BannerConfigResponse getBannerConfigDetail(Long id) {
        BannerConfigPo po = bannerConfigRepository.getById(id);
        List<BannerBusinessRelationPo> bannerRelations = bannerBusinessRelationRepository.getBannerRelations(id);
        return convertToResponse(po, bannerRelations);
    }




    @Override
    public List<BannerConfigResponse> getAllBanners() {
        List<BannerConfigPo> bannerConfigPos = bannerConfigRepository.listAllValidBanners();
        List<BannerConfigResponse> responseList = new ArrayList<>();
        for (BannerConfigPo po : bannerConfigPos){
            Long id = po.getId();
            List<BannerBusinessRelationPo> bannerRelations = bannerBusinessRelationRepository.getBannerRelations(id);
            BannerConfigResponse bannerConfigResponse = convertToResponse(po, bannerRelations);
            responseList.add(bannerConfigResponse);
        }
        return responseList;

    }

    @Override
    public List<CommonBannerVo> getHomeBanners() {
        Long homeBannerId = bannerBusinessRelationRepository.getHomeBannerId();
        BannerConfigPo bannerConfigPo = bannerConfigRepository.getById(homeBannerId);
        if(bannerConfigPo == null){
            return Collections.emptyList();
        }
        List<BannerSingleDetailConfig> banners = bannerConfigPo.getBanners();
        return BannerConvertHelper.convertWithSort(banners);
    }

    @Override
    public List<CommonBannerVo> getBannerConfigByServiceId(Long serviceId) {
        // 1. 参数校验
        if (serviceId == null || serviceId <= 0) {
            throw new BusinessException("服务ID不能为空且必须大于0");
        }

        // 2. 查询服务关联的轮播图ID
        Long bannerId = bannerBusinessRelationRepository.getServiceBannerId(serviceId);
        if (bannerId == null) {
            return Collections.emptyList();
        }

        BannerConfigPo bannerConfigPo = bannerConfigRepository.getById(bannerId);
        if(bannerConfigPo == null){
            return Collections.emptyList();
        }
        List<BannerSingleDetailConfig> banners = bannerConfigPo.getBanners();
        return BannerConvertHelper.convertWithSort(banners);
    }

    @Override
    public Map<Long, List<CommonBannerVo>> getBannerConfigByServiceIds(List<Long> serviceIds) {
        // 1. 参数校验
        if (serviceIds == null || serviceIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 2. 批量查询服务与轮播图的映射关系
        Map<Long, Long> serviceToBannerMap = bannerBusinessRelationRepository
                .batchGetServiceBannerIds(serviceIds);

        // 3. 批量查询轮播图配置
        List<Long> bannerIds = new ArrayList<>(serviceToBannerMap.values());
        Map<Long, BannerConfigPo> bannerConfigMap = bannerConfigRepository
                .batchGetByIds(bannerIds);

        // 4. 组装结果
        Map<Long, List<CommonBannerVo>> result = new HashMap<>();
        serviceToBannerMap.forEach((serviceId, bannerId) -> {
            BannerConfigPo po = bannerConfigMap.get(bannerId);
            if (po != null && po.getStatus() == CommonStatusEnum.NORMAL) {
                List<CommonBannerVo> vos = BannerConvertHelper.convertWithSort(po.getBanners());
                result.put(serviceId, vos);
            } else {
                result.put(serviceId, Collections.emptyList());
            }
        });

        return result;
    }
}