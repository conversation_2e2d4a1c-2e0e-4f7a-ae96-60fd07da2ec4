package com.demon.giraffe.modules.common.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.common.model.dto.request.BannerConfigRequest;
import com.demon.giraffe.modules.common.model.dto.response.BannerConfigResponse;
import com.demon.giraffe.modules.common.model.enums.BannerTypeEnum;
import com.demon.giraffe.modules.common.model.vo.CommonBannerVo;
import com.demon.giraffe.modules.common.service.BannerConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 轮播图配置管理接口
 */
@RestController
@RequestMapping("/common/banner-config")
@RequiredArgsConstructor
@Tag(name = "轮播图配置管理接口", description = "轮播图配置相关操作")
public class BannerConfigController {

    private final BannerConfigService bannerConfigService;


    /**
     * 创建轮播图配置
     *
     * @param request 轮播图配置请求参数
     * @return 创建结果
     */
    @Operation(summary = "创建轮播图配置", description = "创建新的轮播图配置")
    @PostMapping("/create")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<Boolean> create(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "轮播图配置请求参数",
                    required = true,
                    content = @Content(schema = @Schema(implementation = BannerConfigRequest.class))
            )
            @RequestBody BannerConfigRequest request) {
        return ResultBean.success(bannerConfigService.createBannerConfig(request));
    }

    /**
     * 更新轮播图配置
     *
     * @param id      轮播图配置ID
     * @param request 轮播图配置请求参数
     * @return 更新结果
     */
    @Operation(summary = "更新轮播图配置", description = "更新指定ID的轮播图配置")
    @PostMapping("/update/{id}")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<Boolean> update(
            @Schema(description = "轮播图配置ID", required = true) @PathVariable Long id,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "轮播图配置请求参数",
                    required = true,
                    content = @Content(schema = @Schema(implementation = BannerConfigRequest.class))
            )
            @RequestBody BannerConfigRequest request) {
        return ResultBean.success(bannerConfigService.updateBannerConfig(id, request));
    }

    /**
     * 删除轮播图配置
     *
     * @param id 轮播图配置ID
     * @return 删除结果
     */
    @Operation(summary = "删除轮播图配置", description = "删除指定ID的轮播图配置")
    @PostMapping("/delete/{id}")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<Boolean> delete(
            @Schema(description = "轮播图配置ID", required = true) @PathVariable Long id) {
        return ResultBean.success(bannerConfigService.deleteBannerConfig(id));
    }
    /**
     * 获取所有轮播图配置（ROOT权限）
     * @return 所有轮播图配置列表
     */
    @GetMapping("/list-all")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    @Operation(summary = "获取所有轮播图配置", description = "需要ROOT权限")
    public ResultBean<List<BannerConfigResponse>> listAllBanners() {
        return ResultBean.success(bannerConfigService.getAllBanners());
    }

    /**
     * 获取首页轮播图配置
     * @return 首页轮播图配置列表
     */
    @GetMapping("/home")
    @Operation(summary = "获取首页轮播图", description = "获取类型为HOME的轮播图配置")
    public ResultBean<List<CommonBannerVo>> getHomeBanners() {
        return ResultBean.success(bannerConfigService.getHomeBanners());
    }

}