package com.demon.giraffe.modules.common.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.modules.common.model.dto.request.BannerConfigQueryRequest;
import com.demon.giraffe.modules.common.model.dto.request.BannerConfigRequest;
import com.demon.giraffe.modules.common.model.dto.response.BannerConfigResponse;
import com.demon.giraffe.modules.common.model.enums.BannerTypeEnum;
import com.demon.giraffe.modules.common.model.vo.CommonBannerVo;

import java.util.List;
import java.util.Map;

/**
 * 轮播图配置服务接口
 */
public interface BannerConfigService {

    /**
     * 创建轮播图配置
     *
     * @param request 轮播图配置请求参数
     * @return 是否创建成功
     */
    boolean createBannerConfig(BannerConfigRequest request);

    /**
     * 更新轮播图配置
     *
     * @param id      轮播图配置ID
     * @param request 轮播图配置请求参数
     * @return 是否更新成功
     */
    boolean updateBannerConfig(Long id, BannerConfigRequest request);

    /**
     * 删除轮播图配置
     *
     * @param id 轮播图配置ID
     * @return 是否删除成功
     */
    boolean deleteBannerConfig(Long id);

    /**
     * 获取轮播图配置详情
     *
     * @param id 轮播图配置ID
     * @return 轮播图配置详情响应
     */
    BannerConfigResponse getBannerConfigDetail(Long id);



    /**
     * 获取所有轮播图配置
     *
     * @return 所有轮播图配置列表
     */
    List<BannerConfigResponse> getAllBanners();

    /**
     * 获取首页轮播图配置
     *
     * @return 首页轮播图配置列表
     */
    List<CommonBannerVo> getHomeBanners();

    /**
     * 根据服务ID获取轮播图配置详情
     * @param serviceId 服务ID
     * @return 轮播图配置详情列表
     */
    List<CommonBannerVo> getBannerConfigByServiceId(Long serviceId);

    /**
     * 批量根据服务ID获取轮播图配置
     * @param serviceIds 服务ID列表
     * @return Map<服务ID, 轮播图配置列表>
     */
    Map<Long, List<CommonBannerVo>> getBannerConfigByServiceIds(List<Long> serviceIds);
}