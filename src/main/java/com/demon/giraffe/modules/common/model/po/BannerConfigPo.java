package com.demon.giraffe.modules.common.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.framework.mybatis.typehandler.BannerSingleDetailListTypeHandler;
import com.demon.giraffe.modules.common.model.entity.BannerSingleDetailConfig;
import com.demon.giraffe.modules.service.model.enums.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 通用轮播图配置表
 * 表名：banner_config
 * 用于配置首页、服务页等位置的轮播图组
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "banner_config", autoResultMap = true)
@Schema(description = "通用轮播图配置对象")
public class BannerConfigPo extends BasePo implements Serializable {

    private static final long serialVersionUID = 1L;


    @Schema(description = "轮播图标题/名称（后台展示用）")
    @TableField(value = "title")
    private String title;


    @Schema(description = "轮播图明细列表（以 JSON 存储）")
    @TableField(value = "banners", typeHandler = BannerSingleDetailListTypeHandler.class)
    private List<BannerSingleDetailConfig> banners;



    @Schema(description = "轮播图启用状态（ENABLE=启用，DISABLE=禁用）")
    @TableField(value = "status")
    private CommonStatusEnum status;

}
