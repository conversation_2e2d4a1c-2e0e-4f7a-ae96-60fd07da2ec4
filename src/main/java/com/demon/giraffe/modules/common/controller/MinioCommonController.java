package com.demon.giraffe.modules.common.controller;

import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.framework.minio.domain.enums.MinioBucketEnum;
import com.demon.giraffe.framework.minio.domain.vo.MinioUploadVo;
import com.demon.giraffe.framework.minio.util.MinioUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/common/minio")
@Tag(name = "公共上传接口")
@RequiredArgsConstructor
public class MinioCommonController {

    private final MinioUtil minioUtil;

    @PostMapping("/upload")
    @Operation(summary = "上传单文件")
    public ResultBean<MinioUploadVo> upload(@RequestParam("bucket") MinioBucketEnum minioBucketEnum,
                                            @RequestParam("file") MultipartFile file) {
        String objectName = minioUtil.getObjectName(file.getOriginalFilename(), null, false);
        MinioUploadVo result = minioUtil.upload(minioBucketEnum.getCode(), file, objectName);
        return ResultBean.success(result);
    }

    @PostMapping("/uploadBatch")
    @Operation(summary = "批量上传文件")
    public ResultBean<List<MinioUploadVo>> uploadBatch(@RequestParam("bucket") MinioBucketEnum minioBucketEnum,
                                                       @RequestParam("files") List<MultipartFile> files) {
        List<MinioUploadVo> result = minioUtil.batchUpload(minioBucketEnum.getCode(), files);
        return ResultBean.success(result);
    }

    @GetMapping("/download")
    @Operation(summary = "下载文件")
    public void download(@RequestParam("bucket") MinioBucketEnum minioBucketEnum,
                         @RequestParam("objectName") String objectName,
                         HttpServletResponse response) {
        minioUtil.downloadFile(minioBucketEnum.getCode(), objectName, response);
    }

    @GetMapping("/url")
    @Operation(summary = "获取临时URL")
    public ResultBean<String> getTempUrl(@RequestParam("bucket") MinioBucketEnum minioBucketEnum,
                                         @RequestParam("objectName") String objectName) {
        String url = minioUtil.getObjectTempUrl(minioBucketEnum.getCode(), objectName);
        return ResultBean.success(url);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除文件")
    public ResultBean<Boolean> delete(@RequestParam("bucket") MinioBucketEnum minioBucketEnum,
                                      @RequestParam("objectName") String objectName) {
        boolean success = minioUtil.removeObject(minioBucketEnum.getCode(), objectName);
        return ResultBean.success(success);
    }
}
