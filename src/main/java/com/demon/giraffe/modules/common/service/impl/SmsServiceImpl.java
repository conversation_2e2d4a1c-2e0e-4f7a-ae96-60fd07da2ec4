package com.demon.giraffe.modules.common.service.impl;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.*;
import com.demon.giraffe.modules.common.service.SmsService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class SmsServiceImpl implements SmsService {

    @Value("${tencent.cloud.secretId}")
    private String secretId;

    @Value("${tencent.cloud.secretKey}")
    private String secretKey;

    @Value("${tencent.cloud.sms.region}")
    private String region;

    @Value("${tencent.cloud.sms.sdkAppId}")
    private String sdkAppId;

    @Value("${tencent.cloud.sms.signName}")
    private String signName;

    private volatile SmsClient smsClient;

    private SmsClient getSmsClient() {
        if (smsClient == null) {
            synchronized (this) {
                if (smsClient == null) {
                    Credential cred = new Credential(secretId, secretKey);
                    HttpProfile httpProfile = new HttpProfile();
                    httpProfile.setEndpoint("sms.tencentcloudapi.com");
                    ClientProfile clientProfile = new ClientProfile();
                    clientProfile.setHttpProfile(httpProfile);
                    smsClient = new SmsClient(cred, region, clientProfile);
                }
            }
        }
        return smsClient;
    }

    @Override
    public AddSmsTemplateResponse createTemplate(String templateName, String templateContent,
                                                 Integer smsType, Integer international, String remark) throws Exception {
        try {
            SmsClient client = getSmsClient();
            AddSmsTemplateRequest req = new AddSmsTemplateRequest();
            req.setTemplateName(templateName);
            req.setTemplateContent(templateContent);
            req.setSmsType(smsType.longValue());
            req.setInternational(international.longValue());
            req.setRemark(remark);
            return client.AddSmsTemplate(req);
        } catch (TencentCloudSDKException e) {
            throw new Exception("创建短信模板失败: " + e.getMessage(), e);
        }
    }

    @Override
    public PullSmsSendStatusResponse querySendStatus(String smsSdkAppId, Integer limit) throws Exception {
        try {
            SmsClient client = getSmsClient();
            PullSmsSendStatusRequest req = new PullSmsSendStatusRequest();
            req.setSmsSdkAppId(smsSdkAppId);
            req.setLimit(limit.longValue());
            return client.PullSmsSendStatus(req);
        } catch (TencentCloudSDKException e) {
            throw new Exception("查询短信发送状态失败: " + e.getMessage(), e);
        }
    }

    @Override
    public SendStatusStatisticsResponse getSendStatistics(String smsSdkAppId, Integer limit,
                                                          Integer offset, String beginTime, String endTime) throws Exception {
        try {
            SmsClient client = getSmsClient();
            SendStatusStatisticsRequest req = new SendStatusStatisticsRequest();
            req.setSmsSdkAppId(smsSdkAppId);
            req.setLimit(limit.longValue());
            req.setOffset(offset.longValue());
            req.setBeginTime(beginTime);
            req.setEndTime(endTime);
            return client.SendStatusStatistics(req);
        } catch (TencentCloudSDKException e) {
            throw new Exception("获取短信发送统计失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean sendSms(String phoneNumber, String templateId, String[] templateParams) throws Exception {
        try {
            SmsClient client = getSmsClient();
            SendSmsRequest req = new SendSmsRequest();
            req.setSmsSdkAppId(sdkAppId);
            req.setSignName(signName);
            req.setTemplateId(templateId);
            req.setPhoneNumberSet(new String[]{"+86" + phoneNumber});
            req.setTemplateParamSet(templateParams);
            SendSmsResponse resp = client.SendSms(req);
            return "Ok".equalsIgnoreCase(resp.getSendStatusSet()[0].getCode());
        } catch (TencentCloudSDKException e) {
            throw new Exception("发送短信失败: " + e.getMessage(), e);
        }
    }
}