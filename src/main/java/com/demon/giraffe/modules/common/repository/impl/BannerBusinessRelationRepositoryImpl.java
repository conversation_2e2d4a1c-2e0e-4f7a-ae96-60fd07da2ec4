package com.demon.giraffe.modules.common.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.demon.giraffe.modules.common.mapper.BannerBusinessRelationMapper;
import com.demon.giraffe.modules.common.model.entity.BannerBusinessInfo;
import com.demon.giraffe.modules.common.model.po.BannerBusinessRelationPo;
import com.demon.giraffe.modules.common.repository.BannerBusinessRelationRepository;
import com.demon.giraffe.modules.common.model.enums.BannerTypeEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class BannerBusinessRelationRepositoryImpl implements BannerBusinessRelationRepository {

    private final BannerBusinessRelationMapper mapper;

    @Override
    public void saveHomeBannerId(Long bannerId) {
        // 先删除旧的首页关联
        deleteByBusinessType(BannerTypeEnum.HOME, null);
        
        // 创建新关联
        BannerBusinessRelationPo po = new BannerBusinessRelationPo();
        po.setBannerId(bannerId);
        po.setBusinessType(BannerTypeEnum.HOME);
        mapper.insert(po);
    }

    @Override
    public void saveServiceBannerId(Long serviceId, Long bannerId) {
        // 删除该服务已有的关联
        deleteByBusinessType(BannerTypeEnum.SERVICE, serviceId);
        
        // 创建新关联
        BannerBusinessRelationPo po = new BannerBusinessRelationPo();
        po.setBannerId(bannerId);
        po.setBusinessType(BannerTypeEnum.SERVICE);
        po.setBusinessId(serviceId);
        mapper.insert(po);
    }

    @Override
    public Long getHomeBannerId() {
        BannerBusinessRelationPo po = getByBusinessType(BannerTypeEnum.HOME, null);
        return po != null ? po.getBannerId() : null;
    }

    @Override
    public Long getServiceBannerId(Long serviceId) {
        BannerBusinessRelationPo po = getByBusinessType(BannerTypeEnum.SERVICE, serviceId);
        return po != null ? po.getBannerId() : null;
    }

    @Override
    public void deleteByBannerId(Long bannerId) {
        mapper.delete(new LambdaUpdateWrapper<BannerBusinessRelationPo>()
                .eq(BannerBusinessRelationPo::getBannerId, bannerId));
    }

    @Override
    public List<BannerBusinessRelationPo> getBannerRelations(Long bannerId) {
        // 参数校验
        if (bannerId == null || bannerId <= 0) {
            throw new IllegalArgumentException("轮播图ID必须为正数");
        }

        // 构建查询条件
        LambdaQueryWrapper<BannerBusinessRelationPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BannerBusinessRelationPo::getBannerId, bannerId)
                .orderByAsc(BannerBusinessRelationPo::getCreateTime); // 按创建时间排序

        // 执行查询
        List<BannerBusinessRelationPo> relations = mapper.selectList(wrapper);

        // 返回不可修改的列表，防止外部修改
        return Collections.unmodifiableList(relations);
    }

    private BannerBusinessRelationPo getByBusinessType(BannerTypeEnum type, Long businessId) {
        LambdaQueryWrapper<BannerBusinessRelationPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BannerBusinessRelationPo::getBusinessType, type);
        
        if (type == BannerTypeEnum.SERVICE && businessId != null) {
            wrapper.eq(BannerBusinessRelationPo::getBusinessId, businessId);
        }
        
        return mapper.selectOne(wrapper);
    }

    private void deleteByBusinessType(BannerTypeEnum type, Long businessId) {
        LambdaUpdateWrapper<BannerBusinessRelationPo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(BannerBusinessRelationPo::getBusinessType, type);
        
        if (type == BannerTypeEnum.SERVICE && businessId != null) {
            wrapper.eq(BannerBusinessRelationPo::getBusinessId, businessId);
        }
        
        mapper.delete(wrapper);
    }

    @Override
    public Map<Long, Long> batchGetServiceBannerIds(List<Long> serviceIds) {
        if (serviceIds == null || serviceIds.isEmpty()) {
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<BannerBusinessRelationPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(BannerBusinessRelationPo::getBusinessId, BannerBusinessRelationPo::getBannerId)
                .eq(BannerBusinessRelationPo::getBusinessType, BannerTypeEnum.SERVICE)
                .in(BannerBusinessRelationPo::getBusinessId, serviceIds);

        return mapper.selectList(wrapper).stream()
                .collect(Collectors.toMap(
                        BannerBusinessRelationPo::getBusinessId,
                        BannerBusinessRelationPo::getBannerId,
                        (existing, replacement) -> existing // 如果有重复key，保留已存在的
                ));
    }
}