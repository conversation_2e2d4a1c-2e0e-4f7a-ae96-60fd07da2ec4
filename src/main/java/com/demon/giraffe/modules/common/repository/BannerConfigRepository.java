package com.demon.giraffe.modules.common.repository;

import com.demon.giraffe.modules.common.model.po.BannerConfigPo;

import java.util.List;
import java.util.Map;

public interface BannerConfigRepository {

    /**
     * 保存轮播图配置
     *
     * @param po 轮播图配置实体
     * @return 保存后的完整PO对象
     */
    BannerConfigPo save(BannerConfigPo po);

    /**
     * 更新轮播图配置
     *
     * @param po 轮播图配置实体
     * @return 是否更新成功
     */
    boolean update(BannerConfigPo po);

    /**
     * 删除轮播图配置
     *
     * @param id 轮播图配置ID
     * @return 是否删除成功
     */
    boolean delete(Long id);

    /**
     * 根据ID获取轮播图配置
     *
     * @param id 轮播图配置ID
     * @return 轮播图配置实体
     */
    BannerConfigPo getById(Long id);

    /**
     * 获取所有有效的轮播图配置
     *
     * @return 轮播图配置列表
     */
    List<BannerConfigPo> listAllValidBanners();

    /**
     * 批量查询轮播图配置
     * @param ids 轮播图ID列表
     * @return Map<轮播图ID, 轮播图配置>
     */
    Map<Long, BannerConfigPo> batchGetByIds(List<Long> ids);
}