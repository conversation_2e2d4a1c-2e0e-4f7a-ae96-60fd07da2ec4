package com.demon.giraffe.modules.common.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@Schema(description = "轮播图类型")
public enum BannerTypeEnum implements IEnum<String> {
    @Schema(description = "首页")
    HOME("HOME", "首页"),
    
    @Schema(description = "服务页")
    SERVICE("SERVICE", "服务页");

    @EnumValue
    private final String code;
    private final String description;

    public static BannerTypeEnum of(String code) {
        for (BannerTypeEnum type : values()) {
            if (type.code.equals(code)) return type;
        }
        return HOME;
    }

    @Override
    public String getValue() {
        return code;
    }
}