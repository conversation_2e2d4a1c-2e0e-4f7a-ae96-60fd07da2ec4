package com.demon.giraffe.modules.common.model.dto.response;

import com.demon.giraffe.modules.common.model.entity.BannerSingleDetailConfig;
import com.demon.giraffe.modules.common.model.entity.ServiceItemInfo;
import com.demon.giraffe.modules.common.model.enums.BannerTypeEnum;
import com.demon.giraffe.modules.service.model.enums.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 轮播图配置响应参数
 */
@Data
@Builder
@Schema(description = "轮播图配置响应参数")
public class BannerConfigResponse {

    @Schema(description = "轮播图配置ID")
    private Long id;

    @Schema(description = "轮播图标题/名称")
    private String title;

    @Schema(description = "轮播图明细列表")
    private List<BannerSingleDetailConfig> banners;

    @Schema(description = "轮播图状态")
    private CommonStatusEnum status;

    @Schema(description = "服务绑定信息 轮播的绑定对象")
    private List<Long> serviceItemList;

    @Schema(description = "是否绑定首页")
    private Boolean bindHome;

}