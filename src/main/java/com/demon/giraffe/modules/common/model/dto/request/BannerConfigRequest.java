package com.demon.giraffe.modules.common.model.dto.request;

import com.demon.giraffe.modules.common.model.entity.BannerSingleDetailConfig;
import com.demon.giraffe.modules.common.model.entity.ServiceItemInfo;
import com.demon.giraffe.modules.common.model.enums.BannerTypeEnum;
import com.demon.giraffe.modules.service.model.enums.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 轮播图配置请求参数
 */
@Data
@Schema(description = "轮播图配置请求参数")
public class BannerConfigRequest {

    @Schema(description = "轮播图标题/名称", required = true)
    @NotBlank(message = "标题不能为空")
    private String title;

    @Schema(description = "轮播图明细列表", required = true)
    @NotNull(message = "轮播图明细不能为空")
    private List<BannerSingleDetailConfig> banners;

    @Schema(description = "轮播图类型", required = true)
    @NotNull(message = "轮播图类型不能为空")
    private BannerTypeEnum type;

    @Schema(description = "服务绑定信息 轮播的绑定对象")
    private ServiceItemInfo serviceItemInfo;

    @Schema(description = "轮播图启用状态（ENABLE=启用，DISABLE=禁用）")
    private CommonStatusEnum status;
}