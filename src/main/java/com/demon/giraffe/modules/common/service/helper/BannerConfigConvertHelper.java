package com.demon.giraffe.modules.common.service.helper;

import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.common.util.GsonUtil;
import com.demon.giraffe.modules.common.model.dto.request.BannerConfigRequest;
import com.demon.giraffe.modules.common.model.dto.response.BannerConfigResponse;
import com.demon.giraffe.modules.common.model.entity.ServiceItemInfo;
import com.demon.giraffe.modules.common.model.po.BannerBusinessRelationPo;
import com.demon.giraffe.modules.common.model.po.BannerConfigPo;
import com.demon.giraffe.modules.common.model.enums.BannerTypeEnum;
import com.demon.giraffe.modules.service.model.enums.CommonStatusEnum;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 轮播图配置转换工具类
 */
public class BannerConfigConvertHelper {

    /**
     * 将请求参数转换为PO对象
     *
     * @param request 请求参数
     * @return PO对象
     */
    public static BannerConfigPo convertToPo(BannerConfigRequest request) {
        if (request == null) {
            return null;
        }

        BannerConfigPo po = new BannerConfigPo();
        po.setTitle(request.getTitle());
        po.setBanners(request.getBanners());
        po.setStatus(CommonStatusEnum.NORMAL);

        // 根据不同类型设置不同状态
        if (BannerTypeEnum.SERVICE.equals(request.getType())) {
            if (Objects.isNull(request.getServiceItemInfo())) {
                throw new BusinessException("服务的轮播图必须绑定服务");
            }
        }

        return po;
    }

    /**
     * 将PO对象转换为响应对象
     *
     * @param po PO对象
     * @return 响应对象
     */
    public static BannerConfigResponse convertToResponse(BannerConfigPo po, List<BannerBusinessRelationPo> bannerRelations ) {
        if (po == null) {
            return null;
        }

        BannerConfigResponse build = BannerConfigResponse.builder()
                .id(po.getId())
                .title(po.getTitle())
                .banners(po.getBanners())
                .status(po.getStatus())
                .build();
        parseRelations(bannerRelations,build);
        return build;
    }

    public static void parseRelations(List<BannerBusinessRelationPo> bannerRelations,BannerConfigResponse  response) {
        if (bannerRelations == null || bannerRelations.isEmpty()) {
            response.setServiceItemList(Collections.emptyList());
            response.setBindHome(false);

        }

        boolean bindHome = bannerRelations.stream()
                .anyMatch(relation -> relation.getBusinessType() == BannerTypeEnum.HOME);

        List<Long> serviceItemList = bannerRelations.stream()
                .filter(relation -> relation.getBusinessType() == BannerTypeEnum.SERVICE)
                .map(BannerBusinessRelationPo::getBusinessId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        response.setServiceItemList(serviceItemList);
        response.setBindHome(bindHome);

    }

}