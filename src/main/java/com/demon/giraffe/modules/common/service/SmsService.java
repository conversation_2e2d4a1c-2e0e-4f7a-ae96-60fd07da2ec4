package com.demon.giraffe.modules.common.service;

import com.tencentcloudapi.sms.v20210111.models.AddSmsTemplateResponse;
import com.tencentcloudapi.sms.v20210111.models.PullSmsSendStatusResponse;
import com.tencentcloudapi.sms.v20210111.models.SendStatusStatisticsResponse;

/**
 * 短信服务接口
 * 提供完整的短信相关功能，包括模板管理、状态查询和统计功能
 */
public interface SmsService {

    /**
     * 创建短信模板
     * @param templateName 模板名称(例如："腾讯云")
     * @param templateContent 模板内容(例如："{1}为您的登录验证码")
     * @param smsType 短信类型(1:营销 2:通知 3:验证码)
     * @param international 是否国际/港澳台短信(0:国内 1:国际/港澳台)
     * @param remark 模板备注信息
     * @return AddSmsTemplateResponse 腾讯云返回的添加模板响应
     * @throws Exception 添加模板过程中可能抛出的异常
     */
    AddSmsTemplateResponse createTemplate(String templateName, String templateContent,
                                      Integer smsType, Integer international, String remark) throws Exception;

    /**
     * 查询短信发送状态
     * @param smsSdkAppId 短信应用ID(在短信控制台添加应用后生成的实际SdkAppId)
     * @param limit 拉取最大条数，最多100条
     * @return PullSmsSendStatusResponse 腾讯云返回的发送状态响应
     * @throws Exception 查询过程中可能抛出的异常
     */
    PullSmsSendStatusResponse querySendStatus(String smsSdkAppId, Integer limit) throws Exception;

    /**
     * 获取短信发送统计数据
     * @param smsSdkAppId 短信应用ID
     * @param limit 拉取最大条数，最多100条
     * @param offset 偏移量(目前固定设置为0)
     * @param beginTime 开始时间(yyyymmddhh格式，精确到小时)
     * @param endTime 结束时间(yyyymmddhh格式，必须大于beginTime)
     * @return SendStatusStatisticsResponse 腾讯云返回的统计数据响应
     * @throws Exception 获取统计过程中可能抛出的异常
     */
    SendStatusStatisticsResponse getSendStatistics(String smsSdkAppId, Integer limit,
                                               Integer offset, String beginTime, String endTime) throws Exception;

    /**
     * 发送短信
     * @param phoneNumber 目标手机号(带国际区号，如+86)
     * @param templateId 短信模板ID
     * @param templateParams 模板参数数组
     * @return 是否发送成功
     * @throws Exception 发送过程中可能抛出的异常
     */
    boolean sendSms(String phoneNumber, String templateId, String[] templateParams) throws Exception;
}