package com.demon.giraffe.modules.common.model.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Schema(description = "单个轮播图明细")
public class BannerSingleDetailConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "单张图片地址", example = "https://cdn.example.com/banner1.jpg")
    private String imageUrl;

    @Schema(description = "当前图片的顺序（从1开始）", example = "1")
    private Integer sort;

    @Schema(description = "点击图片跳转的地址", example = "https://example.com/promo")
    private String redirectUrl;

    @Schema(description = "图片生效开始时间", example = "1111111111111L")
    private Long startTime;

    @Schema(description = "图片生效截止时间", example = "1111111111111L")
    private Long endTime;

    @Schema(description = "图片放映时间（毫秒），用于前端展示轮播时长", example = "3000")
    private Integer duration;
}
