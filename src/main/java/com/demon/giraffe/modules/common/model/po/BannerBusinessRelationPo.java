package com.demon.giraffe.modules.common.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.modules.common.model.enums.BannerTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 轮播图业务关系表实体类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "banner_business_relation", autoResultMap = true)
@Schema(description = "轮播图业务关系实体")
public class BannerBusinessRelationPo extends BasePo {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableField(value = "banner_id")
    @Schema(description = "关联的轮播图ID", example = "123")
    private Long bannerId;

    @TableField(value = "business_type")
    @Schema(description = "业务类型(HOME-首页/SERVICE-服务)",
            example = "HOME",
            allowableValues = {"HOME", "SERVICE"})
    private BannerTypeEnum businessType;

    @TableField(value = "business_id")
    @Schema(description = "业务ID(服务ID，HOME类型时为null)", example = "456")
    private Long businessId;

}