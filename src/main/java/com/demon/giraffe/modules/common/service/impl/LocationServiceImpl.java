package com.demon.giraffe.modules.common.service.impl;

import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.framework.redis.service.RedisService;
import com.demon.giraffe.modules.common.model.entity.DistanceWrapper;
import com.demon.giraffe.modules.common.service.LocationService;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class LocationServiceImpl implements LocationService {

    // 经纬度Map键名
    public static final String LONGITUDE_KEY = "longitude";
    public static final String LATITUDE_KEY = "latitude";
    // API 端点常量
    private static final String DISTRICT_API = "https://restapi.amap.com/v3/config/district";
    private static final String GEOCODE_API = "https://restapi.amap.com/v3/geocode/geo";

    // 请求参数常量
    private static final String SUBDISTRICT_PARAM = "subdistrict";
    private static final String EXTENSIONS_PARAM = "extensions";
    private static final String KEY_PARAM = "key";
    private static final String FILTER_PARAM = "filter";
    private static final String KEYWORDS_PARAM = "keywords";
    private static final String ADDRESS_PARAM = "address";

    // 响应字段常量
    private static final String STATUS_FIELD = "status";
    private static final String INFO_FIELD = "info";
    private static final String DISTRICTS_FIELD = "districts";
    private static final String ADCODE_FIELD = "adcode";
    private static final String LOCATION_FIELD = "location";
    private static final String GEOCODES_FIELD = "geocodes";

    // 状态码常量
    private static final String SUCCESS_STATUS = "1";

    // 其他常量
    private static final String BASE_EXTENSION = "base";
    private static final int SUBDISTRICT_LEVEL = 0;
    private static final int CONNECT_TIMEOUT_MS = 5000;
    private static final int READ_TIMEOUT_MS = 10000;
    private static final int COORDINATE_SCALE = 7;
    private static final double EARTH_RADIUS = 6371000;
    private static final String COORDINATE_SEPARATOR = ",";
    // 新增缓存相关常量
    private static final String PROVINCE_CACHE_PREFIX = "location:province:";
    private static final String CITY_CACHE_PREFIX = "location:city:";
    private static final String DISTRICT_CACHE_PREFIX = "location:district:";


    private final RedisService redisService;

    public LocationServiceImpl(RedisService redisService) {
        this.redisService = redisService;
    }

    // 高德地图API Key
    @Value("${amap.api-key}")
    private String apiKey;

    @Override
    public Map<String, BigDecimal> convertAddressToCoordinate(String province, String city,
                                                              String district, String detailAddress) throws Exception {
        // 构建完整地址
        String fullAddress = buildFullAddress(province, city, district, detailAddress);

        // 构建请求URL
        String url = buildGeocodeUrl(fullAddress);

        // 发送请求并获取响应
        String response = sendHttpRequest(url);

        // 解析响应
        JsonObject json = parseJsonResponse(response);
        validateResponseStatus(json);

        // 提取并处理坐标数据
        return extractAndProcessCoordinates(json);
    }


    /**
     * 获取或查询省份编码（带缓存）
     */
    private String getCachedOrFetchProvinceAdcode(String province) throws Exception {
        String cacheKey = PROVINCE_CACHE_PREFIX + province;

        // 尝试从缓存获取
        String cachedAdcode = redisService.getValue(cacheKey, String.class);
        if (cachedAdcode != null) {
            return cachedAdcode;
        }

        // 缓存不存在，调用API查询
        String adcode = fetchAdcode(province, null);

        // 查询成功则存入缓存
        if (adcode != null) {
            redisService.set(cacheKey, adcode);
        }

        return adcode;
    }

    /**
     * 获取或查询城市编码（带缓存）
     */
    private String getCachedOrFetchCityAdcode(String city, String provinceAdcode) throws Exception {
        String cacheKey = CITY_CACHE_PREFIX + provinceAdcode + ":" + city;

        // 尝试从缓存获取
        String cachedAdcode = redisService.getValue(cacheKey, String.class);
        if (cachedAdcode != null) {
            return cachedAdcode;
        }

        // 缓存不存在，调用API查询
        String adcode = fetchAdcode(city, provinceAdcode);

        // 查询成功则存入缓存
        if (adcode != null) {
            redisService.set(cacheKey, adcode);
        }

        return adcode;
    }

    /**
     * 获取或查询区县编码（带缓存）
     */
    private String getCachedOrFetchDistrictAdcode(String district, String cityAdcode) throws Exception {
        String cacheKey = DISTRICT_CACHE_PREFIX + cityAdcode + ":" + district;

        // 尝试从缓存获取
        String cachedAdcode = redisService.getValue(cacheKey, String.class);
        if (cachedAdcode != null) {
            return cachedAdcode;
        }

        // 缓存不存在，调用API查询
        String adcode = fetchAdcode(district, cityAdcode);

        // 查询成功则存入缓存
        if (adcode != null) {
            redisService.set(cacheKey, adcode);
        }

        return adcode;
    }

    /**
     * 清除行政区划缓存
     */
    public void clearDistrictCache(String province, String city, String district) {
        try {
            // 清除省份缓存
            redisService.deleteKey(PROVINCE_CACHE_PREFIX + province);

            // 查询省份编码用于清除城市缓存
            String provinceAdcode = fetchAdcode(province, null);
            if (provinceAdcode != null) {
                // 清除城市缓存
                redisService.deleteKey(CITY_CACHE_PREFIX + provinceAdcode + ":" + city);

                // 查询城市编码用于清除区县缓存
                String cityAdcode = fetchAdcode(city, provinceAdcode);
                if (cityAdcode != null) {
                    // 清除区县缓存
                    redisService.deleteKey(DISTRICT_CACHE_PREFIX + cityAdcode + ":" + district);
                }
            }
        } catch (Exception e) {
            // 记录错误但不清除缓存
        }
    }


    private void validateResponseStatus(JsonObject json) {
        if (!SUCCESS_STATUS.equals(json.get(STATUS_FIELD).getAsString())) {
            throw new BusinessException("高德API请求失败: " + json.get(INFO_FIELD).getAsString());
        }
    }

    private void validateAdcode(String adcode, String type, String name) {
        if (adcode == null) {
            throw new BusinessException("未找到" + type + "[" + name + "]的行政区划编码");
        }
    }

    @Override
    public Integer getDistrictAddressCode(String province, String city, String district) {
        try {
            // 1. 查询省份编码
            String provinceAdcode = null;
            provinceAdcode = getCachedOrFetchProvinceAdcode(province);
            validateAdcode(provinceAdcode, "省份", province);

            // 2. 查询城市编码
            String cityAdcode = getCachedOrFetchCityAdcode(city, provinceAdcode);
            validateAdcode(cityAdcode, "城市", city);

            // 3. 查询区县编码
            String districtAdcode = getCachedOrFetchDistrictAdcode(district, cityAdcode);
            validateAdcode(districtAdcode, "区县", district);

            return Integer.parseInt(districtAdcode);
        } catch (Exception e) {
            throw new BusinessException("区域未开放");
        }
    }

    /**
     * 解析区县编码到CountyEnum
     *
     * @param province 省份名称
     * @param city     城市名称
     * @param district 区县名称
     * @return CountyEnum 对应的区县枚举
     * @throws BusinessException 当区域未开放或编码无效时抛出
     */
    public CountyEnum resolveCountyCode(String province, String city, String district) {
        Integer addressCode = getDistrictAddressCode(province, city, district);
        return CountyEnum.fromCode(addressCode);
    }

    @Override
    public double calculateDistance(BigDecimal lon1, BigDecimal lat1,
                                    BigDecimal lon2, BigDecimal lat2) {
        // 将BigDecimal转换为double（保留高精度）
        double lon1Rad = Math.toRadians(lon1.doubleValue());
        double lat1Rad = Math.toRadians(lat1.doubleValue());
        double lon2Rad = Math.toRadians(lon2.doubleValue());
        double lat2Rad = Math.toRadians(lat2.doubleValue());

        // 使用Haversine公式计算距离
        double dLon = lon2Rad - lon1Rad;
        double dLat = lat2Rad - lat1Rad;

        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                        Math.sin(dLon / 2) * Math.sin(dLon / 2);

        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return EARTH_RADIUS * c;
    }

    /**
     * 计算多个位置与单个位置的距离，并过滤排序（泛型版本）
     *
     * @param centerLon    中心点经度
     * @param centerLat    中心点纬度
     * @param points       位置对象列表
     * @param lonExtractor 经度提取函数
     * @param latExtractor 纬度提取函数
     * @param maxDistance  最大距离限制（米）
     * @return 排序后的位置列表（按距离升序）
     */
    public <T> List<DistanceWrapper<T>> calculateDistancesAndFilterGeneric(
            BigDecimal centerLon, BigDecimal centerLat,
            List<T> points,
            Function<T, BigDecimal> lonExtractor,
            Function<T, BigDecimal> latExtractor,
            Double maxDistance) {

        return points.stream()
                .map(point -> {
                    double distance = calculateDistance(
                            centerLon, centerLat,
                            lonExtractor.apply(point),
                            latExtractor.apply(point)
                    );
                    return new DistanceWrapper<>(point, distance);
                })
                .filter(wrapper -> maxDistance == null || wrapper.getDistance() <= maxDistance)
                .sorted(Comparator.comparingDouble(DistanceWrapper::getDistance))
                .collect(Collectors.toList());
    }


    // 以下是私有辅助方法，与之前实现相同，只是移除了@Value依赖
    private String buildFullAddress(String... addressParts) {
        StringBuilder sb = new StringBuilder();
        for (String part : addressParts) {
            if (part != null) {
                sb.append(part);
            }
        }
        String fullAddress = sb.toString().trim();
        if (fullAddress.isEmpty()) {
            throw new BusinessException("地址信息不能全部为空");
        }
        return fullAddress;
    }

    private String buildGeocodeUrl(String address) throws Exception {
        return GEOCODE_API + "?" + ADDRESS_PARAM + "=" + URLEncoder.encode(address, "UTF-8")
                + "&" + KEY_PARAM + "=" + apiKey;
    }

    private String buildDistrictUrl(String keyword, String parentAdcode) throws Exception {
        String url = DISTRICT_API + "?" + KEYWORDS_PARAM + "=" + URLEncoder.encode(keyword, "UTF-8")
                + "&" + SUBDISTRICT_PARAM + "=" + SUBDISTRICT_LEVEL
                + "&" + EXTENSIONS_PARAM + "=" + BASE_EXTENSION
                + "&" + KEY_PARAM + "=" + apiKey;

        if (parentAdcode != null) {
            url += "&" + FILTER_PARAM + "=" + parentAdcode;
        }
        return url;
    }

    private String sendHttpRequest(String url) throws Exception {
        HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(CONNECT_TIMEOUT_MS);
        connection.setReadTimeout(READ_TIMEOUT_MS);

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(connection.getInputStream()))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            return response.toString();
        } finally {
            connection.disconnect();
        }
    }

    private JsonObject parseJsonResponse(String response) {
        return JsonParser.parseString(response).getAsJsonObject();
    }


    private Map<String, BigDecimal> extractAndProcessCoordinates(JsonObject json) {
        JsonArray geocodes = json.getAsJsonArray(GEOCODES_FIELD);
        if (geocodes.size() == 0) {
            throw new RuntimeException("未找到该地址的坐标信息");
        }

        JsonObject firstGeocode = geocodes.get(0).getAsJsonObject();
        String[] location = firstGeocode.get(LOCATION_FIELD).getAsString().split(COORDINATE_SEPARATOR);
        if (location.length != 2) {
            throw new RuntimeException("坐标格式不正确");
        }

        BigDecimal longitude = new BigDecimal(location[0])
                .setScale(COORDINATE_SCALE, RoundingMode.HALF_UP);
        BigDecimal latitude = new BigDecimal(location[1])
                .setScale(COORDINATE_SCALE, RoundingMode.HALF_UP);

        Map<String, BigDecimal> result = new HashMap<>();
        result.put(LONGITUDE_KEY, longitude);
        result.put(LATITUDE_KEY, latitude);
        return result;
    }


    private String fetchAdcode(String keyword, String parentAdcode) throws Exception {
        String url = buildDistrictUrl(keyword, parentAdcode);
        String response = sendHttpRequest(url);
        JsonObject json = parseJsonResponse(response);
        validateResponseStatus(json);

        JsonArray districts = json.getAsJsonArray(DISTRICTS_FIELD);
        if (districts.size() == 0) {
            return null;
        }

        return districts.get(0).getAsJsonObject().get(ADCODE_FIELD).getAsString();
    }


}