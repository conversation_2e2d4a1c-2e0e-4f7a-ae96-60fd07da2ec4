package com.demon.giraffe.modules.laundry.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 洗护流程记录表（P1质量追溯表）
 * 对应数据库表：laundry_process_log
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("laundry_process_log")
public class LaundryProcessLogPo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "工厂订单ID不能为空")
    @TableField("factory_order_id")
    @Schema(description = "工厂订单ID")
    private Long factoryOrderId;

    @NotNull(message = "订单明细ID不能为空")
    @TableField("order_detail_id")
    @Schema(description = "订单明细ID")
    private Long orderDetailId;

    @Size(max = 32)
    @TableField("batch_no")
    @Schema(description = "批次号（冗余存储）")
    private String batchNo;

    @NotNull
    @Min(1)
    @Max(7)
    @TableField("process_step")
    @Schema(description = "处理步骤：1-接收 2-预处理 3-清洗 4-烘干 5-质检 6-包装 7-发货")
    private Integer processStep;

    @NotBlank
    @Size(max = 20)
    @TableField("step_name")
    @Schema(description = "步骤名称")
    private String stepName;

    @NotNull
    @Min(0)
    @Max(4)
    @TableField("step_status")
    @Schema(description = "步骤状态：0-待开始 1-进行中 2-已完成 3-异常 4-已跳过")
    private Integer stepStatus;

    @TableField("step_sequence")
    @Schema(description = "步骤顺序（用于自定义流程）")
    private Integer stepSequence;

    @TableField("operator_id")
    @Schema(description = "操作员ID")
    private Long operatorId;

    @Size(max = 30)
    @TableField("operator_name")
    @Schema(description = "操作员姓名（冗余存储）")
    private String operatorName;

    @Size(max = 20)
    @TableField("operator_role")
    @Schema(description = "操作员角色")
    private String operatorRole;

    @Size(max = 20)
    @TableField("workstation_id")
    @Schema(description = "工位/设备编号")
    private String workstationId;

    @TableField("start_time")
    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @TableField("finish_time")
    @Schema(description = "完成时间")
    private LocalDateTime finishTime;

    @TableField("duration")
    @Schema(description = "处理时长（分钟）")
    private Integer duration;

    @TableField("time_standard")
    @Schema(description = "标准工时（分钟）")
    private Integer timeStandard;

    @TableField("quality_check")
    @Min(1)
    @Max(4)
    @Schema(description = "质检结果：1-优秀 2-良好 3-合格 4-不合格")
    private Integer qualityCheck;

    @TableField("quality_metrics")
    @Schema(description = "质检指标（{\"color\":4,\"texture\":3}）")
    private String qualityMetrics; // JSON字符串

    @Size(max = 200)
    @TableField("issues_found")
    @Schema(description = "发现的问题")
    private String issuesFound;

    @TableField("defect_types")
    @Schema(description = "缺陷类型编码（[101,203]）")
    private String defectTypes; // JSON字符串

    @TableField("before_images")
    @Schema(description = "处理前照片（[{url:\"\",type:1}]）")
    private String beforeImages; // JSON字符串

    @TableField("after_images")
    @Schema(description = "处理后照片（[{url:\"\",type:1}]）")
    private String afterImages; // JSON字符串

    @Size(max = 255)
    @TableField("process_video")
    @Schema(description = "处理视频URL")
    private String processVideo;

    // 系统字段（BasePo中已包含creator, createTime, updater, updateTime）
    // 逻辑删除
    @TableLogic
    @TableField("deleted")
    @Schema(description = "删除标记")
    private Boolean deleted;

}
