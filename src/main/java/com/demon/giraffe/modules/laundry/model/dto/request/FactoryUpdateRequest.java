package com.demon.giraffe.modules.laundry.model.dto.request;

import com.demon.giraffe.common.domain.dto.request.RegionRequest;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.modules.laundry.model.enums.FactoryStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "FactoryUpdateRequest", description = "更新工厂请求DTO")
public class FactoryUpdateRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "工厂id", required = true)
    private Long id;

    @Schema(description = "工厂名称", maxLength = 100)
    @Size(max = 100, message = "工厂名称长度不能超过100个字符")
    private String factoryName;

    /**
     * 区域信息（统一处理，可选更新）
     */
    @Valid
    @Schema(description = "区域信息，留空表示不更新")
    private RegionRequest region;

    @Schema(description = "详细地址", maxLength = 200)
    @Size(max = 200, message = "详细地址长度不能超过200个字符")
    private String detailAddress;

    @Schema(description = "联系人", maxLength = 30)
    @Size(max = 30, message = "联系人长度不能超过30个字符")
    private String contactPerson;

    @Schema(description = "联系电话", maxLength = 20)
    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    private String contactPhone;

    @Schema(description = "状态")
    private FactoryStatusEnum status;

    /**
     * 获取区域编码（兼容性方法）
     */
    public CountyEnum getAddressCode() {
        return region != null ? region.getAddressCode() : null;
    }

    /**
     * 设置区域编码（兼容性方法）
     */
    public void setAddressCode(CountyEnum addressCode) {
        if (region == null) {
            region = new RegionRequest();
        }
        region.setAddressCode(addressCode);
    }

    /**
     * 获取完整地址描述
     */
    public String getFullAddress() {
        return region != null ? region.getFullAddress() + (detailAddress != null ? detailAddress : "") :
               (detailAddress != null ? detailAddress : "");
    }
}
