package com.demon.giraffe.modules.laundry.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 洗护工厂表（P1核心资源表）
 * 对应数据库表：laundry_factory
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("laundry_factory")
public class LaundryFactoryPo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "设施ID不能为空")
    @TableField("facility_id")
    @Schema(description = "关联设施ID")
    private Long facilityId;

    @NotBlank(message = "工厂编号不能为空")
    @Size(max = 20)
    @TableField("factory_no")
    @Schema(description = "工厂编号（规则：LF+6位序列）")
    private String factoryNo;

    @NotBlank(message = "工厂名称不能为空")
    @Size(max = 50)
    @TableField("name")
    @Schema(description = "工厂名称")
    private String name;

    @Size(max = 20)
    @TableField("short_name")
    @Schema(description = "工厂简称")
    private String shortName;

    @NotBlank(message = "联系人不能为空")
    @Size(max = 30)
    @TableField("contact_person")
    @Schema(description = "联系人")
    private String contactPerson;

    @NotBlank(message = "联系电话不能为空")
    @Size(max = 20)
    @TableField("contact_phone")
    @Schema(description = "联系电话（支持分机号）")
    private String contactPhone;

    @Size(max = 20)
    @TableField("emergency_contact")
    @Schema(description = "紧急联系电话")
    private String emergencyContact;

    @Size(max = 20)
    @TableField("service_hotline")
    @Schema(description = "服务热线")
    private String serviceHotline;

    @NotBlank(message = "详细地址不能为空")
    @Size(max = 200)
    @TableField("address")
    @Schema(description = "详细地址")
    private String address;

    @Schema(description = "经度")
    @TableField("longitude")
    private BigDecimal longitude;

    @Schema(description = "纬度")
    @TableField("latitude")
    private BigDecimal latitude;

    @TableField("coverage_areas")
    @Schema(description = "服务覆盖区域（行政区划代码数组）")
    private String coverageAreas; // json字符串，前端或业务层做转换处理

    @Size(max = 50)
    @TableField("business_license")
    @Schema(description = "营业执照号")
    private String businessLicense;

    @Size(max = 255)
    @TableField("license_image")
    @Schema(description = "营业执照照片URL")
    private String licenseImage;

    @TableField("qualification_certs")
    @Schema(description = "资质证书（[{type:1,no:'',image:''}]）")
    private String qualificationCerts; // json字符串

    @NotNull(message = "可提供服务类型不能为空")
    @TableField("service_types")
    @Schema(description = "可提供服务类型（[1,3,5]对应服务分类）")
    private String serviceTypes; // json字符串

    @TableField("special_equipment")
    @Schema(description = "特殊设备（[{\"name\":\"干洗机\",\"count\":2}]）")
    private String specialEquipment; // json字符串

    @NotNull
    @Min(0)
    @TableField("daily_capacity")
    @Schema(description = "日处理能力（件）")
    private Integer dailyCapacity;

    @NotNull
    @Min(0)
    @TableField("current_load")
    @Schema(description = "当前负荷（件）")
    private Integer currentLoad;

    @TableField("max_capacity")
    @Schema(description = "最大弹性产能（件）")
    private Integer maxCapacity;

    @NotNull
    @Min(1)
    @Max(5)
    @TableField("quality_level")
    @Schema(description = "质量等级：1-5星")
    private Integer qualityLevel;

    @NotNull
    @DecimalMin("0.00")
    @TableField("avg_process_time")
    @Schema(description = "平均处理时长（小时）")
    private BigDecimal avgProcessTime;

    @DecimalMin("0.00")
    @TableField("on_time_rate")
    @Schema(description = "准时完成率（%）")
    private BigDecimal onTimeRate;

    @DecimalMin("0.00")
    @TableField("rework_rate")
    @Schema(description = "返工率（%）")
    private BigDecimal reworkRate;

    @Size(max = 100)
    @TableField("work_hours")
    @Schema(description = "工作时间（如：9:00-18:00）")
    private String workHours;

    @TableField("holiday_schedule")
    @Schema(description = "节假日安排")
    private String holidaySchedule; // json字符串

    @NotNull
    @Min(1)
    @Max(3)
    @TableField("status")
    @Schema(description = "状态：1-营业中 2-暂停服务 3-已关闭")
    private Integer status;

}
