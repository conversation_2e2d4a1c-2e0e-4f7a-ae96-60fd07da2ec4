package com.demon.giraffe.modules.laundry.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "工厂状态枚举")
public enum FactoryStatusEnum implements IEnum<Integer> {
    NORMAL(0, "正常"),
    MAINTENANCE(1, "维护中"),
    CLOSED(2, "已关闭");

    @EnumValue
    private final Integer code;
    private final String desc;

    FactoryStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getValue() {
        return code;
    }
}