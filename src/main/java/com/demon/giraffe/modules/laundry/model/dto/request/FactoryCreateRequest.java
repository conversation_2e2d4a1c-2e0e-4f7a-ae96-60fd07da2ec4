package com.demon.giraffe.modules.laundry.model.dto.request;

import com.demon.giraffe.common.domain.dto.request.RegionRequest;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "FactoryCreateRequest", description = "新建工厂请求DTO")
public class FactoryCreateRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "工厂名称", required = true, maxLength = 100)
    @NotBlank(message = "工厂名称不能为空")
    @Size(max = 100, message = "工厂名称长度不能超过100个字符")
    private String factoryName;

    /**
     * 区域信息（统一处理）
     */
    @Valid
    @Schema(description = "区域信息", required = true)
    private RegionRequest region;

    @Schema(description = "详细地址", required = true, maxLength = 200)
    @NotBlank(message = "详细地址不能为空")
    @Size(max = 200, message = "详细地址长度不能超过200个字符")
    private String detailAddress;

    @Schema(description = "联系人", required = true, maxLength = 30)
    @NotBlank(message = "联系人不能为空")
    @Size(max = 30, message = "联系人长度不能超过30个字符")
    private String contactPerson;

    @Schema(description = "联系电话", required = true, maxLength = 20)
    @NotBlank(message = "联系电话不能为空")
    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    private String contactPhone;

    /**
     * 获取区域编码（兼容性方法）
     */
    @Schema(hidden = true)
    public CountyEnum getAddressCode() {
        return region != null ? region.getAddressCode() : null;
    }

    /**
     * 设置区域编码（兼容性方法）
     */
    @Schema(hidden = true)
    public void setAddressCode(CountyEnum addressCode) {
        if (region == null) {
            region = new RegionRequest();
        }
        region.setAddressCode(addressCode);
    }

    /**
     * 获取完整地址描述
     */
    @Schema(hidden = true)
    public String getFullAddress() {
        return region != null ? region.getFullAddress() + detailAddress : detailAddress;
    }
}
