package com.demon.giraffe.modules.laundry.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;

import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.modules.service.model.enums.CleaningTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 衣物单品表PO
 * 用于跟踪每件衣物的处理状态和二维码信息
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("clothing_item")
@Schema(description = "衣物单品实体")
public class ClothingItemPo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "订单ID不能为空")
    @TableField("order_id")
    @Schema(description = "关联订单ID")
    private Long orderId;

    @NotNull(message = "订单任务ID不能为空")
    @TableField("order_task_id")
    @Schema(description = "关联订单任务ID")
    private Long orderTaskId;

    @NotNull(message = "工厂订单ID不能为空")
    @TableField("factory_order_id")
    @Schema(description = "关联工厂订单ID")
    private Long factoryOrderId;

    @NotBlank(message = "衣物编号不能为空")
    @Size(max = 32)
    @TableField("item_no")
    @Schema(description = "衣物编号（规则：CI+年月日+8位序列）")
    private String itemNo;

    @NotBlank(message = "衣物名称不能为空")
    @Size(max = 100)
    @TableField("item_name")
    @Schema(description = "衣物名称")
    private String itemName;

    @NotNull(message = "衣物类型不能为空")
    @TableField("item_type")
    @Schema(description = "衣物类型：1-上衣 2-裤子 3-裙子 4-外套 5-内衣 6-其他")
    private Integer itemType;

    @Size(max = 50)
    @TableField("brand")
    @Schema(description = "品牌")
    private String brand;

    @Size(max = 50)
    @TableField("color")
    @Schema(description = "颜色")
    private String color;

    @Size(max = 20)
    @TableField("size")
    @Schema(description = "尺码")
    private String size;

    @Size(max = 50)
    @TableField("material")
    @Schema(description = "材质")
    private String material;

    @NotNull(message = "清洗类型不能为空")
    @TableField("cleaning_type")
    @Schema(description = "清洗类型")
    private CleaningTypeEnum cleaningType;

    @TableField("qr_code_id")
    @Schema(description = "衣物二维码ID")
    private Long qrCodeId;

    @NotNull(message = "处理状态不能为空")
    @TableField("process_status")
    @Schema(description = "处理状态：0-待处理 1-清洗中 2-烘干中 3-熨烫中 4-质检中 5-已完成 6-异常")
    private Integer processStatus;

    @TableField("process_start_time")
    @Schema(description = "处理开始时间")
    private LocalDateTime processStartTime;

    @TableField("process_end_time")
    @Schema(description = "处理结束时间")
    private LocalDateTime processEndTime;

    @TableField("washing_start_time")
    @Schema(description = "清洗开始时间")
    private LocalDateTime washingStartTime;

    @TableField("washing_end_time")
    @Schema(description = "清洗结束时间")
    private LocalDateTime washingEndTime;

    @TableField("washing_operator_id")
    @Schema(description = "清洗操作员ID")
    private Long washingOperatorId;

    @TableField("drying_start_time")
    @Schema(description = "烘干开始时间")
    private LocalDateTime dryingStartTime;

    @TableField("drying_end_time")
    @Schema(description = "烘干结束时间")
    private LocalDateTime dryingEndTime;

    @TableField("drying_operator_id")
    @Schema(description = "烘干操作员ID")
    private Long dryingOperatorId;

    @TableField("ironing_start_time")
    @Schema(description = "熨烫开始时间")
    private LocalDateTime ironingStartTime;

    @TableField("ironing_end_time")
    @Schema(description = "熨烫结束时间")
    private LocalDateTime ironingEndTime;

    @TableField("ironing_operator_id")
    @Schema(description = "熨烫操作员ID")
    private Long ironingOperatorId;

    @TableField("quality_check_time")
    @Schema(description = "质检时间")
    private LocalDateTime qualityCheckTime;

    @TableField("quality_checker_id")
    @Schema(description = "质检员ID")
    private Long qualityCheckerId;

    @TableField("quality_score")
    @Schema(description = "质量评分（1-5星）")
    private Integer qualityScore;

    @TableField("quality_passed")
    @Schema(description = "质检是否通过")
    private Boolean qualityPassed;

    @Size(max = 500)
    @TableField("quality_notes")
    @Schema(description = "质检备注")
    private String qualityNotes;

    @Size(max = 500)
    @TableField("defect_description")
    @Schema(description = "瑕疵描述")
    private String defectDescription;

    @Size(max = 1000)
    @TableField("process_images")
    @Schema(description = "处理过程照片（JSON格式）")
    private String processImages;

    @Size(max = 500)
    @TableField("special_instructions")
    @Schema(description = "特殊处理说明")
    private String specialInstructions;

    @TableField("reprocess_count")
    @Schema(description = "重新处理次数")
    private Integer reprocessCount;

    @Size(max = 500)
    @TableField("exception_reason")
    @Schema(description = "异常原因")
    private String exceptionReason;

    @NotNull(message = "创建人ID不能为空")
    @TableField("creator")
    @Schema(description = "创建人ID")
    private Long creator;

    @Version
    @TableField("version")
    @Schema(description = "乐观锁版本号")
    private Integer version;

    /**
     * 判断是否已完成处理
     */
    public boolean isProcessCompleted() {
        return processStatus != null && processStatus == 5;
    }

    /**
     * 判断是否有异常
     */
    public boolean hasException() {
        return processStatus != null && processStatus == 6;
    }

    /**
     * 判断是否正在处理中
     */
    public boolean isInProcess() {
        return processStatus != null && processStatus >= 1 && processStatus <= 4;
    }

    /**
     * 判断质检是否通过
     */
    public boolean isQualityPassed() {
        return qualityPassed != null && qualityPassed;
    }

    /**
     * 获取处理进度百分比
     */
    public Double getProcessProgressPercentage() {
        if (processStatus == null) {
            return 0.0;
        }
        
        switch (processStatus) {
            case 0: return 0.0;   // 待处理
            case 1: return 20.0;  // 清洗中
            case 2: return 40.0;  // 烘干中
            case 3: return 60.0;  // 熨烫中
            case 4: return 80.0;  // 质检中
            case 5: return 100.0; // 已完成
            case 6: return 0.0;   // 异常
            default: return 0.0;
        }
    }
}
