package com.demon.giraffe.modules.laundry.model.dto.query;

import com.demon.giraffe.modules.service.model.enums.CleaningTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 衣物单品查询条件
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "衣物单品查询条件")
public class ClothingItemQuery {

    @Schema(description = "衣物ID")
    private Long id;

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "订单任务ID")
    private Long orderTaskId;

    @Schema(description = "工厂订单ID")
    private Long factoryOrderId;

    @Schema(description = "衣物编号")
    private String itemNo;

    @Schema(description = "衣物名称（模糊查询）")
    private String itemName;

    @Schema(description = "衣物类型")
    private Integer itemType;

    @Schema(description = "衣物类型列表")
    private List<Integer> itemTypes;

    @Schema(description = "品牌（模糊查询）")
    private String brand;

    @Schema(description = "颜色")
    private String color;

    @Schema(description = "尺码")
    private String size;

    @Schema(description = "材质")
    private String material;

    @Schema(description = "清洗类型")
    private CleaningTypeEnum cleaningType;

    @Schema(description = "清洗类型列表")
    private List<CleaningTypeEnum> cleaningTypes;

    @Schema(description = "处理状态")
    private Integer processStatus;

    @Schema(description = "处理状态列表")
    private List<Integer> processStatuses;

    @Schema(description = "清洗操作员ID")
    private Long washingOperatorId;

    @Schema(description = "烘干操作员ID")
    private Long dryingOperatorId;

    @Schema(description = "熨烫操作员ID")
    private Long ironingOperatorId;

    @Schema(description = "质检员ID")
    private Long qualityCheckerId;

    @Schema(description = "质检是否通过")
    private Boolean qualityPassed;

    @Schema(description = "质量评分最小值")
    private Integer minQualityScore;

    @Schema(description = "质量评分最大值")
    private Integer maxQualityScore;

    @Schema(description = "重新处理次数最小值")
    private Integer minReprocessCount;

    @Schema(description = "重新处理次数最大值")
    private Integer maxReprocessCount;

    @Schema(description = "处理开始时间-开始")
    private LocalDateTime processStartTimeFrom;

    @Schema(description = "处理开始时间-结束")
    private LocalDateTime processStartTimeTo;

    @Schema(description = "处理结束时间-开始")
    private LocalDateTime processEndTimeFrom;

    @Schema(description = "处理结束时间-结束")
    private LocalDateTime processEndTimeTo;

    @Schema(description = "质检时间-开始")
    private LocalDateTime qualityCheckTimeFrom;

    @Schema(description = "质检时间-结束")
    private LocalDateTime qualityCheckTimeTo;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeFrom;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeTo;

    @Schema(description = "是否有异常")
    private Boolean hasException;

    @Schema(description = "是否已完成")
    private Boolean isCompleted;

    @Schema(description = "是否正在处理中")
    private Boolean isInProcess;

    @Schema(description = "关键词搜索（衣物名称、编号、品牌）")
    private String keyword;

    @Schema(description = "排序字段")
    private String orderBy;

    @Schema(description = "排序方向：ASC/DESC")
    private String orderDirection;

    @Schema(description = "页码")
    private Integer pageNum;

    @Schema(description = "每页大小")
    private Integer pageSize;
}
