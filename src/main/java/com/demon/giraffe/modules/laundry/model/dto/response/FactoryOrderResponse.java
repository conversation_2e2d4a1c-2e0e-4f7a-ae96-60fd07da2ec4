package com.demon.giraffe.modules.laundry.model.dto.response;

import com.demon.giraffe.modules.laundry.model.enums.FactoryOrderStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 工厂订单响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "工厂订单响应")
public class FactoryOrderResponse {

    @Schema(description = "工厂订单ID")
    private Long id;

    @Schema(description = "原订单ID")
    private Long orderId;

    @Schema(description = "工厂ID")
    private Long factoryId;

    @Schema(description = "工厂名称")
    private String factoryName;

    @Schema(description = "批次号")
    private String batchNo;

    @Schema(description = "物品总数")
    private Integer itemCount;

    @Schema(description = "物品详情")
    private String itemDetails;

    @Schema(description = "特殊要求")
    private String specialRequirements;

    @Schema(description = "工厂接收时间")
    private LocalDateTime receiveTime;

    @Schema(description = "开始处理时间")
    private LocalDateTime startTime;

    @Schema(description = "预计完成时间")
    private LocalDateTime estimatedFinishTime;

    @Schema(description = "实际完成时间")
    private LocalDateTime actualFinishTime;

    @Schema(description = "出库时间")
    private LocalDateTime deliveryTime;

    @Schema(description = "主要操作员ID")
    private Long operatorId;

    @Schema(description = "主要操作员姓名")
    private String operatorName;

    @Schema(description = "处理班组")
    private String operatorTeam;

    @Schema(description = "状态")
    private FactoryOrderStatusEnum status;

    @Schema(description = "状态描述")
    private String statusDesc;

    @Schema(description = "当前阶段")
    private Integer currentStage;

    @Schema(description = "当前阶段描述")
    private String currentStageDesc;

    @Schema(description = "质量评分")
    private Integer qualityScore;

    @Schema(description = "质检员ID")
    private Long qcOperator;

    @Schema(description = "质检员姓名")
    private String qcOperatorName;

    @Schema(description = "质检时间")
    private LocalDateTime qcTime;

    @Schema(description = "瑕疵品记录")
    private String defectItems;

    @Schema(description = "处理过程照片")
    private List<String> processImages;

    @Schema(description = "质检报告URL")
    private String qcReport;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "是否最终状态")
    private Boolean isFinalStatus;

    @Schema(description = "是否处理阶段")
    private Boolean isProcessingStage;

    @Schema(description = "是否需要重新处理")
    private Boolean needsReprocessing;

    @Schema(description = "处理进度百分比")
    private Double progressPercentage;

    @Schema(description = "预计剩余时间（小时）")
    private Integer estimatedRemainingHours;

    // 关联的订单信息
    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "会员ID")
    private Long memberId;

    @Schema(description = "会员姓名")
    private String memberName;

    @Schema(description = "订单金额")
    private String orderAmount;

    @Schema(description = "是否加急")
    private Boolean isUrgent;

    @Schema(description = "是否精洗")
    private Boolean isPrecise;

    // 衣物清单
    @Schema(description = "衣物清单")
    private List<ClothingItemResponse> clothingItems;

    // 处理阶段详情
    @Schema(description = "处理阶段列表")
    private List<ProcessStageResponse> processStages;

    // 统计信息
    @Schema(description = "已完成衣物数量")
    private Integer completedItemCount;

    @Schema(description = "质检通过衣物数量")
    private Integer qualityPassedItemCount;

    @Schema(description = "质检不通过衣物数量")
    private Integer qualityFailedItemCount;

    @Schema(description = "异常衣物数量")
    private Integer exceptionItemCount;

    @Schema(description = "平均质量评分")
    private Double averageQualityScore;

    @Schema(description = "总处理时间（小时）")
    private Double totalProcessingHours;

    @Schema(description = "工厂处理效率评分")
    private Integer efficiencyScore;
}
