package com.demon.giraffe.modules.laundry.service;

import com.demon.giraffe.modules.laundry.model.dto.request.ClothingItemRequest;
import com.demon.giraffe.modules.laundry.model.dto.request.FactoryProcessRequest;
import com.demon.giraffe.modules.laundry.model.dto.request.QualityCheckRequest;
import com.demon.giraffe.modules.laundry.model.dto.response.ClothingItemResponse;
import com.demon.giraffe.modules.laundry.model.dto.response.FactoryOrderResponse;
import com.demon.giraffe.modules.laundry.model.dto.response.ProcessStageResponse;
import com.demon.giraffe.modules.laundry.model.enums.FactoryOrderStatusEnum;

import java.util.List;

/**
 * 工厂工作流程服务接口
 * 负责管理工厂内部的订单处理流程
 */
public interface FactoryWorkflowService {

    /**
     * 工厂接收订单
     * 扫描订单二维码，创建工厂订单记录
     * 
     * @param orderQrCodeId 订单二维码ID
     * @param factoryId 工厂ID
     * @param operatorId 操作员ID
     * @return 工厂订单ID
     */
    Long receiveOrder(Long orderQrCodeId, Long factoryId, Long operatorId);

    /**
     * 开始拆包
     * 
     * @param factoryOrderId 工厂订单ID
     * @param operatorId 操作员ID
     * @return 是否成功
     */
    Boolean startUnpacking(Long factoryOrderId, Long operatorId);

    /**
     * 完成拆包并创建衣物清单
     * 
     * @param factoryOrderId 工厂订单ID
     * @param operatorId 操作员ID
     * @param clothingItems 衣物清单
     * @return 创建的衣物ID列表
     */
    List<Long> completeUnpackingAndCreateClothingItems(Long factoryOrderId, Long operatorId, List<ClothingItemRequest> clothingItems);

    /**
     * 为衣物生成二维码
     * 
     * @param clothingItemIds 衣物ID列表
     * @return 生成的二维码ID列表
     */
    List<Long> generateClothingQRCodes(List<Long> clothingItemIds);

    /**
     * 员工扫码开始处理衣物
     * 
     * @param clothingQrCodeId 衣物二维码ID
     * @param operatorId 操作员ID
     * @param processStage 处理阶段：1-清洗 2-烘干 3-熨烫
     * @return 是否成功
     */
    Boolean startClothingProcess(Long clothingQrCodeId, Long operatorId, Integer processStage);

    /**
     * 员工扫码完成处理阶段
     * 
     * @param clothingQrCodeId 衣物二维码ID
     * @param operatorId 操作员ID
     * @param processStage 处理阶段
     * @return 是否成功
     */
    Boolean completeClothingProcess(Long clothingQrCodeId, Long operatorId, Integer processStage);

    /**
     * 开始质检
     * 
     * @param factoryOrderId 工厂订单ID
     * @param qualityCheckerId 质检员ID
     * @return 是否成功
     */
    Boolean startQualityCheck(Long factoryOrderId, Long qualityCheckerId);

    /**
     * 完成质检
     * 
     * @param request 质检请求
     * @return 是否成功
     */
    Boolean completeQualityCheck(QualityCheckRequest request);

    /**
     * 单个衣物质检
     * 
     * @param clothingItemId 衣物ID
     * @param qualityCheckerId 质检员ID
     * @param qualityPassed 是否通过
     * @param qualityScore 质量评分
     * @param qualityNotes 质检备注
     * @return 是否成功
     */
    Boolean checkClothingItemQuality(Long clothingItemId, Long qualityCheckerId, Boolean qualityPassed, Integer qualityScore, String qualityNotes);

    /**
     * 重新处理不合格衣物
     * 
     * @param clothingItemId 衣物ID
     * @param operatorId 操作员ID
     * @param reprocessReason 重新处理原因
     * @return 是否成功
     */
    Boolean reprocessClothingItem(Long clothingItemId, Long operatorId, String reprocessReason);

    /**
     * 开始打包
     * 
     * @param factoryOrderId 工厂订单ID
     * @param operatorId 操作员ID
     * @return 是否成功
     */
    Boolean startPackaging(Long factoryOrderId, Long operatorId);

    /**
     * 完成打包并生成出厂二维码
     * 
     * @param factoryOrderId 工厂订单ID
     * @param operatorId 操作员ID
     * @return 出厂二维码ID
     */
    Long completePackagingAndGenerateExitQR(Long factoryOrderId, Long operatorId);

    /**
     * 更新工厂订单状态
     * 
     * @param factoryOrderId 工厂订单ID
     * @param newStatus 新状态
     * @param operatorId 操作员ID
     * @param notes 备注
     * @return 是否成功
     */
    Boolean updateFactoryOrderStatus(Long factoryOrderId, FactoryOrderStatusEnum newStatus, Long operatorId, String notes);

    /**
     * 处理工厂订单异常
     * 
     * @param factoryOrderId 工厂订单ID
     * @param exceptionCode 异常代码
     * @param exceptionReason 异常原因
     * @param operatorId 操作员ID
     * @return 是否成功
     */
    Boolean handleFactoryOrderException(Long factoryOrderId, String exceptionCode, String exceptionReason, Long operatorId);

    /**
     * 获取工厂订单详情
     * 
     * @param factoryOrderId 工厂订单ID
     * @return 工厂订单详情
     */
    FactoryOrderResponse getFactoryOrderDetail(Long factoryOrderId);

    /**
     * 获取衣物详情
     * 
     * @param clothingItemId 衣物ID
     * @return 衣物详情
     */
    ClothingItemResponse getClothingItemDetail(Long clothingItemId);

    /**
     * 获取工厂待处理订单列表
     * 
     * @param factoryId 工厂ID
     * @param status 状态筛选
     * @return 订单列表
     */
    List<FactoryOrderResponse> getPendingOrders(Long factoryId, FactoryOrderStatusEnum status);

    /**
     * 获取操作员的当前任务
     * 
     * @param operatorId 操作员ID
     * @return 任务列表
     */
    List<ProcessStageResponse> getOperatorCurrentTasks(Long operatorId);

    /**
     * 获取衣物处理历史
     * 
     * @param clothingItemId 衣物ID
     * @return 处理历史
     */
    List<ProcessStageResponse> getClothingProcessHistory(Long clothingItemId);

    /**
     * 批量更新衣物状态
     * 
     * @param request 批量处理请求
     * @return 更新成功的数量
     */
    Integer batchUpdateClothingStatus(FactoryProcessRequest request);

    /**
     * 获取工厂生产统计
     * 
     * @param factoryId 工厂ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 生产统计
     */
    Object getFactoryProductionStats(Long factoryId, String startDate, String endDate);

    /**
     * 检查工厂订单是否超时
     * 
     * @param factoryId 工厂ID
     * @param timeoutHours 超时小时数
     * @return 超时订单列表
     */
    List<Long> checkTimeoutOrders(Long factoryId, Integer timeoutHours);

    /**
     * 处理超时订单
     * 
     * @param factoryOrderIds 超时订单ID列表
     * @return 处理结果
     */
    Boolean handleTimeoutOrders(List<Long> factoryOrderIds);
}
