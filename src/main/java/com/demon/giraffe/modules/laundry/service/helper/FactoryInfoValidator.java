package com.demon.giraffe.modules.laundry.service.helper;

import com.demon.giraffe.modules.laundry.model.po.FactoryInfoPo;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
public class FactoryInfoValidator {

    public boolean validateFactoryCode(FactoryInfoPo po) {
        return StringUtils.hasText(po.getFactoryCode()) && po.getFactoryCode().length() <= 20;
    }

    public boolean validateContactInfo(FactoryInfoPo po) {
        return StringUtils.hasText(po.getContactPerson()) 
                && po.getContactPerson().length() <= 30
                && StringUtils.hasText(po.getContactPhone())
                && po.getContactPhone().length() <= 20;
    }
}