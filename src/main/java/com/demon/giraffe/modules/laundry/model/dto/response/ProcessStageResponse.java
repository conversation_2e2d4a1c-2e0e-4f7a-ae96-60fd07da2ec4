package com.demon.giraffe.modules.laundry.model.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 处理阶段响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "处理阶段响应")
public class ProcessStageResponse {

    @Schema(description = "阶段ID")
    private Long id;

    @Schema(description = "衣物ID")
    private Long clothingItemId;

    @Schema(description = "衣物编号")
    private String itemNo;

    @Schema(description = "衣物名称")
    private String itemName;

    @Schema(description = "工厂订单ID")
    private Long factoryOrderId;

    @Schema(description = "批次号")
    private String batchNo;

    @Schema(description = "阶段类型")
    private Integer stageType;

    @Schema(description = "阶段类型描述")
    private String stageTypeDesc;

    @Schema(description = "阶段名称")
    private String stageName;

    @Schema(description = "阶段状态")
    private Integer stageStatus;

    @Schema(description = "阶段状态描述")
    private String stageStatusDesc;

    @Schema(description = "操作员ID")
    private Long operatorId;

    @Schema(description = "操作员姓名")
    private String operatorName;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "预计耗时（分钟）")
    private Integer estimatedDuration;

    @Schema(description = "实际耗时（分钟）")
    private Integer actualDuration;

    @Schema(description = "质量评分")
    private Integer qualityScore;

    @Schema(description = "质量备注")
    private String qualityNotes;

    @Schema(description = "处理备注")
    private String processNotes;

    @Schema(description = "异常原因")
    private String exceptionReason;

    @Schema(description = "处理过程照片")
    private List<String> processImages;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否已完成")
    private Boolean isCompleted;

    @Schema(description = "是否有异常")
    private Boolean hasException;

    @Schema(description = "是否正在进行中")
    private Boolean isInProgress;

    @Schema(description = "进度百分比")
    private Double progressPercentage;

    @Schema(description = "是否超时")
    private Boolean isOvertime;

    @Schema(description = "超时分钟数")
    private Integer overtimeMinutes;

    // 设备和环境信息
    @Schema(description = "使用设备编号")
    private String equipmentNo;

    @Schema(description = "设备名称")
    private String equipmentName;

    @Schema(description = "工作区域")
    private String workArea;

    @Schema(description = "班组信息")
    private String teamInfo;

    // 处理参数（针对不同阶段）
    @Schema(description = "温度设置")
    private Integer temperature;

    @Schema(description = "洗涤程序")
    private String washProgram;

    @Schema(description = "烘干程序")
    private String dryProgram;

    @Schema(description = "熨烫温度")
    private Integer ironingTemperature;

    @Schema(description = "处理强度")
    private String processIntensity;

    @Schema(description = "使用化学品")
    private List<String> chemicalsUsed;

    // 质量控制信息
    @Schema(description = "质检项目")
    private List<String> qualityCheckItems;

    @Schema(description = "质检标准")
    private String qualityStandard;

    @Schema(description = "是否符合标准")
    private Boolean meetsStandard;

    @Schema(description = "改进建议")
    private String improvementSuggestions;

    // 成本和效率信息
    @Schema(description = "人工成本")
    private Double laborCost;

    @Schema(description = "材料成本")
    private Double materialCost;

    @Schema(description = "能耗成本")
    private Double energyCost;

    @Schema(description = "效率评分")
    private Integer efficiencyScore;

    @Schema(description = "标准用时（分钟）")
    private Integer standardDuration;

    @Schema(description = "效率比率")
    private Double efficiencyRatio;

    // 下一阶段信息
    @Schema(description = "下一阶段类型")
    private Integer nextStageType;

    @Schema(description = "下一阶段名称")
    private String nextStageName;

    @Schema(description = "预计下一阶段开始时间")
    private LocalDateTime nextStageEstimatedStartTime;

    @Schema(description = "是否可以进入下一阶段")
    private Boolean canProceedToNextStage;
}
