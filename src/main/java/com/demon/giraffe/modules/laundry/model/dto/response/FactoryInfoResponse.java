package com.demon.giraffe.modules.laundry.model.dto.response;

import com.demon.giraffe.modules.laundry.model.enums.FactoryStatusEnum;
import com.demon.giraffe.common.domain.dto.response.RegionResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Schema(name = "FactoryInfoResponse", description = "工厂信息响应DTO")
public class FactoryInfoResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "工厂ID")
    private Long id;

    @Schema(description = "工厂编码")
    private String factoryCode;

    @Schema(description = "工厂名称")
    private String factoryName;

    @Schema(description = "地区响应")
    private RegionResponse region;

    @Schema(description = "详细地址")
    private String detailAddress;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "状态")
    private FactoryStatusEnum status;
}