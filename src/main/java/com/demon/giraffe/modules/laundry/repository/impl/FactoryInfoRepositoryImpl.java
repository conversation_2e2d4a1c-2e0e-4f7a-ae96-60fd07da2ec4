package com.demon.giraffe.modules.laundry.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.demon.giraffe.modules.laundry.mapper.FactoryInfoMapper;
import com.demon.giraffe.modules.laundry.model.dto.query.FactoryInfoQuery;
import com.demon.giraffe.modules.laundry.model.po.FactoryInfoPo;
import com.demon.giraffe.modules.laundry.repository.FactoryInfoRepository;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.modules.laundry.model.enums.FactoryStatusEnum;
import com.demon.giraffe.modules.user.mapper.FactoryWorkerMapper;
import com.demon.giraffe.modules.user.mapper.FactoryDirectorMapper;
import com.demon.giraffe.modules.user.mapper.RegionInvestorMapper;
import com.demon.giraffe.modules.user.model.po.FactoryWorkerPo;
import com.demon.giraffe.modules.user.model.po.FactoryDirectorPo;
import com.demon.giraffe.modules.user.model.po.RegionInvestorPo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class FactoryInfoRepositoryImpl implements FactoryInfoRepository {

    private final FactoryInfoMapper factoryInfoMapper;
    private final FactoryWorkerMapper factoryWorkerMapper;
    private final FactoryDirectorMapper factoryDirectorMapper;
    private final RegionInvestorMapper regionInvestorMapper;

    public FactoryInfoRepositoryImpl(FactoryInfoMapper factoryInfoMapper,
                                     FactoryWorkerMapper factoryWorkerMapper,
                                     FactoryDirectorMapper factoryDirectorMapper,
                                     RegionInvestorMapper regionInvestorMapper) {
        this.factoryInfoMapper = factoryInfoMapper;
        this.factoryWorkerMapper = factoryWorkerMapper;
        this.factoryDirectorMapper = factoryDirectorMapper;
        this.regionInvestorMapper = regionInvestorMapper;
    }

    @Override
    public FactoryInfoPo findByFactoryCode(String factoryCode) {
        LambdaQueryWrapper<FactoryInfoPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FactoryInfoPo::getFactoryCode, factoryCode);
        return factoryInfoMapper.selectOne(wrapper);
    }

    @Override
    public IPage<FactoryInfoPo> queryFactoryPage(FactoryInfoQuery query, IPage<FactoryInfoPo> page) {
        LambdaQueryWrapper<FactoryInfoPo> wrapper = buildQueryWrapper(query);
        return factoryInfoMapper.selectPage(page, wrapper);
    }

    @Override
    public List<FactoryInfoPo> findByAddressCode(CountyEnum addressCode) {
        LambdaQueryWrapper<FactoryInfoPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FactoryInfoPo::getAddressCode, addressCode);
        return factoryInfoMapper.selectList(wrapper);
    }

    @Override
    public List<FactoryInfoPo> findByStatus(FactoryStatusEnum status) {
        LambdaQueryWrapper<FactoryInfoPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FactoryInfoPo::getStatus, status);
        return factoryInfoMapper.selectList(wrapper);
    }

    @Override
    public boolean batchDelete(List<Long> ids) {
        return factoryInfoMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public boolean save(FactoryInfoPo po) {
        return factoryInfoMapper.insert(po) > 0;
    }

    @Override
    public boolean updateById(FactoryInfoPo po) {
        return factoryInfoMapper.updateById(po) > 0;
    }

    @Override
    public FactoryInfoPo getById(Long id) {
        return factoryInfoMapper.selectById(id);
    }

    @Override
    public boolean hasFactoryWorkers(Long factoryId) {
        LambdaQueryWrapper<FactoryWorkerPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FactoryWorkerPo::getFactoryId, factoryId);
        return factoryWorkerMapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean hasFactoryDirectors(Long factoryId) {
        LambdaQueryWrapper<FactoryDirectorPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FactoryDirectorPo::getFactoryId, factoryId);
        return factoryDirectorMapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean hasRegionInvestors(Long factoryId) {
        LambdaQueryWrapper<RegionInvestorPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RegionInvestorPo::getFactoryId, factoryId);
        return regionInvestorMapper.selectCount(wrapper) > 0;
    }

    private LambdaQueryWrapper<FactoryInfoPo> buildQueryWrapper(FactoryInfoQuery query) {
        LambdaQueryWrapper<FactoryInfoPo> wrapper = new LambdaQueryWrapper<>();

        if (query != null) {
            if (StringUtils.isNotBlank(query.getFactoryCode())) {
                wrapper.like(FactoryInfoPo::getFactoryCode, query.getFactoryCode());
            }

            if (StringUtils.isNotBlank(query.getFactoryName())) {
                wrapper.like(FactoryInfoPo::getFactoryName, query.getFactoryName());
            }

            if (query.getAddressCode() != null) {
                wrapper.eq(FactoryInfoPo::getAddressCode, query.getAddressCode());
            }

            if (StringUtils.isNotBlank(query.getContactPerson())) {
                wrapper.like(FactoryInfoPo::getContactPerson, query.getContactPerson());
            }

            if (StringUtils.isNotBlank(query.getContactPhone())) {
                wrapper.like(FactoryInfoPo::getContactPhone, query.getContactPhone());
            }

            if (query.getStatus() != null) {
                wrapper.eq(FactoryInfoPo::getStatus, query.getStatus());
            }
        }

        return wrapper;
    }
}