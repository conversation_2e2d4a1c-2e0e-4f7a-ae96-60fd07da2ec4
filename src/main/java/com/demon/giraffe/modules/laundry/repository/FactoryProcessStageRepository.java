package com.demon.giraffe.modules.laundry.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.modules.laundry.model.po.FactoryProcessStagePo;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 工厂处理阶段数据访问接口
 */
public interface FactoryProcessStageRepository {

    /**
     * 保存处理阶段记录
     */
    boolean save(FactoryProcessStagePo po);

    /**
     * 批量保存处理阶段记录
     */
    boolean saveBatch(List<FactoryProcessStagePo> poList);

    /**
     * 根据ID更新处理阶段记录
     */
    boolean updateById(FactoryProcessStagePo po);

    /**
     * 批量更新处理阶段记录
     */
    boolean updateBatchById(List<FactoryProcessStagePo> poList);

    /**
     * 根据ID查询处理阶段记录
     */
    FactoryProcessStagePo getById(Long id);

    /**
     * 根据衣物ID查询处理阶段列表
     */
    List<FactoryProcessStagePo> getByClothingItemId(Long clothingItemId);

    /**
     * 根据工厂订单ID查询处理阶段列表
     */
    List<FactoryProcessStagePo> getByFactoryOrderId(Long factoryOrderId);

    /**
     * 根据阶段类型查询处理阶段列表
     */
    List<FactoryProcessStagePo> getByStageType(Integer stageType);

    /**
     * 根据阶段状态查询处理阶段列表
     */
    List<FactoryProcessStagePo> getByStageStatus(Integer stageStatus);

    /**
     * 根据操作员ID查询处理阶段列表
     */
    List<FactoryProcessStagePo> getByOperatorId(Long operatorId);

    /**
     * 根据衣物ID和阶段类型查询处理阶段
     */
    FactoryProcessStagePo getByClothingItemIdAndStageType(Long clothingItemId, Integer stageType);

    /**
     * 查询操作员当前进行中的任务
     */
    List<FactoryProcessStagePo> getInProgressTasksByOperator(Long operatorId);

    /**
     * 查询指定时间范围内的处理阶段
     */
    List<FactoryProcessStagePo> getByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询超时的处理阶段
     */
    List<FactoryProcessStagePo> getTimeoutStages(LocalDateTime timeoutBefore);

    /**
     * 分页查询处理阶段
     */
    IPage<FactoryProcessStagePo> queryPage(Page<FactoryProcessStagePo> page, Object query);

    /**
     * 统计操作员的工作量
     */
    Object getOperatorWorkloadStats(Long operatorId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计阶段处理效率
     */
    Object getStageEfficiencyStats(Integer stageType, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计工厂订单的处理进度
     */
    Object getFactoryOrderProgress(Long factoryOrderId);

    /**
     * 根据阶段类型和状态统计数量
     */
    Long countByStageTypeAndStatus(Integer stageType, Integer stageStatus);

    /**
     * 根据操作员统计完成的任务数量
     */
    Long countCompletedTasksByOperator(Long operatorId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 批量更新阶段状态
     */
    Integer batchUpdateStageStatus(List<Long> stageIds, Integer newStatus, Long operatorId);

    /**
     * 删除处理阶段记录（软删除）
     */
    boolean deleteById(Long id);

    /**
     * 根据衣物ID删除处理阶段记录（软删除）
     */
    boolean deleteByClothingItemId(Long clothingItemId);

    /**
     * 根据工厂订单ID删除处理阶段记录（软删除）
     */
    boolean deleteByFactoryOrderId(Long factoryOrderId);

    /**
     * 检查处理阶段是否存在
     */
    boolean existsById(Long id);

    /**
     * 检查衣物是否有指定阶段的记录
     */
    boolean existsByClothingItemIdAndStageType(Long clothingItemId, Integer stageType);

    /**
     * 获取衣物的下一个处理阶段
     */
    FactoryProcessStagePo getNextStageForClothingItem(Long clothingItemId);

    /**
     * 获取衣物的当前处理阶段
     */
    FactoryProcessStagePo getCurrentStageForClothingItem(Long clothingItemId);

    /**
     * 检查衣物是否可以进入下一阶段
     */
    boolean canProceedToNextStage(Long clothingItemId, Integer currentStageType);
}
