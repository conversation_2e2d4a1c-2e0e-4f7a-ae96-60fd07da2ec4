package com.demon.giraffe.modules.laundry.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 质检请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "质检请求")
public class QualityCheckRequest {

    @NotNull(message = "工厂订单ID不能为空")
    @Schema(description = "工厂订单ID", required = true)
    private Long factoryOrderId;

    @NotNull(message = "质检员ID不能为空")
    @Schema(description = "质检员ID", required = true)
    private Long qualityCheckerId;

    @Schema(description = "质检员姓名")
    private String qualityCheckerName;

    @Schema(description = "衣物质检详情列表")
    private List<ClothingQualityCheck> clothingQualityChecks;

    @Schema(description = "整体质检是否通过")
    private Boolean overallQualityPassed;

    @Min(value = 1, message = "整体质量评分最小为1")
    @Max(value = 5, message = "整体质量评分最大为5")
    @Schema(description = "整体质量评分（1-5星）")
    private Integer overallQualityScore;

    @Size(max = 1000, message = "整体质检备注长度不能超过1000字符")
    @Schema(description = "整体质检备注")
    private String overallQualityNotes;

    @Schema(description = "质检标准ID")
    private Long qualityStandardId;

    @Schema(description = "质检环境信息")
    private QualityCheckEnvironment environment;

    @Schema(description = "是否需要重新处理")
    private Boolean needsReprocessing;

    @Schema(description = "重新处理原因")
    private String reprocessingReason;

    @Schema(description = "质检图片URL列表")
    private List<String> qualityCheckImages;

    /**
     * 衣物质检详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "衣物质检详情")
    public static class ClothingQualityCheck {

        @NotNull(message = "衣物ID不能为空")
        @Schema(description = "衣物ID", required = true)
        private Long clothingItemId;

        @Schema(description = "衣物编号")
        private String itemNo;

        @Schema(description = "质检是否通过")
        private Boolean qualityPassed;

        @Min(value = 1, message = "质量评分最小为1")
        @Max(value = 5, message = "质量评分最大为5")
        @Schema(description = "质量评分（1-5星）")
        private Integer qualityScore;

        @Size(max = 500, message = "质检备注长度不能超过500字符")
        @Schema(description = "质检备注")
        private String qualityNotes;

        @Size(max = 500, message = "瑕疵描述长度不能超过500字符")
        @Schema(description = "瑕疵描述")
        private String defectDescription;

        @Schema(description = "瑕疵类型：1-污渍未清除 2-破损 3-变形 4-褪色 5-其他")
        private Integer defectType;

        @Schema(description = "瑕疵严重程度：1-轻微 2-中等 3-严重")
        private Integer defectSeverity;

        @Schema(description = "是否可修复")
        private Boolean isRepairable;

        @Schema(description = "修复建议")
        private String repairSuggestion;

        @Schema(description = "质检项目列表")
        private List<String> checkItems;

        @Schema(description = "质检图片URL列表")
        private List<String> itemImages;
    }

    /**
     * 质检环境信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "质检环境信息")
    public static class QualityCheckEnvironment {

        @Schema(description = "质检区域")
        private String checkArea;

        @Schema(description = "光照条件")
        private String lightingCondition;

        @Schema(description = "温度")
        private Integer temperature;

        @Schema(description = "湿度")
        private Integer humidity;

        @Schema(description = "质检设备")
        private String equipment;

        @Schema(description = "质检标准版本")
        private String standardVersion;
    }
}
