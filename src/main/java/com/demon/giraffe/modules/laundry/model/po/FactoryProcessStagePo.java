package com.demon.giraffe.modules.laundry.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;

import com.demon.giraffe.framework.mybatis.base.BasePo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工厂处理阶段表PO
 * 用于跟踪每件衣物在工厂内的详细处理阶段
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("factory_process_stage")
@Schema(description = "工厂处理阶段实体")
public class FactoryProcessStagePo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "衣物ID不能为空")
    @TableField("clothing_item_id")
    @Schema(description = "关联衣物ID")
    private Long clothingItemId;

    @NotNull(message = "工厂订单ID不能为空")
    @TableField("factory_order_id")
    @Schema(description = "关联工厂订单ID")
    private Long factoryOrderId;

    @NotNull(message = "阶段类型不能为空")
    @TableField("stage_type")
    @Schema(description = "阶段类型：1-清洗 2-烘干 3-熨烫 4-质检")
    private Integer stageType;

    @Size(max = 50)
    @TableField("stage_name")
    @Schema(description = "阶段名称")
    private String stageName;

    @NotNull(message = "阶段状态不能为空")
    @TableField("stage_status")
    @Schema(description = "阶段状态：0-待开始 1-进行中 2-已完成 3-异常")
    private Integer stageStatus;

    @TableField("operator_id")
    @Schema(description = "操作员ID")
    private Long operatorId;

    @Size(max = 50)
    @TableField("operator_name")
    @Schema(description = "操作员姓名")
    private String operatorName;

    @TableField("start_time")
    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @TableField("end_time")
    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @TableField("estimated_duration")
    @Schema(description = "预计耗时（分钟）")
    private Integer estimatedDuration;

    @TableField("actual_duration")
    @Schema(description = "实际耗时（分钟）")
    private Integer actualDuration;

    @TableField("quality_score")
    @Schema(description = "质量评分（1-5星）")
    private Integer qualityScore;

    @Size(max = 500)
    @TableField("quality_notes")
    @Schema(description = "质量备注")
    private String qualityNotes;

    @Size(max = 500)
    @TableField("process_notes")
    @Schema(description = "处理备注")
    private String processNotes;

    @Size(max = 500)
    @TableField("exception_reason")
    @Schema(description = "异常原因")
    private String exceptionReason;

    @TableField("process_images")
    @Schema(description = "处理过程照片（JSON格式）")
    private String processImages;

    @NotNull(message = "创建人ID不能为空")
    @TableField("creator")
    @Schema(description = "创建人ID")
    private Long creator;

    @Version
    @TableField("version")
    @Schema(description = "乐观锁版本号")
    private Integer version;

    /**
     * 判断阶段是否已完成
     */
    public boolean isCompleted() {
        return stageStatus != null && stageStatus == 2;
    }

    /**
     * 判断阶段是否有异常
     */
    public boolean hasException() {
        return stageStatus != null && stageStatus == 3;
    }

    /**
     * 判断阶段是否正在进行中
     */
    public boolean isInProgress() {
        return stageStatus != null && stageStatus == 1;
    }

    /**
     * 判断阶段是否待开始
     */
    public boolean isPending() {
        return stageStatus != null && stageStatus == 0;
    }

    /**
     * 计算实际耗时（分钟）
     */
    public Integer calculateActualDuration() {
        if (startTime != null && endTime != null) {
            return (int) java.time.Duration.between(startTime, endTime).toMinutes();
        }
        return null;
    }

    /**
     * 判断是否超时
     */
    public boolean isOvertime() {
        if (estimatedDuration == null || actualDuration == null) {
            return false;
        }
        return actualDuration > estimatedDuration;
    }

    /**
     * 获取超时分钟数
     */
    public Integer getOvertimeMinutes() {
        if (!isOvertime()) {
            return 0;
        }
        return actualDuration - estimatedDuration;
    }

    /**
     * 获取效率比率
     */
    public Double getEfficiencyRatio() {
        if (estimatedDuration == null || actualDuration == null || actualDuration == 0) {
            return null;
        }
        return (double) estimatedDuration / actualDuration;
    }

    /**
     * 获取阶段类型描述
     */
    public String getStageTypeDesc() {
        if (stageType == null) {
            return "未知";
        }
        switch (stageType) {
            case 1: return "清洗";
            case 2: return "烘干";
            case 3: return "熨烫";
            case 4: return "质检";
            default: return "未知";
        }
    }

    /**
     * 获取阶段状态描述
     */
    public String getStageStatusDesc() {
        if (stageStatus == null) {
            return "未知";
        }
        switch (stageStatus) {
            case 0: return "待开始";
            case 1: return "进行中";
            case 2: return "已完成";
            case 3: return "异常";
            default: return "未知";
        }
    }

    /**
     * 获取下一个阶段类型
     */
    public Integer getNextStageType() {
        if (stageType == null) {
            return null;
        }
        switch (stageType) {
            case 1: return 2; // 清洗 -> 烘干
            case 2: return 3; // 烘干 -> 熨烫
            case 3: return 4; // 熨烫 -> 质检
            case 4: return null; // 质检是最后阶段
            default: return null;
        }
    }

    /**
     * 判断是否可以进入下一阶段
     */
    public boolean canProceedToNextStage() {
        return isCompleted() && getNextStageType() != null;
    }
}
