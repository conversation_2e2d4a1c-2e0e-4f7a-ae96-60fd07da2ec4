package com.demon.giraffe.modules.laundry.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.demon.giraffe.common.annotation.ProcessRegion;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.laundry.model.dto.query.FactoryInfoQuery;
import com.demon.giraffe.modules.laundry.model.dto.request.*;
import com.demon.giraffe.modules.laundry.model.dto.response.FactoryInfoResponse;
import com.demon.giraffe.modules.laundry.service.FactoryInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/laundry/factory")
@Tag(name = "工厂管理接口", description = "工厂信息管理相关接口")
public class FactoryInfoController {

    private final FactoryInfoService factoryInfoService;

    public FactoryInfoController(FactoryInfoService factoryInfoService) {
        this.factoryInfoService = factoryInfoService;
    }

    @PostMapping("/create")
    @ProcessRegion
    @Operation(summary = "创建工厂", description = "创建新的工厂信息")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<Long> createFactory(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "工厂创建请求",
                    required = true,
                    content = @Content(schema = @Schema(implementation = FactoryCreateRequest.class))
            )
            @RequestBody @Valid FactoryCreateRequest request) {
        try {
            Long id = factoryInfoService.createFactory(request);
            return ResultBean.success("工厂创建成功", id);
        } catch (Exception e) {
            log.error("创建工厂失败", e);
            return ResultBean.fail("创建失败：" + e.getMessage());
        }
    }

    @PostMapping("/update")
    @ProcessRegion
    @Operation(summary = "更新工厂", description = "更新指定ID的工厂信息")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<Boolean> updateFactory(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "工厂更新请求",
                    required = true,
                    content = @Content(schema = @Schema(implementation = FactoryUpdateRequest.class))
            )
            @RequestBody @Valid FactoryUpdateRequest request) {
        try {
            Boolean result = factoryInfoService.updateFactory(request);
            return ResultBean.success("工厂更新成功", result);
        } catch (Exception e) {
            log.error("更新工厂失败", e);
            return ResultBean.fail("更新失败：" + e.getMessage());
        }
    }

    @GetMapping("/detail/{id}")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    @Operation(summary = "获取工厂详情", description = "根据ID获取工厂详细信息")
    public ResultBean<FactoryInfoResponse> getFactoryDetail(
            @Parameter(description = "工厂ID", required = true)
            @PathVariable Long id) {
        FactoryInfoResponse response = factoryInfoService.getFactoryDetail(id);
        return ResultBean.success(response);
    }

    @PostMapping("/page")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    @Operation(summary = "分页查询工厂信息", description = "根据条件分页查询工厂信息列表")
    public ResultBean<IPage<FactoryInfoResponse>> queryFactoryPage(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "分页查询条件",
                    required = true,
                    content = @Content(schema = @Schema(implementation = BasePageQuery.class))
            )
            @RequestBody @Valid BasePageQuery<FactoryInfoQuery> query) {
        try {
            IPage<FactoryInfoResponse> result = factoryInfoService.queryFactoryPage(query);
            return ResultBean.success("查询成功", result);
        } catch (Exception e) {
            log.error("分页查询工厂信息失败", e);
            return ResultBean.fail("查询失败：" + e.getMessage());
        }
    }



    @PostMapping("/batch-delete")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    @Operation(summary = "批量删除工厂", description = "根据ID列表批量删除工厂信息，删除前会校验工厂是否存在关联的员工、厂长、投资人")
    public ResultBean<Boolean> batchDeleteFactories(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "工厂ID列表",
                    required = true,
                    content = @Content(schema = @Schema(implementation = List.class))
            )
            @RequestBody List<Long> ids) {
        Boolean result = factoryInfoService.batchDeleteFactories(ids);
        return ResultBean.success(result);
    }

    @GetMapping("/by-code/{factoryCode}")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    @Operation(summary = "根据编码查询工厂", description = "根据工厂编码查询工厂信息")
    public ResultBean<FactoryInfoResponse> getByFactoryCode(
            @Parameter(description = "工厂编码", required = true)
            @PathVariable String factoryCode) {
        FactoryInfoResponse response = factoryInfoService.getByFactoryCode(factoryCode);
        return ResultBean.success(response);
    }
}