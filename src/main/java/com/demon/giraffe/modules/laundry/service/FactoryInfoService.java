package com.demon.giraffe.modules.laundry.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.modules.laundry.model.dto.query.FactoryInfoQuery;
import com.demon.giraffe.modules.laundry.model.dto.request.*;
import com.demon.giraffe.modules.laundry.model.dto.response.FactoryInfoResponse;
import com.demon.giraffe.modules.laundry.model.po.FactoryInfoPo;

import java.util.List;

public interface FactoryInfoService {

    /**
     * 创建工厂信息
     *
     * @param request 工厂信息请求参数
     * @return 创建后的工厂ID
     */
    Long createFactory(FactoryCreateRequest request);

    /**
     * 更新工厂信息
     *
     * @param request 工厂信息请求参数
     * @return 是否更新成功
     */
    Boolean updateFactory(FactoryUpdateRequest request);

    /**
     * 获取工厂详情
     *
     * @param id 工厂ID
     * @return 工厂详情响应
     */
    FactoryInfoResponse getFactoryDetail(Long id);

    /**
     * 分页查询工厂信息
     *
     * @param query 查询条件
     * @param page  分页参数
     * @return 分页结果
     */
    IPage<FactoryInfoResponse> queryFactoryPage(FactoryInfoQuery query, IPage<FactoryInfoPo> page);

    /**
     * 分页查询工厂信息（使用BasePageQuery）
     *
     * @param query 分页查询条件
     * @return 分页结果
     */
    IPage<FactoryInfoResponse> queryFactoryPage(BasePageQuery<FactoryInfoQuery> query);

    /**
     * 批量删除工厂信息
     *
     * @param ids 工厂ID列表
     * @return 是否删除成功
     */
    Boolean batchDeleteFactories(List<Long> ids);

    /**
     * 根据工厂编码查询工厂信息
     *
     * @param factoryCode 工厂编码
     * @return 工厂信息响应
     */
    FactoryInfoResponse getByFactoryCode(String factoryCode);
}