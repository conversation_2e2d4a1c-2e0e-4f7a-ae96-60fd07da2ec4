package com.demon.giraffe.modules.laundry.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.modules.laundry.model.dto.query.ClothingItemQuery;
import com.demon.giraffe.modules.laundry.model.po.ClothingItemPo;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 衣物单品数据访问接口
 */
public interface ClothingItemRepository {

    /**
     * 保存衣物单品
     */
    boolean save(ClothingItemPo po);

    /**
     * 批量保存衣物单品
     */
    boolean saveBatch(List<ClothingItemPo> poList);

    /**
     * 根据ID更新衣物单品
     */
    boolean updateById(ClothingItemPo po);

    /**
     * 批量更新衣物单品
     */
    boolean updateBatchById(List<ClothingItemPo> poList);

    /**
     * 根据ID查询衣物单品
     */
    ClothingItemPo getById(Long id);

    /**
     * 根据衣物编号查询衣物单品
     */
    ClothingItemPo getByItemNo(String itemNo);

    /**
     * 根据二维码ID查询衣物单品
     */
    ClothingItemPo getByQrCodeId(Long qrCodeId);

    /**
     * 分页查询衣物单品
     */
    IPage<ClothingItemPo> queryPage(Page<ClothingItemPo> page, ClothingItemQuery query);

    /**
     * 根据订单ID查询衣物列表
     */
    List<ClothingItemPo> getByOrderId(Long orderId);

    /**
     * 根据订单任务ID查询衣物列表
     */
    List<ClothingItemPo> getByOrderTaskId(Long orderTaskId);

    /**
     * 根据工厂订单ID查询衣物列表
     */
    List<ClothingItemPo> getByFactoryOrderId(Long factoryOrderId);

    /**
     * 根据处理状态查询衣物列表
     */
    List<ClothingItemPo> getByProcessStatus(Integer processStatus);

    /**
     * 根据操作员ID查询当前处理的衣物
     */
    List<ClothingItemPo> getByOperator(Long operatorId);

    /**
     * 根据质检员ID查询待质检的衣物
     */
    List<ClothingItemPo> getPendingQualityCheck(Long qualityCheckerId);

    /**
     * 查询质检不通过的衣物
     */
    List<ClothingItemPo> getQualityFailedItems(Long factoryOrderId);

    /**
     * 查询需要重新处理的衣物
     */
    List<ClothingItemPo> getReprocessItems();

    /**
     * 查询超时处理的衣物
     */
    List<ClothingItemPo> getTimeoutItems(LocalDateTime timeoutBefore);

    /**
     * 统计订单的衣物处理进度
     */
    Object getOrderProcessProgress(Long orderId);

    /**
     * 统计工厂订单的衣物处理进度
     */
    Object getFactoryOrderProcessProgress(Long factoryOrderId);

    /**
     * 统计操作员的工作量
     */
    Object getOperatorWorkload(Long operatorId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计质检通过率
     */
    Object getQualityPassRate(Long factoryId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据处理状态统计数量
     */
    Long countByProcessStatus(Integer processStatus);

    /**
     * 根据订单ID统计衣物数量
     */
    Long countByOrderId(Long orderId);

    /**
     * 根据工厂订单ID统计衣物数量
     */
    Long countByFactoryOrderId(Long factoryOrderId);

    /**
     * 批量更新处理状态
     */
    Integer batchUpdateProcessStatus(List<Long> itemIds, Integer newStatus, Long operatorId);

    /**
     * 删除衣物单品（软删除）
     */
    boolean deleteById(Long id);

    /**
     * 根据订单ID删除衣物（软删除）
     */
    boolean deleteByOrderId(Long orderId);

    /**
     * 检查衣物是否存在
     */
    boolean existsById(Long id);

    /**
     * 检查衣物编号是否存在
     */
    boolean existsByItemNo(String itemNo);
}
