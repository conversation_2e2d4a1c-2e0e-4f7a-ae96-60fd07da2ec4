package com.demon.giraffe.modules.laundry.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 工厂处理请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "工厂处理请求")
public class FactoryProcessRequest {

    @NotEmpty(message = "衣物ID列表不能为空")
    @Schema(description = "衣物ID列表", required = true)
    private List<Long> clothingItemIds;

    @NotNull(message = "处理状态不能为空")
    @Schema(description = "处理状态：0-待处理 1-清洗中 2-烘干中 3-熨烫中 4-质检中 5-已完成 6-异常", required = true)
    private Integer processStatus;

    @NotNull(message = "操作员ID不能为空")
    @Schema(description = "操作员ID", required = true)
    private Long operatorId;

    @Schema(description = "操作员姓名")
    private String operatorName;

    @Schema(description = "处理阶段：1-清洗 2-烘干 3-熨烫 4-质检")
    private Integer processStage;

    @Size(max = 500, message = "处理备注长度不能超过500字符")
    @Schema(description = "处理备注")
    private String processNotes;

    @Schema(description = "预计处理时间（分钟）")
    private Integer estimatedDuration;

    @Schema(description = "是否强制更新（忽略状态验证）")
    private Boolean forceUpdate ;

    @Schema(description = "批处理标识")
    private Boolean isBatchProcess ;

    @Size(max = 200, message = "异常原因长度不能超过200字符")
    @Schema(description = "异常原因（当状态为异常时必填）")
    private String exceptionReason;

    @Schema(description = "处理图片URL列表")
    private List<String> processImages;

    @Schema(description = "工厂订单ID")
    private Long factoryOrderId;

    @Schema(description = "班组信息")
    private String teamInfo;

    @Schema(description = "设备编号")
    private String equipmentNo;

    @Schema(description = "温度设置（清洗/烘干时使用）")
    private Integer temperature;

    @Schema(description = "洗涤程序（清洗时使用）")
    private String washProgram;

    @Schema(description = "烘干程序（烘干时使用）")
    private String dryProgram;

    @Schema(description = "熨烫温度（熨烫时使用）")
    private Integer ironingTemperature;
}
