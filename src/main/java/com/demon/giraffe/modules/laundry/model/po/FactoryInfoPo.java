package com.demon.giraffe.modules.laundry.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.modules.laundry.model.enums.FactoryStatusEnum;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("factory_info")
public class FactoryInfoPo extends BasePo {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "工厂编码不能为空")
    @Size(max = 20, message = "工厂编码长度不能超过20个字符")
    @TableField("factory_code")
    private String factoryCode;

    @NotBlank(message = "工厂名称不能为空")
    @Size(max = 100, message = "工厂名称长度不能超过100个字符")
    @TableField("factory_name")
    private String factoryName;

    //这里区域是以县级为单位
    @Schema(description = "区域ID")
    @TableField("address_code")
    private CountyEnum addressCode;

    @NotBlank(message = "详细地址不能为空")
    @Size(max = 200, message = "详细地址长度不能超过200个字符")
    @TableField("detail_address")
    private String detailAddress;

    @Digits(integer = 3, fraction = 7, message = "经度格式不正确")
    @TableField("longitude")
    private BigDecimal longitude;

    @Digits(integer = 3, fraction = 7, message = "纬度格式不正确")
    @TableField("latitude")
    private BigDecimal latitude;

    @NotBlank(message = "联系人不能为空")
    @Size(max = 30, message = "联系人长度不能超过30个字符")
    @TableField("contact_person")
    private String contactPerson;

    @NotBlank(message = "联系电话不能为空")
    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    @TableField("contact_phone")
    private String contactPhone;

    @TableField("status")
    private FactoryStatusEnum status;

}