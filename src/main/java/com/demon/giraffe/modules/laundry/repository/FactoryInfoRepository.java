package com.demon.giraffe.modules.laundry.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.demon.giraffe.modules.laundry.model.dto.query.FactoryInfoQuery;
import com.demon.giraffe.modules.laundry.model.po.FactoryInfoPo;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.modules.laundry.model.enums.FactoryStatusEnum;

import java.util.List;

public interface FactoryInfoRepository {

    FactoryInfoPo findByFactoryCode(String factoryCode);

    IPage<FactoryInfoPo> queryFactoryPage(FactoryInfoQuery query, IPage<FactoryInfoPo> page);

    List<FactoryInfoPo> findByAddressCode(CountyEnum addressCode);

    List<FactoryInfoPo> findByStatus(FactoryStatusEnum status);

    boolean batchDelete(List<Long> ids);

    boolean save(FactoryInfoPo po);

    boolean updateById(FactoryInfoPo po);

    FactoryInfoPo getById(Long id);

    /**
     * 检查工厂是否有关联的员工
     * @param factoryId 工厂ID
     * @return 是否存在关联员工
     */
    boolean hasFactoryWorkers(Long factoryId);

    /**
     * 检查工厂是否有关联的厂长
     * @param factoryId 工厂ID
     * @return 是否存在关联厂长
     */
    boolean hasFactoryDirectors(Long factoryId);

    /**
     * 检查工厂是否有关联的投资人
     * @param factoryId 工厂ID
     * @return 是否存在关联投资人
     */
    boolean hasRegionInvestors(Long factoryId);
}