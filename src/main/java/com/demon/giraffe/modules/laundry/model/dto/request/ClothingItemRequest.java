package com.demon.giraffe.modules.laundry.model.dto.request;

import com.demon.giraffe.modules.service.model.enums.CleaningTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 衣物单品请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "衣物单品请求")
public class ClothingItemRequest {

    @NotBlank(message = "衣物名称不能为空")
    @Size(max = 100, message = "衣物名称长度不能超过100字符")
    @Schema(description = "衣物名称", required = true, example = "白色衬衫")
    private String itemName;

    @NotNull(message = "衣物类型不能为空")
    @Schema(description = "衣物类型：1-上衣 2-裤子 3-裙子 4-外套 5-内衣 6-其他", required = true, example = "1")
    private Integer itemType;

    @Size(max = 50, message = "品牌长度不能超过50字符")
    @Schema(description = "品牌", example = "优衣库")
    private String brand;

    @Size(max = 50, message = "颜色长度不能超过50字符")
    @Schema(description = "颜色", example = "白色")
    private String color;

    @Size(max = 20, message = "尺码长度不能超过20字符")
    @Schema(description = "尺码", example = "L")
    private String size;

    @Size(max = 50, message = "材质长度不能超过50字符")
    @Schema(description = "材质", example = "纯棉")
    private String material;

    @NotNull(message = "清洗类型不能为空")
    @Schema(description = "清洗类型", required = true)
    private CleaningTypeEnum cleaningType;

    @Size(max = 500, message = "特殊处理说明长度不能超过500字符")
    @Schema(description = "特殊处理说明", example = "请轻柔洗涤")
    private String specialInstructions;

    @Schema(description = "是否需要精洗", example = "false")
    private Boolean needsPreciseCleaning;

    @Schema(description = "是否加急处理", example = "false")
    private Boolean isUrgent;

    @Size(max = 200, message = "备注长度不能超过200字符")
    @Schema(description = "备注")
    private String notes;
}
