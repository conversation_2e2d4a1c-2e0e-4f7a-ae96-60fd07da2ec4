package com.demon.giraffe.modules.laundry.model.dto.query;

import com.demon.giraffe.modules.laundry.model.enums.FactoryStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Schema(name = "FactoryInfoQuery", description = "工厂信息查询条件")
public class FactoryInfoQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "工厂编码")
    private String factoryCode;

    @Schema(description = "工厂名称")
    private String factoryName;

    @Schema(description = "区域编码")
    private Integer addressCode;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "状态")
    private FactoryStatusEnum status;
}