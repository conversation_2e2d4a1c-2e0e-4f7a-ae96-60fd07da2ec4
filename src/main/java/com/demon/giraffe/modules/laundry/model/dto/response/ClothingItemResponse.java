package com.demon.giraffe.modules.laundry.model.dto.response;

import com.demon.giraffe.modules.service.model.enums.CleaningTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 衣物单品响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "衣物单品响应")
public class ClothingItemResponse {

    @Schema(description = "衣物ID")
    private Long id;

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "订单任务ID")
    private Long orderTaskId;

    @Schema(description = "工厂订单ID")
    private Long factoryOrderId;

    @Schema(description = "衣物编号")
    private String itemNo;

    @Schema(description = "衣物名称")
    private String itemName;

    @Schema(description = "衣物类型")
    private Integer itemType;

    @Schema(description = "衣物类型描述")
    private String itemTypeDesc;

    @Schema(description = "品牌")
    private String brand;

    @Schema(description = "颜色")
    private String color;

    @Schema(description = "尺码")
    private String size;

    @Schema(description = "材质")
    private String material;

    @Schema(description = "清洗类型")
    private CleaningTypeEnum cleaningType;

    @Schema(description = "清洗类型描述")
    private String cleaningTypeDesc;

    @Schema(description = "二维码ID")
    private Long qrCodeId;

    @Schema(description = "二维码路径")
    private String qrCodePath;

    @Schema(description = "处理状态")
    private Integer processStatus;

    @Schema(description = "处理状态描述")
    private String processStatusDesc;

    @Schema(description = "处理开始时间")
    private LocalDateTime processStartTime;

    @Schema(description = "处理结束时间")
    private LocalDateTime processEndTime;

    @Schema(description = "处理进度百分比")
    private Double processProgressPercentage;

    // 清洗阶段信息
    @Schema(description = "清洗开始时间")
    private LocalDateTime washingStartTime;

    @Schema(description = "清洗结束时间")
    private LocalDateTime washingEndTime;

    @Schema(description = "清洗操作员ID")
    private Long washingOperatorId;

    @Schema(description = "清洗操作员姓名")
    private String washingOperatorName;

    // 烘干阶段信息
    @Schema(description = "烘干开始时间")
    private LocalDateTime dryingStartTime;

    @Schema(description = "烘干结束时间")
    private LocalDateTime dryingEndTime;

    @Schema(description = "烘干操作员ID")
    private Long dryingOperatorId;

    @Schema(description = "烘干操作员姓名")
    private String dryingOperatorName;

    // 熨烫阶段信息
    @Schema(description = "熨烫开始时间")
    private LocalDateTime ironingStartTime;

    @Schema(description = "熨烫结束时间")
    private LocalDateTime ironingEndTime;

    @Schema(description = "熨烫操作员ID")
    private Long ironingOperatorId;

    @Schema(description = "熨烫操作员姓名")
    private String ironingOperatorName;

    // 质检信息
    @Schema(description = "质检时间")
    private LocalDateTime qualityCheckTime;

    @Schema(description = "质检员ID")
    private Long qualityCheckerId;

    @Schema(description = "质检员姓名")
    private String qualityCheckerName;

    @Schema(description = "质量评分")
    private Integer qualityScore;

    @Schema(description = "质检是否通过")
    private Boolean qualityPassed;

    @Schema(description = "质检备注")
    private String qualityNotes;

    @Schema(description = "瑕疵描述")
    private String defectDescription;

    @Schema(description = "处理过程照片")
    private List<String> processImages;

    @Schema(description = "特殊处理说明")
    private String specialInstructions;

    @Schema(description = "重新处理次数")
    private Integer reprocessCount;

    @Schema(description = "异常原因")
    private String exceptionReason;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否已完成处理")
    private Boolean isProcessCompleted;

    @Schema(description = "是否有异常")
    private Boolean hasException;

    @Schema(description = "是否正在处理中")
    private Boolean isInProcess;

    @Schema(description = "是否质检通过")
    private Boolean isQualityPassed;

    // 订单基本信息
    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "会员姓名")
    private String memberName;

    @Schema(description = "工厂名称")
    private String factoryName;

    @Schema(description = "当前处理阶段详情")
    private ProcessStageResponse currentStage;

    @Schema(description = "处理历史")
    private List<ProcessStageResponse> processHistory;
}
