package com.demon.giraffe.modules.laundry.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * 工厂订单状态枚举
 * 用于跟踪工厂内部订单处理状态
 */
@Getter
@Schema(description = "工厂订单状态枚举")
public enum FactoryOrderStatusEnum implements IEnum<Integer> {
    
    @Schema(description = "待接收")
    PENDING_RECEIVE(0, "待接收"),
    
    @Schema(description = "已接收")
    RECEIVED(1, "已接收"),
    
    @Schema(description = "拆包中")
    UNPACKING(2, "拆包中"),
    
    @Schema(description = "已拆包")
    UNPACKED(3, "已拆包"),
    
    @Schema(description = "衣物分类中")
    CATEGORIZING(4, "衣物分类中"),
    
    @Schema(description = "已分类")
    CATEGORIZED(5, "已分类"),
    
    @Schema(description = "处理中")
    PROCESSING(6, "处理中"),
    
    @Schema(description = "清洗中")
    WASHING(7, "清洗中"),
    
    @Schema(description = "烘干中")
    DRYING(8, "烘干中"),
    
    @Schema(description = "熨烫中")
    IRONING(9, "熨烫中"),
    
    @Schema(description = "质检中")
    QUALITY_CHECKING(10, "质检中"),
    
    @Schema(description = "质检通过")
    QUALITY_PASSED(11, "质检通过"),
    
    @Schema(description = "质检不通过")
    QUALITY_FAILED(12, "质检不通过"),
    
    @Schema(description = "重新处理")
    REPROCESSING(13, "重新处理"),
    
    @Schema(description = "打包中")
    PACKAGING(14, "打包中"),
    
    @Schema(description = "已完成")
    COMPLETED(15, "已完成"),
    
    @Schema(description = "异常")
    EXCEPTION(16, "异常"),
    
    @Schema(description = "已取消")
    CANCELLED(17, "已取消");

    @EnumValue
    private final Integer code;
    private final String desc;

    FactoryOrderStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getValue() {
        return code;
    }

    /**
     * 根据代码获取枚举
     */
    public static FactoryOrderStatusEnum of(Integer code) {
        for (FactoryOrderStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("无效的工厂订单状态代码: " + code);
    }

    /**
     * 判断是否是最终状态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == CANCELLED || this == EXCEPTION;
    }

    /**
     * 判断是否是处理阶段
     */
    public boolean isProcessingStage() {
        return code >= PROCESSING.code && code <= QUALITY_PASSED.code;
    }

    /**
     * 判断是否需要重新处理
     */
    public boolean needsReprocessing() {
        return this == QUALITY_FAILED;
    }

    /**
     * 获取下一个状态
     */
    public FactoryOrderStatusEnum getNextStatus() {
        switch (this) {
            case PENDING_RECEIVE:
                return RECEIVED;
            case RECEIVED:
                return UNPACKING;
            case UNPACKING:
                return UNPACKED;
            case UNPACKED:
                return CATEGORIZING;
            case CATEGORIZING:
                return CATEGORIZED;
            case CATEGORIZED:
                return PROCESSING;
            case PROCESSING:
                return WASHING;
            case WASHING:
                return DRYING;
            case DRYING:
                return IRONING;
            case IRONING:
                return QUALITY_CHECKING;
            case QUALITY_CHECKING:
                return QUALITY_PASSED; // 默认通过，实际需要根据质检结果决定
            case QUALITY_PASSED:
                return PACKAGING;
            case QUALITY_FAILED:
                return REPROCESSING;
            case REPROCESSING:
                return PROCESSING;
            case PACKAGING:
                return COMPLETED;
            default:
                return this; // 最终状态不变
        }
    }

    /**
     * 获取质检失败后的状态
     */
    public FactoryOrderStatusEnum getQualityFailedNextStatus() {
        return REPROCESSING;
    }
}
