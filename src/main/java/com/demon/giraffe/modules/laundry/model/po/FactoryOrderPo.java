package com.demon.giraffe.modules.laundry.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工厂订单表（P1生产管理表）
 * 对应数据库表：factory_order
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("factory_order")
public class FactoryOrderPo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "原订单ID不能为空")
    @TableField("order_id")
    @Schema(description = "原订单ID")
    private Long orderId;

    @NotNull(message = "工厂ID不能为空")
    @TableField("factory_id")
    @Schema(description = "工厂ID")
    private Long factoryId;

    @NotBlank(message = "批次号不能为空")
    @Size(max = 32)
    @TableField("batch_no")
    @Schema(description = "批次号（规则：BATCH+年月日+4位序列）")
    private String batchNo;

    @NotNull
    @Min(0)
    @TableField("item_count")
    @Schema(description = "物品总数")
    private Integer itemCount;

    @TableField("item_details")
    @Schema(description = "物品详情（[{id:1,name:\"\",type:1}]）")
    private String itemDetails; // json字符串

    @Size(max = 500)
    @TableField("special_requirements")
    @Schema(description = "特殊要求")
    private String specialRequirements;

    @TableField("receive_time")
    @Schema(description = "工厂接收时间")
    private LocalDateTime receiveTime;

    @TableField("start_time")
    @Schema(description = "开始处理时间")
    private LocalDateTime startTime;

    @TableField("estimated_finish_time")
    @Schema(description = "预计完成时间")
    private LocalDateTime estimatedFinishTime;

    @TableField("actual_finish_time")
    @Schema(description = "实际完成时间")
    private LocalDateTime actualFinishTime;

    @TableField("delivery_time")
    @Schema(description = "出库时间")
    private LocalDateTime deliveryTime;

    @TableField("operator_id")
    @Schema(description = "主要操作员ID")
    private Long operatorId;

    @Size(max = 50)
    @TableField("operator_team")
    @Schema(description = "处理班组")
    private String operatorTeam;

    @NotNull
    @Min(0)
    @Max(6)
    @TableField("status")
    @Schema(description = "状态：0-待接收 1-已接收 2-处理中 3-质检中 4-已完成 5-异常 6-已取消")
    private Integer status;

    @TableField("current_stage")
    @Schema(description = "当前阶段（关联工序表）")
    private Integer currentStage;

    @TableField("quality_score")
    @Min(1)
    @Max(5)
    @Schema(description = "质量评分（1-5星）")
    private Integer qualityScore;

    @TableField("qc_operator")
    @Schema(description = "质检员ID")
    private Long qcOperator;

    @TableField("qc_time")
    @Schema(description = "质检时间")
    private LocalDateTime qcTime;

    @TableField("defect_items")
    @Schema(description = "瑕疵品记录（[{item_id:1,defect_type:3}]）")
    private String defectItems; // json字符串

    @TableField("process_images")
    @Schema(description = "处理过程照片（[{time:\"\",url:\"\",type:1}]）")
    private String processImages; // json字符串

    @Size(max = 255)
    @TableField("qc_report")
    @Schema(description = "质检报告URL")
    private String qcReport;

    @NotNull(message = "创建人ID不能为空")
    @TableField("creator")
    @Schema(description = "创建人ID")
    private Long creator;

    // createTime, updateTime继承自BasePo
    // 版本号和删除标记字段

    @Version
    @TableField("version")
    @Schema(description = "乐观锁版本号")
    private Integer version;


}
