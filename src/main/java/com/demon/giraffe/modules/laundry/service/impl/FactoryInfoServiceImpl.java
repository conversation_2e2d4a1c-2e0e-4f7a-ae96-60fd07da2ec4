package com.demon.giraffe.modules.laundry.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.modules.laundry.model.dto.query.FactoryInfoQuery;
import com.demon.giraffe.modules.laundry.model.dto.request.*;
import com.demon.giraffe.modules.laundry.model.dto.response.FactoryInfoResponse;
import com.demon.giraffe.modules.laundry.model.po.FactoryInfoPo;
import com.demon.giraffe.modules.laundry.repository.FactoryInfoRepository;
import com.demon.giraffe.modules.laundry.service.FactoryInfoService;
import com.demon.giraffe.modules.laundry.service.helper.FactoryInfoConvertHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Service
public class FactoryInfoServiceImpl implements FactoryInfoService {

    private final FactoryInfoRepository factoryInfoRepository;

    private final FactoryInfoConvertHelper factoryInfoConvertHelper;

    public FactoryInfoServiceImpl(FactoryInfoRepository factoryInfoRepository, FactoryInfoConvertHelper factoryInfoConvertHelper) {
        this.factoryInfoRepository = factoryInfoRepository;
        this.factoryInfoConvertHelper = factoryInfoConvertHelper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createFactory(FactoryCreateRequest request) {

        // 数据转换
        FactoryInfoPo po = factoryInfoConvertHelper.convertToPo(request);

        // 持久化
        if (!factoryInfoRepository.save(po)) {
            throw new BusinessException("工厂创建失败");
        }

        return po.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateFactory(FactoryUpdateRequest request) {
        // 校验工厂存在性
        FactoryInfoPo existingPo = factoryInfoRepository.getById(request.getId());
        if (existingPo == null) {
            throw new BusinessException("工厂不存在");
        }
        // 数据转换
        factoryInfoConvertHelper.convertForUpdate(existingPo,request);
        // 更新
        return factoryInfoRepository.updateById(existingPo);
    }

    @Override
    public FactoryInfoResponse getFactoryDetail(Long id) {
        FactoryInfoPo po = factoryInfoRepository.getById(id);
        if (po == null) {
            throw new BusinessException("工厂不存在");
        }
        return factoryInfoConvertHelper.convertToResponse(po);
    }

    @Override
    public IPage<FactoryInfoResponse> queryFactoryPage(FactoryInfoQuery query, IPage<FactoryInfoPo> page) {
        IPage<FactoryInfoPo> poPage = factoryInfoRepository.queryFactoryPage(query, page);
        return poPage.convert(factoryInfoConvertHelper::convertToResponse);
    }

    @Override
    public IPage<FactoryInfoResponse> queryFactoryPage(BasePageQuery<FactoryInfoQuery> query) {
        query.init();

        Page<FactoryInfoPo> page = new Page<>(query.getPage(), query.getPerPage());
        IPage<FactoryInfoPo> poPage = factoryInfoRepository.queryFactoryPage(query.getQuery(), page);

        return poPage.convert(factoryInfoConvertHelper::convertToResponse);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeleteFactories(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("请提供要删除的工厂ID列表");
        }

        // 校验每个工厂是否可以删除
        List<String> cannotDeleteFactories = new ArrayList<>();
        for (Long factoryId : ids) {
            FactoryInfoPo factory = factoryInfoRepository.getById(factoryId);
            if (factory == null) {
                continue; // 工厂不存在，跳过
            }

            List<String> reasons = new ArrayList<>();

            // 检查是否有关联的员工
            if (factoryInfoRepository.hasFactoryWorkers(factoryId)) {
                reasons.add("员工");
            }

            // 检查是否有关联的厂长
            if (factoryInfoRepository.hasFactoryDirectors(factoryId)) {
                reasons.add("厂长");
            }

            // 检查是否有关联的投资人
            if (factoryInfoRepository.hasRegionInvestors(factoryId)) {
                reasons.add("投资人");
            }

            if (!reasons.isEmpty()) {
                cannotDeleteFactories.add(String.format("工厂[%s]存在关联的%s",
                    factory.getFactoryName(), String.join("、", reasons)));
            }
        }

        if (!cannotDeleteFactories.isEmpty()) {
            throw new BusinessException("无法删除工厂：" + String.join("；", cannotDeleteFactories));
        }

        return factoryInfoRepository.batchDelete(ids);
    }

    @Override
    public FactoryInfoResponse getByFactoryCode(String factoryCode) {
        if (!StringUtils.hasText(factoryCode)) {
            throw new BusinessException("工厂编码不能为空");
        }

        FactoryInfoPo po = factoryInfoRepository.findByFactoryCode(factoryCode);
        if (po == null) {
            throw new BusinessException("工厂不存在");
        }
        return factoryInfoConvertHelper.convertToResponse(po);
    }

}