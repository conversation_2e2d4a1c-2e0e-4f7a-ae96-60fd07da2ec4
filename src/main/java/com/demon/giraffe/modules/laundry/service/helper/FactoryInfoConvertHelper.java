package com.demon.giraffe.modules.laundry.service.helper;

import com.demon.giraffe.common.domain.dto.request.RegionRequest;
import com.demon.giraffe.common.domain.dto.response.RegionResponse;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.common.util.CodeGeneratorUtil;
import com.demon.giraffe.modules.common.service.impl.LocationServiceImpl;
import com.demon.giraffe.modules.laundry.model.dto.request.FactoryCreateRequest;
import com.demon.giraffe.modules.laundry.model.dto.request.FactoryUpdateRequest;
import com.demon.giraffe.modules.laundry.model.dto.response.FactoryInfoResponse;
import com.demon.giraffe.modules.laundry.model.enums.FactoryStatusEnum;
import com.demon.giraffe.modules.laundry.model.po.FactoryInfoPo;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Map;

import static com.demon.giraffe.modules.common.service.impl.LocationServiceImpl.LATITUDE_KEY;
import static com.demon.giraffe.modules.common.service.impl.LocationServiceImpl.LONGITUDE_KEY;

@Component
public class FactoryInfoConvertHelper {

    private final CodeGeneratorUtil codeGeneratorUtil;
    private final LocationServiceImpl locationService;

    public FactoryInfoConvertHelper(CodeGeneratorUtil codeGeneratorUtil, LocationServiceImpl locationService) {
        this.codeGeneratorUtil = codeGeneratorUtil;
        this.locationService = locationService;

    }

    /**
     * 创建用：DTO -> Po（自动生成工厂编号与默认状态）
     */
    public FactoryInfoPo convertToPo(FactoryCreateRequest request) {
        RegionRequest region = request.getRegion();
        Map<String, BigDecimal> coordinate;
        try {
            coordinate = locationService.convertAddressToCoordinate(region.getProvince(), region.getCity(), region.getDistrict(), request.getDetailAddress());
        } catch (Exception e) {
            throw new BusinessException("地址坐标解析失败: " + e.getMessage());
        }

        FactoryInfoPo po = new FactoryInfoPo();
        po.setFactoryName(request.getFactoryName());
        po.setAddressCode(request.getAddressCode());
        po.setDetailAddress(request.getDetailAddress());
        po.setLongitude(coordinate.get(LONGITUDE_KEY));
        po.setLatitude(coordinate.get(LATITUDE_KEY));
        po.setContactPerson(request.getContactPerson());
        po.setContactPhone(request.getContactPhone());
        po.setStatus(FactoryStatusEnum.NORMAL);
        po.setFactoryCode(codeGeneratorUtil.generateFactoryCode(request.getAddressCode()));
        return po;
    }


    /**
     * Po -> Response DTO
     */
    public FactoryInfoResponse convertToResponse(FactoryInfoPo po) {
        FactoryInfoResponse response = new FactoryInfoResponse();
        response.setId(po.getId());
        response.setFactoryCode(po.getFactoryCode());
        response.setFactoryName(po.getFactoryName());

        // 转换地区信息
        if (po.getAddressCode() != null) {
            response.setRegion(new RegionResponse(po.getAddressCode()));
        }

        response.setDetailAddress(po.getDetailAddress());
        response.setContactPerson(po.getContactPerson());
        response.setContactPhone(po.getContactPhone());
        response.setStatus(po.getStatus());
        return response;
    }


    public FactoryInfoPo convertForUpdate(FactoryInfoPo existingPo, FactoryUpdateRequest request) {
        if (request.getFactoryName() != null) {
            existingPo.setFactoryName(request.getFactoryName());
        }
        CountyEnum addressCode = existingPo.getAddressCode();
        String detailAddress = existingPo.getDetailAddress();
        boolean tag = false;
        if (request.getRegion() != null && !request.getRegion().getAddressCode().equals(addressCode)) {
            existingPo.setAddressCode(request.getRegion().getAddressCode());
            tag =true;
        }

        if (request.getDetailAddress() != null&& !request.getDetailAddress().equals(detailAddress)) {
            existingPo.setDetailAddress(request.getDetailAddress());
            tag =true;
        }
        if (tag) {
            Map<String, BigDecimal> coordinate;
            try {
                coordinate = locationService.convertAddressToCoordinate(request.getRegion().getProvince(), request.getRegion().getCity(), request.getRegion().getDistrict(), request.getDetailAddress());
            } catch (Exception e) {
                throw new BusinessException("地址坐标解析失败: " + e.getMessage());
            }
            existingPo.setLongitude(coordinate.get(LONGITUDE_KEY));
            existingPo.setLatitude(coordinate.get(LATITUDE_KEY));
        }

        if (request.getContactPerson() != null) {
            existingPo.setContactPerson(request.getContactPerson());
        }

        if (request.getContactPhone() != null) {
            existingPo.setContactPhone(request.getContactPhone());
        }

        if (request.getStatus() != null) {
            existingPo.setStatus(request.getStatus());
        }

        return existingPo;
    }
}
