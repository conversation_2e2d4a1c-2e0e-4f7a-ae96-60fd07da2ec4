package com.demon.giraffe.modules.cabinet.model.dto.request;

import com.demon.giraffe.common.domain.dto.request.RegionRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import lombok.Data;


import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 附近柜子查询请求参数
 */
@Data
@Schema(name = "NearbyCabinetRequest", description = "附近柜子查询请求参数")
public class NearbyCabinetRequest {

    @Schema(
            description = "当前经度(GCJ-02坐标系)",
            example = "116.404",
            required = true
    )
    @NotNull(message = "经度不能为空")
    private BigDecimal longitude;

    @Schema(
            description = "当前纬度(GCJ-02坐标系)",
            example = "39.915",
            required = true
    )
    @NotNull(message = "纬度不能为空")
    private BigDecimal latitude;

    @Schema(
            description = "查询半径(米)，默认1000米，范围[2000, 10000]",
            example = "3000",
            defaultValue = "1000"
    )
    @Min(value = 2000, message = "查询半径不能小于2000米")
    @Max(value = 10000, message = "查询半径不能超过10000米")
    private Integer radius = 1000;



    @Schema(description = "区域信息")
    @NotNull(message = "区域信息不能为空")
    @Valid
    private RegionRequest region;
}
