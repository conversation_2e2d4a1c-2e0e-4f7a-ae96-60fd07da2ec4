package com.demon.giraffe.modules.cabinet.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.demon.giraffe.modules.cabinet.model.po.SmartCabinetPo;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface SmartCabinetMapper extends BaseMapper<SmartCabinetPo> {
    List<SmartCabinetPo> findNearby(BigDecimal longitude, BigDecimal latitude, double distance, int limit);
}