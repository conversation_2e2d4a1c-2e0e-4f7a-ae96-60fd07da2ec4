package com.demon.giraffe.modules.cabinet.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.modules.cabinet.mapper.SmartCabinetMapper;
import com.demon.giraffe.modules.cabinet.model.dto.query.SmartCabinetQuery;
import com.demon.giraffe.modules.cabinet.model.enums.OnlineStatus;
import com.demon.giraffe.modules.cabinet.model.enums.WorkStatus;
import com.demon.giraffe.modules.cabinet.model.po.SmartCabinetPo;
import com.demon.giraffe.modules.cabinet.repository.SmartCabinetRepository;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;

@Repository
public class SmartCabinetRepositoryImpl implements SmartCabinetRepository {

    private final SmartCabinetMapper smartCabinetMapper;

    public SmartCabinetRepositoryImpl(SmartCabinetMapper smartCabinetMapper) {
        this.smartCabinetMapper = smartCabinetMapper;
    }

    @Override
    public SmartCabinetPo save(SmartCabinetPo cabinet) {
        int result = smartCabinetMapper.insert(cabinet);
        if (result <= 0) {
            throw new BusinessException("保存失败");
        }
        return cabinet;
    }

    @Override
    public SmartCabinetPo update(SmartCabinetPo cabinet) {
        smartCabinetMapper.updateById(cabinet);
        return cabinet;
    }

    @Override
    public boolean deleteById(Long id) {
        return smartCabinetMapper.deleteById(id) > 0;
    }

    @Override
    public SmartCabinetPo findById(Long id) {
        return smartCabinetMapper.selectById(id);
    }

    @Override
    public List<SmartCabinetPo> findByRegionInvestorId(Long regionInvestorId) {
        LambdaQueryWrapper<SmartCabinetPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SmartCabinetPo::getRegionInvestorId, regionInvestorId);
        return smartCabinetMapper.selectList(queryWrapper);
    }

    @Override
    public List<SmartCabinetPo> findByAddressCode(CountyEnum addressCode) {
        LambdaQueryWrapper<SmartCabinetPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SmartCabinetPo::getAddressCode, addressCode);
        return smartCabinetMapper.selectList(queryWrapper);
    }

    @Override
    public List<SmartCabinetPo> findNearby(BigDecimal longitude, BigDecimal latitude, double distance, int limit) {
        // 这里实现根据坐标筛选附近的柜子
        // 实际SQL需要根据您的数据库类型实现距离计算
        return smartCabinetMapper.findNearby(longitude, latitude, distance, limit);
    }

    @Override
    public List<SmartCabinetPo> findAvailableByAddressCode(CountyEnum addressCode) {
        LambdaQueryWrapper<SmartCabinetPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SmartCabinetPo::getAddressCode, addressCode)
                .eq(SmartCabinetPo::getOnlineStatus, OnlineStatus.ONLINE)
                .eq(SmartCabinetPo::getWorkStatus, WorkStatus.NORMAL);
        return smartCabinetMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<SmartCabinetPo> queryPage(Page<SmartCabinetPo> page, SmartCabinetQuery query) {
        LambdaQueryWrapper<SmartCabinetPo> wrapper = buildQueryWrapper(query);
        return smartCabinetMapper.selectPage(page, wrapper);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<SmartCabinetPo> buildQueryWrapper(SmartCabinetQuery query) {
        LambdaQueryWrapper<SmartCabinetPo> wrapper = new LambdaQueryWrapper<>();

        if (query != null) {
            // 柜体编号模糊查询
            if (StringUtils.hasText(query.getCabinetNo())) {
                wrapper.like(SmartCabinetPo::getCabinetNo, query.getCabinetNo());
            }

            // 柜体名称模糊查询
            if (StringUtils.hasText(query.getName())) {
                wrapper.like(SmartCabinetPo::getName, query.getName());
            }

            // 在线状态
            if (query.getOnlineStatus() != null) {
                wrapper.eq(SmartCabinetPo::getOnlineStatus, query.getOnlineStatus());
            }

            // 工作状态
            if (query.getWorkStatus() != null) {
                wrapper.eq(SmartCabinetPo::getWorkStatus, query.getWorkStatus());
            }

            // 区域编码
            if (query.getAddressCode() != null) {
                wrapper.eq(SmartCabinetPo::getAddressCode, query.getAddressCode());
            }

            // 投资人ID（从facilityId字段映射）
            if (query.getFacilityId() != null) {
                wrapper.eq(SmartCabinetPo::getRegionInvestorId, query.getFacilityId());
            }
        }

        // 默认按创建时间倒序
        wrapper.orderByDesc(SmartCabinetPo::getCreateTime);
        return wrapper;
    }

}