package com.demon.giraffe.modules.cabinet.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.modules.cabinet.model.enums.*;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 柜体使用记录实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cabinet_usage_log")
@Schema(name = "CabinetUsageLogPo", description = "柜体使用记录实体")
public class CabinetUsageLogPo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /* 设备关联信息 */
    @Schema(description = "关联柜体ID", required = true)
    @TableField("cabinet_id")
    private Long cabinetId;

    @Schema(description = "关联格口ID", required = true)
    @TableField("cell_id")
    private Long cellId;

    /* 业务关联信息 */
    @Schema(description = "关联订单ID")
    @TableField("order_id")
    private Long orderId;

    @Schema(description = "操作用户ID")
    @TableField("user_id")
    private Long userId;

    /* 操作信息 */
    @Schema(description = "操作类型", required = true)
    @TableField("operation_type")
    private OperationType operationType;

    @Schema(description = "操作人类型", required = true)
    @TableField("operator_type")
    private UserRole operatorType;

    /* 安全验证 */
    @Schema(description = "验证码")
    @TableField("verification_code")
    private String verificationCode;

    @Schema(description = "开启方式", required = true)
    @TableField("open_method")
    private OpenMethod openMethod;

    /* 时间记录 */
    @Schema(description = "开门时间", required = true)
    @TableField("open_time")
    private LocalDateTime openTime;

    @Schema(description = "关门时间")
    @TableField("close_time")
    private LocalDateTime closeTime;

    @Schema(description = "使用时长(秒)")
    @TableField("duration")
    private Integer duration;

    @Schema(description = "是否超时", required = true)
    @TableField("is_timeout")
    private Boolean timeout;

    /* 凭证信息 */
    @Schema(description = "操作凭证照片")
    @TableField("images")
    private String images; // 实际存储JSON字符串
}