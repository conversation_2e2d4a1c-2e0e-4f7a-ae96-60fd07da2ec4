package com.demon.giraffe.modules.cabinet.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.modules.cabinet.model.dto.query.CabinetUsageLogQuery;
import com.demon.giraffe.modules.cabinet.model.dto.request.CabinetUsageLogCreateRequest;
import com.demon.giraffe.modules.cabinet.model.dto.response.CabinetUsageLogDetailResponse;
import com.demon.giraffe.modules.cabinet.model.dto.response.CabinetUsageLogPageResponse;
import com.demon.giraffe.modules.cabinet.model.po.CabinetUsageLogPo;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

public interface CabinetUsageLogService extends IService<CabinetUsageLogPo> {
    
    /**
     * 创建柜体使用记录
     * @param request 创建请求参数
     * @return 创建后的记录详情
     */
    CabinetUsageLogDetailResponse createUsageLog(CabinetUsageLogCreateRequest request);
    
    /**
     * 获取柜体使用记录详情
     * @param id 记录ID
     * @return 记录详情
     */
    CabinetUsageLogDetailResponse getUsageLogDetail(Long id);
    
    /**
     * 分页查询柜体使用记录
     * @param query 分页查询条件
     * @return 分页记录列表
     */
    IPage<CabinetUsageLogPageResponse> getUsageLogPage(BasePageQuery<CabinetUsageLogQuery> query);
    
    /**
     * 根据柜体ID查询最近的使用记录
     * @param cabinetId 柜体ID
     * @param limit 查询条数
     * @return 使用记录列表
     */
    List<CabinetUsageLogDetailResponse> getRecentLogsByCabinetId(Long cabinetId, Integer limit);
    
    /**
     * 根据订单ID查询使用记录
     * @param orderId 订单ID
     * @return 使用记录详情
     */
    CabinetUsageLogDetailResponse getLogByOrderId(Long orderId);
}