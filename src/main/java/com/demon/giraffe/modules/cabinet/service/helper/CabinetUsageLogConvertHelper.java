package com.demon.giraffe.modules.cabinet.service.helper;

import com.demon.giraffe.modules.cabinet.model.dto.request.CabinetUsageLogCreateRequest;
import com.demon.giraffe.modules.cabinet.model.dto.response.CabinetUsageLogDetailResponse;
import com.demon.giraffe.modules.cabinet.model.dto.response.CabinetUsageLogPageResponse;
import com.demon.giraffe.modules.cabinet.model.po.CabinetUsageLogPo;
import org.springframework.stereotype.Component;

@Component
public class CabinetUsageLogConvertHelper {

    /**
     * 将创建请求转换为PO
     * @param request 创建请求
     * @return 转换后的PO对象
     */
    public CabinetUsageLogPo convertToPo(CabinetUsageLogCreateRequest request) {
        CabinetUsageLogPo po = new CabinetUsageLogPo();
        po.setCabinetId(request.getCabinetId());
        po.setCellId(request.getCellId());
        po.setOrderId(request.getOrderId());
        po.setUserId(request.getUserId());
        po.setOperationType(request.getOperationType());
        po.setOperatorType(request.getOperatorType());
        po.setVerificationCode(request.getVerificationCode());
        po.setOpenMethod(request.getOpenMethod());
        po.setOpenTime(request.getOpenTime());
        po.setTimeout(request.getTimeout());
        po.setImages(request.getImages());
        return po;
    }

    /**
     * 将PO转换为详情响应
     * @param po 持久化对象
     * @return 详情响应对象
     */
    public CabinetUsageLogDetailResponse convertToDetailResponse(CabinetUsageLogPo po) {
        if (po == null) {
            return null;
        }
        CabinetUsageLogDetailResponse response = new CabinetUsageLogDetailResponse();
        response.setId(po.getId());
        response.setCabinetId(po.getCabinetId());
        response.setCellId(po.getCellId());
        response.setOrderId(po.getOrderId());
        response.setUserId(po.getUserId());
        response.setOperationType(po.getOperationType());
        response.setOperatorType(po.getOperatorType());
        response.setVerificationCode(po.getVerificationCode());
        response.setOpenMethod(po.getOpenMethod());
        response.setOpenTime(po.getOpenTime());
        response.setCloseTime(po.getCloseTime());
        response.setDuration(po.getDuration());
        response.setTimeout(po.getTimeout());
        response.setImages(po.getImages());
        response.setCreateTime(po.getCreateTime());
        return response;
    }

    /**
     * 将PO转换为分页响应
     * @param po 持久化对象
     * @return 分页响应对象
     */
    public CabinetUsageLogPageResponse convertToPageResponse(CabinetUsageLogPo po) {
        CabinetUsageLogPageResponse response = new CabinetUsageLogPageResponse();
        response.setId(po.getId());
        response.setCabinetId(po.getCabinetId());
        response.setCellId(po.getCellId());
        response.setOperationType(po.getOperationType());
        response.setOpenMethod(po.getOpenMethod());
        response.setOpenTime(po.getOpenTime());
        response.setCloseTime(po.getCloseTime());
        response.setTimeout(po.getTimeout());
        return response;
    }
}