package com.demon.giraffe.modules.cabinet.service.helper;

import com.demon.giraffe.common.domain.dto.request.RegionRequest;
import com.demon.giraffe.common.domain.dto.response.RegionResponse;
import com.demon.giraffe.common.domain.enums.*;
import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.common.util.CodeGeneratorUtil;
import com.demon.giraffe.modules.cabinet.model.dto.request.SmartCabinetCreateRequest;
import com.demon.giraffe.modules.cabinet.model.dto.request.SmartCabinetUpdateRequest;
import com.demon.giraffe.modules.cabinet.model.dto.response.CabinetCellResponse;
import com.demon.giraffe.modules.cabinet.model.dto.response.NearbySmartCabinetResponse;
import com.demon.giraffe.modules.cabinet.model.dto.response.SmartCabinetResponse;
import com.demon.giraffe.modules.cabinet.model.enums.*;
import com.demon.giraffe.modules.cabinet.model.po.CabinetCellPo;
import com.demon.giraffe.modules.cabinet.model.po.SmartCabinetPo;
import com.demon.giraffe.modules.common.model.entity.DistanceWrapper;
import com.demon.giraffe.modules.common.service.LocationService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.demon.giraffe.modules.common.service.impl.LocationServiceImpl.LATITUDE_KEY;
import static com.demon.giraffe.modules.common.service.impl.LocationServiceImpl.LONGITUDE_KEY;

/**
 * 智能柜对象转换助手
 * 负责DTO与PO之间的转换及合并操作
 */
@Component
public class SmartCabinetConvertHelper {

    private final CodeGeneratorUtil codeGeneratorUtil;
    private final LocationService locationService;

    public SmartCabinetConvertHelper(CodeGeneratorUtil codeGeneratorUtil, LocationService locationService) {
        this.codeGeneratorUtil = codeGeneratorUtil;
        this.locationService = locationService;
    }


    /**
     * 将创建请求转换为PO对象
     *
     * @param request 创建请求对象
     * @return 转换后的PO对象
     * @throws BusinessException 当地址解析失败时抛出
     */
    public SmartCabinetPo convertToPo(SmartCabinetCreateRequest request) {
        Assert.notNull(request, "创建请求不能为空");
        Assert.notNull(request.getRegion(), "区域信息不能为空");

        SmartCabinetPo po = new SmartCabinetPo();
        // 设置投资人ID，默认为0
        po.setRegionInvestorId(request.getRegionInvestorId() != null ? request.getRegionInvestorId() : 0L);

        // 使用统一区域处理服务解析区域编码
        CountyEnum countyEnum = request.getAddressCode();

        // 生成柜体编号
        po.setCabinetNo(codeGeneratorUtil.generateCabinetCode(countyEnum));
        po.setName(request.getName());
        po.setAddressCode(countyEnum);
        po.setDetailAddress(request.getDetailAddress());

        setAddressInfoFromRegion(po, request.getRegion(), request.getDetailAddress());

        // 设置默认状态
        po.setOnlineStatus(OnlineStatus.OFFLINE);
        po.setWorkStatus(WorkStatus.DISABLED);

        return po;
    }

    /**
     * 合并更新请求到现有PO对象
     * 只更新请求中非空的字段
     *
     * @param existingPo 已存在的PO对象
     * @param request    更新请求对象
     * @return 更新后的PO对象
     * @throws BusinessException 当地址解析失败时抛出
     */
    public SmartCabinetPo mergeUpdateToPo(SmartCabinetPo existingPo, SmartCabinetUpdateRequest request) {
        Assert.notNull(existingPo, "现有PO对象不能为空");
        Assert.notNull(request, "更新请求不能为空");
        SmartCabinetPo smartCabinetPo = new SmartCabinetPo();
        // 更新基础信息（只更新非空字段）

        if (Objects.nonNull(request.getRegionInvestorId())) {
            smartCabinetPo.setRegionInvestorId(request.getRegionInvestorId());
        }
        if (StringUtils.isNotBlank(request.getName())) {
            smartCabinetPo.setName(request.getName());
        }
        if (StringUtils.isNotBlank(request.getDetailAddress())) {
            smartCabinetPo.setDetailAddress(request.getDetailAddress());
        }
        if (request.getOnlineStatus() != null) {
            smartCabinetPo.setOnlineStatus(request.getOnlineStatus());
        }
        if (request.getWorkStatus() != null) {
            smartCabinetPo.setWorkStatus(request.getWorkStatus());
        }
        // 处理地址变更（使用统一区域处理）
        if (request.getRegion() != null) {
            // 使用统一区域处理服务验证和解析区域
            CountyEnum countyEnum = request.getRegion().getAddressCode();
            smartCabinetPo.setAddressCode(countyEnum);

            // 设置地址相关信息（包括经纬度）
            setAddressInfoFromRegion(smartCabinetPo, request.getRegion(),
                    request.getDetailAddress() != null ? request.getDetailAddress() : existingPo.getDetailAddress());
        }

        // 将更新的字段合并到现有PO中
        if (smartCabinetPo.getRegionInvestorId() != null) {
            existingPo.setRegionInvestorId(smartCabinetPo.getRegionInvestorId());
        }
        if (smartCabinetPo.getName() != null) {
            existingPo.setName(smartCabinetPo.getName());
        }
        if (smartCabinetPo.getDetailAddress() != null) {
            existingPo.setDetailAddress(smartCabinetPo.getDetailAddress());
        }
        if (smartCabinetPo.getOnlineStatus() != null) {
            existingPo.setOnlineStatus(smartCabinetPo.getOnlineStatus());
        }
        if (smartCabinetPo.getWorkStatus() != null) {
            existingPo.setWorkStatus(smartCabinetPo.getWorkStatus());
        }
        if (smartCabinetPo.getAddressCode() != null) {
            existingPo.setAddressCode(smartCabinetPo.getAddressCode());
        }
        if (smartCabinetPo.getLongitude() != null) {
            existingPo.setLongitude(smartCabinetPo.getLongitude());
        }
        if (smartCabinetPo.getLatitude() != null) {
            existingPo.setLatitude(smartCabinetPo.getLatitude());
        }

        return existingPo;
    }

    /**
     * 检查地址信息是否有变更
     */
    private boolean isAddressChanged(SmartCabinetUpdateRequest request, SmartCabinetPo existingPo) {
        return request.getRegion() != null || StringUtils.isNotBlank(request.getDetailAddress());
    }

    /**
     * 创建单个格口PO
     *
     * @param cabinetId 柜体ID
     * @param cellType  格口类型
     * @return 格口PO对象
     */
    public CabinetCellPo createCellPo(Long cabinetId, CellType cellType) {
        Assert.notNull(cabinetId, "柜体ID不能为空");
        Assert.notNull(cellType, "格口类型不能为空");

        CabinetCellPo cell = new CabinetCellPo();
        cell.setCabinetId(cabinetId);
        cell.setCellType(cellType);
        cell.setStatus(CellStatus.IDLE);
        cell.setLockStatus(LockStatus.CLOSED);
        return cell;
    }


    /**
     * 使用统一区域处理设置地址信息
     */
    private void setAddressInfoFromRegion(SmartCabinetPo po, RegionRequest region, String detailAddress) {
        try {
            // 处理经纬度
            Map<String, BigDecimal> coordinate = resolveCoordinateFromRegion(region, detailAddress);
            po.setLongitude(coordinate.get(LONGITUDE_KEY));
            po.setLatitude(coordinate.get(LATITUDE_KEY));
        } catch (Exception e) {
            throw new BusinessException("地址信息转换失败: " + e.getMessage());
        }
    }


    /**
     * 从RegionRequest解析经纬度坐标
     */
    private Map<String, BigDecimal> resolveCoordinateFromRegion(RegionRequest region, String detailAddress) {
        try {
            return locationService.convertAddressToCoordinate(
                    region.getProvince(),
                    region.getCity(),
                    region.getDistrict(),
                    detailAddress);
        } catch (Exception e) {
            throw new BusinessException("地址坐标解析失败: " + e.getMessage());
        }
    }

    /**
     * 将智能柜PO列表和格子分组映射转换为响应DTO列表
     *
     * @param cabinets              智能柜PO列表
     * @param cellsGroupByCabinetId 按柜体ID分组的格子列表
     * @return 转换后的响应DTO列表
     */
    public List<SmartCabinetResponse> convertToResponseList(
            List<SmartCabinetPo> cabinets,
            Map<Long, List<CabinetCellPo>> cellsGroupByCabinetId) {

        return cabinets.stream()
                .map(cabinet -> convertToResponse(
                        cabinet,
                        cellsGroupByCabinetId.get(cabinet.getId())
                ))
                .collect(Collectors.toList());
    }

    /**
     * 单个智能柜PO转换为响应DTO
     *
     * @param cabinet 智能柜PO
     * @param cells   关联的格子列表
     * @return 转换后的响应DTO
     */
    public SmartCabinetResponse convertToResponse(
            SmartCabinetPo cabinet,
            List<CabinetCellPo> cells) {
        CountyEnum addressCode = cabinet.getAddressCode();

        return SmartCabinetResponse.builder()
                .id(cabinet.getId())
                .cabinetNo(cabinet.getCabinetNo())
                .name(cabinet.getName())
                .region(new RegionResponse(addressCode))
                .detailAddress(cabinet.getDetailAddress())
                .onlineStatus(cabinet.getOnlineStatus())
                .workStatus(cabinet.getWorkStatus())
                .cabinetCellList(convertCellList(cells))
                .build();
    }

    /**
     * 格子PO列表转换为响应DTO列表
     *
     * @param cells 格子PO列表
     * @return 格子响应DTO列表
     */
    private List<CabinetCellResponse> convertCellList(List<CabinetCellPo> cells) {
        if (cells == null) {
            return List.of();
        }

        return cells.stream()
                .map(this::convertCell)
                .collect(Collectors.toList());
    }

    /**
     * 单个格子PO转换为响应DTO
     *
     * @param cell 格子PO
     * @return 格子响应DTO
     */
    private CabinetCellResponse convertCell(CabinetCellPo cell) {
        return new CabinetCellResponse(
                cell.getId(),
                cell.getCellType(),
                cell.getStatus()
        );
    }

    /**
     * 将带距离的智能柜PO转换为附近智能柜响应DTO
     *
     * @param distanceWrapper 包含距离信息的智能柜包装对象
     * @param cells           关联的格子列表
     * @return 转换后的附近智能柜响应DTO
     */
    public NearbySmartCabinetResponse convertToNearbyResponse(
            DistanceWrapper<SmartCabinetPo> distanceWrapper,
            List<CabinetCellPo> cells) {

        SmartCabinetPo cabinet = distanceWrapper.getPoint();
        Double distance = distanceWrapper.getDistance();

        // 转换地区信息
        RegionResponse regionResponse = null;
        if (cabinet.getAddressCode() != null) {
            regionResponse = new RegionResponse(cabinet.getAddressCode());
        }

        return NearbySmartCabinetResponse.builder()
                .id(cabinet.getId())
                .cabinetNo(cabinet.getCabinetNo())
                .name(cabinet.getName())
                .region(regionResponse)
                .detailAddress(cabinet.getDetailAddress())
                .onlineStatus(cabinet.getOnlineStatus())
                .workStatus(cabinet.getWorkStatus())
                .cabinetCellList(convertCellList(cells))
                .distance(distance)
                .build();
    }

    /**
     * 批量转换带距离的智能柜PO列表为附近智能柜响应DTO列表
     * 重新实现版本：优化性能和错误处理
     *
     * @param distanceWrappers 包含距离信息的智能柜包装对象列表
     * @param cellsMap         柜格信息映射（柜子ID -> 柜格列表）
     * @return 转换后的附近智能柜响应DTO列表
     */
    public List<NearbySmartCabinetResponse> convertToNearbyResponseList(
            List<DistanceWrapper<SmartCabinetPo>> distanceWrappers,
            Map<Long, List<CabinetCellPo>> cellsMap) {

        // 参数校验
        if (distanceWrappers == null || distanceWrappers.isEmpty()) {
            return new ArrayList<>();
        }

        // 确保cellsMap不为null
        Map<Long, List<CabinetCellPo>> safeCellsMap = cellsMap != null ? cellsMap : new HashMap<>();

        // 使用ArrayList预分配容量，提高性能
        List<NearbySmartCabinetResponse> responseList = new ArrayList<>(distanceWrappers.size());

        // 遍历转换，添加详细的错误处理
        for (DistanceWrapper<SmartCabinetPo> wrapper : distanceWrappers) {
            try {
                if (wrapper == null || wrapper.getPoint() == null) {
                    // 跳过无效的wrapper
                    continue;
                }

                SmartCabinetPo cabinet = wrapper.getPoint();
                Long cabinetId = cabinet.getId();

                if (cabinetId == null) {
                    // 跳过没有ID的柜子
                    continue;
                }

                // 获取对应的柜格列表，如果没有则使用空列表
                List<CabinetCellPo> cells = safeCellsMap.getOrDefault(cabinetId, Collections.emptyList());

                // 转换单个对象
                NearbySmartCabinetResponse response = convertToNearbyResponse(wrapper, cells);

                if (response != null) {
                    responseList.add(response);
                }

            } catch (Exception e) {
                // 记录错误但不中断整个转换过程
                // 可以根据需要添加日志记录
                // log.warn("转换智能柜响应时发生错误，柜子ID: {}, 错误: {}",
                //          wrapper.getPoint() != null ? wrapper.getPoint().getId() : "unknown", e.getMessage());
            }
        }

        return responseList;
    }

}