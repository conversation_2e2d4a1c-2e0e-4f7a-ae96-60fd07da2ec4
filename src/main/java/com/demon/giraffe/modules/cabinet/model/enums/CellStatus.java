package com.demon.giraffe.modules.cabinet.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "格口状态枚举")
public enum CellStatus implements IEnum<Integer> {
    @Schema(description = "空闲")
    IDLE(0, "空闲"),

    @Schema(description = "占用")
    OCCUPIED(1, "占用"),

    @Schema(description = "故障")
    FAULT(2, "故障"),

    @Schema(description = "维护")
    MAINTENANCE(3, "维护"),

    @Schema(description = "已停用")
    DISABLED(4, "已停用");

    @EnumValue
    private final int code;
    private final String desc;

    CellStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CellStatus of(int code) {
        for (CellStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("无效的状态码: " + code);
    }

    @Override
    public Integer getValue() {
        return code;
    }
}