package com.demon.giraffe.modules.cabinet.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.modules.cabinet.model.enums.OnlineStatus;
import com.demon.giraffe.modules.cabinet.model.enums.WorkStatus;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 智能柜主表实体
 * 对应表：smart_cabinet
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("smart_cabinet")
@Schema(name = "SmartCabinetPo", description = "智能柜主表实体")
public class SmartCabinetPo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "所属人投资ID")
    @TableField("region_investor_id")
    private Long regionInvestorId;

    @Schema(description = "柜体编号（规则：SC+6位序列）")
    @TableField("cabinet_no")
    private String cabinetNo;

    @Schema(description = "柜体名称（如：A栋1楼快递柜）")
    @TableField("name")
    private String name;

    @Schema(description = "安装区域编码")
    @TableField("address_code")
    private CountyEnum addressCode;

    @Schema(description = "详细安装地址")
    @TableField("detail_address")
    private String detailAddress;

    @Schema(description = "经度(GCJ-02)")
    @TableField("longitude")
    private BigDecimal longitude;

    @Schema(description = "纬度(GCJ-02)")
    @TableField("latitude")
    private BigDecimal latitude;

    @Schema(description = "在线状态：0-离线 1-在线")
    @TableField("online_status")
    private OnlineStatus onlineStatus;

    @Schema(description = "工作状态：0-正常 1-故障 2-维护 3-已停用")
    @TableField("work_status")
    private WorkStatus workStatus;

    /**
     * 判断当前柜子是否可用
     * @return true-可用，false-不可用
     */
    public boolean isAvailable() {
        return onlineStatus == OnlineStatus.ONLINE
                && workStatus == WorkStatus.NORMAL;
    }
}