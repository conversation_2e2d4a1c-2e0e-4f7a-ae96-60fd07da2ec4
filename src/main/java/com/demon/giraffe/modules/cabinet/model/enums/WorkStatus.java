package com.demon.giraffe.modules.cabinet.model.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 工作状态枚举
 */
@Schema(description = "工作状态")
public enum WorkStatus implements IEnum<Integer> {
    @Schema(description = "正常")
    NORMAL(0, "正常"),

    @Schema(description = "故障")
    FAULT(1, "故障"),

    @Schema(description = "维护")
    MAINTENANCE(2, "维护"),

    @Schema(description = "已停用")
    DISABLED(3, "已停用");

    private final Integer code;
    private final String description;

    WorkStatus(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getValue() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}