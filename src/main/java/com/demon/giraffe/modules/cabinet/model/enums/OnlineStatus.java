package com.demon.giraffe.modules.cabinet.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 在线状态枚举
 */
@Schema(description = "在线状态")
public enum OnlineStatus implements IEnum<Integer> {
    @Schema(description = "离线")
    OFFLINE(0, "离线"),

    @Schema(description = "在线")
    ONLINE(1, "在线");

    @EnumValue
    private final Integer code;
    private final String description;

    OnlineStatus(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getValue() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}