package com.demon.giraffe.modules.cabinet.model.dto.request;

import com.demon.giraffe.common.domain.dto.request.RegionRequest;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;

/**
 * 智能柜创建请求参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "SmartCabinetCreateRequest", description = "智能柜创建请求参数")
public class SmartCabinetCreateRequest implements Serializable {

    private static final long serialVersionUID = -1L;
    @Schema(
            description = "投资人ID，不传默认为0",
            example = "123",
            required = true
    )
    @NotNull(message = "投资人ID不能为空")
    private Long regionInvestorId;

    @Schema(
            description = "柜体名称(如：A栋1楼快递柜)",
            example = "A栋1楼快递柜",
            required = true
    )
    @NotBlank(message = "柜体名称不能为空")
    @Size(max = 50, message = "柜体名称长度不能超过50个字符")
    private String name;

    /**
     * 区域信息（统一处理）
     */
    @Valid
    @Schema(description = "区域信息", required = true)
    private RegionRequest region;

    @Schema(
            description = "详细安装地址",
            example = "科技园路123号",
            required = true
    )
    @NotBlank(message = "详细地址不能为空")
    @Size(max = 200, message = "详细地址长度不能超过200个字符")
    private String detailAddress;

    /**
     * 获取区域编码（兼容性方法）
     */
    @Schema(hidden = true)
    public CountyEnum getAddressCode() {
        return region != null ? region.getAddressCode() : null;
    }

    /**
     * 设置区域编码（兼容性方法）
     */
    @Schema(hidden = true)
    public void setAddressCode(CountyEnum addressCode) {
        if (region == null) {
            region = new RegionRequest();
        }
        region.setAddressCode(addressCode);
    }

    /**
     * 获取完整地址描述
     */
    @Schema(hidden = true)
    public String getFullAddress() {
        return region != null ? region.getFullAddress() + detailAddress : detailAddress;
    }
}