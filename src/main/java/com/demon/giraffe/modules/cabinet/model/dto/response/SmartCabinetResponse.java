package com.demon.giraffe.modules.cabinet.model.dto.response;

import com.demon.giraffe.common.domain.dto.response.RegionResponse;
import com.demon.giraffe.modules.cabinet.model.enums.OnlineStatus;
import com.demon.giraffe.modules.cabinet.model.enums.WorkStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

@Data
@SuperBuilder
@Schema(name = "SmartCabinetResponse", description = "智能柜响应信息")
public class SmartCabinetResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "柜体编号")
    private String cabinetNo;

    @Schema(description = "柜体名称")
    private String name;

    @Schema(description = "地区响应")
    private RegionResponse region;

    @Schema(description = "详细安装地址")
    private String detailAddress;

    @Schema(description = "在线状态")
    private OnlineStatus onlineStatus;

    @Schema(description = "工作状态")
    private WorkStatus workStatus;

    @Schema(description = "柜格列表")
    private List<CabinetCellResponse> cabinetCellList;
}