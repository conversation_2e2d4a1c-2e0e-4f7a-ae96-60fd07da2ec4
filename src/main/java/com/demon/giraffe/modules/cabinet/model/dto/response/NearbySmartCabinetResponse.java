package com.demon.giraffe.modules.cabinet.model.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * 附近智能柜响应信息（包含距离）
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Schema(name = "NearbySmartCabinetResponse", description = "附近智能柜响应信息（包含距离）")
public class NearbySmartCabinetResponse extends SmartCabinetResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "距离当前位置的距离（米）", example = "150.5")
    private Double distance;

    @Schema(description = "距离描述", example = "150米")
    private String distanceText;

    /**
     * 获取格式化的距离描述
     */
    public String getDistanceText() {
        if (distance == null) {
            return "未知距离";
        }
        
        if (distance < 1000) {
            return String.format("%.0f米", distance);
        } else {
            return String.format("%.1f公里", distance / 1000);
        }
    }
}
