package com.demon.giraffe.modules.cabinet.model.entity;

import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.modules.cabinet.model.enums.OnlineStatus;
import com.demon.giraffe.modules.cabinet.model.enums.WorkStatus;
import com.demon.giraffe.modules.cabinet.model.po.SmartCabinetPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 智能柜实体类
 */
@Data
@Schema(description = "智能柜实体")
public class SmartCabinet {
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "所属投资人ID")
    private Long regionInvestorId;

    @Schema(description = "柜体编号")
    private String cabinetNo;

    @Schema(description = "柜体名称")
    private String name;

    @Schema(description = "所属区域编码")
    private CountyEnum addressCode;

    @Schema(description = "详细地址")
    private String detailAddress;

    @Schema(description = "经度坐标")
    private BigDecimal longitude;

    @Schema(description = "纬度坐标")
    private BigDecimal latitude;

    @Schema(description = "在线状态")
    private OnlineStatus onlineStatus;

    @Schema(description = "工作状态")
    private WorkStatus workStatus;

    /**
     * 从PO转换为Entity
     * @param po 持久化对象
     * @return 实体对象
     */
    public static SmartCabinet fromPo(SmartCabinetPo po) {
        SmartCabinet entity = new SmartCabinet();
        entity.setId(po.getId());
        entity.setRegionInvestorId(po.getRegionInvestorId());
        entity.setCabinetNo(po.getCabinetNo());
        entity.setName(po.getName());
        entity.setAddressCode(po.getAddressCode());
        entity.setDetailAddress(po.getDetailAddress());
        entity.setLongitude(po.getLongitude());
        entity.setLatitude(po.getLatitude());
        entity.setOnlineStatus(po.getOnlineStatus());
        entity.setWorkStatus(po.getWorkStatus());
        return entity;
    }

    /**
     * 转换为PO对象
     * @return 持久化对象
     */
    public SmartCabinetPo toPo() {
        SmartCabinetPo po = new SmartCabinetPo();
        po.setId(this.id);
        po.setRegionInvestorId(this.regionInvestorId);
        po.setCabinetNo(this.cabinetNo);
        po.setName(this.name);
        po.setAddressCode(this.addressCode);
        po.setDetailAddress(this.detailAddress);
        po.setLongitude(this.longitude);
        po.setLatitude(this.latitude);
        po.setOnlineStatus(this.onlineStatus);
        po.setWorkStatus(this.workStatus);
        return po;
    }
}