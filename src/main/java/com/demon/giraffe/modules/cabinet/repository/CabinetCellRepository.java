package com.demon.giraffe.modules.cabinet.repository;

import com.demon.giraffe.modules.cabinet.model.po.CabinetCellPo;
import java.util.List;
import java.util.Map;

/**
 * 柜体格口数据访问接口
 */
public interface CabinetCellRepository {

    /**
     * 保存格口信息
     * @param cell 格口实体对象
     * @return 保存后的格口实体
     */
    CabinetCellPo save(CabinetCellPo cell);

    /**
     * 批量保存格口信息
     * @param cells 格口实体列表
     * @return 成功保存的记录数
     */
    int batchSave(List<CabinetCellPo> cells);

    /**
     * 更新格口信息
     * @param cell 格口实体对象
     * @return 更新后的格口实体
     */
    CabinetCellPo update(CabinetCellPo cell);

    /**
     * 根据ID删除格口
     * @param id 格口ID
     * @return 是否删除成功
     */
    boolean deleteById(Long id);

    /**
     * 根据柜体ID删除所有格口
     * @param cabinetId 柜体ID
     * @return 删除的记录数
     */
    int deleteByCabinetId(Long cabinetId);

    /**
     * 根据ID查询格口
     * @param id 格口ID
     * @return 格口实体对象，未找到返回null
     */
    CabinetCellPo findById(Long id);

    /**
     * 根据柜体ID查询所有格口
     * @param cabinetId 柜体ID
     * @return 格口列表，无数据返回空集合
     */
    List<CabinetCellPo> findByCabinetId(Long cabinetId);

    /**
     * 根据订单ID查询格口
     * @param orderId 订单ID
     * @return 格口实体对象，未找到返回null
     */
    CabinetCellPo findByOrderId(Long orderId);

    /**
     * 根据用户ID查询占用中的格口
     * @param userId 用户ID
     * @return 格口列表，无数据返回空集合
     */
    List<CabinetCellPo> findOccupiedByUserId(Long userId);


    /**
     * 批量查询柜体格口并按柜体ID分组
     * @param cabinetIds 柜体ID列表
     * @return 按柜体ID分组的格口Map (Key: 柜体ID, Value: 该柜体的格口列表)
     */
    Map<Long, List<CabinetCellPo>> batchQueryCellsGroupedByCabinet(List<Long> cabinetIds);
}