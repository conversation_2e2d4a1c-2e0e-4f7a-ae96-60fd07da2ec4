package com.demon.giraffe.modules.cabinet.model.dto.response;

import com.demon.giraffe.modules.cabinet.model.po.CabinetCellPo;
import com.demon.giraffe.modules.cabinet.model.po.SmartCabinetPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(name = "CabinetListResponse", description = "柜子列表响应")
public class CabinetListResponse {
    @Schema(description = "柜子信息")
    private SmartCabinetPo cabinet;
    
    @Schema(description = "柜子格子信息")
    private List<CabinetCellPo> cells;
}