package com.demon.giraffe.modules.cabinet.model.dto.query;

import com.demon.giraffe.common.domain.dto.request.RegionRequest;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.modules.cabinet.model.enums.OnlineStatus;
import com.demon.giraffe.modules.cabinet.model.enums.WorkStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "SmartCabinetQuery", description = "智能柜查询条件")
public class SmartCabinetQuery implements Serializable {

    @Schema(description = "关联设施ID")
    private Long facilityId;

    @Schema(description = "柜体编号(模糊查询)")
    private String cabinetNo;

    @Schema(description = "柜体名称(模糊查询)")
    private String name;

    @Schema(description = "在线状态")
    private OnlineStatus onlineStatus;

    @Schema(description = "工作状态")
    private WorkStatus workStatus;

    /**
     * 区域信息（统一处理，可选查询）
     */
    @Valid
    @Schema(description = "区域信息，用于按区域查询")
    private RegionRequest region;

    /**
     * 获取区域编码（兼容性方法）
     */
    public CountyEnum getAddressCode() {
        return region != null ? region.getAddressCode() : null;
    }

    /**
     * 设置区域编码（兼容性方法）
     */
    public void setAddressCode(CountyEnum addressCode) {
        if (region == null) {
            region = new RegionRequest();
        }
        region.setAddressCode(addressCode);
    }
}