package com.demon.giraffe.modules.cabinet.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "柜体格口开启方式枚举")
public enum OpenMethod {
    @Schema(description = "扫码开启")
    SCAN(1, "扫码"),

    @Schema(description = "蓝牙开启")
    BLUETOOTH(2, "蓝牙"),

    @Schema(description = "密码开启")
    PASSWORD(3, "密码"),

    @Schema(description = "远程开启")
    REMOTE(4, "远程"),

    @Schema(description = "指纹开启")
    FINGERPRINT(5, "指纹"),

    @Schema(description = "IC卡开启")
    IC_CARD(6, "IC卡");

    @EnumValue
    private final int code;
    private final String desc;

    OpenMethod(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static OpenMethod of(int code) {
        for (OpenMethod method : values()) {
            if (method.code == code) {
                return method;
            }
        }
        throw new IllegalArgumentException("无效的开启方式代码: " + code);
    }
}