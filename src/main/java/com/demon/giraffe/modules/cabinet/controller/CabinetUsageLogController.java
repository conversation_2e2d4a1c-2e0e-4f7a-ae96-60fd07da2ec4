package com.demon.giraffe.modules.cabinet.controller;

import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.cabinet.model.dto.query.CabinetUsageLogQuery;
import com.demon.giraffe.modules.cabinet.model.dto.request.CabinetUsageLogCreateRequest;
import com.demon.giraffe.modules.cabinet.model.dto.response.CabinetUsageLogDetailResponse;
import com.demon.giraffe.modules.cabinet.model.dto.response.CabinetUsageLogPageResponse;
import com.demon.giraffe.modules.cabinet.service.CabinetUsageLogService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/cabinet/usage-logs")
@Tag(name = "柜体使用记录管理接口", description = "柜体使用记录的创建、查询等操作")
public class CabinetUsageLogController {

    private final CabinetUsageLogService usageLogService;

    public CabinetUsageLogController(CabinetUsageLogService usageLogService) {
        this.usageLogService = usageLogService;
    }

    @PostMapping
    @Operation(summary = "创建柜体使用记录", description = "记录柜体的使用情况")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "柜体使用记录创建请求",
            required = true,
            content = @Content(schema = @Schema(implementation = CabinetUsageLogCreateRequest.class))
    )
    public ResultBean<CabinetUsageLogDetailResponse> createUsageLog(
            @Valid @RequestBody CabinetUsageLogCreateRequest request) {
        return ResultBean.success(usageLogService.createUsageLog(request));
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取使用记录详情", description = "根据ID获取柜体使用记录的详细信息")
    public ResultBean<CabinetUsageLogDetailResponse> getUsageLogDetail(
            @Parameter(description = "使用记录ID", required = true) @PathVariable Long id) {
        return ResultBean.success(usageLogService.getUsageLogDetail(id));
    }

    @GetMapping("/page")
    @Operation(summary = "分页查询使用记录", description = "根据条件分页查询柜体使用记录")
    public ResultBean<IPage<CabinetUsageLogPageResponse>> getUsageLogPage(
            @Valid BasePageQuery<CabinetUsageLogQuery> query) {
        return ResultBean.success(usageLogService.getUsageLogPage(query));
    }

    @GetMapping("/recent/{cabinetId}")
    @Operation(summary = "获取柜体最近使用记录", description = "获取指定柜体的最近使用记录")
    public ResultBean<List<CabinetUsageLogDetailResponse>> getRecentLogsByCabinetId(
            @Parameter(description = "柜体ID", required = true) @PathVariable Long cabinetId,
            @Parameter(description = "查询条数", example = "5") @RequestParam(defaultValue = "5") Integer limit) {
        return ResultBean.success(usageLogService.getRecentLogsByCabinetId(cabinetId, limit));
    }

    @GetMapping("/by-order/{orderId}")
    @Operation(summary = "根据订单ID查询使用记录", description = "获取与指定订单关联的柜体使用记录")
    public ResultBean<CabinetUsageLogDetailResponse> getLogByOrderId(
            @Parameter(description = "订单ID", required = true) @PathVariable Long orderId) {
        return ResultBean.success(usageLogService.getLogByOrderId(orderId));
    }
}