package com.demon.giraffe.modules.cabinet.model.dto.response;

import com.demon.giraffe.modules.cabinet.model.enums.OpenMethod;
import com.demon.giraffe.modules.cabinet.model.enums.OperationType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "柜体使用记录分页响应")
public class CabinetUsageLogPageResponse {
    
    @Schema(description = "记录ID")
    private Long id;
    
    @Schema(description = "关联柜体ID")
    private Long cabinetId;
    
    @Schema(description = "关联格口ID")
    private Long cellId;
    
    @Schema(description = "操作类型")
    private OperationType operationType;
    
    @Schema(description = "开启方式")
    private OpenMethod openMethod;
    
    @Schema(description = "开门时间")
    private LocalDateTime openTime;
    
    @Schema(description = "关门时间")
    private LocalDateTime closeTime;
    
    @Schema(description = "是否超时")
    private Boolean timeout;
}