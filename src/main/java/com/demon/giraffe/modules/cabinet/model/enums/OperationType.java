package com.demon.giraffe.modules.cabinet.model.enums;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "柜体格口操作类型枚举")
public enum OperationType {
    @Schema(description = "存入物品")
    STORE(1, "存入"),

    @Schema(description = "取出物品")
    RETRIEVE(2, "取出"),

    @Schema(description = "维护操作")
    MAINTENANCE(3, "维护"),

    @Schema(description = "检查操作")
    INSPECT(4, "检查");

    private final int code;
    private final String desc;

    OperationType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static OperationType of(int code) {
        for (OperationType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的操作类型代码: " + code);
    }
}