package com.demon.giraffe.modules.cabinet.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.demon.giraffe.modules.cabinet.model.po.CabinetCellPo;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 柜体格口Mapper接口
 */
@Mapper
public interface CabinetCellMapper extends BaseMapper<CabinetCellPo> {

    @Insert({
            "<script>",
            "INSERT INTO cabinet_cell (id, cabinet_id, cell_code, status, create_time, update_time) VALUES",
            "<foreach collection='list' item='item' separator=','>",
            "(#{item.id}, #{item.cabinetId}, #{item.cellCode}, #{item.status}, #{item.createTime}, #{item.updateTime})",
            "</foreach>",
            "</script>"
    })
    int batchInsert(@Param("list") List<CabinetCellPo> cells);

}