package com.demon.giraffe.modules.cabinet.model.dto.query;

import com.demon.giraffe.modules.cabinet.model.enums.OpenMethod;
import com.demon.giraffe.modules.cabinet.model.enums.OperationType;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Schema(description = "柜体使用记录查询条件")
public class CabinetUsageLogQuery implements Serializable {
    
    @Schema(description = "柜体ID")
    private Long cabinetId;
    
    @Schema(description = "格口ID")
    private Long cellId;
    
    @Schema(description = "订单ID")
    private Long orderId;
    
    @Schema(description = "用户ID")
    private Long userId;
    
    @Schema(description = "操作类型")
    private OperationType operationType;
    
    @Schema(description = "操作人类型")
    private UserRole operatorType;
    
    @Schema(description = "开启方式")
    private OpenMethod openMethod;
    
    @Schema(description = "开始时间")
    private LocalDateTime startTime;
    
    @Schema(description = "结束时间")
    private LocalDateTime endTime;
    
    @Schema(description = "是否超时")
    private Boolean timeout;
}