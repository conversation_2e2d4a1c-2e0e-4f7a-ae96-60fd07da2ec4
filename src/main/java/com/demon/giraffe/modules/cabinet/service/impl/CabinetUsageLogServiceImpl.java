package com.demon.giraffe.modules.cabinet.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.modules.cabinet.mapper.CabinetUsageLogMapper;
import com.demon.giraffe.modules.cabinet.model.dto.query.CabinetUsageLogQuery;
import com.demon.giraffe.modules.cabinet.model.dto.request.CabinetUsageLogCreateRequest;
import com.demon.giraffe.modules.cabinet.model.dto.response.CabinetUsageLogDetailResponse;
import com.demon.giraffe.modules.cabinet.model.dto.response.CabinetUsageLogPageResponse;
import com.demon.giraffe.modules.cabinet.model.po.CabinetUsageLogPo;
import com.demon.giraffe.modules.cabinet.service.CabinetUsageLogService;
import com.demon.giraffe.modules.cabinet.service.helper.CabinetUsageLogConvertHelper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CabinetUsageLogServiceImpl extends ServiceImpl<CabinetUsageLogMapper, CabinetUsageLogPo> implements CabinetUsageLogService {

    private final CabinetUsageLogConvertHelper convertHelper;

    @Override
    public CabinetUsageLogDetailResponse createUsageLog(CabinetUsageLogCreateRequest request) {
        CabinetUsageLogPo logPo = convertHelper.convertToPo(request);
        save(logPo);
        return convertHelper.convertToDetailResponse(logPo);
    }

    @Override
    public CabinetUsageLogDetailResponse getUsageLogDetail(Long id) {
        CabinetUsageLogPo logPo = getById(id);
        return convertHelper.convertToDetailResponse(logPo);
    }

    @Override
    public IPage<CabinetUsageLogPageResponse> getUsageLogPage(BasePageQuery<CabinetUsageLogQuery> query) {
        Page<CabinetUsageLogPo> page = new Page<>(query.getPage(), query.getPerPage());
        IPage<CabinetUsageLogPo> poPage = page(page, buildQueryWrapper(query.getQuery()));
        return poPage.convert(convertHelper::convertToPageResponse);
    }

    @Override
    public List<CabinetUsageLogDetailResponse> getRecentLogsByCabinetId(Long cabinetId, Integer limit) {
        List<CabinetUsageLogPo> logs = lambdaQuery()
                .eq(CabinetUsageLogPo::getCabinetId, cabinetId)
                .orderByDesc(CabinetUsageLogPo::getOpenTime)
                .last("LIMIT " + limit)
                .list();
        return logs.stream()
                .map(convertHelper::convertToDetailResponse)
                .collect(Collectors.toList());
    }

    @Override
    public CabinetUsageLogDetailResponse getLogByOrderId(Long orderId) {
        CabinetUsageLogPo logPo = lambdaQuery()
                .eq(CabinetUsageLogPo::getOrderId, orderId)
                .one();
        return convertHelper.convertToDetailResponse(logPo);
    }

    /**
     * 构建查询条件Wrapper
     * @param query 查询条件
     * @return 构建好的Wrapper
     */
    private Wrapper<CabinetUsageLogPo> buildQueryWrapper(CabinetUsageLogQuery query) {
        return new QueryWrapper<CabinetUsageLogPo>()
                .eq(query.getCabinetId() != null, "cabinet_id", query.getCabinetId())
                .eq(query.getCellId() != null, "cell_id", query.getCellId())
                .eq(query.getOrderId() != null, "order_id", query.getOrderId())
                .eq(query.getUserId() != null, "user_id", query.getUserId())
                .eq(query.getOperationType() != null, "operation_type", query.getOperationType())
                .eq(query.getOperatorType() != null, "operator_type", query.getOperatorType())
                .eq(query.getOpenMethod() != null, "open_method", query.getOpenMethod())
                .ge(query.getStartTime() != null, "open_time", query.getStartTime())
                .le(query.getEndTime() != null, "open_time", query.getEndTime())
                .eq(query.getTimeout() != null, "is_timeout", query.getTimeout())
                .orderByDesc("open_time");
    }
}