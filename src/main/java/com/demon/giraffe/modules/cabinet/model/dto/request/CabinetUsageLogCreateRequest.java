package com.demon.giraffe.modules.cabinet.model.dto.request;

import com.demon.giraffe.modules.cabinet.model.enums.OpenMethod;
import com.demon.giraffe.modules.cabinet.model.enums.OperationType;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@Schema(description = "柜体使用记录创建请求")
public class CabinetUsageLogCreateRequest {
    
    @NotNull
    @Schema(description = "关联柜体ID", required = true)
    private Long cabinetId;
    
    @NotNull
    @Schema(description = "关联格口ID", required = true)
    private Long cellId;
    
    @Schema(description = "关联订单ID")
    private Long orderId;
    
    @Schema(description = "操作用户ID")
    private Long userId;
    
    @NotNull
    @Schema(description = "操作类型", required = true)
    private OperationType operationType;
    
    @NotNull
    @Schema(description = "操作人类型", required = true)
    private UserRole operatorType;
    
    @Schema(description = "验证码")
    private String verificationCode;
    
    @NotNull
    @Schema(description = "开启方式", required = true)
    private OpenMethod openMethod;
    
    @NotNull
    @Schema(description = "开门时间", required = true)
    private LocalDateTime openTime;
    
    @Schema(description = "是否超时", required = true)
    private Boolean timeout;
    
    @Schema(description = "操作凭证照片(JSON数组)")
    private String images;
}