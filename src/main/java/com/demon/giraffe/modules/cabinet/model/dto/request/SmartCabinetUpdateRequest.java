package com.demon.giraffe.modules.cabinet.model.dto.request;

import com.demon.giraffe.common.domain.dto.request.RegionRequest;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.modules.cabinet.model.enums.OnlineStatus;
import com.demon.giraffe.modules.cabinet.model.enums.WorkStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.Size;

/**
 * 智能柜更新请求参数
 * 注意：此请求体采用部分更新模式，所有字段均为可选（除id外）
 * 更新逻辑：根据id查询当前记录，然后将非空字段合并到现有记录中
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "SmartCabinetUpdateRequest", description = "智能柜更新请求参数（部分更新模式）")
public class SmartCabinetUpdateRequest {
    @Schema(
            description = "设备编号（必填）",
            example = "123456",
            required = true
    )
    private Long id;

    @Schema(
            description = "投资人ID，留空表示不更新",
            example = "123"
    )
    private Long regionInvestorId;

    @Schema(
            description = "柜体名称(如：A栋1楼快递柜)，留空表示不更新",
            example = "A栋1楼快递柜"
    )
    @Size(max = 50, message = "柜体名称长度不能超过50个字符")
    private String name;

    /**
     * 区域信息（统一处理，可选更新）
     */
    @Valid
    @Schema(description = "区域信息，留空表示不更新")
    private RegionRequest region;


    @Schema(
            description = "详细安装地址，留空表示不更新",
            example = "科技园路123号"
    )
    @Size(max = 200, message = "详细地址长度不能超过200个字符")
    private String detailAddress;

    @Schema(
            description = "在线状态，留空表示不更新",
            example = "ONLINE"
    )
    private OnlineStatus onlineStatus;

    @Schema(
            description = "工作状态，留空表示不更新",
            example = "WORKING"
    )
    private WorkStatus workStatus;

    /**
     * 获取区域编码（兼容性方法）
     */
    public CountyEnum getAddressCode() {
        return region != null ? region.getAddressCode() : null;
    }

    /**
     * 设置区域编码（兼容性方法）
     */
    public void setAddressCode(CountyEnum addressCode) {
        if (region == null) {
            region = new RegionRequest();
        }
        region.setAddressCode(addressCode);
    }

    /**
     * 获取完整地址描述
     */
    public String getFullAddress() {
        return region != null ? region.getFullAddress() + (detailAddress != null ? detailAddress : "") :
               (detailAddress != null ? detailAddress : "");
    }
}