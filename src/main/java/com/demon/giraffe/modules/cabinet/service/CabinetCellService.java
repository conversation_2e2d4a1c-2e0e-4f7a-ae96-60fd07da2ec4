package com.demon.giraffe.modules.cabinet.service;

import com.demon.giraffe.modules.cabinet.model.po.CabinetCellPo;
import com.demon.giraffe.common.domain.ResultBean;

import java.util.List;

/**
 * 柜体格口服务接口
 */
public interface CabinetCellService {

    /**
     * 创建格口
     * @param cell 格口实体
     * @return 创建结果
     */
    ResultBean<CabinetCellPo> createCell(CabinetCellPo cell);

    /**
     * 更新格口信息
     * @param cell 格口实体
     * @return 更新结果
     */
    ResultBean<CabinetCellPo> updateCell(CabinetCellPo cell);

    /**
     * 删除格口
     * @param id 格口ID
     * @return 删除结果
     */
    ResultBean<Boolean> deleteCell(Long id);

    /**
     * 获取格口详情
     * @param id 格口ID
     * @return 格口详情
     */
    ResultBean<CabinetCellPo> getCellById(Long id);

    /**
     * 获取柜体所有格口
     * @param cabinetId 柜体ID
     * @return 格口列表
     */
    ResultBean<List<CabinetCellPo>> getCellsByCabinetId(Long cabinetId);

    /**
     * 获取订单关联的格口
     * @param orderId 订单ID
     * @return 格口详情
     */
    ResultBean<CabinetCellPo> getCellByOrderId(Long orderId);

    /**
     * 获取用户占用的格口
     * @param userId 用户ID
     * @return 格口列表
     */
    ResultBean<List<CabinetCellPo>> getOccupiedCellsByUserId(Long userId);
}