package com.demon.giraffe.modules.cabinet.service.impl;

import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.cabinet.model.po.CabinetCellPo;
import com.demon.giraffe.modules.cabinet.repository.CabinetCellRepository;
import com.demon.giraffe.modules.cabinet.service.CabinetCellService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 柜体格口服务实现
 */
@Service
public class CabinetCellServiceImpl implements CabinetCellService {

    private final CabinetCellRepository cabinetCellRepository;

    public CabinetCellServiceImpl(CabinetCellRepository cabinetCellRepository) {
        this.cabinetCellRepository = cabinetCellRepository;
    }

    @Override
    public ResultBean<CabinetCellPo> createCell(CabinetCellPo cell) {
        CabinetCellPo savedCell = cabinetCellRepository.save(cell);
        return ResultBean.success(savedCell);
    }

    @Override
    public ResultBean<CabinetCellPo> updateCell(CabinetCellPo cell) {
        CabinetCellPo updatedCell = cabinetCellRepository.update(cell);
        return ResultBean.success(updatedCell);
    }

    @Override
    public ResultBean<Boolean> deleteCell(Long id) {
        boolean deleted = cabinetCellRepository.deleteById(id);
        return ResultBean.success(deleted);
    }

    @Override
    public ResultBean<CabinetCellPo> getCellById(Long id) {
        CabinetCellPo cell = cabinetCellRepository.findById(id);
        return ResultBean.success(cell);
    }

    @Override
    public ResultBean<List<CabinetCellPo>> getCellsByCabinetId(Long cabinetId) {
        List<CabinetCellPo> cells = cabinetCellRepository.findByCabinetId(cabinetId);
        return ResultBean.success(cells);
    }

    @Override
    public ResultBean<CabinetCellPo> getCellByOrderId(Long orderId) {
        CabinetCellPo cell = cabinetCellRepository.findByOrderId(orderId);
        return ResultBean.success(cell);
    }

    @Override
    public ResultBean<List<CabinetCellPo>> getOccupiedCellsByUserId(Long userId) {
        List<CabinetCellPo> cells = cabinetCellRepository.findOccupiedByUserId(userId);
        return ResultBean.success(cells);
    }
}