package com.demon.giraffe.modules.cabinet.model.dto.response;

import com.demon.giraffe.modules.cabinet.model.enums.OpenMethod;
import com.demon.giraffe.modules.cabinet.model.enums.OperationType;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "柜体使用记录详情响应")
public class CabinetUsageLogDetailResponse {
    
    @Schema(description = "记录ID")
    private Long id;
    
    @Schema(description = "关联柜体ID")
    private Long cabinetId;
    
    @Schema(description = "关联格口ID")
    private Long cellId;
    
    @Schema(description = "关联订单ID")
    private Long orderId;
    
    @Schema(description = "操作用户ID")
    private Long userId;
    
    @Schema(description = "操作类型")
    private OperationType operationType;
    
    @Schema(description = "操作人类型")
    private UserRole operatorType;
    
    @Schema(description = "验证码")
    private String verificationCode;
    
    @Schema(description = "开启方式")
    private OpenMethod openMethod;
    
    @Schema(description = "开门时间")
    private LocalDateTime openTime;
    
    @Schema(description = "关门时间")
    private LocalDateTime closeTime;
    
    @Schema(description = "使用时长(秒)")
    private Integer duration;
    
    @Schema(description = "是否超时")
    private Boolean timeout;
    
    @Schema(description = "操作凭证照片(JSON数组)")
    private String images;
    
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}