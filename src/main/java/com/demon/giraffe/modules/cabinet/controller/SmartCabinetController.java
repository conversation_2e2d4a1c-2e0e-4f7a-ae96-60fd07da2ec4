package com.demon.giraffe.modules.cabinet.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.demon.giraffe.common.annotation.ProcessRegion;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.cabinet.model.dto.query.SmartCabinetQuery;
import com.demon.giraffe.modules.cabinet.model.dto.request.*;
import com.demon.giraffe.modules.cabinet.model.dto.response.NearbySmartCabinetResponse;
import com.demon.giraffe.modules.cabinet.model.dto.response.SmartCabinetResponse;
import com.demon.giraffe.modules.cabinet.service.SmartCabinetService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/cabinet/api")
@Tag(name = "智能柜管理接口", description = "智能柜相关操作")
public class SmartCabinetController {

    private final SmartCabinetService smartCabinetService;

    public SmartCabinetController(SmartCabinetService smartCabinetService) {
        this.smartCabinetService = smartCabinetService;
    }

    @PostMapping("/create")
    @ProcessRegion
    @Operation(summary = "创建智能柜", description = "root用户创建新的智能柜")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "智能柜创建参数",
            required = true,
            content = @Content(schema = @Schema(implementation = SmartCabinetCreateRequest.class))
    )
    public ResultBean<Boolean> createCabinet(@RequestBody @Valid SmartCabinetCreateRequest request) {
        try {
            smartCabinetService.createCabinet(request);
            return ResultBean.success("智能柜创建成功", Boolean.TRUE);
        } catch (Exception e) {
            log.error("创建智能柜失败", e);
            return ResultBean.fail("创建失败：" + e.getMessage());
        }
    }

    @PostMapping("/batchCreate")
    @ProcessRegion
    @Operation(summary = "批量创建智能柜", description = "root用户批量创建新的智能柜")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "智能柜批量创建参数列表",
            required = true,
            content = @Content(schema = @Schema(implementation = SmartCabinetCreateRequest.class))
    )
    public ResultBean<Boolean> batchCreateCabinet(@RequestBody @Valid List<SmartCabinetCreateRequest> requestList) {
        if (CollUtil.isEmpty(requestList)) {
            return ResultBean.fail("请求数据不能为空");
        }
        int successCount = 0;
        for (SmartCabinetCreateRequest request : requestList) {
            try {
                smartCabinetService.createCabinet(request);
                successCount++;
            } catch (Exception e) {
                log.error("批量创建智能柜失败，当前失败请求：{}", request, e);
                // 继续下一个，记录日志不中断
            }
        }
        if (successCount == 0) {
            return ResultBean.fail("所有智能柜创建失败");
        }
        return ResultBean.success("成功创建 " + successCount + " 个智能柜", Boolean.TRUE);
    }


    @PostMapping("/update")
    @ProcessRegion
    @Operation(summary = "更新智能柜信息", description = "root用户更新智能柜信息")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "智能柜更新参数",
            required = true,
            content = @Content(schema = @Schema(implementation = SmartCabinetUpdateRequest.class))
    )
    public ResultBean<Boolean> updateCabinet(@RequestBody @Valid SmartCabinetUpdateRequest request) {
        try {
            smartCabinetService.updateCabinet(request);
            return ResultBean.success("智能柜更新成功", Boolean.TRUE);
        } catch (Exception e) {
            log.error("更新智能柜失败", e);
            return ResultBean.fail("更新失败：" + e.getMessage());
        }
    }

    @PostMapping("/delete/{id}")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    @Operation(summary = "删除智能柜", description = "root用户删除智能柜")
    public ResultBean<Boolean> deleteCabinet(@PathVariable Long id) {
        return smartCabinetService.deleteCabinet(id);
    }


    @GetMapping("/page")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    @Operation(summary = "分页查询智能柜列表", description = "ROOT权限：根据条件分页查询智能柜信息列表，包含柜格信息")
    public ResultBean<IPage<SmartCabinetResponse>> querySmartCabinetPage(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "分页查询条件",
                    required = true,
                    content = @Content(schema = @Schema(implementation = BasePageQuery.class))
            )
            @RequestBody @Valid BasePageQuery<SmartCabinetQuery> query) {
        try {
            IPage<SmartCabinetResponse> result = smartCabinetService.querySmartCabinetPage(query);
            return ResultBean.success("查询成功", result);
        } catch (Exception e) {
            log.error("分页查询智能柜信息失败", e);
            return ResultBean.fail("查询失败：" + e.getMessage());
        }
    }



    @PostMapping("/nearby")
    @ProcessRegion
    @Operation(summary = "获取附近的柜子", description = "用户获取附近的智能柜，返回包含距离信息的柜子列表")
    @SaCheckRole(value = {"CUSTOMER"}, mode = SaMode.OR)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "附近智能柜查询条件",
            required = true,
            content = @Content(schema = @Schema(implementation = NearbyCabinetRequest.class))
    )
    public ResultBean<List<NearbySmartCabinetResponse>> findNearbyCabinets(
            @RequestBody @Valid NearbyCabinetRequest nearbyCabinetRequest) {
        try {
            List<NearbySmartCabinetResponse> result = smartCabinetService.queryNearbyCabinetsWithDistance(nearbyCabinetRequest);
            return ResultBean.success("查询成功", result);
        } catch (Exception e) {
            log.error("查询附近智能柜失败", e);
            return ResultBean.fail("查询失败：" + e.getMessage());
        }
    }


    @PostMapping("/open")
    @Operation(summary = "打开柜格", description = "取送员或被授权用户打开柜子")
    public ResultBean<Boolean> openCabinet(
            @RequestParam Long cabinetId,
            @RequestParam Long cellId) {
        return smartCabinetService.openCabinet(cabinetId, cellId);
    }

    @PostMapping("/close")
    @Operation(summary = "关闭柜格", description = "取送员或被授权用户关闭柜子")
    public ResultBean<Boolean> closeCabinet(
            @RequestParam Long cabinetId,
            @RequestParam Long cellId) {
        return smartCabinetService.closeCabinet(cabinetId, cellId);
    }
}