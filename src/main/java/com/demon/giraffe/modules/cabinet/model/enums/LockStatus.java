package com.demon.giraffe.modules.cabinet.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "锁状态")
public enum LockStatus implements IEnum<Integer> {
    @Schema(description = "关闭")
    CLOSED(0, "关闭"),
    
    @Schema(description = "开启")
    OPENED(1, "开启"),
    
    @Schema(description = "异常")
    ABNORMAL(2, "异常");

    @EnumValue
    private final int code;
    private final String desc;

    LockStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getValue() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}