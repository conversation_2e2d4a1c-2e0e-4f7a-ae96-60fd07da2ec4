package com.demon.giraffe.modules.cabinet.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.modules.cabinet.model.dto.query.SmartCabinetQuery;
import com.demon.giraffe.modules.cabinet.model.dto.request.*;
import com.demon.giraffe.modules.cabinet.model.dto.response.NearbySmartCabinetResponse;
import com.demon.giraffe.modules.cabinet.model.dto.response.SmartCabinetResponse;
import com.demon.giraffe.modules.cabinet.model.entity.SmartCabinet;
import com.demon.giraffe.modules.cabinet.model.enums.*;
import com.demon.giraffe.modules.cabinet.model.po.CabinetCellPo;
import com.demon.giraffe.modules.cabinet.model.po.SmartCabinetPo;
import com.demon.giraffe.modules.cabinet.repository.CabinetCellRepository;
import com.demon.giraffe.modules.cabinet.repository.SmartCabinetRepository;
import com.demon.giraffe.modules.cabinet.service.SmartCabinetService;
import com.demon.giraffe.modules.cabinet.service.helper.SmartCabinetConvertHelper;
import com.demon.giraffe.modules.common.model.entity.DistanceWrapper;
import com.demon.giraffe.modules.common.service.LocationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SmartCabinetServiceImpl implements SmartCabinetService {

    private final SmartCabinetRepository smartCabinetRepository;
    private final SmartCabinetConvertHelper convertHelper;
    private final CabinetCellRepository cabinetCellRepository;
    private final LocationService locationService;
    private static final Double MAX_NEARBY_CABINETS = 10000D;

    public SmartCabinetServiceImpl(SmartCabinetRepository smartCabinetRepository,
                                   SmartCabinetConvertHelper convertHelper,
                                   CabinetCellRepository cabinetCellRepository, LocationService locationService) {
        this.smartCabinetRepository = smartCabinetRepository;
        this.convertHelper = convertHelper;
        this.cabinetCellRepository = cabinetCellRepository;
        this.locationService = locationService;
    }

    @Override
    @Transactional
    public ResultBean<SmartCabinetPo> createCabinet(SmartCabinetCreateRequest request) {
        SmartCabinetPo cabinet = convertHelper.convertToPo(request);
        SmartCabinetPo savedCabinet = smartCabinetRepository.save(cabinet);
        Long cabinetId = savedCabinet.getId();

        // 创建默认格口
        List<CabinetCellPo> cells = Arrays.stream(CellType.values())
                .map(cellType -> convertHelper.createCellPo(cabinetId, cellType))
                .collect(Collectors.toList());

        cabinetCellRepository.batchSave(cells);
        return ResultBean.success(savedCabinet);
    }

    @Override
    public ResultBean<SmartCabinetPo> updateCabinet(SmartCabinetUpdateRequest request) {
        SmartCabinetPo existingCabinet = smartCabinetRepository.findById(request.getId());
        if (existingCabinet == null) {
            return ResultBean.fail("柜子不存在");
        }

        SmartCabinetPo updatedCabinet = convertHelper.mergeUpdateToPo(existingCabinet, request);
        smartCabinetRepository.update(updatedCabinet);
        return ResultBean.success(updatedCabinet);
    }

    @Override
    @Transactional
    public ResultBean<Boolean> deleteCabinet(Long id) {
        // 1. 先删除关联的所有格口
        int deletedCells = cabinetCellRepository.deleteByCabinetId(id);
        log.info("删除柜体ID={}的{}个关联格口", id, deletedCells);

        // 2. 再删除柜体本身
        boolean deletedCabinet = smartCabinetRepository.deleteById(id);

        if (deletedCabinet) {
            return ResultBean.success(true);
        } else {
            log.error("删除柜体ID={}失败，但已删除其{}个关联格口", id, deletedCells);
            return ResultBean.fail("删除柜体失败");
        }
    }



    @Override
    public List<NearbySmartCabinetResponse> queryNearbyCabinetsWithDistance(NearbyCabinetRequest request) {
        // 需要实际读出
        CountyEnum countyEnum = request.getRegion().getAddressCode();
        // 1. 根据区域查询柜子
        List<SmartCabinetPo> cabinets = smartCabinetRepository.findByAddressCode(countyEnum);

        // 根据 cabinets 计算附近的位置
        List<DistanceWrapper<SmartCabinetPo>> distanceWrappers = locationService.calculateDistancesAndFilterGeneric(
                request.getLongitude(),
                request.getLatitude(),
                cabinets,
                SmartCabinetPo::getLongitude,
                SmartCabinetPo::getLatitude,
                Double.valueOf(request.getRadius())
        );

        // 获取前N个元素，注意防止下标越界
        List<DistanceWrapper<SmartCabinetPo>> topWrappers = distanceWrappers.subList(0, distanceWrappers.size());

        // 2. 获取这些柜子的格子信息
        List<Long> cabinetIds = topWrappers.stream()
                .map(wrapper -> wrapper.getPoint().getId())
                .collect(Collectors.toList());

        Map<Long, List<CabinetCellPo>> cellsMap = Collections.emptyMap();
        if (!cabinetIds.isEmpty()) {
            cellsMap = cabinetCellRepository.batchQueryCellsGroupedByCabinet(cabinetIds);
        }

        // 3. 转换为带距离信息的响应对象
        return convertHelper.convertToNearbyResponseList(topWrappers, cellsMap);
    }

    @Override
    public Map<Long, List<CabinetCellPo>> getCabinetCellDetails(List<Long> cabinetIds) {
        if (CollectionUtils.isEmpty(cabinetIds)) {
            return Collections.emptyMap();
        }

        return cabinetCellRepository.batchQueryCellsGroupedByCabinet(cabinetIds);
    }

    @Override
    public Map<Long, List<CabinetCellPo>> batchQueryCellsGroupedByCabinet(List<Long> cabinetIds) {
        return getCabinetCellDetails(cabinetIds);
    }

    @Override
    public ResultBean<Boolean> openCabinet(Long cabinetId, Long cellId) {
        // 1. 验证柜子和格子是否存在
        SmartCabinetPo cabinet = smartCabinetRepository.findById(cabinetId);
        if (cabinet == null) {
            return ResultBean.fail("柜子不存在");
        }

        CabinetCellPo cell = cabinetCellRepository.findById(cellId);
        if (cell == null || !cell.getCabinetId().equals(cabinetId)) {
            return ResultBean.fail("格子不存在或不属于该柜子");
        }

        // 2. 调用硬件接口开箱
        boolean success = callHardwareOpen(cabinet, cell);

        // 3. 更新格子状态
        if (success) {
            cell.setLockStatus(LockStatus.OPENED);
            cabinetCellRepository.update(cell);
        }

        return success ? ResultBean.success(true) : ResultBean.fail("开箱失败");
    }

    @Override
    public ResultBean<Boolean> closeCabinet(Long cabinetId, Long cellId) {
        // 类似openCabinet的实现
        SmartCabinetPo cabinet = smartCabinetRepository.findById(cabinetId);
        if (cabinet == null) {
            return ResultBean.fail("柜子不存在");
        }

        CabinetCellPo cell = cabinetCellRepository.findById(cellId);
        if (cell == null || !cell.getCabinetId().equals(cabinetId)) {
            return ResultBean.fail("格子不存在或不属于该柜子");
        }

        boolean success = callHardwareClose(cabinet, cell);

        if (success) {
            cell.setLockStatus(LockStatus.CLOSED);
            cabinetCellRepository.update(cell);
        }

        return success ? ResultBean.success(true) : ResultBean.fail("关箱失败");
    }



    private boolean callHardwareOpen(SmartCabinetPo cabinet, CabinetCellPo cell) {
        // 实际项目中这里应该调用硬件API
        log.info("调用硬件接口开箱：柜子ID={}, 格子ID={}", cabinet.getId(), cell.getId());
        return true; // 模拟成功
    }

    private boolean callHardwareClose(SmartCabinetPo cabinet, CabinetCellPo cell) {
        // 实际项目中这里应该调用硬件API
        log.info("调用硬件接口关箱：柜子ID={}, 格子ID={}", cabinet.getId(), cell.getId());
        return true; // 模拟成功
    }

    @Override
    public List<SmartCabinet> getAvailableCabinetsByRegion(CountyEnum county) {
        // 1. 查询该区域所有可用柜子
        List<SmartCabinetPo> cabinets = smartCabinetRepository.findAvailableByAddressCode(county);
        if(cabinets.isEmpty()){
           log.warn("该地区未提供柜子服务,没有柜子服务也没有上门服务");
           return Collections.emptyList();
        }
        return cabinets.stream().map(SmartCabinet::fromPo).collect(Collectors.toList());
    }

    @Override
    public IPage<SmartCabinetResponse> querySmartCabinetPage(BasePageQuery<SmartCabinetQuery> query) {
        query.init();

        Page<SmartCabinetPo> page = new Page<>(query.getPage(), query.getPerPage());
        IPage<SmartCabinetPo> poPage = smartCabinetRepository.queryPage(page, query.getQuery());

        // 获取所有柜子的ID
        List<Long> cabinetIds = poPage.getRecords().stream()
                .map(SmartCabinetPo::getId)
                .collect(Collectors.toList());

        // 批量查询柜格信息
        Map<Long, List<CabinetCellPo>> cellsMap = Collections.emptyMap();
        if (!cabinetIds.isEmpty()) {
            cellsMap = cabinetCellRepository.batchQueryCellsGroupedByCabinet(cabinetIds);
        }

        // 转换为响应对象
        final Map<Long, List<CabinetCellPo>> finalCellsMap = cellsMap;
        return poPage.convert(cabinet -> convertHelper.convertToResponse(cabinet,
                finalCellsMap.getOrDefault(cabinet.getId(), Collections.emptyList())));
    }
}