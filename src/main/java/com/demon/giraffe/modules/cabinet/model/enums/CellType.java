package com.demon.giraffe.modules.cabinet.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * 格口编号类型
 */
@Schema(description = "格口编号类型")
public enum CellType implements IEnum<Integer> {
    A1(1,"A1", "格口 A1"),
    A2(2,"A2", "格口 A2"),
    A3(3,"A3", "格口 A3"),
    A4(4,"A4", "格口 A4"),
    A5(5,"A5", "格口 A5");

    @EnumValue
    private final Integer code;
    @Getter
    private final String cellName;
    @Getter
    private final String description;

    CellType(Integer code,String cellName, String description) {
        this.code = code;
        this.cellName = cellName;
        this.description = description;
    }

    @Override
    public Integer getValue() {
        return code;
    }

}
