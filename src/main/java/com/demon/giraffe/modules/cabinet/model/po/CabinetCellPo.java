package com.demon.giraffe.modules.cabinet.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;

import com.demon.giraffe.modules.cabinet.model.enums.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 柜体格口实体
 * 对应数据库表：cabinet_cell
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cabinet_cell")
@Schema(name = "CabinetCellPo", description = "柜体格口实体")
public class CabinetCellPo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "关联柜体ID")
    @TableField("cabinet_id")
    private Long cabinetId;

    @Schema(description = "格口类型：1-A1 2-A2 3-A3 4-A4 5-A5")
    @TableField("cell_type")
    private CellType cellType;

    @Schema(description = "状态：0-空闲 1-占用 2-故障 3-维护")
    @TableField("status")
    private CellStatus status;

    @Schema(description = "锁状态：0-关闭 1-开启 2-异常")
    @TableField("lock_status")
    private LockStatus lockStatus;

    @Schema(description = "当前订单ID")
    @TableField("order_id")
    private Long orderId;

    @Schema(description = "当前使用用户ID")
    @TableField("user_id")
    private Long userId;

    @Schema(description = "占用开始时间")
    @TableField("occupy_time")
    private LocalDateTime occupyTime;

}