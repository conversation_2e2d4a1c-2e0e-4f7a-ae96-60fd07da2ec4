package com.demon.giraffe.modules.cabinet.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.modules.cabinet.model.dto.query.SmartCabinetQuery;
import com.demon.giraffe.modules.cabinet.model.po.SmartCabinetPo;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import java.math.BigDecimal;
import java.util.List;

/**
 * 智能柜数据持久化接口
 */
public interface SmartCabinetRepository {

    /**
     * 保存智能柜信息
     * @param cabinet 智能柜实体对象
     * @return 保存后的智能柜实体（包含生成的主键ID）
     */
    SmartCabinetPo save(SmartCabinetPo cabinet);

    /**
     * 更新智能柜信息
     * @param cabinet 智能柜实体对象
     * @return 更新后的智能柜实体
     */
    SmartCabinetPo update(SmartCabinetPo cabinet);

    /**
     * 根据ID删除智能柜
     * @param id 智能柜ID
     * @return 是否删除成功
     */
    boolean deleteById(Long id);

    /**
     * 根据ID查询智能柜详情
     * @param id 智能柜ID
     * @return 智能柜实体对象，未找到时返回null
     */
    SmartCabinetPo findById(Long id);

    /**
     * 根据投资人ID查询所属智能柜
     * @param regionInvestorId 投资人ID
     * @return 智能柜列表，无数据时返回空集合
     */
    List<SmartCabinetPo> findByRegionInvestorId(Long regionInvestorId);

    /**
     * 根据区域编码查询智能柜
     * @param addressCode 区域编码枚举
     * @return 智能柜列表，无数据时返回空集合
     */
    List<SmartCabinetPo> findByAddressCode(CountyEnum addressCode);

    /**
     * 根据经纬度查询附近智能柜
     * @param longitude 经度坐标
     * @param latitude 纬度坐标
     * @param distance 查询范围（单位：公里）
     * @param limit 返回结果数量限制
     * @return 附近的智能柜列表，按距离排序，无数据时返回空集合
     */
    List<SmartCabinetPo> findNearby(BigDecimal longitude, BigDecimal latitude, double distance, int limit);

    /**
     * 根据区域查询所有可用的智能柜
     * @param addressCode 区域编码枚举
     * @return 可用智能柜列表，无数据时返回空集合
     */
    List<SmartCabinetPo> findAvailableByAddressCode(CountyEnum addressCode);

    /**
     * 分页查询智能柜
     * @param page 分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<SmartCabinetPo> queryPage(Page<SmartCabinetPo> page, SmartCabinetQuery query);
}