package com.demon.giraffe.modules.cabinet.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.modules.cabinet.model.dto.query.SmartCabinetQuery;
import com.demon.giraffe.modules.cabinet.model.dto.request.*;
import com.demon.giraffe.modules.cabinet.model.dto.response.NearbySmartCabinetResponse;
import com.demon.giraffe.modules.cabinet.model.dto.response.SmartCabinetResponse;
import com.demon.giraffe.modules.cabinet.model.entity.SmartCabinet;
import com.demon.giraffe.modules.cabinet.model.po.CabinetCellPo;
import com.demon.giraffe.modules.cabinet.model.po.SmartCabinetPo;

import java.util.List;
import java.util.Map;

/**
 * 智能柜服务接口
 * 提供智能柜的创建、更新、删除、查询及操作能力
 */
public interface SmartCabinetService {

    /**
     * 创建智能柜
     *
     * @param request 智能柜创建请求参数
     * @return 创建后的智能柜信息
     */
    ResultBean<SmartCabinetPo> createCabinet(SmartCabinetCreateRequest request);

    /**
     * 更新智能柜信息
     *
     * @param request 智能柜更新请求参数
     * @return 更新后的智能柜信息
     */
    ResultBean<SmartCabinetPo> updateCabinet(SmartCabinetUpdateRequest request);

    /**
     * 删除智能柜
     *
     * @param id 智能柜ID
     * @return 删除是否成功
     */
    ResultBean<Boolean> deleteCabinet(Long id);


    /**
     * 查找附近的智能柜（包含距离信息）
     *
     * @param nearbyCabinetRequest 位置信息（区县级）
     * @return 附近的智能柜列表（包含距离信息）
     */
    List<NearbySmartCabinetResponse> queryNearbyCabinetsWithDistance(NearbyCabinetRequest nearbyCabinetRequest);


    /**
     * 获取柜体所有格口的详细信息
     *
     * @param cabinetId 智能柜唯一标识
     * @return 按格口类型分组的格子列表（Key为格口类型枚举值）
     */
    Map<Long, List<CabinetCellPo>> getCabinetCellDetails(List<Long> cabinetId);

    /**
     * 批量查询多个柜体的格口信息（按柜体ID分组）
     *
     * @param cabinetIds 智能柜ID列表
     * @return 按柜体ID分组的格口列表（Key为柜体ID，Value为该柜体的所有格口）
     */
    Map<Long, List<CabinetCellPo>> batchQueryCellsGroupedByCabinet(List<Long> cabinetIds);

    /**
     * 远程开箱操作
     *
     * @param cabinetId 智能柜ID
     * @param cellId    箱格ID
     * @return 是否成功开箱
     */
    ResultBean<Boolean> openCabinet(Long cabinetId, Long cellId);

    /**
     * 远程关箱操作
     *
     * @param cabinetId 智能柜ID
     * @param cellId    箱格ID
     * @return 是否成功关箱
     */
    ResultBean<Boolean> closeCabinet(Long cabinetId, Long cellId);

    /**
     * 根据区域获取所有可用柜子信息
     * @param county 区域枚举
     * @return 可用柜子列表
     */
    List<SmartCabinet> getAvailableCabinetsByRegion(CountyEnum county);

    /**
     * 分页查询智能柜列表
     * @param query 分页查询条件
     * @return 分页结果
     */
    IPage<SmartCabinetResponse> querySmartCabinetPage(BasePageQuery<SmartCabinetQuery> query);
}
