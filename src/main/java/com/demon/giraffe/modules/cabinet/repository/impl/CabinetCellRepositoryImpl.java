package com.demon.giraffe.modules.cabinet.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.demon.giraffe.modules.cabinet.mapper.CabinetCellMapper;
import com.demon.giraffe.modules.cabinet.model.enums.CellStatus;
import com.demon.giraffe.modules.cabinet.model.po.CabinetCellPo;
import com.demon.giraffe.modules.cabinet.repository.CabinetCellRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 柜体格口数据访问实现类
 */
@Repository
public class CabinetCellRepositoryImpl implements CabinetCellRepository {

    private final CabinetCellMapper cabinetCellMapper;

    public CabinetCellRepositoryImpl(CabinetCellMapper cabinetCellMapper) {
        this.cabinetCellMapper = cabinetCellMapper;
    }

    @Override
    public CabinetCellPo save(CabinetCellPo cell) {
        cabinetCellMapper.insert(cell);
        return cell;
    }

    @Override
    public CabinetCellPo update(CabinetCellPo cell) {
        cabinetCellMapper.updateById(cell);
        return cell;
    }

    @Override
    public boolean deleteById(Long id) {
        return cabinetCellMapper.deleteById(id) > 0;
    }

    @Override
    public CabinetCellPo findById(Long id) {
        return cabinetCellMapper.selectById(id);
    }

    @Override
    public List<CabinetCellPo> findByCabinetId(Long cabinetId) {
        LambdaQueryWrapper<CabinetCellPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CabinetCellPo::getCabinetId, cabinetId);
        return cabinetCellMapper.selectList(queryWrapper);
    }

    @Override
    public CabinetCellPo findByOrderId(Long orderId) {
        LambdaQueryWrapper<CabinetCellPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CabinetCellPo::getOrderId, orderId);
        return cabinetCellMapper.selectOne(queryWrapper);
    }

    @Override
    public List<CabinetCellPo> findOccupiedByUserId(Long userId) {
        LambdaQueryWrapper<CabinetCellPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CabinetCellPo::getUserId, userId)
                .eq(CabinetCellPo::getStatus, CellStatus.OCCUPIED);
        return cabinetCellMapper.selectList(queryWrapper);
    }

    @Override
    public int batchSave(List<CabinetCellPo> cells) {
        if (cells == null || cells.isEmpty()) {
            return 0;
        }
        // 使用MyBatis-Plus的saveBatch方法
        return cells.stream()
                .map(cabinetCellMapper::insert)
                .reduce(0, Integer::sum);
    }

    @Override
    public int deleteByCabinetId(Long cabinetId) {
        LambdaQueryWrapper<CabinetCellPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CabinetCellPo::getCabinetId, cabinetId);
        return cabinetCellMapper.delete(queryWrapper);
    }

    @Override
    public Map<Long, List<CabinetCellPo>> batchQueryCellsGroupedByCabinet(List<Long> cabinetIds) {
        LambdaQueryWrapper<CabinetCellPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .in(CabinetCellPo::getCabinetId, cabinetIds)
                .orderByAsc(CabinetCellPo::getCellType);

        List<CabinetCellPo> cells = cabinetCellMapper.selectList(queryWrapper);

        return cells.stream()
                .collect(Collectors.groupingBy(
                        CabinetCellPo::getCabinetId,
                        Collectors.toList()
                ));
    }
}