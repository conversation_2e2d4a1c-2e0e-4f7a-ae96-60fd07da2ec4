package com.demon.giraffe.modules.cabinet.model.dto.response;

import com.demon.giraffe.modules.cabinet.model.enums.CellStatus;
import com.demon.giraffe.modules.cabinet.model.enums.CellType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * 格口基础响应
 */
@Data
@AllArgsConstructor
@Schema(name = "CabinetCellResponse", description = "格口基础响应")
public class CabinetCellResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "格口主键ID")
    private Long id;

    @Schema(description = "格口类型")
    private CellType cellType;

    @Schema(description = "状态")
    private CellStatus status;

}