package com.demon.giraffe.modules.message.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 消息推送记录表（P1消息追踪表）
 * 对应数据库表：message_push_log
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("message_push_log")
public class MessagePushLogPo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotBlank
    @Size(max = 32)
    @TableField("push_no")
    @Schema(description = "推送编号（规则：MSG+年月日+8位序列）")
    private String pushNo;

    @Size(max = 32)
    @TableField("batch_no")
    @Schema(description = "批次号（用于批量推送）")
    private String batchNo;

    @NotNull
    @TableField("template_id")
    @Schema(description = "消息模板ID")
    private Long templateId;

    @NotBlank
    @Size(max = 50)
    @TableField("template_code")
    @Schema(description = "模板编码（冗余存储）")
    private String templateCode;

    @TableField("template_version")
    @Schema(description = "模板版本号")
    private Integer templateVersion;

    @NotNull
    @TableField("user_id")
    @Schema(description = "目标用户ID")
    private Long userId;

    @TableField("member_id")
    @Schema(description = "会员ID（关联业务）")
    private Long memberId;

    @TableField("user_type")
    @Schema(description = "用户类型：1-普通用户 2-商家 3-配送员")
    private Integer userType;

    @Size(max = 64)
    @TableField("device_id")
    @Schema(description = "设备标识")
    private String deviceId;

    @NotNull
    @TableField("channel_type")
    @Schema(description = "推送渠道：1-小程序模板 2-小程序订阅 3-微信客服 4-短信 5-APP推送 6-邮件")
    private Integer channelType;

    @NotNull
    @TableField("business_type")
    @Schema(description = "业务类型：1-订单 2-营销 3-系统 4-区域开放 5-账户安全")
    private Integer businessType;

    @TableField("business_id")
    @Schema(description = "关联业务ID")
    private Long businessId;

    @Size(max = 30)
    @TableField("scenario_code")
    @Schema(description = "场景编码（如：order_paid）")
    private String scenarioCode;

    @NotBlank
    @Size(max = 100)
    @TableField("title")
    @Schema(description = "消息标题")
    private String title;

    @NotBlank
    @TableField("content")
    @Schema(description = "实际发送内容")
    private String content;

    @TableField("content_variables")
    @Schema(description = "渲染变量（{\"orderNo\":\"123\"}）")
    private String contentVariables; // JSON字符串

    @TableField("raw_template")
    @Schema(description = "原始模板内容（冗余存储）")
    private String rawTemplate;

    @TableField("extra_data")
    @Schema(description = "额外数据（跳转参数/埋点数据等）")
    private String extraData; // JSON字符串

    @Size(max = 255)
    @TableField("deep_link")
    @Schema(description = "深度链接")
    private String deepLink;

    @Size(max = 200)
    @TableField("page_path")
    @Schema(description = "小程序页面路径")
    private String pagePath;

    @NotNull
    @TableField(value = "send_time", fill = FieldFill.INSERT)
    @Schema(description = "发送时间")
    private LocalDateTime sendTime;

    @TableField("schedule_time")
    @Schema(description = "预定发送时间")
    private LocalDateTime scheduleTime;

    @NotNull
    @TableField("send_status")
    @Schema(description = "发送状态：0-待发送 1-成功 2-失败 3-用户拒收 4-已取消")
    private Integer sendStatus;

    @Size(max = 64)
    @TableField("third_msg_id")
    @Schema(description = "第三方消息ID")
    private String thirdMsgId;

    @Size(max = 500)
    @TableField("failure_reason")
    @Schema(description = "失败详情")
    private String failureReason;

    @NotNull
    @TableField("retry_count")
    @Schema(description = "重试次数")
    private Integer retryCount;

    @TableField("final_send_time")
    @Schema(description = "最终发送时间")
    private LocalDateTime finalSendTime;

    @TableField("click_time")
    @Schema(description = "首次点击时间")
    private LocalDateTime clickTime;

    @TableField("is_clicked")
    @Schema(description = "是否已点击")
    private Boolean isClicked;

    @NotNull
    @TableField("click_count")
    @Schema(description = "点击次数")
    private Integer clickCount;

    @TableField("last_click_time")
    @Schema(description = "最后点击时间")
    private LocalDateTime lastClickTime;

    @TableField("conversion_data")
    @Schema(description = "转化数据（如：下单ID）")
    private String conversionData; // JSON字符串

}
