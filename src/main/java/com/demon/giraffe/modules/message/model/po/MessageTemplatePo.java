package com.demon.giraffe.modules.message.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * 消息推送模板表（P1消息中心）
 * 对应数据库表：message_template
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("message_template")
public class MessageTemplatePo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotBlank
    @Size(max = 50)
    @TableField("template_code")
    @Schema(description = "模板编码（业务唯一标识）")
    private String templateCode;

    @NotBlank
    @Size(max = 100)
    @TableField("template_name")
    @Schema(description = "模板名称")
    private String templateName;

    @NotNull
    @Min(1)
    @Max(5)
    @TableField("template_type")
    @Schema(description = "模板类型：1-订单通知 2-营销推广 3-系统通知 4-区域开放 5-账户安全")
    private Integer templateType;

    @NotNull
    @Min(1)
    @Max(6)
    @TableField("channel_type")
    @Schema(description = "推送渠道：1-小程序模板 2-小程序订阅 3-微信客服 4-短信 5-APP推送 6-邮件")
    private Integer channelType;

    @Size(max = 100)
    @TableField("wx_template_id")
    @Schema(description = "微信模板ID")
    private String wxTemplateId;

    @Size(max = 50)
    @TableField("sms_template_id")
    @Schema(description = "短信模板ID")
    private String smsTemplateId;

    @TableField("channel_config")
    @Schema(description = "渠道特殊配置")
    private String channelConfig; // JSON字符串

    @NotBlank
    @Size(max = 100)
    @TableField("title")
    @Schema(description = "消息标题")
    private String title;

    @NotBlank
    @TableField("content_template")
    @Schema(description = "内容模板（支持{{变量}}语法）")
    private String contentTemplate;

    @TableField("content_sample")
    @Schema(description = "渲染示例（自动生成）")
    private String contentSample;

    @Size(max = 10)
    @TableField("language")
    @Schema(description = "语言版本")
    private String language;

    @Size(max = 200)
    @TableField("jump_page")
    @Schema(description = "跳转页面（path?param=value）")
    private String jumpPage;

    @TableField("jump_type")
    @Schema(description = "跳转类型：1-小程序页 2-Web页 3-APP页")
    private Integer jumpType;

    @TableField("button_config")
    @Schema(description = "按钮配置（[{text:\"\",action:\"\"}]）")
    private String buttonConfig; // JSON字符串

    @TableField("keyword_mapping")
    @Schema(description = "变量映射（{\"orderNo\":\"order_no\"}）")
    private String keywordMapping; // JSON字符串

    @TableField("variable_rules")
    @Schema(description = "变量校验规则（{\"phone\":\"^1\\\\d{10}$\"}）")
    private String variableRules; // JSON字符串

    @TableField("send_condition")
    @Schema(description = "发送条件（{\"time_range\":[9,18],\"user_tags\":[1,3]}）")
    private String sendCondition; // JSON字符串

    @NotNull
    @Min(1)
    @Max(5)
    @TableField("priority")
    @Schema(description = "优先级：1-5（数字越大优先级越高）")
    private Integer priority;

    @NotNull
    @Min(0)
    @Max(2)
    @TableField("status")
    @Schema(description = "状态：0-正常 1-停用 2-测试中")
    private Integer status;

    @NotNull
    @Min(0)
    @TableField("daily_limit")
    @Schema(description = "每日发送上限")
    private Integer dailyLimit;

    @NotNull
    @Min(0)
    @TableField("user_limit")
    @Schema(description = "每用户每日上限")
    private Integer userLimit;

    @TableField("rate_limit")
    @Schema(description = "每分钟发送上限")
    private Integer rateLimit;

    @TableField("blacklist_config")
    @Schema(description = "黑名单配置")
    private String blacklistConfig; // JSON字符串

}
