package com.demon.giraffe.modules.message.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.math.BigDecimal;

/**
 * 用户消息偏好表（P2用户体验优化）
 * 对应数据库表：user_message_preference
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("user_message_preference")
public class UserMessagePreferencePo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull
    @Schema(description = "用户ID")
    @TableField("user_id")
    private Long userId;

    @Schema(description = "会员ID（冗余存储）")
    @TableField("member_id")
    private Long memberId;

    @Size(max = 64)
    @Schema(description = "设备标识（多端同步用）")
    @TableField("device_id")
    private String deviceId;

    @NotNull
    @Schema(description = "消息类型：1-订单通知 2-营销推广 3-系统通知 4-区域开放 5-账户安全")
    @TableField("message_type")
    private Integer messageType;

    @Size(max = 30)
    @Schema(description = "消息子类型（如：order_paid）")
    @TableField("message_subtype")
    private String messageSubtype;

    @NotNull
    @Schema(description = "渠道类型：1-小程序 2-短信 3-APP推送 4-邮件 5-微信客服")
    @TableField("channel_type")
    private Integer channelType;

    @NotNull
    @Schema(description = "是否启用：0-关闭 1-开启")
    @TableField("is_enabled")
    private Boolean isEnabled;

    @Schema(description = "允许推送开始时间")
    @TableField("time_range_start")
    private LocalTime timeRangeStart;

    @Schema(description = "允许推送结束时间")
    @TableField("time_range_end")
    private LocalTime timeRangeEnd;

    @NotNull
    @Schema(description = "每日接收频次限制")
    @TableField("frequency_limit")
    private Integer frequencyLimit;

    @Schema(description = "优先级过滤（仅接收该级别以上）")
    @TableField("priority_filter")
    private Integer priorityFilter;

    @Schema(description = "最后推送时间")
    @TableField("last_push_time")
    private LocalDateTime lastPushTime;

    @NotNull
    @Schema(description = "累计接收数量")
    @TableField("total_received")
    private Integer totalReceived;

    @NotNull
    @Schema(description = "累计点击数量")
    @TableField("total_clicked")
    private Integer totalClicked;

    @Schema(description = "点击率（自动计算）")
    @TableField("click_rate")
    private BigDecimal clickRate;

    @Schema(description = "临时屏蔽截止时间")
    @TableField("block_until")
    private LocalDateTime blockUntil;

    @Size(max = 50)
    @Schema(description = "屏蔽原因（用户设置/系统判定）")
    @TableField("block_reason")
    private String blockReason;

    @Schema(description = "全局屏蔽（所有消息类型）")
    @TableField("global_block")
    private Boolean globalBlock;

}
