package com.demon.giraffe.modules.message.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 推送任务队列表（P2异步处理）
 * 对应数据库表：message_push_queue
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("message_push_queue")
public class MessagePushQueuePo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotBlank
    @Size(max = 32)
    @Schema(description = "任务编号（规则：TASK+年月日+6位序列）")
    @TableField("task_no")
    private String taskNo;

    @Size(max = 32)
    @Schema(description = "批次号（用于关联任务）")
    @TableField("batch_no")
    private String batchNo;

    @NotNull
    @Schema(description = "消息模板ID")
    @TableField("template_id")
    private Long templateId;

    @Size(max = 50)
    @Schema(description = "模板编码（冗余存储）")
    @TableField("template_code")
    private String templateCode;

    @NotNull
    @Schema(description = "内容变量数据（{\"orderNo\":\"123\"}）")
    @TableField("content_data")
    private String contentData; // json字符串存储，或用JSONObject/Map等，根据项目习惯

    @Size(max = 100)
    @Schema(description = "消息标题（冗余存储）")
    @TableField("message_title")
    private String messageTitle;

    @NotNull
    @Schema(description = "目标用户ID列表（[1001,1002]）")
    @TableField("user_ids")
    private String userIds; // json字符串或 List<Long>，视项目json处理能力决定

    @Schema(description = "用户筛选条件（{\"tags\":[1,3],\"vip\":true}）")
    @TableField("user_filter")
    private String userFilter;

    @Schema(description = "排除用户ID列表")
    @TableField("exclude_users")
    private String excludeUsers;

    @NotNull
    @Schema(description = "目标用户总数")
    @TableField("total_count")
    private Integer totalCount;

    @NotNull
    @Schema(description = "业务类型：1-订单 2-营销 3-系统 4-区域开放")
    @TableField("business_type")
    private Integer businessType;

    @Schema(description = "关联业务ID")
    @TableField("business_id")
    private Long businessId;

    @Size(max = 30)
    @Schema(description = "场景编码（如：order_paid）")
    @TableField("scenario_code")
    private String scenarioCode;

    @Schema(description = "预定发送时间（NULL表示立即发送）")
    @TableField("scheduled_time")
    private LocalDateTime scheduledTime;

    @NotNull
    @Schema(description = "任务优先级：1-5（数字越大优先级越高）")
    @TableField("priority")
    private Integer priority;

    @Schema(description = "任务过期时间")
    @TableField("expire_time")
    private LocalDateTime expireTime;

    @Schema(description = "重试策略（{\"max_attempts\":3,\"interval\":300}）")
    @TableField("retry_policy")
    private String retryPolicy;

    @NotNull
    @Schema(description = "任务状态：0-待处理 1-处理中 2-已完成 3-已失败 4-已取消")
    @TableField("status")
    private Integer status;

    @Schema(description = "当前进度（已处理数）")
    @TableField("current_progress")
    private Integer currentProgress;

    @NotNull
    @Schema(description = "发送成功数")
    @TableField("success_count")
    private Integer successCount;

    @NotNull
    @Schema(description = "发送失败数")
    @TableField("failure_count")
    private Integer failureCount;

    @Schema(description = "失败用户记录")
    @TableField("failure_users")
    private String failureUsers;

    @Schema(description = "开始处理时间")
    @TableField("start_time")
    private LocalDateTime startTime;

    @Schema(description = "完成时间")
    @TableField("finish_time")
    private LocalDateTime finishTime;

    @Size(max = 50)
    @Schema(description = "执行节点标识")
    @TableField("executor_node")
    private String executorNode;

    @Schema(description = "失败原因详情")
    @TableField("failure_reason")
    private String failureReason;

    // 乐观锁版本号，BasePo中可能已经包含，如没有再加
    @Version
    @Schema(description = "乐观锁版本号")
    @TableField("version")
    private Integer version;
}
