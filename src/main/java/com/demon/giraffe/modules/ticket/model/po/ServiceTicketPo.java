package com.demon.giraffe.modules.ticket.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 客服工单表（P3客户服务）
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("service_ticket")
@Schema(description = "客服工单表")
public class ServiceTicketPo extends BasePo {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "工单号")
    @NotBlank
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String ticketNo;

    @Schema(description = "工单分类编码")
    private String ticketCode;

    @Schema(description = "用户ID")
    private Long memberId;

    @Schema(description = "用户姓名")
    private String memberName;

    @Schema(description = "用户等级")
    private Integer memberLevel;

    @Schema(description = "关联订单ID")
    private Long orderId;

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "关联工单ID")
    private Long relatedTicketId;

    @Schema(description = "工单类型（1-投诉 2-建议 3-咨询 4-退款申请 5-异常处理 6-账户问题）")
    @NotNull
    private Integer type;

    @Schema(description = "工单子类型")
    private String subType;

    @Schema(description = "优先级（1-低 2-中 3-高 4-紧急）")
    @NotNull
    private Integer priority;

    @Schema(description = "来源渠道（1-APP 2-电话 3-网页 4-邮件 5-社交媒体）")
    private Integer channel;

    @Schema(description = "标题")
    @NotBlank
    private String title;

    @Schema(description = "内容描述")
    @NotBlank
    private String content;

    @Schema(description = "相关图片")
    @TableField(typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private List<Map<String, Object>> images;

    @Schema(description = "附件")
    @TableField(typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private List<Map<String, Object>> attachments;

    @Schema(description = "联系方式")
    private String contactInfo;

    @Schema(description = "联系方式类型（1-电话 2-邮箱 3-微信）")
    private Integer contactType;

    @Schema(description = "偏好联系时间段")
    private String preferredTime;

    @Schema(description = "状态（0-待处理 1-处理中 2-已解决 3-已关闭 4-已驳回 5-待反馈）")
    @NotNull
    private Integer status;

    @Schema(description = "当前处理人ID")
    private Long currentHandler;

    @Schema(description = "当前处理部门")
    private Integer currentDepartment;

    @Schema(description = "首次响应时间")
    private LocalDateTime firstResponseTime;

    @Schema(description = "解决时间")
    private LocalDateTime resolveTime;

    @Schema(description = "关闭时间")
    private LocalDateTime closeTime;

    @Schema(description = "满意度评分（1-5星）")
    private Integer satisfaction;

    @Schema(description = "评价内容")
    private String evaluationComment;

    @Schema(description = "响应速度评分")
    private Integer responseRating;

    @Schema(description = "解决方案评分")
    private Integer solutionRating;

    @Schema(description = "乐观锁")
    @Version
    private Integer version;
}
