package com.demon.giraffe.modules.marketing.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.modules.marketing.mapper.CouponTemplateMapper;
import com.demon.giraffe.modules.marketing.model.dto.query.CouponTemplatePageQuery;
import com.demon.giraffe.modules.marketing.model.po.CouponTemplatePo;
import com.demon.giraffe.modules.marketing.model.enums.CouponStatusEnum;
import com.demon.giraffe.modules.marketing.repository.CouponTemplateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 优惠券模板仓库实现
 */
@Repository
@RequiredArgsConstructor
public class CouponTemplateRepositoryImpl implements CouponTemplateRepository {

    private final CouponTemplateMapper baseMapper;

    @Override
    public CouponTemplatePo save(CouponTemplatePo po) {
        if (po.getId() == null) {
            baseMapper.insert(po);
        } else {
            baseMapper.updateById(po);
        }
        return po;
    }

    @Override
    public CouponTemplatePo getById(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public CouponTemplatePo getByCode(String templateCode) {

        LambdaUpdateWrapper<CouponTemplatePo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CouponTemplatePo::getTemplateCode, templateCode);
        return baseMapper.selectOne(wrapper);
    }

    @Override
    public boolean updateStatus(Long id, CouponStatusEnum status) {
        LambdaUpdateWrapper<CouponTemplatePo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CouponTemplatePo::getId, id)
                .set(CouponTemplatePo::getStatus, status);
        return baseMapper.update(null, wrapper) > 0;
    }

    @Override
    public boolean delete(Long id) {
        return baseMapper.deleteById(id) > 0;
    }

    @Override
    public IPage<CouponTemplatePo> pageQuery(IPage<CouponTemplatePo> page, CouponStatusEnum status) {
        LambdaQueryWrapper<CouponTemplatePo> queryWrapper = new LambdaQueryWrapper<>();

        // 状态条件
        if (status != null) {
            queryWrapper.eq(CouponTemplatePo::getStatus, status);
        }

        // 按创建时间倒序
        queryWrapper.orderByDesc(CouponTemplatePo::getCreateTime);

        return baseMapper.selectPage(page, queryWrapper);
    }

    @Override
    public Boolean incrementIssuedCount(Long templateId, Integer increment) {
        if (templateId == null || increment == null || increment <= 0) {
            return false;
        }

        int updated = baseMapper.incrementIssuedCount(templateId, increment);
        return updated > 0;
    }

    @Override
    public boolean decrementUsedCount(Long templateId) {
        LambdaUpdateWrapper<CouponTemplatePo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CouponTemplatePo::getId, templateId)
                .setSql("used_count = used_count - 1");

        return baseMapper.update(null, updateWrapper) > 0;
    }

    @Override
    public List<CouponTemplatePo> listByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<CouponTemplatePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CouponTemplatePo::getId, ids)
                .eq(CouponTemplatePo::getStatus, CouponStatusEnum.NORMAL); // 只查询可用的模板

        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<CouponTemplatePo> pageQueryByConditions(BasePageQuery<CouponTemplatePageQuery> pageQuery) {
        // 初始化分页参数
        pageQuery.init();

        // 创建分页对象
        Page<CouponTemplatePo> page = new Page<>(
                Objects.nonNull(pageQuery.getPage()) ? pageQuery.getPage() : 1,
                Objects.nonNull(pageQuery.getPerPage()) ? pageQuery.getPerPage() : 10
        );

        // 获取查询条件
        CouponTemplatePageQuery query = pageQuery.getQuery();
        LambdaQueryWrapper<CouponTemplatePo> wrapper = new LambdaQueryWrapper<>();

        if(query != null){
            // 状态条件
            if (query.getStatus() != null) {
                wrapper.eq(CouponTemplatePo::getStatus, query.getStatus());
            }

            // 类型条件
            if (query.getType() != null) {
                wrapper.eq(CouponTemplatePo::getType, query.getType());
            }

            // 模板名称模糊查询
            if (StringUtils.hasText(query.getNameKeyword())) {
                wrapper.like(CouponTemplatePo::getName, query.getNameKeyword());
            }

            // 模板编码精确查询
            if (StringUtils.hasText(query.getTemplateCode())) {
                wrapper.eq(CouponTemplatePo::getTemplateCode, query.getTemplateCode());
            }
        }

        // 按创建时间倒序
        wrapper.orderByDesc(CouponTemplatePo::getCreateTime);

        return baseMapper.selectPage(page, wrapper);
    }

}