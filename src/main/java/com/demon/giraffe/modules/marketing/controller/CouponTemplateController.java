package com.demon.giraffe.modules.marketing.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.baomidou.mybatisplus.core.metadata.IPage;

import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.marketing.model.dto.query.CouponTemplatePageQuery;
import com.demon.giraffe.modules.marketing.model.dto.request.CouponTemplateCreateRequest;
import com.demon.giraffe.modules.marketing.model.dto.response.CouponTemplateDetailResponse;
import com.demon.giraffe.modules.marketing.model.enums.CouponStatusEnum;
import com.demon.giraffe.modules.marketing.service.CouponTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import  org.springframework.web.bind.annotation.RequestBody;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 优惠券模板管理接口
 */
@RestController
@RequestMapping("/marketing/coupon/template")
@Tag(name = "优惠券模板管理接口", description = "优惠券模板的创建、查询、状态管理等")
public class CouponTemplateController {

    private final CouponTemplateService couponTemplateService;

    public CouponTemplateController(CouponTemplateService couponTemplateService) {
        this.couponTemplateService = couponTemplateService;
    }

    @PostMapping("/create")
    @Operation(summary = "创建优惠券模板", description = "创建一个新的优惠券模板")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "优惠券模板创建参数",
            required = true,
            content = @Content(schema = @Schema(implementation = CouponTemplateCreateRequest.class))
    )
    public ResultBean<CouponTemplateDetailResponse> createTemplate(
            @Valid @RequestBody CouponTemplateCreateRequest request) {
        CouponTemplateDetailResponse response = couponTemplateService.createTemplate(request);
        return ResultBean.success(response);
    }

    @GetMapping("/detail/{id}")
    @Operation(summary = "获取优惠券模板详情", description = "根据ID获取优惠券模板详细信息")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<CouponTemplateDetailResponse> getTemplateDetail(
            @PathVariable Long id) {
        CouponTemplateDetailResponse response = couponTemplateService.getTemplateDetail(id);
        return ResultBean.success(response);
    }

    @PostMapping("/status/update")
    @Operation(summary = "更新优惠券模板状态", description = "更新优惠券模板的状态（启用/禁用）")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<Boolean> updateTemplateStatus(
            @RequestParam Long id,
            @RequestParam CouponStatusEnum status) {
        boolean result = couponTemplateService.updateTemplateStatus(id, status);
        return ResultBean.success(result);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除优惠券模板", description = "根据ID删除优惠券模板")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<Boolean> deleteTemplate(
            @RequestParam Long id) {
        boolean result = couponTemplateService.deleteTemplate(id);
        return ResultBean.success(result);
    }

    @PostMapping("/page")
    @Operation(summary = "分页查询优惠券模板", description = "分页查询优惠券模板列表")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<IPage<CouponTemplateDetailResponse>> pageQueryTemplate(
            @RequestBody BasePageQuery<CouponTemplatePageQuery> pageQuery) {
        IPage<CouponTemplateDetailResponse> result = couponTemplateService.pageQueryTemplate(pageQuery);
        return ResultBean.success(result);
    }
}