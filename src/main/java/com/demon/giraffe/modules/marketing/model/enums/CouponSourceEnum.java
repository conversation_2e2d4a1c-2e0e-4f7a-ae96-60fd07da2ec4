package com.demon.giraffe.modules.marketing.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "优惠券来源枚举")
public enum CouponSourceEnum implements IEnum<Integer> {
    @Schema(description = "主动领取")
    MANUAL_CLAIM(1, "主动领取"),

    @Schema(description = "系统赠送")
    SYSTEM_GIFT(2, "系统赠送"),

    @Schema(description = "活动获得")
    ACTIVITY(3, "活动获得"),

    @Schema(description = "积分兑换")
    POINTS_EXCHANGE(4, "积分兑换"),
    ;

    @EnumValue
    private final int code;
    private final String desc;

    CouponSourceEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CouponSourceEnum of(int code) {
        for (CouponSourceEnum source : values()) {
            if (source.code == code) {
                return source;
            }
        }
        throw new IllegalArgumentException("无效的优惠券来源: " + code);
    }

    @Override
    public Integer getValue() {
        return code;
    }
}