package com.demon.giraffe.modules.marketing.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "优惠券类型枚举")
public enum CouponTypeEnum implements IEnum<Integer> {
    @Schema(description = "满减券")
    FULL_REDUCTION(1, "满减券"),
    
    @Schema(description = "折扣券")
    DISCOUNT(2, "折扣券");

    @EnumValue
    private final int code;
    private final String desc;

    CouponTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CouponTypeEnum of(int code) {
        for (CouponTypeEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的优惠券类型: " + code);
    }

    @Override
    public Integer getValue() {
        return code;
    }
}