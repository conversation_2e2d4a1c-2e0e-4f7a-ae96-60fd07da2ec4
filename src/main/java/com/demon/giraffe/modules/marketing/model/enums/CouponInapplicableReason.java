package com.demon.giraffe.modules.marketing.model.enums;

public enum CouponInapplicableReason {
    APPLICABLE("适用"),
    AMOUNT_NOT_ENOUGH("订单金额不足"),
    COUPON_NOT_AVAILABLE("优惠券不可用");

    // 已移除的限制原因：
    // SERVICE_NOT_MATCH("服务分类不匹配"),
    // SERVICE_ITEM_NOT_MATCH("服务项不匹配"),
    // REGION_NOT_MATCH("区域不匹配"),

    private final String description;

    CouponInapplicableReason(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}