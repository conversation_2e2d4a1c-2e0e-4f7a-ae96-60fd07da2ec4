package com.demon.giraffe.modules.marketing.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.modules.marketing.model.enums.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 优惠券模板持久化对象
 * <p>定义优惠券的基本规则和属性</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("coupon_template")
@Schema(description = "优惠券模板实体")
public class CouponTemplatePo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /* 数据库字段：template_code */
    @Schema(description = "模板编码（唯一业务标识）", example = "TEMP20230710001")
    @TableField("template_code")
    private String templateCode;

    /* 数据库字段：name */
    @Schema(description = "优惠券名称", example = "新用户专享优惠券")
    @TableField("name")
    private String name;

    /* 数据库字段：type */
    @Schema(description = "类型：1-满减券 2-折扣券", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("type")
    private CouponTypeEnum type;

    /* 数据库字段：discount_type */
    @Schema(description = "优惠类型：1-金额 2-比例", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("discount_type")
    private DiscountTypeEnum discountType;

    /* 数据库字段：discount_value */
    @Schema(description = "优惠值（金额或折扣率）", example = "10.00")
    @TableField("discount_value")
    private BigDecimal discountValue;

    /* 数据库字段：min_amount */
    @Schema(description = "最低消费金额", example = "100.00")
    @TableField("min_amount")
    private BigDecimal minAmount;

    /* 数据库字段：max_discount */
    @Schema(description = "最大优惠金额（折扣券用）", example = "50.00")
    @TableField("max_discount")
    private BigDecimal maxDiscount;

    /* 数据库字段：valid_days */
    @Schema(description = "领取后有效天数（单位：天）", example = "30")
    @TableField("valid_days")
    private Integer validDays;

    // 移除了地区限制、适用服务限制、适用商品项ID限制字段

    /* 数据库字段：total_count */
    @Schema(description = "总发放数量（0表示不限制）", example = "1000")
    @TableField("total_count")
    private Integer totalCount;

    /* 数据库字段：issued_count */
    @Schema(description = "已发放数量", example = "500")
    @TableField("issued_count")
    private Integer issuedCount;

    /* 数据库字段：used_count */
    @Schema(description = "已使用数量", example = "200")
    @TableField("used_count")
    private Integer usedCount;

    /* 数据库字段：per_user_limit */
    @Schema(description = "每人限领数量", example = "2")
    @TableField("per_user_limit")
    private Integer perUserLimit;

    /* 数据库字段：icon_url */
    @Schema(description = "图标URL", example = "https://example.com/icon.png")
    @TableField("icon_url")
    private String iconUrl;

    /* 数据库字段：description */
    @Schema(description = "使用说明", example = "本券仅限新用户使用")
    @TableField("description")
    private String description;

    /* 数据库字段：start_time */
    @Schema(description = "领取开始时间", example = "2023-07-10T00:00:00")
    @TableField("start_time")
    private LocalDateTime startTime;

    /* 数据库字段：end_time */
    @Schema(description = "领取截止时间", example = "2023-07-31T23:59:59")
    @TableField("end_time")
    private LocalDateTime endTime;

    /* 数据库字段：status */
    @Schema(description = "模板状态", implementation = CouponStatusEnum.class)
    @TableField("status")
    private CouponStatusEnum status;
}