package com.demon.giraffe.modules.marketing.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "优惠券状态枚举")
public enum CouponStatusEnum implements IEnum<Integer> {
    @Schema(description = "正常")
    NORMAL(0, "正常"),

    @Schema(description = "停用")
    DISABLED(1, "停用"),
    ;

    @EnumValue
    private final int code;
    private final String desc;

    CouponStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CouponStatusEnum of(int code) {
        for (CouponStatusEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("无效的优惠券状态: " + code);
    }

    @Override
    public Integer getValue() {
        return code;
    }
}