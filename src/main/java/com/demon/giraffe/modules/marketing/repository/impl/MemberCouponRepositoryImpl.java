package com.demon.giraffe.modules.marketing.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.modules.marketing.mapper.MemberCouponMapper;
import com.demon.giraffe.modules.marketing.model.dto.query.MemberCouponPageQuery;
import com.demon.giraffe.modules.marketing.model.enums.CouponStatusEnum;
import com.demon.giraffe.modules.marketing.model.po.MemberCouponPo;
import com.demon.giraffe.modules.marketing.repository.MemberCouponRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 用户优惠券仓库实现（更新版）
 */
@Repository
@RequiredArgsConstructor
public class MemberCouponRepositoryImpl implements MemberCouponRepository {

    private final MemberCouponMapper memberCouponMapper;

    @Override
    public MemberCouponPo save(MemberCouponPo po) {
        if (po.getId() == null) {
            memberCouponMapper.insert(po);
        } else {
            memberCouponMapper.updateById(po);
        }
        return po;
    }

    @Override
    public MemberCouponPo getById(Long id) {
        return memberCouponMapper.selectById(id);
    }


    @Override
    public boolean markAsUsed(Long orderId, LocalDateTime useTime) {
        LambdaUpdateWrapper<MemberCouponPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(MemberCouponPo::getOrderId, orderId)
                .set(MemberCouponPo::getUseTime, useTime)
                .eq(MemberCouponPo::getId, orderId);
        return memberCouponMapper.update(null, updateWrapper) == 1;
    }

    @Override
    public IPage<MemberCouponPo> pageQueryByMember(IPage<MemberCouponPo> page, Long memberId, Boolean isUsed) {
        LambdaQueryWrapper<MemberCouponPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberCouponPo::getMemberId, memberId)
                .eq(isUsed != null, MemberCouponPo::getOrderId, isUsed ? null : "IS NOT NULL");
        return memberCouponMapper.selectPage(page, queryWrapper);
    }

    @Override
    public List<MemberCouponPo> listAvailableCoupons(Long memberId, BigDecimal amount) {
        return memberCouponMapper.selectAvailableCoupons(memberId, amount);
    }

    @Override
    public List<MemberCouponPo> listValidCoupons(Long memberId, LocalDateTime now) {
        LambdaQueryWrapper<MemberCouponPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberCouponPo::getMemberId, memberId)
                .isNull(MemberCouponPo::getOrderId) // 未使用
                .le(MemberCouponPo::getStartTime, now)  // 已生效
                .ge(MemberCouponPo::getExpireTime, now); // 未过期
        return memberCouponMapper.selectList(queryWrapper);
    }

    @Override
    public long countByMemberAndTemplate(Long memberId, Long templateId) {
        LambdaQueryWrapper<MemberCouponPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberCouponPo::getMemberId, memberId)
                .eq(MemberCouponPo::getTemplateId, templateId);
        return memberCouponMapper.selectCount(queryWrapper);
    }

    @Override
    public boolean cancelCouponUsage(Long couponId) {
        LambdaUpdateWrapper<MemberCouponPo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MemberCouponPo::getId, couponId)
                .set(MemberCouponPo::getStatus, CouponStatusEnum.NORMAL)
                .set(MemberCouponPo::getOrderId, null)
                .set(MemberCouponPo::getUseTime, null);

        return memberCouponMapper.update(null, updateWrapper) > 0;
    }

    @Override
    public List<MemberCouponPo> listByMemberAndStatus(Long memberId, CouponStatusEnum status, LocalDateTime now) {
        LambdaQueryWrapper<MemberCouponPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberCouponPo::getMemberId, memberId)
                .eq(MemberCouponPo::getStatus, status)
                .le(MemberCouponPo::getStartTime, now)  // 已生效
                .ge(MemberCouponPo::getExpireTime, now) // 未过期
                .isNull(MemberCouponPo::getOrderId);    // 未使用

        return memberCouponMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<MemberCouponPo> pageQueryByConditions(BasePageQuery<MemberCouponPageQuery> pageQuery) {
        // 初始化分页参数
        pageQuery.init();

        // 创建分页对象
        Page<MemberCouponPo> page = new Page<>(
                Objects.nonNull(pageQuery.getPage()) ? pageQuery.getPage() : 1,
                Objects.nonNull(pageQuery.getPerPage()) ? pageQuery.getPerPage() : 10
        );

        // 获取查询条件
        MemberCouponPageQuery query = pageQuery.getQuery();
        LambdaQueryWrapper<MemberCouponPo> wrapper = new LambdaQueryWrapper<>();

        // 如果查询条件为空，直接返回所有数据（按时间倒序）
        if (query == null) {
            wrapper.orderByDesc(MemberCouponPo::getReceiveTime);
            return memberCouponMapper.selectPage(page, wrapper);
        }

        // 会员ID条件（必须）
        if (query.getMemberId() != null) {
            wrapper.eq(MemberCouponPo::getMemberId, query.getMemberId());
        }

        // 状态条件
        if (query.getStatus() != null) {
            wrapper.eq(MemberCouponPo::getStatus, query.getStatus());
        }


        // 是否已使用条件
        if (query.getIsUsed() != null) {
            if (query.getIsUsed()) {
                wrapper.isNotNull(MemberCouponPo::getOrderId);
            } else {
                wrapper.isNull(MemberCouponPo::getOrderId);
            }
        }

        // 是否已过期条件
        if (query.getIsExpired() != null) {
            LocalDateTime now = LocalDateTime.now();
            if (query.getIsExpired()) {
                wrapper.lt(MemberCouponPo::getExpireTime, now);
            } else {
                wrapper.ge(MemberCouponPo::getExpireTime, now);
            }
        }

        // 按领取时间倒序
        wrapper.orderByDesc(MemberCouponPo::getReceiveTime);

        return memberCouponMapper.selectPage(page, wrapper);
    }

    @Override
    public MemberCouponPo getByOrderId(Long orderId) {
        if (orderId == null) {
            return null;
        }

        LambdaQueryWrapper<MemberCouponPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberCouponPo::getOrderId, orderId);

        return memberCouponMapper.selectOne(wrapper);
    }
}