package com.demon.giraffe.modules.marketing.util;

import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.modules.marketing.model.entity.Coupon;
import com.demon.giraffe.modules.marketing.model.enums.CouponInapplicableReason;
import com.demon.giraffe.modules.marketing.model.enums.CouponStatusEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 优惠券计算工具类
 *
 * <AUTHOR>
 * @date 24/7/2025
 */
public class CouponCalculator {

    /**
     * 计算优惠券可抵扣金额
     *
     * @param coupon 优惠券实体
     * @param orderAmount 订单金额
     * @return 实际可抵扣金额
     * @throws BusinessException 当不满足使用条件时抛出
     *
     * @apiNote 支持两种优惠券类型：
     *          1. 满减券(FULL_REDUCTION): 直接减免固定金额
     *          2. 折扣券(DISCOUNT): 按比例折扣，可设置最高优惠上限
     *          最终返回金额不会超过订单金额本身
     */
    public static BigDecimal calculateDiscountAmount(Coupon coupon, BigDecimal orderAmount) throws BusinessException {
        // 参数校验
        if (coupon == null || !coupon.isAvailable()) {
            return BigDecimal.ZERO;
        }

        // 检查最低消费门槛
        CouponInapplicableReason couponInapplicableReason = coupon.checkApplicable(orderAmount);
        if(couponInapplicableReason != CouponInapplicableReason.APPLICABLE){
           throw new BusinessException("优惠券异常："+couponInapplicableReason.getDescription());
        }

        // 根据优惠券类型计算优惠金额
        BigDecimal discountAmount = calculateByCouponType(coupon, orderAmount);

        // 确保优惠金额不超过订单金额
        return applyMaximumDiscountLimit(discountAmount, orderAmount);
    }




    /**
     * 根据优惠券类型计算优惠金额
     */
    private static BigDecimal calculateByCouponType(Coupon coupon, BigDecimal orderAmount) {
        switch (coupon.getType()) {
            case FULL_REDUCTION:
                return coupon.getDiscountValue();
            case DISCOUNT:
                return calculatePercentageDiscount(coupon, orderAmount);
            default:
                return BigDecimal.ZERO;
        }
    }

    /**
     * 计算百分比折扣金额
     */
    private static BigDecimal calculatePercentageDiscount(Coupon coupon, BigDecimal orderAmount) {
        BigDecimal discountAmount = orderAmount.multiply(
                BigDecimal.ONE.subtract(coupon.getDiscountValue()));

        // 应用最高优惠限制
        if (coupon.getMaxDiscount() != null) {
            discountAmount = discountAmount.min(coupon.getMaxDiscount());
        }

        return discountAmount;
    }

    /**
     * 应用最终优惠金额限制（不超过订单金额）
     */
    private static BigDecimal applyMaximumDiscountLimit(BigDecimal discountAmount, BigDecimal orderAmount) {
        return discountAmount.min(orderAmount);
    }
}