package com.demon.giraffe.modules.marketing.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.demon.giraffe.common.domain.BasePageQuery;

import com.demon.giraffe.modules.marketing.model.dto.query.MemberCouponPageQuery;
import com.demon.giraffe.modules.marketing.model.enums.CouponStatusEnum;
import com.demon.giraffe.modules.marketing.model.po.MemberCouponPo;
import com.demon.giraffe.modules.marketing.model.enums.CouponSourceEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户优惠券仓库接口（更新版，不依赖CouponStatusEnum）
 */
public interface MemberCouponRepository {

    /**
     * 保存用户优惠券
     * @param po 用户优惠券PO
     * @return 保存后的用户优惠券
     */
    MemberCouponPo save(MemberCouponPo po);

    /**
     * 根据ID获取用户优惠券
     * @param id 用户优惠券ID
     * @return 用户优惠券
     */
    MemberCouponPo getById(Long id);


    /**
     * 标记优惠券为已使用
     * @param orderId 订单ID
     * @param useTime 使用时间
     * @return 是否更新成功
     */
    boolean markAsUsed( Long orderId, LocalDateTime useTime);

    /**
     * 分页查询用户优惠券
     * @param page 分页参数
     * @param memberId 用户ID
     * @param isUsed 是否已使用
     * @return 分页结果
     */
    IPage<MemberCouponPo> pageQueryByMember(IPage<MemberCouponPo> page, Long memberId, Boolean isUsed);

    /**
     * 获取用户可用的优惠券列表（已移除地区、服务、商品项限制）
     * @param memberId 用户ID
     * @param amount 订单金额
     * @return 可用优惠券列表
     */
    List<MemberCouponPo> listAvailableCoupons(Long memberId, BigDecimal amount);

    /**
     * 查询用户有效优惠券（基于时间判断）
     * @param memberId 用户ID
     * @param now 当前时间
     * @return 有效优惠券列表
     */
    List<MemberCouponPo> listValidCoupons(Long memberId, LocalDateTime now);

    /**
     * 统计用户领取某模板的优惠券数量
     * @param memberId 用户ID
     * @param templateId 模板ID
     * @return 数量
     */
    long countByMemberAndTemplate(Long memberId, Long templateId);

    /**
     * 取消使用优惠券（回退状态）
     * @param couponId 优惠券ID
     * @return 是否回退成功
     */
    boolean cancelCouponUsage(Long couponId);

    /**
     * 根据用户ID和状态查询优惠券
     * @param memberId 用户ID
     * @param status 优惠券状态
     * @param now 当前时间（用于检查有效期）
     * @return 符合条件的用户优惠券列表
     */
    List<MemberCouponPo> listByMemberAndStatus(Long memberId, CouponStatusEnum status, LocalDateTime now);

    /**
     * 分页查询用户优惠券（支持复杂查询条件）
     * @param pageQuery 分页查询参数
     * @return 分页结果
     */
    IPage<MemberCouponPo> pageQueryByConditions(BasePageQuery<MemberCouponPageQuery> pageQuery);

    /**
     * 根据订单ID查询使用的优惠券
     * @param orderId 订单ID
     * @return 使用的优惠券，如果没有使用优惠券则返回null
     */
    MemberCouponPo getByOrderId(Long orderId);
}