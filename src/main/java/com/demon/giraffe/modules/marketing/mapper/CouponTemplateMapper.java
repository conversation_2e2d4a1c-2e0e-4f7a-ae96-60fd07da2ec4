package com.demon.giraffe.modules.marketing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.demon.giraffe.modules.marketing.model.po.CouponTemplatePo;
import org.apache.ibatis.annotations.*;

/**
 * <AUTHOR>
 * @description TODO
 * @date 9/7/2025  下午10:52
 */
@Mapper
public interface CouponTemplateMapper extends BaseMapper<CouponTemplatePo> {

    /**
     * 原子更新优惠券模板已发放数量，防止超发
     * 仅在 issued_count + increment <= total_count 时更新
     *
     * @param id        模板ID
     * @param increment 增量（一般为 1）
     * @return 受影响行数（0 表示失败，1 表示成功）
     */
    @Update("""
            UPDATE coupon_template
            SET issued_count = issued_count + #{increment}
            WHERE id = #{id}
              AND issued_count + #{increment} <= total_count
            """)
    int incrementIssuedCount(@Param("id") Long id, @Param("increment") Integer increment);
}