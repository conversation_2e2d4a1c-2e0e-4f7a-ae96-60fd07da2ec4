package com.demon.giraffe.modules.marketing.service.helper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.common.util.CodeGeneratorUtil;
import com.demon.giraffe.common.util.TimeUtil;
import com.demon.giraffe.modules.marketing.model.dto.request.CouponTemplateCreateRequest;
import com.demon.giraffe.modules.marketing.model.dto.request.CouponClaimRequest;
import com.demon.giraffe.modules.marketing.model.dto.response.CouponTemplateDetailResponse;
import com.demon.giraffe.modules.marketing.model.dto.response.MemberCouponDetailResponse;
import com.demon.giraffe.modules.marketing.model.entity.Coupon;
import com.demon.giraffe.modules.marketing.model.enums.*;
import com.demon.giraffe.modules.marketing.model.po.CouponTemplatePo;
import com.demon.giraffe.modules.marketing.model.po.MemberCouponPo;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * 优惠券转换工具类
 */
@Component
public class CouponConvertHelper {
    private final CodeGeneratorUtil codeGeneratorUtil;

    public CouponConvertHelper(CodeGeneratorUtil codeGeneratorUtil) {
        this.codeGeneratorUtil = codeGeneratorUtil;
    }


    public CouponTemplatePo toCouponTemplatePo(CouponTemplateCreateRequest request) {
        DiscountTypeEnum discountType = request.getDiscountType();
        CouponTypeEnum type = request.getType();
        if(discountType == null){
            type = CouponTypeEnum.DISCOUNT;
        }
        String code = codeGeneratorUtil.generateCouponCode(type, discountType);

// 默认时间处理
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startDateTime = request.getStartTime() != null
                ? TimeUtil.fromTimestampMillis(request.getStartTime())
                : now;
        LocalDateTime endDateTime = request.getEndTime() != null
                ? TimeUtil.fromTimestampMillis(request.getEndTime())
                : now.plusDays(5);

        CouponTemplatePo po = new CouponTemplatePo();
        po.setTemplateCode(code);
        po.setName(request.getName());
        po.setType(type);
        po.setDiscountType(discountType);
        po.setDiscountValue(request.getDiscountValue());
        po.setMinAmount(request.getMinAmount());

// 固定折扣类型时，最大优惠金额等于优惠值
        if (DiscountTypeEnum.AMOUNT.equals(discountType)) {
            po.setMaxDiscount(request.getDiscountValue());
        } else {
            po.setMaxDiscount(request.getMaxDiscount());
        }

// 移除了地区限制、适用服务限制、适用商品项ID限制字段的设置

// 设置默认值
        po.setTotalCount(request.getTotalCount() != null ? request.getTotalCount() : 1000);
        po.setPerUserLimit(request.getPerUserLimit() != null ? request.getPerUserLimit() : 1);

        po.setValidDays(request.getValidDays());
        po.setIconUrl(request.getIconUrl());
        po.setDescription(request.getDescription());
        po.setStartTime(startDateTime);
        po.setEndTime(endDateTime);
        po.setStatus(CouponStatusEnum.NORMAL);
        po.setIssuedCount(0);
        po.setUsedCount(0);
        return po;
    }

    public MemberCouponPo toMemberCouponPo(CouponTemplatePo template, Long memberId) {
        MemberCouponPo po = new MemberCouponPo();

        po.setMemberId(memberId);
        po.setTemplateId(template.getId());
        po.setReceiveTime(LocalDateTime.now());
        po.setStartTime(LocalDateTime.now());
        po.setExpireTime(LocalDateTime.now().plus(template.getValidDays(), ChronoUnit.DAYS));
        po.setSource(CouponSourceEnum.MANUAL_CLAIM);
        return po;
    }

    public CouponTemplateDetailResponse toCouponTemplateDetailResponse(CouponTemplatePo po) {
        CouponTemplateDetailResponse response = new CouponTemplateDetailResponse();
        response.setId(po.getId());
        response.setTemplateCode(po.getTemplateCode());
        response.setName(po.getName());
        response.setType(po.getType());
        response.setDiscountType(po.getDiscountType());
        response.setDiscountValue(po.getDiscountValue());
        response.setMinAmount(po.getMinAmount());
        response.setMaxDiscount(po.getMaxDiscount());
        response.setValidDays(po.getValidDays());
        // 移除了地区限制、适用服务限制、适用商品项ID限制字段的转换
        response.setTotalCount(po.getTotalCount());
        response.setIssuedCount(po.getIssuedCount());
        response.setUsedCount(po.getUsedCount());
        response.setPerUserLimit(po.getPerUserLimit());
        response.setIconUrl(po.getIconUrl());
        response.setDescription(po.getDescription());
        response.setStartTime(po.getStartTime());
        response.setEndTime(po.getEndTime());
        response.setStatus(po.getStatus());
        response.setCreateTime(po.getCreateTime());
        return response;
    }

    public MemberCouponDetailResponse toMemberCouponDetailResponse(MemberCouponPo po, CouponTemplatePo template) {
        MemberCouponDetailResponse response = new MemberCouponDetailResponse();
        response.setId(po.getId());
        response.setMemberId(po.getMemberId());
        response.setTemplateId(po.getTemplateId());
        response.setTemplateName(template.getName());
        response.setTemplateType(template.getType());
        response.setDiscountType(template.getDiscountType());
        response.setDiscountValue(template.getDiscountValue());
        response.setMinAmount(template.getMinAmount());
        response.setMaxDiscount(template.getMaxDiscount());
        response.setReceiveTime(po.getReceiveTime());
        response.setStartTime(po.getStartTime());
        response.setExpireTime(po.getExpireTime());
        response.setSource(po.getSource());
        response.setUseTime(po.getUseTime());
        return response;
    }

    public IPage<CouponTemplatePo> toCouponTemplatePoPage(IPage<CouponTemplateDetailResponse> page) {
        return new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
    }

    public IPage<MemberCouponPo> toMemberCouponPoPage(IPage<MemberCouponDetailResponse> page) {
        return new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
    }

    private String generateCouponNo() {
        return "CPN" + System.currentTimeMillis();
    }


    /**
     * 将用户优惠券PO和模板PO合并转换为Coupon实体
     * @param memberCoupon 用户优惠券PO
     * @param template 优惠券模板PO
     * @return 组合后的Coupon实体
     */
    /**
     * 将用户优惠券PO和模板PO合并转换为Coupon实体
     */
    public Coupon toCouponEntity(MemberCouponPo memberCoupon, CouponTemplatePo template) {
        Coupon coupon = new Coupon();

        // 设置用户优惠券信息
        coupon.setId(memberCoupon.getId());
        coupon.setMemberId(memberCoupon.getMemberId());
        coupon.setOrderId(memberCoupon.getOrderId());
        coupon.setReceiveTime(memberCoupon.getReceiveTime());
        coupon.setStartTime(memberCoupon.getStartTime());
        coupon.setExpireTime(memberCoupon.getExpireTime());
        coupon.setSource(memberCoupon.getSource());
        coupon.setUseTime(memberCoupon.getUseTime());
        coupon.setUserStatus(memberCoupon.getStatus());

        // 设置模板信息
        coupon.setTemplateId(template.getId());
        coupon.setTemplateCode(template.getTemplateCode());
        coupon.setName(template.getName());
        coupon.setType(template.getType());
        coupon.setDiscountType(template.getDiscountType());
        coupon.setDiscountValue(template.getDiscountValue());
        coupon.setMinAmount(template.getMinAmount());
        coupon.setMaxDiscount(template.getMaxDiscount());
        coupon.setIconUrl(template.getIconUrl());
        coupon.setDescription(template.getDescription());
        coupon.setTemplateStatus(template.getStatus());

        return coupon;
    }


}