package com.demon.giraffe.modules.marketing.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.modules.marketing.model.dto.query.CouponTemplatePageQuery;
import com.demon.giraffe.modules.marketing.model.dto.request.CouponTemplateCreateRequest;
import com.demon.giraffe.modules.marketing.model.dto.response.*;
import com.demon.giraffe.modules.marketing.model.enums.CouponStatusEnum;

/**
 * 优惠券模板服务接口
 */
public interface CouponTemplateService {

    /**
     * 创建优惠券模板
     * @param request 创建请求
     * @return 创建的模板详情
     */
    CouponTemplateDetailResponse createTemplate(CouponTemplateCreateRequest request);

    /**
     * 获取优惠券模板详情
     * @param id 模板ID
     * @return 模板详情
     */
    CouponTemplateDetailResponse getTemplateDetail(Long id);

    /**
     * 更新优惠券模板状态
     * @param id 模板ID
     * @param status 新状态
     * @return 是否更新成功
     */
    boolean updateTemplateStatus(Long id, CouponStatusEnum status);

    /**
     * 删除优惠券模板
     * @param id 模板ID
     * @return 是否删除成功
     */
    boolean deleteTemplate(Long id);

    /**
     * 分页查询优惠券模板
     * @param page 分页参数
     * @return 分页结果
     */
    IPage<CouponTemplateDetailResponse> pageQueryTemplate(BasePageQuery<CouponTemplatePageQuery> page);



}