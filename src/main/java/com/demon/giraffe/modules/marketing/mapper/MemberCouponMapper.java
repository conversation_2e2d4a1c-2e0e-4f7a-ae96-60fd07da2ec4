package com.demon.giraffe.modules.marketing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.demon.giraffe.modules.marketing.model.po.MemberCouponPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户优惠券Mapper
 */
@Mapper
public interface MemberCouponMapper extends BaseMapper<MemberCouponPo> {

    /**
     * 查询用户可用优惠券（已移除地区、服务、商品项限制）
     */
    List<MemberCouponPo> selectAvailableCoupons(
            @Param("memberId") Long memberId,
            @Param("amount") BigDecimal amount);
}