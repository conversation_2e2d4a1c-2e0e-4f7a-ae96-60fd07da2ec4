package com.demon.giraffe.modules.marketing.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.modules.marketing.model.enums.CouponSourceEnum;
import com.demon.giraffe.modules.marketing.model.enums.CouponStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户优惠券持久化对象
 * <p>记录用户领取和使用的优惠券信息</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("member_coupon")
@Schema(description = "用户优惠券实体")
public class MemberCouponPo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /* 数据库字段：member_id */
    @Schema(description = "会员ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "100001")
    @TableField("member_id")
    private Long memberId;

    /* 数据库字段：template_id */
    @Schema(description = "关联的优惠券模板ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @TableField("template_id")
    private Long templateId;

    /* 数据库字段：order_id */
    @Schema(description = "使用订单ID（未使用时为空）", example = "ORD20230710001")
    @TableField("order_id")
    private Long orderId;

    /* 数据库字段：receive_time */
    @Schema(description = "领取时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2023-07-10T10:00:00")
    @TableField("receive_time")
    private LocalDateTime receiveTime;

    /* 数据库字段：start_time */
    @Schema(description = "生效时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2023-07-10T10:00:00")
    @TableField("start_time")
    private LocalDateTime startTime;

    /* 数据库字段：expire_time */
    @Schema(description = "过期时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2023-08-10T23:59:59")
    @TableField("expire_time")
    private LocalDateTime expireTime;

    /* 数据库字段：status */
    @Schema(description = "优惠券状态", implementation = CouponStatusEnum.class)
    @TableField("status")
    private CouponStatusEnum status;

    /* 数据库字段：source */
    @Schema(description = "来源渠道", requiredMode = Schema.RequiredMode.REQUIRED,
            implementation = CouponSourceEnum.class)
    @TableField("source")
    private CouponSourceEnum source;

    /* 数据库字段：use_time */
    @Schema(description = "使用时间（未使用时为空）", example = "2023-07-15T14:30:00")
    @TableField("use_time")
    private LocalDateTime useTime;
}