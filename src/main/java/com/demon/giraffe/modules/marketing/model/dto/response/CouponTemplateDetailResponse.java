package com.demon.giraffe.modules.marketing.model.dto.response;


import com.demon.giraffe.modules.marketing.model.enums.CouponStatusEnum;
import com.demon.giraffe.modules.marketing.model.enums.CouponTypeEnum;
import com.demon.giraffe.modules.marketing.model.enums.DiscountTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(name = "CouponTemplateDetailResponse", description = "优惠券模板详情响应")
public class CouponTemplateDetailResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "模板ID", example = "1")
    private Long id;

    @Schema(description = "模板编码", example = "T20230001")
    private String templateCode;

    @Schema(description = "优惠券名称", example = "新用户专享券")
    private String name;

    @Schema(description = "类型：1-满减券 2-折扣券")
    private CouponTypeEnum type;

    @Schema(description = "优惠类型：1-金额 2-比例")
    private DiscountTypeEnum discountType;

    @Schema(description = "优惠值（金额或折扣率）", example = "10.00")
    private BigDecimal discountValue;

    @Schema(description = "最低消费金额", example = "100.00")
    private BigDecimal minAmount;

    @Schema(description = "最大优惠金额（折扣券用）", example = "50.00")
    private BigDecimal maxDiscount;

    @Schema(description = "领取后有效天数", example = "30")
    private Integer validDays;

    // 移除了地区限制、适用服务限制、适用商品项ID限制字段

    @Schema(description = "总发放数量（0表示不限制）", example = "1000")
    private Integer totalCount;

    @Schema(description = "已发放数量", example = "150")
    private Integer issuedCount;

    @Schema(description = "已使用数量", example = "80")
    private Integer usedCount;

    @Schema(description = "每人限领数量", example = "2")
    private Integer perUserLimit;

    @Schema(description = "图标URL", example = "https://example.com/coupon.png")
    private String iconUrl;

    @Schema(description = "使用说明", example = "新用户专享优惠")
    private String description;

    @Schema(description = "领取开始发放时间")
    private LocalDateTime startTime;

    @Schema(description = "领取截至发放时间")
    private LocalDateTime endTime;

    @Schema(description = "状态：0-未使用 1-已使用 2-已过期")
    private CouponStatusEnum status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;


}