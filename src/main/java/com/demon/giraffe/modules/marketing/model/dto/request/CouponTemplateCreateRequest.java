package com.demon.giraffe.modules.marketing.model.dto.request;


import com.demon.giraffe.modules.marketing.model.enums.CouponTypeEnum;
import com.demon.giraffe.modules.marketing.model.enums.DiscountTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(name = "CouponTemplateCreateRequest", description = "创建优惠券模板请求")
public class CouponTemplateCreateRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotBlank(message = "优惠券名称不能为空")
    @Size(max = 64, message = "优惠券名称长度不能超过64")
    @Schema(description = "优惠券名称", required = true, example = "新用户专享券")
    private String name;

    @NotNull(message = "优惠券类型不能为空")
    @Schema(description = "类型：1-满减券 2-折扣券", required = true)
    private CouponTypeEnum type;

    @NotNull(message = "优惠类型不能为空")
    @Schema(description = "优惠类型：1-金额 2-比例", required = true)
    private DiscountTypeEnum discountType;

    @NotNull(message = "优惠值不能为空")
    @DecimalMin(value = "0.01", message = "优惠值必须大于0")
    @Schema(description = "优惠值（金额或折扣率）", required = true, example = "10.00")
    private BigDecimal discountValue;

    @DecimalMin(value = "0.00", message = "最低消费金额不能小于0")
    @Schema(description = "最低消费金额", example = "100.00")
    private BigDecimal minAmount;

    @DecimalMin(value = "0.00", message = "最大优惠金额不能小于0")
    @Schema(description = "最大优惠金额（折扣券用）", example = "50.00")
    private BigDecimal maxDiscount;

    @Min(value = 1, message = "有效天数不能小于1")
    @Max(value = 365, message = "有效天数不能超过365")
    @Schema(description = "领取后有效天数", required = true, example = "30")
    private Integer validDays;

    // 移除了地区限制、适用服务限制、适用商品项ID限制字段

    @Min(value = 0, message = "总发放数量不能小于0")
    @Schema(description = "总发放数量（0表示不限制）", example = "1000")
    private Integer totalCount;

    @Min(value = 1, message = "每人限领数量不能小于1")
    @Schema(description = "每人限领数量", example = "2")
    private Integer perUserLimit;

    @Size(max = 255, message = "图标URL长度不能超过255")
    @Schema(description = "图标URL", example = "https://example.com/coupon.png")
    private String iconUrl;

    @Size(max = 500, message = "使用说明长度不能超过500")
    @Schema(description = "使用说明", example = "新用户专享优惠")
    private String description;

    @NotNull(message = "领取开始时间不能为空")
    @Schema(description = "领取开始发放时间", required = true)
    private Long startTime;

    @NotNull(message = "领取截止时间不能为空")
    @Schema(description = "领取截至发放时间", required = true)
    private Long endTime;
}