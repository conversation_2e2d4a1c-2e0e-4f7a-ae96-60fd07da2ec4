package com.demon.giraffe.modules.marketing.model.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 可用优惠券响应
 * 简化版本，只包含用户选择优惠券时需要的核心信息
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "可用优惠券响应")
public class AvailableCouponResponse {

    @Schema(description = "优惠券ID")
    private Long couponId;

    @Schema(description = "优惠券名称")
    private String couponName;

    @Schema(description = "优惠券类型描述", example = "满减券")
    private String couponTypeDesc;

    @Schema(description = "优惠金额", example = "10.00")
    private BigDecimal discountAmount;

    @Schema(description = "使用门槛", example = "满100元可用")
    private String usageThreshold;

    @Schema(description = "有效期至")
    private LocalDateTime expireTime;

    @Schema(description = "剩余天数", example = "7")
    private Integer remainingDays;

    @Schema(description = "是否即将过期", example = "false")
    private Boolean isExpiringSoon;

    @Schema(description = "优惠券描述")
    private String description;
}
