package com.demon.giraffe.modules.marketing.model.dto.response;

import com.demon.giraffe.modules.marketing.model.enums.CouponTypeEnum;
import com.demon.giraffe.modules.marketing.model.enums.DiscountTypeEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 适用优惠券响应实体
 */
@Data
public class ApplicableCouponResponse {
    /**
     * 优惠券ID
     */
    private Long couponId;
    
    /**
     * 优惠券名称
     */
    private String name;
    
    /**
     * 优惠券类型
     */
    private CouponTypeEnum type;
    
    /**
     * 优惠类型
     */
    private DiscountTypeEnum discountType;
    
    /**
     * 优惠值
     */
    private BigDecimal discountValue;
    
    /**
     * 最低消费金额
     */
    private BigDecimal minAmount;
    
    /**
     * 最大优惠金额
     */
    private BigDecimal maxDiscount;
    
    /**
     * 预计优惠金额
     */
    private BigDecimal estimatedDiscount;
    
    /**
     * 使用说明
     */
    private String description;
}