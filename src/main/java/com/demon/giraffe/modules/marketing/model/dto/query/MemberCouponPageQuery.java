package com.demon.giraffe.modules.marketing.model.dto.query;

import com.demon.giraffe.modules.marketing.model.enums.CouponSourceEnum;
import com.demon.giraffe.modules.marketing.model.enums.CouponStatusEnum;
import com.demon.giraffe.modules.marketing.model.enums.CouponTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户优惠券分页查询参数
 */
@Data
@Schema(description = "用户优惠券分页查询参数")
public class MemberCouponPageQuery implements Serializable {

    @Schema(description = "会员ID,后端自动覆盖传入无效")
    private Long memberId;

    @Schema(description = "优惠券状态")
    private CouponStatusEnum status;

    @Schema(description = "优惠券类型")
    private CouponTypeEnum type;

    @Schema(description = "是否已使用（true-已使用，false-未使用）")
    private Boolean isUsed;

    @Schema(description = "是否已过期（true-已过期，false-未过期）")
    private Boolean isExpired;
}
