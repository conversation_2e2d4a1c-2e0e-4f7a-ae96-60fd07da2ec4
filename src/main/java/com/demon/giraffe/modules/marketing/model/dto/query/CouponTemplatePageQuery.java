package com.demon.giraffe.modules.marketing.model.dto.query;

import com.demon.giraffe.modules.marketing.model.enums.CouponStatusEnum;
import com.demon.giraffe.modules.marketing.model.enums.CouponTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 优惠券模板分页查询参数
 */
@Data
@Schema(description = "优惠券模板分页查询参数")
public class CouponTemplatePageQuery implements Serializable {

    @Schema(description = "优惠券模板状态")
    private CouponStatusEnum status;

    @Schema(description = "优惠券类型")
    private CouponTypeEnum type;

    @Schema(description = "模板名称模糊查询")
    private String nameKeyword;

    @Schema(description = "模板编码精确查询")
    private String templateCode;

}