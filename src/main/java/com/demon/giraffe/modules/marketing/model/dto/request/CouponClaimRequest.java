package com.demon.giraffe.modules.marketing.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Schema(name = "CouponClaimRequest", description = "领取优惠券请求")
public class CouponClaimRequest implements Serializable {

    private static final long serialVersionUID = 1L;


    @NotNull(message = "模板ID不能为空")
    @Schema(description = "模板ID", required = true, example = "1")
    private Long templateId;

}