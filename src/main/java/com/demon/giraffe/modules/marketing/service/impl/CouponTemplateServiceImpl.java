package com.demon.giraffe.modules.marketing.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;

import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.modules.marketing.model.dto.query.CouponTemplatePageQuery;
import com.demon.giraffe.modules.marketing.model.dto.request.CouponTemplateCreateRequest;
import com.demon.giraffe.modules.marketing.model.dto.response.*;
import com.demon.giraffe.modules.marketing.model.po.CouponTemplatePo;
import com.demon.giraffe.modules.marketing.model.enums.CouponStatusEnum;
import com.demon.giraffe.modules.marketing.repository.CouponTemplateRepository;
import com.demon.giraffe.modules.marketing.service.CouponTemplateService;
import com.demon.giraffe.modules.marketing.service.helper.CouponConvertHelper;
import org.springframework.stereotype.Service;

/**
 * 优惠券模板服务实现
 */
@Service
public class CouponTemplateServiceImpl implements CouponTemplateService {

    private final CouponTemplateRepository couponTemplateRepository;
    private final CouponConvertHelper convertHelper;

    public CouponTemplateServiceImpl(CouponTemplateRepository couponTemplateRepository,
                                     CouponConvertHelper convertHelper) {
        this.couponTemplateRepository = couponTemplateRepository;
        this.convertHelper = convertHelper;
    }

    @Override
    public CouponTemplateDetailResponse createTemplate(CouponTemplateCreateRequest request) {
        CouponTemplatePo po = convertHelper.toCouponTemplatePo(request);
        CouponTemplatePo savedPo = couponTemplateRepository.save(po);
        return convertHelper.toCouponTemplateDetailResponse(savedPo);
    }

    @Override
    public CouponTemplateDetailResponse getTemplateDetail(Long id) {
        CouponTemplatePo po = couponTemplateRepository.getById(id);
        return convertHelper.toCouponTemplateDetailResponse(po);
    }

    @Override
    public boolean updateTemplateStatus(Long id, CouponStatusEnum status) {
        return couponTemplateRepository.updateStatus(id, status);
    }

    @Override
    public boolean deleteTemplate(Long id) {
        return couponTemplateRepository.delete(id);
    }

    @Override
    public IPage<CouponTemplateDetailResponse> pageQueryTemplate(BasePageQuery<CouponTemplatePageQuery> page) {
        // 调用Repository的复杂查询方法
        IPage<CouponTemplatePo> poPage = couponTemplateRepository.pageQueryByConditions(page);

        // 转换结果
        return poPage.convert(convertHelper::toCouponTemplateDetailResponse);
    }

}