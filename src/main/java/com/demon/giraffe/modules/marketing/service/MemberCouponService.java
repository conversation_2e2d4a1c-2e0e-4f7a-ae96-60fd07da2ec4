package com.demon.giraffe.modules.marketing.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.demon.giraffe.common.domain.BasePageQuery;

import com.demon.giraffe.modules.marketing.model.dto.query.MemberCouponPageQuery;
import com.demon.giraffe.modules.marketing.model.dto.request.CouponClaimRequest;
import com.demon.giraffe.modules.marketing.model.dto.request.OrderInfo;
import com.demon.giraffe.modules.marketing.model.dto.response.*;
import com.demon.giraffe.modules.marketing.model.entity.Coupon;
import com.demon.giraffe.modules.marketing.model.enums.CouponStatusEnum;
import com.demon.giraffe.modules.marketing.model.po.MemberCouponPo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户优惠券服务接口
 */
public interface MemberCouponService {

    /**
     * 领取优惠券
     *
     * @param request 领取请求
     * @return 领取的优惠券详情
     */
    MemberCouponDetailResponse claimCoupon(CouponClaimRequest request);

    /**
     * 使用优惠券
     *
     * @param couponId 优惠券id
     * @param orderId  订单ID
     * @return 是否使用成功
     */
    boolean useCoupon(Long couponId, Long orderId);

    /**
     * 取消使用优惠券
     *
     * @param couponId 优惠券id
     * @return 是否取消成功
     */
    boolean cancelCouponUsage(Long couponId);

    /**
     * 获取用户优惠券列表
     *
     * @param pageQuery 分页查询参数
     * @return 分页结果
     */
    IPage<MemberCouponDetailResponse> listMemberCoupons(BasePageQuery<MemberCouponPageQuery> pageQuery);

    /**
     * 获取用户可用优惠券列表（已移除地区、服务、商品项限制）
     *
     * @param memberId      用户ID
     * @param amount        订单金额
     * @return 可用优惠券列表
     */
    List<MemberCouponDetailResponse> listAvailableCoupons(Long memberId, BigDecimal amount);


    /**
     * 获取当前用户的可用优惠券实体列表
     * @param memberId 用户ID
     * @return 可用优惠券实体列表（未过期+未使用+未停用）
     */
    List<Coupon> listAvailableCouponEntities(Long memberId);



    /**
     * 根据ID获取用户优惠券
     * @param id 优惠券ID
     * @return 用户优惠券PO
     */
    MemberCouponPo getById(Long id);



     Coupon getCouponById(Long couponId) ;
}