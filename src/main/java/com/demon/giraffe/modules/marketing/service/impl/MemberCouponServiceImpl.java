package com.demon.giraffe.modules.marketing.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.common.domain.enums.CountyEnum;

import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.common.util.TimeUtil;
import com.demon.giraffe.framework.satoken.util.SaTokenUtil;
import com.demon.giraffe.modules.marketing.model.dto.query.MemberCouponPageQuery;
import com.demon.giraffe.modules.marketing.model.dto.request.CouponClaimRequest;
import com.demon.giraffe.modules.marketing.model.dto.request.OrderInfo;
import com.demon.giraffe.modules.marketing.model.dto.response.*;
import com.demon.giraffe.modules.marketing.model.entity.Coupon;
import com.demon.giraffe.modules.marketing.model.enums.*;
import com.demon.giraffe.modules.marketing.model.po.CouponTemplatePo;
import com.demon.giraffe.modules.marketing.model.po.MemberCouponPo;
import com.demon.giraffe.modules.marketing.repository.CouponTemplateRepository;
import com.demon.giraffe.modules.marketing.repository.MemberCouponRepository;
import com.demon.giraffe.modules.marketing.service.MemberCouponService;
import com.demon.giraffe.modules.marketing.service.helper.CouponConvertHelper;
import com.demon.giraffe.modules.user.model.enums.UserStatus;
import com.demon.giraffe.modules.user.model.po.MemberIdentityPo;
import com.demon.giraffe.modules.user.model.po.UserPo;
import com.demon.giraffe.modules.user.service.MemberIdentityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户优惠券服务实现
 */
@Service
@Slf4j
public class MemberCouponServiceImpl implements MemberCouponService {

    private final MemberCouponRepository memberCouponRepository;
    private final CouponTemplateRepository couponTemplateRepository;
    private final CouponConvertHelper convertHelper;
    private final MemberIdentityService memberIdentityService;

    public MemberCouponServiceImpl(MemberCouponRepository memberCouponRepository,
                                   CouponTemplateRepository couponTemplateRepository,
                                   CouponConvertHelper convertHelper, MemberIdentityService memberIdentityService) {
        this.memberCouponRepository = memberCouponRepository;
        this.couponTemplateRepository = couponTemplateRepository;
        this.convertHelper = convertHelper;
        this.memberIdentityService = memberIdentityService;
    }

    @Override
    @Transactional
    public MemberCouponDetailResponse claimCoupon(CouponClaimRequest request) {
        UserPo userPo = SaTokenUtil.getUserPo();
        MemberIdentityPo memberIdentityPo = memberIdentityService.getByUserId(userPo.getId());

        if (memberIdentityPo == null || !UserStatus.NORMAL.equals(memberIdentityPo.getStatus())) {
            throw new BusinessException("会信息异常，请联系系管理员");
        }
        // 1. 检查模板是否存在且可领取
        CouponTemplatePo template = couponTemplateRepository.getById(request.getTemplateId());

        if (template == null || template.getStatus() != CouponStatusEnum.NORMAL) {
            throw new BusinessException("优惠券未找到");
        }
        if (TimeUtil.isExpired(template.getEndTime())) {
            throw new BusinessException("优惠券已过期");
        }

        // 2. 检查领取限制
        checkClaimLimit(memberIdentityPo.getId(), template);

        // 3. 创建用户优惠券
        MemberCouponPo po = convertHelper.toMemberCouponPo(template, memberIdentityPo.getId());
        MemberCouponPo savedPo = memberCouponRepository.save(po);

        // 4. 更新模板发放数量
        Boolean b = couponTemplateRepository.incrementIssuedCount(template.getId(), 1);
        if (!b) {
            throw new BusinessException("优惠券模板发放数量异常");
        }

        return convertHelper.toMemberCouponDetailResponse(savedPo, template);
    }

    @Override
    @Transactional
    public boolean useCoupon(Long couponId, Long orderId) {
        // 更新优惠券状态
        boolean updated = memberCouponRepository.markAsUsed(couponId, LocalDateTime.now());
        if (updated) {
            // 更新模板使用数量
            couponTemplateRepository.incrementIssuedCount(couponId, 1);
        }else {
            log.error("更新优惠券状态失败，优惠券ID：{}", couponId);
        }
        //更新模板使用数量
        return updated;
    }

    @Override
    @Transactional
    public boolean cancelCouponUsage(Long couponId) {
        // 1. 获取优惠券信息
        MemberCouponPo coupon = memberCouponRepository.getById(couponId);
        if (coupon == null || coupon.getOrderId() == null) {
            return false;
        }

        // 2. 回退优惠券状态
        boolean success = memberCouponRepository.cancelCouponUsage(couponId);

        // 3. 如果回退成功，更新模板使用计数
        if (success) {
            couponTemplateRepository.decrementUsedCount(coupon.getTemplateId());
        } else {
            throw new BusinessException("回退优惠券状态异常");
        }

        return true;
    }

    @Override
    public IPage<MemberCouponDetailResponse> listMemberCoupons(BasePageQuery<MemberCouponPageQuery> pageQuery) {
        // 调用Repository的复杂查询方法
        IPage<MemberCouponPo> poPage = memberCouponRepository.pageQueryByConditions(pageQuery);

        // 如果没有数据，直接返回空结果
        if (poPage.getRecords().isEmpty()) {
            return poPage.convert(po -> null);
        }

        // 批量查询优惠券模板信息
        Set<Long> templateIds = poPage.getRecords().stream()
                .map(MemberCouponPo::getTemplateId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Map<Long, CouponTemplatePo> templateMap = Collections.emptyMap();
        if (!templateIds.isEmpty()) {
            List<CouponTemplatePo> templates = couponTemplateRepository.listByIds(new ArrayList<>(templateIds));
            templateMap = templates.stream()
                    .collect(Collectors.toMap(CouponTemplatePo::getId, template -> template));
        }

        // 使用批量查询结果进行转换
        final Map<Long, CouponTemplatePo> finalTemplateMap = templateMap;
        return poPage.convert(po -> {
            CouponTemplatePo template = finalTemplateMap.get(po.getTemplateId());
            return convertHelper.toMemberCouponDetailResponse(po, template);
        });
    }

    @Override
    public List<MemberCouponDetailResponse> listAvailableCoupons(Long memberId, BigDecimal amount) {
        List<MemberCouponPo> coupons = memberCouponRepository.listAvailableCoupons(memberId, amount);
        return coupons.stream().map(po -> {
            CouponTemplatePo template = couponTemplateRepository.getById(po.getTemplateId());
            return convertHelper.toMemberCouponDetailResponse(po, template);
        }).collect(Collectors.toList());
    }

    private void checkClaimLimit(Long memberId, CouponTemplatePo template) {
        // 检查总发放限制
        if (template.getTotalCount() > 0 && template.getIssuedCount() >= template.getTotalCount()) {
            throw new IllegalArgumentException("优惠券已领完");
        }

        // 检查个人领取限制
        if (template.getPerUserLimit() > 0) {
            long claimedCount = memberCouponRepository.countByMemberAndTemplate(memberId, template.getId());
            if (claimedCount >= template.getPerUserLimit()) {
                throw new IllegalArgumentException("已达到个人领取上限");
            }
        }

        // 检查领取时间
        LocalDateTime now = LocalDateTime.now();
        if (now.isBefore(template.getStartTime()) || now.isAfter(template.getEndTime())) {
            throw new IllegalArgumentException("不在优惠券领取时间内");
        }
    }


    @Override
    public List<Coupon> listAvailableCouponEntities(Long memberId) {
        // 1. 查询用户所有可用的优惠券（未使用+未过期+状态正常）
        List<MemberCouponPo> memberCoupons = memberCouponRepository.listByMemberAndStatus(
                memberId,
                CouponStatusEnum.NORMAL,
                LocalDateTime.now()
        );

        if (memberCoupons.isEmpty()) {
            return Collections.emptyList();
        }

        // 2. 批量查询对应的优惠券模板
        List<Long> templateIds = memberCoupons.stream()
                .map(MemberCouponPo::getTemplateId)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, CouponTemplatePo> templateMap = couponTemplateRepository.listByIds(templateIds)
                .stream()
                .collect(Collectors.toMap(CouponTemplatePo::getId, Function.identity()));

        // 3. 合并数据并转换为Coupon实体
        return memberCoupons.stream()
                .map(memberCoupon -> {
                    CouponTemplatePo template = templateMap.get(memberCoupon.getTemplateId());
                    if (template == null) {
                        return null; // 模板不存在或不可用
                    }
                    return convertHelper.toCouponEntity(memberCoupon, template);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }






    @Override
    public MemberCouponPo getById(Long id) {
        return memberCouponRepository.getById(id);
    }

    @Override
    public Coupon getCouponById(Long couponId) {
        // 1. 获取用户优惠券记录
        MemberCouponPo memberCouponPo = memberCouponRepository.getById(couponId);
        if (memberCouponPo == null) {
            throw new BusinessException("优惠券不存在");
        }

        // 2. 获取对应的模板信息
        CouponTemplatePo template = couponTemplateRepository.getById(memberCouponPo.getTemplateId());
        if (template == null) {
            throw new BusinessException("优惠券模板不存在");
        }

        // 3. 转换为 Coupon 实体
        return convertHelper.toCouponEntity(memberCouponPo, template);
    }

}