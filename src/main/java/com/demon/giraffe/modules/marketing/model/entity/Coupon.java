package com.demon.giraffe.modules.marketing.model.entity;


import com.demon.giraffe.modules.marketing.model.enums.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 优惠券实体类（聚合优惠券模板和用户优惠券信息）
 */
@Data
@EqualsAndHashCode
@Schema(description = "优惠券实体")
public class Coupon implements Serializable {

    private static final long serialVersionUID = 1L;

    // ============ 用户优惠券信息 ============
    @Schema(description = "用户优惠券ID")
    private Long id;



    @Schema(description = "会员ID", required = true)
    private Long memberId;

    @Schema(description = "使用订单ID")
    private Long orderId;

    @Schema(description = "领取时间", required = true)
    private LocalDateTime receiveTime;

    @Schema(description = "生效时间", required = true)
    private LocalDateTime startTime;

    @Schema(description = "过期时间", required = true)
    private LocalDateTime expireTime;

    @Schema(description = "来源：1-主动领取 2-系统赠送 3-活动获得 4-积分兑换", required = true)
    private CouponSourceEnum source;

    @Schema(description = "使用时间")
    private LocalDateTime useTime;

    @Schema(description = "用户优惠券状态")
    private CouponStatusEnum userStatus;

    // ============ 优惠券模板信息 ============
    @Schema(description = "模板ID", required = true)
    private Long templateId;

    @Schema(description = "模板编码（唯一业务标识）")
    private String templateCode;

    @Schema(description = "优惠券名称")
    private String name;

    @Schema(description = "类型：1-满减券 2-折扣券")
    private CouponTypeEnum type;

    @Schema(description = "优惠类型：1-金额 2-比例")
    private DiscountTypeEnum discountType;

    @Schema(description = "优惠值（金额或折扣率）")
    private BigDecimal discountValue;

    @Schema(description = "最低消费金额")
    private BigDecimal minAmount;

    @Schema(description = "最大优惠金额（折扣券用）")
    private BigDecimal maxDiscount;

    // 移除了地区限制、适用服务限制、适用商品项ID限制字段

    @Schema(description = "图标URL")
    private String iconUrl;

    @Schema(description = "使用说明")
    private String description;

    @Schema(description = "模板状态")
    private CouponStatusEnum templateStatus;

    // ============ 业务方法 ============

    /**
     * 检查优惠券是否可用
     *
     * @return true-可用，false-不可用
     */
    @Schema(description = "是否可用", accessMode = Schema.AccessMode.READ_ONLY)
    public boolean isAvailable() {
        LocalDateTime now = LocalDateTime.now();
        return userStatus == CouponStatusEnum.NORMAL
                && templateStatus == CouponStatusEnum.NORMAL
                && now.isAfter(startTime)
                && now.isBefore(expireTime);
    }

    /**
     * 检查优惠券是否适用于当前订单（已移除地区、服务、商品项限制）
     * @param amount 订单金额
     * @return 不适用原因枚举，返回APPLICABLE表示适用
     */
    public CouponInapplicableReason checkApplicable(BigDecimal amount) {
        // 1. 检查优惠券是否可用
        if (userStatus != CouponStatusEnum.NORMAL ||
                templateStatus != CouponStatusEnum.NORMAL) {
            return CouponInapplicableReason.COUPON_NOT_AVAILABLE;
        }

        LocalDateTime now = LocalDateTime.now();
        if (now.isBefore(startTime) || now.isAfter(expireTime)) {
            return CouponInapplicableReason.COUPON_NOT_AVAILABLE;
        }

        // 2. 检查最低消费（移除了服务范围、服务项范围、区域限制检查）
        if (minAmount != null &&
                (amount == null || amount.compareTo(minAmount) < 0)) {
            return CouponInapplicableReason.AMOUNT_NOT_ENOUGH;
        }

        return CouponInapplicableReason.APPLICABLE;
    }



    /**
     * 获取剩余有效天数
     *
     * @return 剩余天数
     */
    @Schema(description = "剩余有效天数", accessMode = Schema.AccessMode.READ_ONLY)
    public long getRemainingDays() {
        return LocalDateTime.now().until(expireTime, java.time.temporal.ChronoUnit.DAYS);
    }

    /**
     * 是否即将过期（7天内）
     *
     * @return true-即将过期，false-未过期
     */
    @Schema(description = "是否即将过期", accessMode = Schema.AccessMode.READ_ONLY)
    public boolean isExpiringSoon() {
        return getRemainingDays() <= 7;
    }


    /**
     * 生成优惠券使用条件描述
     */
    public String generateConditionDescription() {
        StringBuilder sb = new StringBuilder();

        if (this.getMinAmount() != null && this.getMinAmount().compareTo(BigDecimal.ZERO) > 0) {
            sb.append("满").append(this.getMinAmount()).append("元可用");
        }

        return sb.toString();
    }
}