package com.demon.giraffe.modules.marketing.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.framework.satoken.util.SaTokenUtil;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.modules.marketing.model.dto.query.MemberCouponPageQuery;
import com.demon.giraffe.modules.marketing.model.dto.request.CouponClaimRequest;
import com.demon.giraffe.modules.marketing.model.dto.request.MyCouponClaimRequest;
import com.demon.giraffe.modules.marketing.model.dto.response.MemberCouponDetailResponse;
import com.demon.giraffe.modules.marketing.service.MemberCouponService;
import com.demon.giraffe.modules.user.model.enums.UserStatus;
import com.demon.giraffe.modules.user.model.po.MemberIdentityPo;
import com.demon.giraffe.modules.user.model.po.UserPo;
import com.demon.giraffe.modules.user.service.MemberIdentityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 用户优惠券管理接口
 */
@Slf4j
@RestController
@RequestMapping("/marketing/member/coupon")
@Tag(name = "用户优惠券管理接口", description = "用户优惠券的领取、查询等操作")
@RequiredArgsConstructor
public class MemberCouponController {

    private final MemberCouponService memberCouponService;
    private final MemberIdentityService memberIdentityService;

    @PostMapping("/claim")
    @Operation(summary = "领取优惠券", description = "用户领取指定模板的优惠券")
    @SaCheckRole(value = {"CUSTOMER"}, mode = SaMode.OR)
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "优惠券领取参数",
            required = true,
            content = @Content(schema = @Schema(implementation = MyCouponClaimRequest.class))
    )

    public ResultBean<MemberCouponDetailResponse> claimCoupon(
            @Valid @RequestBody MyCouponClaimRequest request) {
        try {
            CouponClaimRequest serviceRequest = new CouponClaimRequest();
            serviceRequest.setTemplateId(request.getTemplateId());
            MemberCouponDetailResponse response = memberCouponService.claimCoupon(serviceRequest);
            return ResultBean.success("优惠券领取成功", response);
        } catch (Exception e) {
            log.error("领取优惠券失败，用户ID：{}，模板ID：{}", SaTokenUtil.getLoginId(), request.getTemplateId(), e);
            return ResultBean.fail("领取失败：" + e.getMessage());
        }
    }

    @PostMapping("/my/list")
    @Operation(summary = "获取我的优惠券列表", description = "分页查询当前用户的优惠券列表")
    @SaCheckRole(value = {"CUSTOMER"}, mode = SaMode.OR)
    public ResultBean<IPage<MemberCouponDetailResponse>> getMyCouponList(
            @RequestBody BasePageQuery<MemberCouponPageQuery> pageQuery) {
        try {
            // 获取当前用户信息
            UserPo userPo = SaTokenUtil.getUserPo();
            MemberIdentityPo memberIdentityPo = memberIdentityService.getByUserId(userPo.getId());

            if (Objects.isNull(memberIdentityPo) || !UserStatus.NORMAL.equals(memberIdentityPo.getStatus())) {
                throw new BusinessException("会员信息异常，请联系管理员");
            }

            // 如果查询条件为空，创建一个新的查询条件
            if (pageQuery.getQuery() == null) {
                pageQuery.setQuery(new MemberCouponPageQuery());
            }

            // 设置当前用户的会员ID
            pageQuery.getQuery().setMemberId(memberIdentityPo.getId());

            IPage<MemberCouponDetailResponse> result = memberCouponService.listMemberCoupons(pageQuery);
            return ResultBean.success(result);
        } catch (Exception e) {
            log.error("获取我的优惠券列表失败，用户ID：{}", SaTokenUtil.getLoginId(), e);
            return ResultBean.fail("查询失败：" + e.getMessage());
        }
    }


    @GetMapping("/available")
    @Operation(summary = "获取可用优惠券列表", description = "获取当前用户可用的优惠券列表（未过期+未使用+状态正常）")
    @SaCheckRole(value = {"CUSTOMER"}, mode = SaMode.OR)
    public ResultBean<Object> getAvailableCoupons() {
        try {
            // 获取当前用户信息
            UserPo userPo = SaTokenUtil.getUserPo();
            MemberIdentityPo memberIdentityPo = memberIdentityService.getByUserId(userPo.getId());

            if (Objects.isNull(memberIdentityPo) || !UserStatus.NORMAL.equals(memberIdentityPo.getStatus())) {
                throw new BusinessException("会员信息异常，请联系管理员");
            }

            // 调用服务获取可用优惠券实体列表
            var availableCoupons = memberCouponService.listAvailableCouponEntities(memberIdentityPo.getId());
            return ResultBean.success(availableCoupons);
        } catch (Exception e) {
            log.error("获取可用优惠券列表失败，用户ID：{}", SaTokenUtil.getLoginId(), e);
            return ResultBean.fail("查询失败：" + e.getMessage());
        }
    }
}
