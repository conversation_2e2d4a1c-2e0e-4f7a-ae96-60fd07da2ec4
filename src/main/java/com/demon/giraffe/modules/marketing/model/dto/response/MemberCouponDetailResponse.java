package com.demon.giraffe.modules.marketing.model.dto.response;

import com.demon.giraffe.modules.marketing.model.enums.CouponSourceEnum;
import com.demon.giraffe.modules.marketing.model.enums.CouponStatusEnum;
import com.demon.giraffe.modules.marketing.model.enums.CouponTypeEnum;
import com.demon.giraffe.modules.marketing.model.enums.DiscountTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(name = "MemberCouponDetailResponse", description = "用户优惠券详情响应")
public class MemberCouponDetailResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户优惠券ID", example = "1")
    private Long id;

    @Schema(description = "会员ID", example = "123456")
    private Long memberId;

    @Schema(description = "模板ID", example = "1")
    private Long templateId;

    @Schema(description = "模板名称", example = "新用户专享券")
    private String templateName;

    @Schema(description = "模板类型：1-满减券 2-折扣券")
    private CouponTypeEnum templateType;

    @Schema(description = "优惠类型：1-金额 2-比例")
    private DiscountTypeEnum discountType;

    @Schema(description = "优惠值（金额或折扣率）", example = "10.00")
    private BigDecimal discountValue;

    @Schema(description = "最低消费金额", example = "100.00")
    private BigDecimal minAmount;

    @Schema(description = "最大优惠金额（折扣券用）", example = "50.00")
    private BigDecimal maxDiscount;

    @Schema(description = "领取时间")
    private LocalDateTime receiveTime;

    @Schema(description = "生效时间")
    private LocalDateTime startTime;

    @Schema(description = "过期时间")
    private LocalDateTime expireTime;

    @Schema(description = "来源：1-主动领取 2-系统赠送 3-活动获得 4-积分兑换")
    private CouponSourceEnum source;

    @Schema(description = "使用时间")
    private LocalDateTime useTime;

    @Schema(description = "状态：0-正常 1-已使用 2-已过期")
    private CouponStatusEnum status;

    @Schema(description = "剩余有效天数", example = "15")
    public Long getRemainingDays() {
        if (expireTime == null) return null;
        return LocalDateTime.now().until(expireTime, java.time.temporal.ChronoUnit.DAYS);
    }

    @Schema(description = "是否即将过期（7天内）", example = "false")
    public Boolean getIsExpiringSoon() {
        if (expireTime == null) return false;
        return LocalDateTime.now().until(expireTime, java.time.temporal.ChronoUnit.DAYS) <= 7;
    }
}