package com.demon.giraffe.modules.marketing.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "优惠类型枚举")
public enum DiscountTypeEnum implements IEnum<Integer> {
    @Schema(description = "金额优惠")
    AMOUNT(1, "金额优惠"),
    
    @Schema(description = "比例优惠")
    PERCENTAGE(2, "比例优惠");

    @EnumValue
    private final int code;
    private final String desc;

    DiscountTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static DiscountTypeEnum of(int code) {
        for (DiscountTypeEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的优惠类型: " + code);
    }

    @Override
    public Integer getValue() {
        return code;
    }
}