package com.demon.giraffe.modules.marketing.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.modules.marketing.model.dto.query.CouponTemplatePageQuery;
import com.demon.giraffe.modules.marketing.model.po.CouponTemplatePo;
import com.demon.giraffe.modules.marketing.model.enums.CouponStatusEnum;

import java.util.List;

/**
 * 优惠券模板仓库接口
 */
public interface CouponTemplateRepository {

    /**
     * 保存优惠券模板
     * @param po 优惠券模板PO
     * @return 保存后的优惠券模板
     */
    CouponTemplatePo save(CouponTemplatePo po);

    /**
     * 根据ID获取优惠券模板
     * @param id 模板ID
     * @return 优惠券模板
     */
    CouponTemplatePo getById(Long id);

    /**
     * 根据模板编码获取优惠券模板
     * @param templateCode 模板编码
     * @return 优惠券模板
     */
    CouponTemplatePo getByCode(String templateCode);

    /**
     * 更新优惠券模板状态
     * @param id 模板ID
     * @param status 新状态
     * @return 是否更新成功
     */
    boolean updateStatus(Long id, CouponStatusEnum status);

    /**
     * 删除优惠券模板
     * @param id 模板ID
     * @return 是否删除成功
     */
    boolean delete(Long id);

    /**
     * 分页查询优惠券模板
     * @param page 分页参数
     * @param status 状态筛选条件
     * @return 分页结果
     */
    IPage<CouponTemplatePo> pageQuery(IPage<CouponTemplatePo> page, CouponStatusEnum status);

    /**
     * 分页查询优惠券模板（支持复杂查询条件）
     * @param pageQuery 分页查询参数
     * @return 分页结果
     */
    IPage<CouponTemplatePo> pageQueryByConditions(BasePageQuery<CouponTemplatePageQuery> pageQuery);


    /**
     *
     */
    Boolean incrementIssuedCount(Long id, Integer increment);


    /**
     * 减少模板已使用数量
     * @param templateId 模板ID
     * @return 是否更新成功
     */
    boolean decrementUsedCount(Long templateId);

    /**
     * 批量查询优惠券模板
     * @param ids 模板ID列表
     * @return 模板列表
     */
    List<CouponTemplatePo> listByIds(List<Long> ids);
}