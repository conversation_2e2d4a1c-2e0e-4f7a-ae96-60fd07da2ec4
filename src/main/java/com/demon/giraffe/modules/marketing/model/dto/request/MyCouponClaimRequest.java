package com.demon.giraffe.modules.marketing.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 我的优惠券领取请求
 */
@Data
@Schema(name = "MyCouponClaimRequest", description = "我的优惠券领取请求")
public class MyCouponClaimRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "模板ID不能为空")
    @Schema(description = "优惠券模板ID", required = true, example = "1")
    private Long templateId;
}
