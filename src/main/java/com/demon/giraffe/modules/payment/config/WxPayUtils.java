//package com.demon.giraffe.modules.payment.config;
//
//import com.alibaba.fastjson.JSONObject;
//import com.github.binarywang.wxpay.bean.request.*;
//import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryV3Result;
//import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
//import com.github.binarywang.wxpay.config.WxPayConfig;
//import com.github.binarywang.wxpay.constant.WxPayConstants;
//import com.github.binarywang.wxpay.exception.WxPayException;
//import com.github.binarywang.wxpay.service.WxPayService;
//import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//import java.math.BigDecimal;
//import java.text.SimpleDateFormat;
//import java.util.Calendar;
//
//@Slf4j
//@Component
//public class WxPayUtils {
//
//
//    @Value("${wechat.appid}")
//    private String appId;
//
//    @Value("${wechat.merchant-id}")
//    private String merchantId;
//
//    @Value("${wechat.api-v3-key}")
//    private String apiV3Key;
//
//    @Value("${wechat.merchant-serial-number}")
//    private String merchantSerialNumber;
//
//    @Value("${wechat.private-key-path}")
//    private String privateKeyPath;
//
//    @Value("${wechat.key-path:}")
//    private String keyPath;
//
//    @Value("${wechat.public-key-path:}")
//    private String publicKeyPath;
//
//    @Value("${wechat.public-key-id:}")
//    private String publicKeyId;
//
//    @Value("${wechat.private-cert-path:}")
//    private String privateCertPath;
//
//    private static WxPayService wxPayServiceInstance;
//
//    @Autowired
//    private WxPayNotifyConfig notifyConfig;
//
//    @PostConstruct
//    public void init() {
//        try {
//            wxPayServiceInstance = createWxPayService();
//            log.info("微信支付服务初始化成功");
//        } catch (Exception e) {
//            log.error("微信支付服务初始化失败", e);
//            throw new RuntimeException("微信支付服务初始化失败", e);
//        }
//    }
//
//    private WxPayService createWxPayService() {
//        WxPayConfig payConfig = new WxPayConfig();
//
//        // 基础配置
//        payConfig.setAppId(appId);
//        payConfig.setMchId(merchantId);
//        payConfig.setApiV3Key(apiV3Key);
//        payConfig.setPrivateKeyPath(privateKeyPath);
//
//        // 可选配置
//        if (keyPath != null && !keyPath.trim().isEmpty()) {
//            payConfig.setKeyPath(keyPath);
//        }
//        if (publicKeyPath != null && !publicKeyPath.trim().isEmpty()) {
//            payConfig.setPublicKeyPath(publicKeyPath);
//        }
//        if (publicKeyId != null && !publicKeyId.trim().isEmpty()) {
//            payConfig.setPublicKeyId(publicKeyId);
//        }
//        if (privateCertPath != null && !privateCertPath.trim().isEmpty()) {
//            payConfig.setPrivateCertPath(privateCertPath);
//        }
//
//        // 验证必要配置
//        validateConfig(payConfig);
//
//        WxPayService wxPayService = new WxPayServiceImpl();
//        wxPayService.setConfig(payConfig);
//        return wxPayService;
//    }
//
//    private void validateConfig(WxPayConfig config) {
//        if (config.getAppId() == null || config.getAppId().trim().isEmpty()) {
//            throw new RuntimeException("微信支付配置错误：AppId 不能为空");
//        }
//        if (config.getMchId() == null || config.getMchId().trim().isEmpty()) {
//            throw new RuntimeException("微信支付配置错误：商户号 不能为空");
//        }
//        if (config.getApiV3Key() == null || config.getApiV3Key().trim().isEmpty()) {
//            throw new RuntimeException("微信支付配置错误：API v3 密钥 不能为空");
//        }
//        if (config.getPrivateKeyPath() == null || config.getPrivateKeyPath().trim().isEmpty()) {
//            throw new RuntimeException("微信支付配置错误：私钥路径 不能为空");
//        }
//
//
//        log.info("微信支付配置验证通过 - AppId: {}, 商户号: {}", config.getAppId(), config.getMchId());
//    }
//
//    private static WxPayService wxPayService() {
//        if (wxPayServiceInstance == null) {
//            throw new RuntimeException("微信支付服务未初始化，请检查配置");
//        }
//        return wxPayServiceInstance;
//    }
//
//    /**
//     * 微信小程序支付(v3) - 使用默认回调地址
//     *
//     * @param orderNo     订单唯一编号
//     * @param total       金额(元)
//     * @param description 商品描述
//     * @param openid      用户标识
//     * @return 返回支付参数
//     */
//    public String createOrderV3(String orderNo, BigDecimal total, String description, String openid) {
//        return createOrderV3(orderNo, total, description, openid, notifyConfig.getPayNotifyUrl());
//    }
//
//    /**
//     * 微信小程序支付(v3) - 自定义回调地址
//     *
//     * @param orderNo     订单唯一编号
//     * @param total       金额(元)
//     * @param description 商品描述
//     * @param openid      用户标识
//     * @param notifyUrl   回调地址
//     * @return 返回支付参数
//     */
//    public static String createOrderV3(String orderNo, BigDecimal total, String description, String openid, String notifyUrl) {
//        WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request();
//        request.setAmount(new WxPayUnifiedOrderV3Request.Amount()
//                .setCurrency(WxPayConstants.CurrencyType.CNY)
//                .setTotal(BaseWxPayRequest.yuan2Fen(total)));
//        request.setDescription(description);
//        request.setOutTradeNo(orderNo);
//        request.setPayer(new WxPayUnifiedOrderV3Request.Payer().setOpenid(openid));
//        request.setNotifyUrl(notifyUrl);
//        request.setTimeExpire(timeExpire());
//
//        try {
//            return JSONObject.toJSONString(wxPayService().createOrderV3(TradeTypeEnum.JSAPI, request));
//        } catch (Exception e) {
//            log.error("微信小程序支付(v3)异常: ", e);
//            throw new RuntimeException("创建支付订单失败", e);
//        }
//    }
//
//    /**
//     * 微信小程序退款(v3)
//     *
//     * @param orderNo     原订单编号
//     * @param refundNo    退款唯一编号
//     * @param total       原订单金额(元)
//     * @param refundPrice 退款金额(元)
//     * @return 退款状态
//     */
//    public static boolean refundOrderV3(String orderNo, String refundNo, BigDecimal total, BigDecimal refundPrice) {
//        WxPayRefundV3Request request = new WxPayRefundV3Request();
//        request.setOutTradeNo(orderNo);
//        request.setOutRefundNo(refundNo);
//        request.setAmount(new WxPayRefundV3Request.Amount()
//                .setTotal(BaseWxPayRequest.yuan2Fen(total))
//                .setRefund(BaseWxPayRequest.yuan2Fen(refundPrice))
//                .setCurrency(WxPayConstants.CurrencyType.CNY));
//
//        try {
//            String status = wxPayService().refundV3(request).getStatus();
//            return WxPayConstants.RefundStatus.PROCESSING.equals(status) ||
//                   WxPayConstants.RefundStatus.SUCCESS.equals(status);
//        } catch (WxPayException e) {
//            log.error("微信退款(v3)异常: ", e);
//            return false;
//        }
//    }
//
//    /**
//     * 根据商户订单号查询订单支付状态
//     *
//     * @param orderNo 订单唯一编号(商户订单号)
//     * @return 支付状态
//     */
//    public static boolean queryOrderV3(String orderNo) {
//        try {
//            WxPayOrderQueryV3Result result = wxPayService().queryOrderV3(
//                new WxPayOrderQueryV3Request().setOutTradeNo(orderNo));
//            log.info("查询订单结果(V3): {}", result);
//            return WxPayConstants.WxpayTradeStatus.SUCCESS.equals(result.getTradeState());
//        } catch (WxPayException e) {
//            log.error("查询订单结果异常(V3): ", e);
//            return false;
//        }
//    }
//
//    /**
//     * 根据商户订单号关闭订单
//     *
//     * @param orderNo 订单唯一编号(商户订单号)
//     */
//    public static void closeOrderV3(String orderNo) {
//        WxPayOrderCloseV3Request request = new WxPayOrderCloseV3Request();
//        request.setOutTradeNo(orderNo);
//        try {
//            wxPayService().closeOrderV3(request);
//            log.info("关闭订单成功: {}", orderNo);
//        } catch (WxPayException e) {
//            log.error("关闭订单异常(V3): ", e);
//        }
//    }
//
//
//
//    /**
//     * 交易结束时间(30分钟)
//     */
//    private static String timeExpire() {
//        Calendar calendar = Calendar.getInstance();
//        calendar.add(Calendar.MINUTE, 30);
//        return new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX").format(calendar.getTime());
//    }
//}
////
////    /**
////     * 微信小程序支付(v3)
////     *
////     * @param orderNo     订单唯一编号
////     * @param total       金额(元)
////     * @param description 商品描述
////     * @param openid      用户标识
////     * @return 返回支付链接
////     */
////    public static String createOrderV3(String orderNo, BigDecimal total, String description, String openid, String notifyUrl) {
////        WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request();
////        request.setAmount(new WxPayUnifiedOrderV3Request.Amount()
////                .setCurrency((WxPayConstants.CurrencyType.CNY))
////                .setTotal(BaseWxPayRequest.yuan2Fen(total)));
////        request.setDescription(description);
////        request.setOutTradeNo(orderNo);
////        request.setPayer(new WxPayUnifiedOrderV3Request.Payer().setOpenid(openid));
////        //异步回调通知
////        request.setNotifyUrl(notifyUrl);
//////        request.setTimeExpire(timeExpire());
////        try {
////            return JSONObject.toJSONString(wxPayService().createOrderV3(TradeTypeEnum.JSAPI, request));
////        } catch (Exception e) {
////            log.info("微信小程序支付(v3)异常: " + e);
////        }
////        return null;
////    }
////
////
////    /**
////     * 微信小程序退款(v3)
////     *
////     * @param orderNo     原订单编号
////     * @param refundNo    退款唯一编号
////     * @param total       金额(元)
////     * @param refundPrice 退款金额(元)
////     * @return 退款状态
////     */
////    public static boolean refundOrderV3(String orderNo, String refundNo, BigDecimal total, BigDecimal refundPrice) {
////        WxPayRefundV3Request request = new WxPayRefundV3Request();
////        request.setOutTradeNo(orderNo);
////        request.setOutRefundNo(refundNo);
////        request.setAmount(new WxPayRefundV3Request.Amount()
////                .setTotal(BaseWxPayRequest.yuan2Fen(total))
////                .setRefund(BaseWxPayRequest.yuan2Fen(refundPrice))
////                .setCurrency(WxPayConstants.CurrencyType.CNY));
////        try {
////            String status = wxPayService().refundV3(request).getStatus();
////            return WxPayConstants.RefundStatus.PROCESSING.equals(status) || WxPayConstants.RefundStatus.SUCCESS.equals(status);
////        } catch (WxPayException e) {
////            log.info("微信app支付(v3)异常: " + e);
////        }
////        return false;
////    }
////
////
////    /**
////     * 根据商户订单号查询订单支付状态
////     *
////     * @param orderNo 订单唯一编号(商户订单号)
////     * @return 支付状态
////     */
////    public static boolean queryOrderV3(String orderNo) {
////        try {
////            WxPayOrderQueryV3Result result = wxPayService().queryOrderV3(new WxPayOrderQueryV3Request().setOutTradeNo(orderNo));
////            log.info("查询订单结果(V3): {}", result);
////            return WxPayConstants.WxpayTradeStatus.SUCCESS.equals(result.getTradeState());
////        } catch (WxPayException e) {
////            log.info("查询订单结果异常(V3): " + e);
////        }
////        return false;
////    }
////
////
////    /**
////     * 根据商户订单号关闭订单
////     *
////     * @param orderNo 订单唯一编号(商户订单号)
////     */
////    public static void closeOrderV3(String orderNo) {
////        WxPayOrderCloseV3Request request = new WxPayOrderCloseV3Request();
////        request.setOutTradeNo(orderNo);
////        try {
////            wxPayService().closeOrderV3(request);
////        } catch (WxPayException e) {
////            log.info("关闭订单异常(V3): " + e);
////        }
////    }
////
////
////    /**
////     * 获取订单支付回调信息
////     */
////    public static WxPayNotifyV3Result.DecryptNotifyResult notifyV3Result(HttpServletRequest request) {
////        try {
////            return wxPayService()
////                    .parseOrderNotifyV3Result(RequestUtils.readData(request), SignatureHeader.builder()
////                            .timeStamp(request.getHeader("Wechatpay-TimeStamp"))
////                            .nonce(request.getHeader("Wechatpay-Nonce"))
////                            .signature(request.getHeader("Wechatpay-Signature"))
////                            .serial(request.getHeader("Wechatpay-Serial")).build())
////                    .getResult();
////        } catch (WxPayException e) {
////            log.info("获取订单支付回调信息异常(V3): " + e);
////            throw new SxkfException("获取订单支付回调信息异常code:" + e.getErrCode() + "msg:" + e.getCustomErrorMsg());
////        }
////    }
////
////
////    /**
////     * 交易结束时间(10分钟)
////     */
////    private static String timeExpire() {
////        Calendar calendar = Calendar.getInstance();
////        calendar.add(Calendar.MINUTE, 10);
////        return new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX").format(calendar.getTime());
////    }
////
////
////}
////
////
////
