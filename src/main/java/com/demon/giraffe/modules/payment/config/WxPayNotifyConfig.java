package com.demon.giraffe.modules.payment.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 微信支付回调地址配置
 */
@Component
public class WxPayNotifyConfig {

    // 常量定义
    private static final String PAY_NOTIFY_PATH = "/api/payment/wechat/notify";
    private static final String REFUND_NOTIFY_PATH = "/payment/wechat/refund-notify";
    private static final String HTTPS_PREFIX = "https://";
    private static final String PATH_SEPARATOR = "/";

    @Value("${server.domain:}")
    private String serverDomain;

    /**
     * 获取支付成功回调地址
     */
    public String getPayNotifyUrl() {
        return buildNotifyUrl(PAY_NOTIFY_PATH);
    }

    /**
     * 获取退款成功回调地址
     */
    public String getRefundNotifyUrl() {
        return buildNotifyUrl(REFUND_NOTIFY_PATH);
    }

    /**
     * 构建完整的回调地址
     */
    private String buildNotifyUrl(String path) {
        if (serverDomain == null || serverDomain.trim().isEmpty()) {
            throw new RuntimeException("服务器域名未配置，无法生成微信支付回调地址");
        }

        String domain = serverDomain.trim();
        if (domain.endsWith(PATH_SEPARATOR)) {
            domain = domain.substring(0, domain.length() - 1);
        }

        if (!path.startsWith(PATH_SEPARATOR)) {
            path = PATH_SEPARATOR + path;
        }

        return domain + path;
    }

    /**
     * 验证回调地址是否有效
     */
    public boolean isValidNotifyUrl(String url) {
        return url != null &&
                url.startsWith(HTTPS_PREFIX) &&
                url.contains(serverDomain);
    }
}