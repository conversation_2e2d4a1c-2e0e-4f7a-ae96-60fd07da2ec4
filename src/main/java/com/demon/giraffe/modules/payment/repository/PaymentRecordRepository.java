package com.demon.giraffe.modules.payment.repository;

import com.demon.giraffe.modules.payment.model.enums.PaymentStatusEnum;
import com.demon.giraffe.modules.payment.model.po.PaymentRecordPo;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 支付记录仓储接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface PaymentRecordRepository {

    /**
     * 保存支付记录
     *
     * @param po 支付记录PO
     * @return 保存后的支付记录
     */
    PaymentRecordPo save(PaymentRecordPo po);

    /**
     * 根据ID查询支付记录
     *
     * @param id 支付记录ID
     * @return 支付记录
     */
    PaymentRecordPo getById(Long id);

    /**
     * 根据支付单号查询支付记录
     *
     * @param paymentNo 支付单号
     * @return 支付记录
     */
    PaymentRecordPo getByPaymentNo(String paymentNo);

    /**
     * 根据订单号查询支付记录
     *
     * @param orderNo 订单号
     * @return 支付记录
     */
    PaymentRecordPo getByOrderNo(String orderNo);

    /**
     * 根据第三方交易号查询支付记录
     *
     * @param transactionId 第三方交易号
     * @return 支付记录
     */
    PaymentRecordPo getByTransactionId(String transactionId);

    /**
     * 根据订单ID查询支付记录列表
     *
     * @param orderId 订单ID
     * @return 支付记录列表
     */
    List<PaymentRecordPo> listByOrderId(Long orderId);

    /**
     * 更新支付记录
     *
     * @param po 支付记录PO
     * @return 是否更新成功
     */
    boolean updateById(PaymentRecordPo po);

    /**
     * 更新支付状态
     *
     * @param id 支付记录ID
     * @param status 支付状态
     * @return 是否更新成功
     */
    boolean updateStatus(Long id, PaymentStatusEnum status);

    /**
     * 根据会员ID查询支付记录列表
     *
     * @param memberId 会员ID
     * @return 支付记录列表
     */
    List<PaymentRecordPo> listByMemberId(Long memberId);

    /**
     * 查询过期的未支付记录
     *
     * @param currentTime 当前时间
     * @return 过期的支付记录列表
     */
    List<PaymentRecordPo> findExpiredUnpaidRecords(LocalDateTime currentTime);
}
