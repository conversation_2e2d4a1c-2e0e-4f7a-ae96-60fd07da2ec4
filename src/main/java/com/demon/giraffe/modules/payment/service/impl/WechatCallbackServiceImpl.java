package com.demon.giraffe.modules.payment.service.impl;

import com.demon.giraffe.modules.payment.service.CallbackService;
import com.demon.giraffe.modules.payment.service.WechatPayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 微信支付回调服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service("wechatCallbackService")
@RequiredArgsConstructor
public class WechatCallbackServiceImpl implements CallbackService {

    private final WechatPayService wechatPayService;

    @Override
    public String handleWechatPayCallback(String callbackData, HttpServletRequest request) {
        try {
            log.info("收到微信支付回调通知");
            return wechatPayService.handleNotify(callbackData, request);
        } catch (Exception e) {
            log.error("处理微信支付回调失败", e);
            return "FAIL";
        }
    }

    @Override
    public String handleWechatRefundCallback(String callbackData, HttpServletRequest request) {
        try {
            log.info("收到微信退款回调通知");
            return wechatPayService.handleRefundNotify(callbackData, request);
        } catch (Exception e) {
            log.error("处理微信退款回调失败", e);
            return "FAIL";
        }
    }
}
