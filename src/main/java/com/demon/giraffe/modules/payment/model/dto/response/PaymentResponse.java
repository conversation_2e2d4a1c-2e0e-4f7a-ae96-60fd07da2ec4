package com.demon.giraffe.modules.payment.model.dto.response;

import com.demon.giraffe.modules.payment.model.entity.JsapiPayData;
import com.demon.giraffe.modules.payment.model.enums.PaymentStatusEnum;
import com.demon.giraffe.modules.payment.model.enums.PaymentTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付响应
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "支付响应")
public class PaymentResponse {

    @Schema(description = "支付记录ID")
    private Long paymentId;

    @Schema(description = "支付单号")
    private String paymentNo;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "支付金额")
    private BigDecimal amount;

    @Schema(description = "支付方式")
    private PaymentTypeEnum paymentType;

    @Schema(description = "支付状态")
    private PaymentStatusEnum paymentStatus;

    @Schema(description = "预支付交易会话标识")
    private String prepayId;

    @Schema(description = "二维码链接（Native支付）")
    private String codeUrl;

    @Schema(description = "JSAPI支付参数")
    private JsapiPayData jsapiPayData;

    @Schema(description = "H5支付链接")
    private String h5Url;

    @Schema(description = "第三方交易号")
    private String transactionId;

    @Schema(description = "支付完成时间")
    private LocalDateTime payTime;

    @Schema(description = "订单失效时间")
    private LocalDateTime expireTime;

    @Schema(description = "错误信息")
    private String errorMessage;


}
