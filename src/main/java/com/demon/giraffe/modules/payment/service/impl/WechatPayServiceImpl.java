package com.demon.giraffe.modules.payment.service.impl;

import com.demon.giraffe.common.config.WxConfig;
import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.common.util.CodeGeneratorUtil;
import com.demon.giraffe.framework.redis.service.RedisService;
import com.demon.giraffe.framework.redis.util.RedisUtils;
import com.demon.giraffe.modules.order.model.enums.PayStatusEnum;
import com.demon.giraffe.modules.order.model.po.OrderMainPo;
import com.demon.giraffe.modules.order.repository.OrderMainRepository;
import com.demon.giraffe.modules.order.service.OrderOperationService;
import com.demon.giraffe.modules.user.repository.UserRepository;
import com.demon.giraffe.modules.user.service.MemberIdentityService;
import com.demon.giraffe.modules.payment.model.dto.response.PaymentResponse;
import com.demon.giraffe.modules.payment.model.entity.CreateOrderRequest;
import com.demon.giraffe.modules.payment.model.entity.JsapiPayData;
import com.demon.giraffe.modules.payment.model.enums.PaymentStatusEnum;
import com.demon.giraffe.modules.payment.model.enums.PaymentTypeEnum;
import com.demon.giraffe.modules.payment.model.po.PaymentRecordPo;
import com.demon.giraffe.modules.payment.repository.PaymentRecordRepository;
import com.demon.giraffe.modules.payment.service.WechatPayService;
import com.wechat.pay.java.core.exception.ServiceException;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.service.payments.jsapi.JsapiService;
import com.wechat.pay.java.service.payments.jsapi.model.Payer;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import com.wechat.pay.java.service.payments.nativepay.model.*;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.servlet.http.HttpServletRequest;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.UUID;

import static com.demon.giraffe.modules.payment.constants.PayConstants.PAY_EXPIRE_MINUTES;
import static com.demon.giraffe.modules.payment.constants.PayConstants.PAYMENT_EXPIRE_KEY_PREFIX;

@Slf4j
@Service
@RequiredArgsConstructor
public class WechatPayServiceImpl implements WechatPayService {

    private final NativePayService nativePayService;
    private final JsapiService jsapiService;
    private final RefundService refundService;
    private final NotificationParser notificationParser;
    private final PaymentRecordRepository paymentRecordRepository;
    private final OrderMainRepository orderMainRepository;
    private final CodeGeneratorUtil codeGeneratorUtil;
    private final RedisService redisService;
    private final OrderOperationService orderOperationService;
    private final MemberIdentityService memberIdentityService;
    private final UserRepository userRepository;

    private final WxConfig wxConfig;


    @Value("${server.domain:https://www.giraffewash.com}")
    private String serverDomain;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public PaymentResponse createPayment(CreateOrderRequest request) {
        // 参数校验
        validateCreateOrderRequest(request);

        try {
            // 1. 创建支付记录
            PaymentRecordPo paymentRecord = createPaymentRecord(request);
            PaymentRecordPo savedRecord = paymentRecordRepository.save(paymentRecord);

            // 2. 构建响应
            PaymentResponse response = buildPaymentResponse(request, savedRecord);

            // 3. 执行支付操作
            executePayment(request, response);

            // 4. 存储支付过期信息到Redis
            storePaymentExpireInfo(request.getOrderInfo().getOrderNo(), savedRecord.getExpireTime());

            return response;
        } catch (Exception e) {
            log.error("创建支付失败，订单号：{}", request.getOrderInfo().getOrderNo(), e);
            throw new BusinessException("创建支付失败：" + e.getMessage());
        }
    }

    private void validateCreateOrderRequest(CreateOrderRequest request) {
        if (request == null || request.getOrderInfo() == null || request.getMemberInfo() == null
                || request.getPaymentInfo() == null) {
            throw new BusinessException("下单请求参数不完整");
        }

        if (StringUtils.isBlank(request.getMemberInfo().getOpenid())) {
            throw new BusinessException("微信openid不能为空");
        }
    }

    private PaymentRecordPo createPaymentRecord(CreateOrderRequest request) {
        PaymentRecordPo paymentRecord = new PaymentRecordPo();
        paymentRecord.setOrderId(request.getOrderInfo().getOrderId());
        paymentRecord.setOrderNo(request.getOrderInfo().getOrderNo());
        paymentRecord.setPaymentNo(codeGeneratorUtil.generatePaymentCode());
        paymentRecord.setMemberId(request.getMemberInfo().getMemberId());
        paymentRecord.setPaymentType(request.getPaymentInfo().getPaymentType());
        paymentRecord.setPaymentAmount(request.getOrderInfo().getPayAmount());
        paymentRecord.setPaymentStatus(PaymentStatusEnum.NOTPAY);
        paymentRecord.setCurrency("CNY");

        LocalDateTime expireTime = LocalDateTime.now()
                .plusMinutes(PAY_EXPIRE_MINUTES);
        paymentRecord.setExpireTime(expireTime);

        return paymentRecord;
    }

    private PaymentResponse buildPaymentResponse(CreateOrderRequest request, PaymentRecordPo savedRecord) {
        PaymentResponse response = new PaymentResponse();
        response.setPaymentId(savedRecord.getId());
        response.setPaymentNo(savedRecord.getPaymentNo());
        response.setOrderNo(request.getOrderInfo().getOrderNo());
        response.setAmount(request.getOrderInfo().getPayAmount());
        response.setPaymentType(request.getPaymentInfo().getPaymentType());
        response.setPaymentStatus(PaymentStatusEnum.NOTPAY);
        return response;
    }

    private void executePayment(CreateOrderRequest request, PaymentResponse response) {
        if (request.getPaymentInfo().getPaymentType() == PaymentTypeEnum.WECHAT) {
            int totalAmount = request.getOrderInfo().getPayAmount()
                    .multiply(BigDecimal.valueOf(100)).intValue();
            String description = request.getOrderInfo().getDescription();

            PaymentResponse jsapiResponse = createJsapiOrder(
                    request.getOrderInfo().getOrderNo(),
                    description,
                    totalAmount,
                    request.getMemberInfo().getOpenid());

            response.setPrepayId(jsapiResponse.getPrepayId());
            response.setJsapiPayData(jsapiResponse.getJsapiPayData());

            // 更新支付记录中的prepayId
            updatePaymentRecordPrepayId(response.getPaymentId(), jsapiResponse.getPrepayId());
        } else {
            throw new BusinessException("暂不支持的支付方式");
        }
    }


    /**
     * JSAPI下单
     */
    @Override
    public PaymentResponse createJsapiOrder(String outTradeNo, String description, int totalAmount, String openid) {
        com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest request =
                new com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest();

        com.wechat.pay.java.service.payments.jsapi.model.Amount amount =
                new com.wechat.pay.java.service.payments.jsapi.model.Amount();
        amount.setTotal(totalAmount);
        request.setAmount(amount);

        Payer payer = new Payer();
        payer.setOpenid(openid);
        request.setPayer(payer);

        request.setAppid(wxConfig.getAppid());
        request.setMchid(wxConfig.getMerchantId());
        request.setDescription(description);
        request.setNotifyUrl(serverDomain + "/api/payment/callback/wechat");
        request.setOutTradeNo(outTradeNo);

        com.wechat.pay.java.service.payments.jsapi.model.PrepayResponse response = jsapiService.prepay(request);
        return buildJsapiPayData(response.getPrepayId());
    }


    /**
     * JSAPI再次下单（支持检查prepayId是否过期）
     *
     * @param originalOutTradeNo 原订单号
     * @return PaymentResponse
     */

    public PaymentResponse recreateJsapiOrder(String prepayId, String originalOutTradeNo) {
        // 1. 检查prepayId是否在有效期内
        if (isPrepayIdValid(prepayId, originalOutTradeNo)) {
            // 2. 如果有效，直接使用原prepayId构建支付参数
            return buildJsapiPayData(prepayId);
        }
        throw new BusinessException("订单已过期");
    }

    @Override
    public PaymentResponse repayOrder(String orderNo) {
        log.info("开始重新支付，订单号：{}", orderNo);

        try {
            // 1. 根据订单号查找支付记录获取prepayId
            PaymentRecordPo paymentRecord = paymentRecordRepository.getByOrderNo(orderNo);
            if (paymentRecord == null) {
                throw new BusinessException("支付记录不存在，订单号：" + orderNo);
            }

            String prepayId = paymentRecord.getPrepayId();

            // 2. 检查prepayId是否在有效期内
            if (StringUtils.isNotBlank(prepayId) && isPrepayIdValid(prepayId, orderNo)) {
                // 如果有效，直接使用原prepayId构建支付参数
                log.info("使用现有prepayId重新支付，订单号：{}", orderNo);
                PaymentResponse response = buildJsapiPayData(prepayId);
                response.setPaymentId(paymentRecord.getId());
                return response;
            }

            // 3. prepayId无效或不存在，抛出异常
            throw new BusinessException("订单已过期");

        } catch (BusinessException e) {
            log.error("重新支付失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("重新支付异常，订单号：{}", orderNo, e);
            throw new BusinessException("重新支付失败：" + e.getMessage());
        }
    }


    /**
     * 检查prepayId是否有效（更可靠的实现）
     *
     * @param prepayId   预支付ID
     * @param outTradeNo 商户订单号
     * @return 是否有效
     */
    private boolean isPrepayIdValid(String prepayId, String outTradeNo) {
        if (StringUtils.isEmpty(prepayId)) {
            return false;
        }

        // 1. 先检查Redis中的订单过期状态
        if (checkPaymentExpired(outTradeNo)) {
            log.info("订单已过期，订单号：{}", outTradeNo);
            return false;
        }

        try {
            // 2. 再通过微信接口查询订单状态
            com.wechat.pay.java.service.payments.jsapi.model.QueryOrderByOutTradeNoRequest queryRequest = new com.wechat.pay.java.service.payments.jsapi.model.QueryOrderByOutTradeNoRequest();
            queryRequest.setMchid(wxConfig.getMerchantId());
            queryRequest.setOutTradeNo(outTradeNo);
            Transaction transaction = jsapiService.queryOrderByOutTradeNo(queryRequest);

            // 3. 根据订单状态判断
            Transaction.TradeStateEnum tradeState = transaction.getTradeState();

            // 4. 根据订单状态判断
            return switch (tradeState) {    // 未支付
                case NOTPAY, USERPAYING -> // 用户支付中
                        true;   // 支付成功
                // 已退款
                // 已关闭
                // 已撤销
                // 支付失败
                // 已接收（等待扣款）
                default -> false;
            };
        } catch (ServiceException e) {
            // 如果订单不存在或其他错误，视为无效
            if (e.getErrorCode().equals("ORDER_NOT_EXIST")) {
                return false;
            }
            // 其他异常情况，可以记录日志并根据业务需求处理
            log.error("查询微信订单状态异常", e);
            return false;
        }
    }

    /**
     * 构建JSAPI支付参数
     *
     * @param prepayId 预支付ID
     * @return PaymentResponse
     */
    private PaymentResponse buildJsapiPayData(String prepayId) {
        PaymentResponse paymentResponse = new PaymentResponse();
        paymentResponse.setPrepayId(prepayId);

        // 构造JSAPI支付参数
        JsapiPayData jsapiPayData = new JsapiPayData();
        jsapiPayData.setAppId(wxConfig.getAppid());
        jsapiPayData.setTimeStamp(String.valueOf(System.currentTimeMillis() / 1000));
        jsapiPayData.setNonceStr(UUID.randomUUID().toString().replace("-", ""));
        jsapiPayData.setPackageValue("prepay_id=" + prepayId);
        jsapiPayData.setSignType("RSA");

        // 计算签名
        String sign = generateSignature(
                jsapiPayData.getAppId(),
                jsapiPayData.getTimeStamp(),
                jsapiPayData.getNonceStr(),
                jsapiPayData.getPackageValue()
        );
        jsapiPayData.setPaySign(sign);

        paymentResponse.setJsapiPayData(jsapiPayData);
        return paymentResponse;
    }

    /**
     * 生成支付签名
     */
    private String generateSignature(String appId, String timeStamp, String nonceStr, String packageValue) {
        try {
            String message = appId + "\n" +
                    timeStamp + "\n" +
                    nonceStr + "\n" +
                    packageValue + "\n";

            // 从文件路径加载私钥
            PrivateKey privateKey = loadPrivateKeyFromFile();
            // 使用商户私钥进行签名
            Signature sign = Signature.getInstance("SHA256withRSA");
            sign.initSign(privateKey);
            sign.update(message.getBytes(StandardCharsets.UTF_8));

            return Base64.getEncoder().encodeToString(sign.sign());
        } catch (Exception e) {
            throw new RuntimeException("生成签名失败", e);
        }
    }

    /**
     * 从 PEM 格式的私钥文件加载 PrivateKey
     */
    private PrivateKey loadPrivateKeyFromFile() throws Exception {
        String privateKeyPath = wxConfig.getPrivateKeyPath();
        // 读取私钥文件内容
        String privateKeyContent = Files.readString(Paths.get(privateKeyPath));

        // 移除 PEM 文件的头尾标记（如 "-----BEGIN PRIVATE KEY-----"）
        privateKeyContent = privateKeyContent
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s", "");  // 移除所有空白字符

        // 解码 Base64 并生成 PrivateKey
        byte[] decodedKey = Base64.getDecoder().decode(privateKeyContent);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decodedKey);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePrivate(keySpec);
    }


    /**
     * 查询订单状态（通过商户订单号）
     */
    @Override
    public Transaction queryOrder(String outTradeNo) {
        QueryOrderByOutTradeNoRequest request = new QueryOrderByOutTradeNoRequest();
        request.setMchid(wxConfig.getMerchantId());
        request.setOutTradeNo(outTradeNo);

        Transaction result = nativePayService.queryOrderByOutTradeNo(request);
        log.info("微信订单查询结果：{}", result);

        return result;
    }

    /**
     * 申请退款
     */
    @Override
    public String refund(String outTradeNo, String outRefundNo, BigDecimal refundAmount, BigDecimal totalAmount, String reason) {
        try {
            CreateRequest request = new CreateRequest();
            request.setOutTradeNo(outTradeNo);
            request.setOutRefundNo(outRefundNo);
            request.setReason(reason);
            request.setNotifyUrl(serverDomain + "/api/payment/callback/wechat/refund");

            AmountReq amountReq = new AmountReq();
            amountReq.setRefund(refundAmount.multiply(BigDecimal.valueOf(100)).longValue()); // 转换为分
            amountReq.setTotal(totalAmount.multiply(BigDecimal.valueOf(100)).longValue()); // 转换为分
            amountReq.setCurrency("CNY");
            request.setAmount(amountReq);

            Refund refund = refundService.create(request);
            log.info("微信退款申请成功：{}", refund);

            return refund.getRefundId();

        } catch (Exception e) {
            log.error("微信退款申请失败，订单号：{}，退款单号：{}", outTradeNo, outRefundNo, e);
            throw new BusinessException("退款申请失败：" + e.getMessage());
        }
    }

    /**
     * 查询退款状态
     */
    @Override
    public String queryRefund(String outRefundNo) {
        try {
            QueryByOutRefundNoRequest request = new QueryByOutRefundNoRequest();
            request.setOutRefundNo(outRefundNo);

            Refund refund = refundService.queryByOutRefundNo(request);
            log.info("微信退款查询结果：{}", refund);

            return refund.getStatus().name();

        } catch (Exception e) {
            log.error("微信退款查询失败，退款单号：{}", outRefundNo, e);
            throw new BusinessException("退款查询失败：" + e.getMessage());
        }
    }

    /**
     * 关闭订单
     */
    @Override
    public void closeOrder(String outTradeNo) {
        CloseOrderRequest request = new CloseOrderRequest();
        request.setMchid(wxConfig.getMerchantId());
        request.setOutTradeNo(outTradeNo);

        nativePayService.closeOrder(request);
        log.info("关闭微信订单成功：{}", outTradeNo);
    }

    /**
     * 处理支付通知
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String handleNotify(String notifyBody, HttpServletRequest request) {
        try {
            // 构造通知参数
            RequestParam param = new RequestParam.Builder()
                    .serialNumber(request.getHeader("Wechatpay-Serial"))
                    .nonce(request.getHeader("Wechatpay-Nonce"))
                    .signature(request.getHeader("Wechatpay-Signature"))
                    .timestamp(request.getHeader("Wechatpay-Timestamp"))
                    .body(notifyBody)
                    .build();

            // 验签 + 解密
            Transaction transaction = notificationParser.parse(param, Transaction.class);
            log.info("支付成功通知内容: {}", transaction);

            // 处理业务逻辑
            processPaymentNotify(transaction, notifyBody);

            return "SUCCESS";
        } catch (Exception e) {
            log.error("处理支付回调失败", e);
            return "FAIL";
        }
    }

    /**
     * 处理退款通知
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String handleRefundNotify(String notifyBody, HttpServletRequest request) {
        try {
            // 构造通知参数
            RequestParam param = new RequestParam.Builder()
                    .serialNumber(request.getHeader("Wechatpay-Serial"))
                    .nonce(request.getHeader("Wechatpay-Nonce"))
                    .signature(request.getHeader("Wechatpay-Signature"))
                    .timestamp(request.getHeader("Wechatpay-Timestamp"))
                    .body(notifyBody)
                    .build();

            // 验签 + 解密
            Refund refund = notificationParser.parse(param, Refund.class);
            log.info("退款成功通知内容: {}", refund);

            // 处理业务逻辑
            processRefundNotify(refund, notifyBody);

            return "SUCCESS";
        } catch (Exception e) {
            log.error("处理退款回调失败", e);
            return "FAIL";
        }
    }


    /**
     * 处理支付通知业务逻辑
     */
    private void processPaymentNotify(Transaction transaction, String notifyBody) {
        String outTradeNo = transaction.getOutTradeNo();
        String transactionId = transaction.getTransactionId();

        // 1. 更新支付记录
        PaymentRecordPo paymentRecord = paymentRecordRepository.getByOrderNo(outTradeNo);
        if (paymentRecord == null) {
            log.warn("支付回调：找不到支付记录，订单号：{}", outTradeNo);
            return;
        }

        // 防重复处理
        if (paymentRecord.getPaymentStatus() == PaymentStatusEnum.SUCCESS) {
            log.info("支付回调：订单已处理，订单号：{}", outTradeNo);
            return;
        }

        paymentRecord.setPaymentStatus(PaymentStatusEnum.SUCCESS);
        paymentRecord.setTransactionId(transactionId);
        paymentRecord.setPayTime(LocalDateTime.now());
        paymentRecord.setNotifyData(notifyBody);

        paymentRecordRepository.updateById(paymentRecord);

        // 2. 更新订单状态
        OrderMainPo order = orderMainRepository.getById(paymentRecord.getOrderId());
        if (order != null) {
            orderMainRepository.updatePayStatus(order.getId(), PayStatusEnum.PAID);

            // 3. 调用订单操作服务的成功下单处理方法
            try {
                boolean success = orderOperationService.handleSuccessfulOrder(outTradeNo);
                if (success) {
                    log.info("支付成功处理完成，订单号：{}", outTradeNo);
                } else {
                    log.warn("支付成功处理失败，订单号：{}", outTradeNo);
                }
            } catch (Exception e) {
                log.error("支付成功处理异常，订单号：{}", outTradeNo, e);
                // 不影响支付回调的成功返回
            }
        }

        log.info("支付回调处理完成，订单号：{}，交易号：{}", outTradeNo, transactionId);
    }

    /**
     * 处理退款通知业务逻辑
     */
    private void processRefundNotify(Refund refund, String notifyBody) {
        String outTradeNo = refund.getOutTradeNo();
        String outRefundNo = refund.getOutRefundNo();

        log.info("退款回调处理，订单号：{}，退款单号：{}，状态：{}",
                outTradeNo, outRefundNo, refund.getStatus());

        // TODO: 根据业务需求处理退款通知
        // 1. 更新退款记录状态
        // 2. 更新订单状态
        // 3. 发送退款通知等
    }

    /**
     * 存储支付过期信息到Redis
     *
     * @param orderNo    订单号
     * @param expireTime 过期时间
     */
    private void storePaymentExpireInfo(String orderNo, LocalDateTime expireTime) {
        try {
            String redisKey = RedisUtils.redisAssembleKey(PAYMENT_EXPIRE_KEY_PREFIX, orderNo);
            long expireSeconds = Duration.between(LocalDateTime.now(), expireTime).getSeconds();

            if (expireSeconds > 0) {
                // 存储过期时间戳，设置Redis过期时间为支付过期时间
                redisService.set(redisKey, expireTime.toString(), expireSeconds);
                log.info("支付过期信息已存储到Redis，订单号：{}，过期时间：{}", orderNo, expireTime);
            }
        } catch (Exception e) {
            log.error("存储支付过期信息到Redis失败，订单号：{}", orderNo, e);
        }
    }

    /**
     * 更新支付记录中的prepayId
     *
     * @param paymentId 支付记录ID
     * @param prepayId  预支付交易会话标识
     */
    private void updatePaymentRecordPrepayId(Long paymentId, String prepayId) {
        try {
            PaymentRecordPo paymentRecord = paymentRecordRepository.getById(paymentId);
            if (paymentRecord != null) {
                paymentRecord.setPrepayId(prepayId);
                paymentRecordRepository.updateById(paymentRecord);
                log.info("支付记录prepayId已更新，支付ID：{}，prepayId：{}", paymentId, prepayId);
            }
        } catch (Exception e) {
            log.error("更新支付记录prepayId失败，支付ID：{}", paymentId, e);
        }
    }

    @Override
    public long getRemainingPaymentTime(String orderNo) {
        try {
            String redisKey = RedisUtils.redisAssembleKey(PAYMENT_EXPIRE_KEY_PREFIX, orderNo);
            String expireTimeStr = redisService.getValue(redisKey, String.class);

            if (StringUtils.isNotBlank(expireTimeStr)) {
                LocalDateTime expireTime = LocalDateTime.parse(expireTimeStr);
                long remainingSeconds = Duration.between(LocalDateTime.now(), expireTime).getSeconds();
                return Math.max(0, remainingSeconds);
            }

            return 0;
        } catch (Exception e) {
            log.error("查询剩余支付时间失败，订单号：{}", orderNo, e);
            return 0;
        }
    }

    @Override
    public boolean checkPaymentExpired(String orderNo) {
        try {
            String redisKey = RedisUtils.redisAssembleKey(PAYMENT_EXPIRE_KEY_PREFIX, orderNo);
            String expireTimeStr = redisService.getValue(redisKey, String.class);

            if (StringUtils.isBlank(expireTimeStr)) {
                // Redis中没有记录，可能已过期或不存在
                return true;
            }

            LocalDateTime expireTime = LocalDateTime.parse(expireTimeStr);
            boolean expired = LocalDateTime.now().isAfter(expireTime);

            if (expired) {
                // 如果已过期，删除Redis记录
                redisService.deleteKey(redisKey);
                log.info("订单支付已过期，订单号：{}，过期时间：{}", orderNo, expireTime);
            }

            return expired;
        } catch (Exception e) {
            log.error("检查订单支付过期状态失败，订单号：{}", orderNo, e);
            // 出现异常时，为了安全起见，认为已过期
            return true;
        }
    }
}
