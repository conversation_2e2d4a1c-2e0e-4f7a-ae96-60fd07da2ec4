package com.demon.giraffe.modules.payment.service;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 支付回调服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface CallbackService {
    /**
     * 微信支付回调
     *
     * @param callbackData 微信回调数据
     * @param request HTTP请求
     * @return 处理结果
     */
    String handleWechatPayCallback(String callbackData, HttpServletRequest request);

    /**
     * 微信退款回调
     *
     * @param callbackData 微信回调数据
     * @param request HTTP请求
     * @return 处理结果
     */
    String handleWechatRefundCallback(String callbackData, HttpServletRequest request);
}
