package com.demon.giraffe.modules.payment.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.modules.payment.model.enums.PaymentStatusEnum;
import com.demon.giraffe.modules.payment.model.enums.PaymentTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付记录表PO
 * <p>记录所有支付交易的详细信息</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("payment_record")
@Schema(description = "支付记录实体")
public class PaymentRecordPo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /* 数据库字段：order_id */
    @Schema(description = "关联订单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "订单ID不能为空")
    @TableField("order_id")
    private Long orderId;

    /* 数据库字段：order_no */
    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "订单号不能为空")
    @Size(max = 32, message = "订单号长度不能超过32个字符")
    @TableField("order_no")
    private String orderNo;

    /* 数据库字段：payment_no */
    @Schema(description = "支付单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "支付单号不能为空")
    @Size(max = 32, message = "支付单号长度不能超过32个字符")
    @TableField("payment_no")
    private String paymentNo;

    /* 数据库字段：member_id */
    @Schema(description = "会员ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "会员ID不能为空")
    @TableField("member_id")
    private Long memberId;

    /* 数据库字段：payment_type */
    @Schema(description = "支付方式", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "支付方式不能为空")
    @TableField("payment_type")
    private PaymentTypeEnum paymentType;

    /* 数据库字段：payment_amount */
    @Schema(description = "支付金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "支付金额不能为空")
    @DecimalMin(value = "0.01", message = "支付金额必须大于0")
    @Digits(integer = 8, fraction = 2, message = "支付金额格式错误")
    @TableField("payment_amount")
    private BigDecimal paymentAmount;

    /* 数据库字段：payment_status */
    @Schema(description = "支付状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "支付状态不能为空")
    @TableField("payment_status")
    private PaymentStatusEnum paymentStatus;

    /* 数据库字段：transaction_id */
    @Schema(description = "第三方交易号")
    @Size(max = 64, message = "第三方交易号长度不能超过64个字符")
    @TableField("transaction_id")
    private String transactionId;

    /* 数据库字段：currency */
    @Schema(description = "货币类型")
    @Size(max = 8, message = "货币类型长度不能超过8个字符")
    @TableField("currency")
    private String currency;

    /* 数据库字段：pay_time */
    @Schema(description = "支付完成时间")
    @TableField("pay_time")
    private LocalDateTime payTime;

    /* 数据库字段：expire_time */
    @Schema(description = "订单失效时间")
    @TableField("expire_time")
    private LocalDateTime expireTime;

    /* 数据库字段：notify_data */
    @Schema(description = "支付回调原始数据")
    @TableField("notify_data")
    private String notifyData;

    /* 数据库字段：prepay_id */
    @Schema(description = "预支付交易会话标识")
    @Size(max = 64, message = "预支付交易会话标识长度不能超过64个字符")
    @TableField("prepay_id")
    private String prepayId;

}
