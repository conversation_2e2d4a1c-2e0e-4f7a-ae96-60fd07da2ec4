package com.demon.giraffe.modules.payment.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.modules.payment.mapper.PaymentRecordMapper;
import com.demon.giraffe.modules.payment.model.enums.PaymentStatusEnum;
import com.demon.giraffe.modules.payment.model.po.PaymentRecordPo;
import com.demon.giraffe.modules.payment.repository.PaymentRecordRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 支付记录仓储实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
@RequiredArgsConstructor
public class PaymentRecordRepositoryImpl implements PaymentRecordRepository {

    private final PaymentRecordMapper mapper;

    @Override
    public PaymentRecordPo save(PaymentRecordPo po) {
        int result = mapper.insert(po);
        if (result <= 0) {
            throw new BusinessException("支付记录保存失败");
        }
        return po;
    }

    @Override
    public PaymentRecordPo getById(Long id) {
        return mapper.selectById(id);
    }

    @Override
    public PaymentRecordPo getByPaymentNo(String paymentNo) {
        LambdaQueryWrapper<PaymentRecordPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PaymentRecordPo::getPaymentNo, paymentNo);
        wrapper.eq(PaymentRecordPo::getDeleted, false);
        return mapper.selectOne(wrapper);
    }

    @Override
    public PaymentRecordPo getByOrderNo(String orderNo) {
        LambdaQueryWrapper<PaymentRecordPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PaymentRecordPo::getOrderNo, orderNo);
        wrapper.eq(PaymentRecordPo::getDeleted, false);
        wrapper.orderByDesc(PaymentRecordPo::getCreateTime);
        wrapper.last("LIMIT 1");
        return mapper.selectOne(wrapper);
    }

    @Override
    public PaymentRecordPo getByTransactionId(String transactionId) {
        LambdaQueryWrapper<PaymentRecordPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PaymentRecordPo::getTransactionId, transactionId);
        wrapper.eq(PaymentRecordPo::getDeleted, false);
        return mapper.selectOne(wrapper);
    }

    @Override
    public List<PaymentRecordPo> listByOrderId(Long orderId) {
        LambdaQueryWrapper<PaymentRecordPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PaymentRecordPo::getOrderId, orderId);
        wrapper.eq(PaymentRecordPo::getDeleted, false);
        wrapper.orderByDesc(PaymentRecordPo::getCreateTime);
        return mapper.selectList(wrapper);
    }

    @Override
    public boolean updateById(PaymentRecordPo po) {
        return mapper.updateById(po) > 0;
    }

    @Override
    public boolean updateStatus(Long id, PaymentStatusEnum status) {
        LambdaUpdateWrapper<PaymentRecordPo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PaymentRecordPo::getId, id);
        wrapper.set(PaymentRecordPo::getPaymentStatus, status);
        
        return mapper.update(null, wrapper) > 0;
    }

    @Override
    public List<PaymentRecordPo> listByMemberId(Long memberId) {
        LambdaQueryWrapper<PaymentRecordPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PaymentRecordPo::getMemberId, memberId);
        wrapper.eq(PaymentRecordPo::getDeleted, false);
        wrapper.orderByDesc(PaymentRecordPo::getCreateTime);
        return mapper.selectList(wrapper);
    }

    @Override
    public List<PaymentRecordPo> findExpiredUnpaidRecords(LocalDateTime currentTime) {
        LambdaQueryWrapper<PaymentRecordPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PaymentRecordPo::getDeleted, false);
        wrapper.in(PaymentRecordPo::getPaymentStatus, PaymentStatusEnum.NOTPAY, PaymentStatusEnum.USERPAYING);
        wrapper.lt(PaymentRecordPo::getExpireTime, currentTime);
        wrapper.orderByAsc(PaymentRecordPo::getExpireTime);
        return mapper.selectList(wrapper);
    }
}
