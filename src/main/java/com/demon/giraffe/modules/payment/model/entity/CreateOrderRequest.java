package com.demon.giraffe.modules.payment.model.entity;

import com.demon.giraffe.modules.payment.model.enums.PaymentTypeEnum;
import com.demon.giraffe.modules.user.model.enums.MemberLevelEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 下单请求实体
 */
@Data
@Schema(description = "下单请求参数")
public class CreateOrderRequest {

    @Schema(description = "订单信息", required = true)
    @NotNull(message = "订单信息不能为空")
    private OrderInfo orderInfo;

    @Schema(description = "会员信息", required = true)
    @NotNull(message = "会员信息不能为空")
    private MemberInfo memberInfo;

    @Schema(description = "支付信息", required = true)
    @NotNull(message = "支付信息不能为空")
    private PaymentInfo paymentInfo;

    @Data
    @Schema(description = "订单信息")
    public static class OrderInfo {
        @Schema(description = "订单号", required = true)
        private String orderNo;

        @Schema(description = "支付金额", required = true)
        private BigDecimal payAmount;

        @Schema(description = "订单描述", required = true)
        private String description;

        @Schema(description = "订单ID", required = true)
        private Long orderId;
    }

    @Data
    @Schema(description = "会员信息")
    public static class MemberInfo {
        @Schema(description = "会员ID", required = true)
        private Long memberId;
        @Schema(description = "微信openid", required = true)
        private String openid;

    }

    @Data
    @Schema(description = "支付信息")
    public static class PaymentInfo {

        @Schema(description = "支付类型", required = true)
        private PaymentTypeEnum paymentType;

    }
}