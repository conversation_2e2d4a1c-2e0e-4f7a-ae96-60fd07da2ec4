package com.demon.giraffe.modules.payment.handler;

import com.demon.giraffe.framework.redis.expiration.AbstractRedisExpirationHandler;
import com.demon.giraffe.modules.order.service.OrderOperationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.demon.giraffe.modules.payment.constants.PayConstants.PAYMENT_EXPIRE_KEY_PREFIX;

/**
 * 支付过期Redis处理器
 * 继承AbstractRedisExpirationHandler，处理支付订单过期逻辑
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PaymentExpirationHandler extends AbstractRedisExpirationHandler {


    private final OrderOperationService orderOperationService;
    @Override
    public String getKeyPrefix() {
        return PAYMENT_EXPIRE_KEY_PREFIX;
    }

    @Override
    protected void doHandle(String expiredKey, String extractedData) {
        String orderNo = extractedData;
        log.info("执行支付过期处理，订单号：{}", orderNo);
        orderOperationService.cancelOrder(orderNo,"支付超时自动取消");
    }

    @Override
    protected void beforeHandle(String expiredKey, String extractedData) {
        super.beforeHandle(expiredKey, extractedData);

        // 支付过期处理的前置检查
        String orderNo = extractedData;
        if (!isValidOrderNo(orderNo)) {
            throw new IllegalArgumentException("无效的订单号格式：" + orderNo);
        }
    }

    @Override
    protected void afterHandle(String expiredKey, String extractedData) {
        String orderNo = extractedData;
        log.info("支付过期处理后置操作，订单号：{}", orderNo);


    }

    @Override
    protected void handleException(String expiredKey, String extractedData, Exception exception) {
        String orderNo = extractedData;
        log.error("支付过期处理异常，订单号：{}，异常：{}", orderNo, exception.getMessage());

        // TODO: 可以在这里添加：
        // 1. 异常告警
        // 2. 失败重试机制
        // 3. 死信队列处理
    }

    @Override
    public int getPriority() {
        return 10; // 高优先级
    }

    /**
     * 验证订单号格式
     *
     * @param orderNo 订单号
     * @return 是否有效
     */
    private boolean isValidOrderNo(String orderNo) {
        // 简单的订单号格式验证
        return orderNo != null && orderNo.length() > 0 && !orderNo.contains(" ");
    }
}
