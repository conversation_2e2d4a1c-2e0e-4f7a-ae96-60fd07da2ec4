package com.demon.giraffe.modules.payment.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.payment.service.CallbackService;
import com.demon.giraffe.modules.payment.service.WechatPayService;
import org.springframework.beans.factory.annotation.Qualifier;
import com.wechat.pay.java.service.payments.model.Transaction;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;

import java.io.BufferedReader;
import java.io.IOException;
import java.math.BigDecimal;

/**
 * 支付控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/payment")
@RequiredArgsConstructor
@Tag(name = "支付接口", description = "支付相关操作，包括创建支付、查询支付状态、退款等")
public class PaymentController {

    // 常量定义

    private static final String CALLBACK_FAIL_RESPONSE = "FAIL";
    private static final String PAYMENT_PATH_PREFIX = "/payment";
    private static final String WECHAT_CALLBACK_PATH = "/callback/wechat";
    private static final String WECHAT_REFUND_CALLBACK_PATH = "/callback/wechat/refund";
    private static final String QUERY_PATH = "/query";
    private static final String CLOSE_PATH = "/close";
    private static final String REFUND_PATH = "/refund";
    private static final String REFUND_QUERY_PATH = "/refund/query";

    // 错误消息常量
    private static final String QUERY_PAYMENT_ERROR = "查询支付状态失败：";
    private static final String CLOSE_PAYMENT_ERROR = "关闭支付失败：";
    private static final String REFUND_ERROR = "申请退款失败：";
    private static final String QUERY_REFUND_ERROR = "查询退款状态失败：";
    private static final String WECHAT_CALLBACK_ERROR = "处理微信支付回调失败";
    private static final String WECHAT_REFUND_CALLBACK_ERROR = "处理微信退款回调失败";

    private final WechatPayService wechatPayService;

    @Qualifier("wechatCallbackService")
    private final CallbackService callbackService;

    @GetMapping(QUERY_PATH + "/{orderNo}")
    @Operation(summary = "查询支付状态", description = "根据订单号查询支付状态")
    @SaCheckRole(value = {"ROOT", "CUSTOMER"}, mode = SaMode.OR)
    public ResultBean<Transaction> queryPayment(
            @Parameter(description = "订单号", required = true)
            @PathVariable String orderNo) {
        try {
            Transaction transaction = wechatPayService.queryOrder(orderNo);
            return ResultBean.success(transaction);
        } catch (Exception e) {
            log.error(QUERY_PAYMENT_ERROR, e);
            return ResultBean.fail(QUERY_PAYMENT_ERROR + e.getMessage());
        }
    }

    @PostMapping(CLOSE_PATH + "/{orderNo}")
    @Operation(summary = "关闭支付", description = "关闭未支付的订单")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<Boolean> closePayment(
            @Parameter(description = "订单号", required = true)
            @PathVariable String orderNo) {
        try {
            wechatPayService.closeOrder(orderNo);
            return ResultBean.success(true);
        } catch (Exception e) {
            log.error(CLOSE_PAYMENT_ERROR, e);
            return ResultBean.fail(CLOSE_PAYMENT_ERROR + e.getMessage());
        }
    }

    @PostMapping(REFUND_PATH)
    @Operation(summary = "申请退款", description = "申请订单退款")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<String> refund(
            @Parameter(description = "订单号", required = true)
            @RequestParam String orderNo,
            @Parameter(description = "退款单号", required = true)
            @RequestParam String refundNo,
            @Parameter(description = "退款金额", required = true)
            @RequestParam BigDecimal refundAmount,
            @Parameter(description = "订单总金额", required = true)
            @RequestParam BigDecimal totalAmount,
            @Parameter(description = "退款原因")
            @RequestParam(required = false) String reason) {
        try {
            String refundId = wechatPayService.refund(orderNo, refundNo, refundAmount, totalAmount, reason);
            return ResultBean.success(refundId);
        } catch (Exception e) {
            log.error(REFUND_ERROR, e);
            return ResultBean.fail(REFUND_ERROR + e.getMessage());
        }
    }

    @GetMapping(REFUND_QUERY_PATH + "/{refundNo}")
    @Operation(summary = "查询退款状态", description = "根据退款单号查询退款状态")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<String> queryRefund(
            @Parameter(description = "退款单号", required = true)
            @PathVariable String refundNo) {
        try {
            String status = wechatPayService.queryRefund(refundNo);
            return ResultBean.success(status);
        } catch (Exception e) {
            log.error(QUERY_REFUND_ERROR, e);
            return ResultBean.fail(QUERY_REFUND_ERROR + e.getMessage());
        }
    }

    @PostMapping(WECHAT_CALLBACK_PATH)
    @Operation(summary = "微信支付回调", description = "微信支付成功后的回调通知")
    public String wechatPayCallback(HttpServletRequest request) {
        try {
            String body = getRequestBody(request);
            return callbackService.handleWechatPayCallback(body, request);
        } catch (Exception e) {
            log.error(WECHAT_CALLBACK_ERROR, e);
            return CALLBACK_FAIL_RESPONSE;
        }
    }

    @PostMapping(WECHAT_REFUND_CALLBACK_PATH)
    @Operation(summary = "微信退款回调", description = "微信退款成功后的回调通知")
    public String wechatRefundCallback(HttpServletRequest request) {
        try {
            String body = getRequestBody(request);
            return callbackService.handleWechatRefundCallback(body, request);
        } catch (Exception e) {
            log.error(WECHAT_REFUND_CALLBACK_ERROR, e);
            return CALLBACK_FAIL_RESPONSE;
        }
    }

    /**
     * 获取请求体内容
     */
    private String getRequestBody(HttpServletRequest request) throws IOException {
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        }
        return sb.toString();
    }
}