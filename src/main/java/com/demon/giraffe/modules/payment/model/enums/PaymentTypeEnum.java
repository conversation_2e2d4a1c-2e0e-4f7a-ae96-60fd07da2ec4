package com.demon.giraffe.modules.payment.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * 支付方式枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Schema(description = "支付方式枚举")
public enum PaymentTypeEnum implements IEnum<Integer> {
    @Schema(description = "微信支付")
    WECHAT(1, "微信支付");

    @EnumValue
    private final Integer code;
    private final String desc;

    PaymentTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据代码获取枚举
     */
    public static PaymentTypeEnum of(Integer code) {
        for (PaymentTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的支付方式代码: " + code);
    }

    @Override
    public Integer getValue() {
        return code;
    }
}
