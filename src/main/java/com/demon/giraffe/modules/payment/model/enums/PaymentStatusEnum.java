package com.demon.giraffe.modules.payment.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * 支付状态枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Schema(description = "支付状态枚举")
public enum PaymentStatusEnum implements IEnum<String> {
    @Schema(description = "支付中")
    NOTPAY("NOTPAY", "支付中"),
    
    @Schema(description = "支付成功")
    SUCCESS("SUCCESS", "支付成功"),
    
    @Schema(description = "转入退款")
    REFUND("REFUND", "转入退款"),
    
    @Schema(description = "已关闭")
    CLOSED("CLOSED", "已关闭"),
    
    @Schema(description = "已撤销")
    REVOKED("REVOKED", "已撤销"),
    
    @Schema(description = "用户支付中")
    USERPAYING("USERPAYING", "用户支付中"),
    
    @Schema(description = "支付失败")
    PAYERROR("PAYERROR", "支付失败");

    @EnumValue
    private final String code;
    private final String desc;

    PaymentStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据代码获取枚举
     */
    public static PaymentStatusEnum of(String code) {
        for (PaymentStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("无效的支付状态代码: " + code);
    }

    @Override
    public String getValue() {
        return code;
    }
}
