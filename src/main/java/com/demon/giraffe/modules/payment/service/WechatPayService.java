package com.demon.giraffe.modules.payment.service;

import com.demon.giraffe.modules.payment.model.dto.response.PaymentResponse;
import com.demon.giraffe.modules.payment.model.entity.CreateOrderRequest;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.payments.nativepay.model.PrepayResponse;
import jakarta.servlet.http.HttpServletRequest;

import java.math.BigDecimal;

/**
 * 微信支付业务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface WechatPayService {

    /**
     * 创建支付订单
     *
     * @param request 创建订单请求
     * @return 创建订单响应
     */
    PaymentResponse createPayment(CreateOrderRequest request);


    /**
     * 创建 JSAPI 支付订单
     *
     * @param outTradeNo  商户订单号
     * @param description 商品描述
     * @param totalAmount 金额（单位：分）
     * @param openid      用户openid
     * @return 支付响应
     */
    PaymentResponse createJsapiOrder(String outTradeNo, String description, int totalAmount, String openid);


    /**
     * JSAPI再次下单（支持检查prepayId是否过期）
     *
     * @param prepayId 预支付ID
     * @param originalOutTradeNo 原订单号
     * @return PaymentResponse
     */
    PaymentResponse recreateJsapiOrder(String prepayId, String originalOutTradeNo);

    /**
     * 重新支付订单
     * 根据订单号查找支付记录，重新创建支付参数
     *
     * @param orderNo 订单号
     * @return PaymentResponse
     */
    PaymentResponse repayOrder(String orderNo);

    /**
     * 查询支付状态
     *
     * @param outTradeNo 商户订单号
     * @return 交易详情
     */
    Transaction queryOrder(String outTradeNo);

    /**
     * 关闭订单
     *
     * @param outTradeNo 商户订单号
     */
    void closeOrder(String outTradeNo);

    /**
     * 申请退款
     *
     * @param outTradeNo   商户订单号
     * @param outRefundNo  商户退款单号
     * @param refundAmount 退款金额
     * @param totalAmount  订单总金额
     * @param reason       退款原因
     * @return 退款单号
     */
    String refund(String outTradeNo, String outRefundNo, BigDecimal refundAmount, BigDecimal totalAmount, String reason);

    /**
     * 查询退款状态
     *
     * @param outRefundNo 商户退款单号
     * @return 退款详情
     */
    String queryRefund(String outRefundNo);

    /**
     * 处理支付回调通知
     *
     * @param notifyBody 回调通知的 JSON 内容
     * @param request    HTTP请求
     * @return 微信服务器需要的响应
     */
    String handleNotify(String notifyBody, HttpServletRequest request);

    /**
     * 根据订单编号查询剩余支付时间
     *
     * @param orderNo 订单号
     * @return 剩余支付时间（秒），如果订单不存在或已过期返回0
     */
    long getRemainingPaymentTime(String orderNo);

    /**
     * 检测订单支付过期状态
     *
     * @param orderNo 订单号
     * @return 是否已过期
     */
    boolean checkPaymentExpired(String orderNo);

    /**
     * 处理退款回调通知
     *
     * @param notifyBody 回调通知的 JSON 内容
     * @param request    HTTP请求
     * @return 微信服务器需要的响应
     */
    String handleRefundNotify(String notifyBody, HttpServletRequest request);
}
