package com.demon.giraffe.modules.payment.config;

import com.demon.giraffe.common.config.WxConfig;
import com.wechat.pay.java.core.*;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.service.payments.h5.H5Service;
import com.wechat.pay.java.service.payments.jsapi.JsapiService;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import com.wechat.pay.java.service.refund.RefundService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 微信支付配置类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class WechatPayConfiguration {

    private final WxConfig wxConfig;

    /**
     * 微信支付配置
     */
    @Bean
    public Config wechatPayConfig() {
        try {
            log.info("初始化微信支付配置，商户号：{}", wxConfig.getMerchantId());

            return new RSAPublicKeyConfig.Builder()
                    .merchantId(wxConfig.getMerchantId())
                    .merchantSerialNumber(wxConfig.getMerchantSerialNumber())
                    .privateKeyFromPath(wxConfig.getPrivateKeyPath())
                    .publicKeyFromPath(wxConfig.getPublicKeyPath()) // 微信平台公钥
                    .publicKeyId(wxConfig.getPublicKeyId())
                    .apiV3Key(wxConfig.getApiV3Key())
                    .build();

        } catch (Exception e) {
            log.error("微信支付配置初始化失败", e);
            throw new RuntimeException("微信支付配置初始化失败", e);
        }
    }

    /**
     * Native支付服务
     */
    @Bean
    public NativePayService nativePayService(Config config) {
        return new NativePayService.Builder().config(config).build();
    }

    /**
     * JSAPI支付服务
     */
    @Bean
    public JsapiService jsapiService(Config config) {
        return new JsapiService.Builder().config(config).build();
    }

    /**
     * H5支付服务
     */
    @Bean
    public H5Service h5Service(Config config) {
        return new H5Service.Builder().config(config).build();
    }

    /**
     * 退款服务
     */
    @Bean
    public RefundService refundService(Config config) {
        return new RefundService.Builder().config(config).build();
    }

    /**
     * 通知解析器
     */
    @Bean
    public NotificationParser notificationParser(Config config) {
        if (!(config instanceof RSAPublicKeyConfig)) {
            throw new IllegalArgumentException("配置必须为 RSAAutoCertificateConfig 类型");
        }
        return new NotificationParser((RSAPublicKeyConfig) config);
    }

}
