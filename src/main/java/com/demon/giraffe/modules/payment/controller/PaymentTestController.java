package com.demon.giraffe.modules.payment.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 支付测试控制器
 * 用于测试微信支付功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/payment/test")
@RequiredArgsConstructor
@Tag(name = "支付测试接口", description = "用于测试微信支付功能的接口")
public class PaymentTestController {
//
//    private final WechatPayService wechatPayService;
//
//    @PostMapping("/create-native-test")
//    @Operation(
//        summary = "创建Native测试支付",
//        description = "创建Native扫码支付测试订单，金额0.01-0.05元",
//        responses = {
//            @ApiResponse(
//                responseCode = "200",
//                description = "创建成功",
//                content = @Content(
//                    mediaType = "application/json",
//                    schema = @Schema(implementation = PaymentResponse.class)
//                )
//            )
//        }
//    )
//    public ResultBean<PaymentResponse> createNativeTestPayment() {
//        try {
//            // 创建Native支付测试请求
//            PaymentRequest request = createTestPaymentRequest();
//            request.setOpenid(null); // Native支付不需要openid
//            request.setDescription("Native扫码支付测试-" + request.getDescription());
//
//            log.info("=== 创建Native测试支付订单 ===");
//            log.info("订单号: {}", request.getOrderNo());
//            log.info("金额: {}元", request.getAmount());
//            log.info("描述: {}", request.getDescription());
//
//            // 调用支付服务
//            PaymentResponse response = wechatPayService.createPayment(request);
//
//            log.info("=== Native支付订单创建成功 ===");
//            log.info("支付单号: {}", response.getPaymentNo());
//            log.info("二维码链接: {}", response.getCodeUrl());
//            log.info("支付状态: {}", response.getPaymentStatus());
//
//            return ResultBean.success(response);
//
//        } catch (Exception e) {
//            log.error("❌ 创建Native测试支付订单失败", e);
//            return ResultBean.fail("创建Native测试支付订单失败: " + e.getMessage());
//        }
//    }
//
//    @PostMapping("/create-jsapi-test")
//    @Operation(
//        summary = "创建JSAPI测试支付",
//        description = "创建JSAPI测试支付订单，需要提供openid",
//        responses = {
//            @ApiResponse(
//                responseCode = "200",
//                description = "创建成功",
//                content = @Content(
//                    mediaType = "application/json",
//                    schema = @Schema(implementation = PaymentResponse.class)
//                )
//            )
//        }
//    )
//    public ResultBean<PaymentResponse> createJsapiTestPayment(
//            @RequestParam(defaultValue = "test_openid_123456") String openid) {
//        try {
//            // 创建JSAPI测试支付请求
//            PaymentRequest request = createTestPaymentRequest();
//            request.setOpenid(openid);
//            request.setDescription("JSAPI测试支付-" + request.getDescription());
//
//            log.info("创建JSAPI测试支付订单: {}", request);
//
//            // 调用支付服务
//            PaymentResponse response = wechatPayService.createPayment(request);
//
//            log.info("JSAPI测试支付订单创建成功: {}", response);
//
//            return ResultBean.success(response);
//
//        } catch (Exception e) {
//            log.error("创建JSAPI测试支付订单失败", e);
//            return ResultBean.fail("创建JSAPI测试支付订单失败: " + e.getMessage());
//        }
//    }
//
//    @GetMapping("/generate-test-data")
//    @Operation(
//        summary = "生成测试数据",
//        description = "生成测试支付请求的示例数据",
//        responses = {
//            @ApiResponse(
//                responseCode = "200",
//                description = "生成成功",
//                content = @Content(
//                    mediaType = "application/json",
//                    schema = @Schema(implementation = PaymentRequest.class)
//                )
//            )
//        }
//    )
//    public ResultBean<PaymentRequest> generateTestData() {
//        PaymentRequest request = createTestPaymentRequest();
//        return ResultBean.success(request);
//    }
//
//    /**
//     * 创建测试支付请求
//     */
//    private PaymentRequest createTestPaymentRequest() {
//        PaymentRequest request = new PaymentRequest();
//
//        // 生成唯一的订单号
//        String orderNo = generateTestOrderNo();
//        request.setOrderNo(orderNo);
//
//        // 生成随机的极小金额（0.01-0.10元）
//        BigDecimal amount = generateRandomSmallAmount();
//        request.setAmount(amount);
//
//        // 设置支付方式为微信支付
//        request.setPaymentType(PaymentTypeEnum.WECHAT);
//
//        // 设置商品描述
//        request.setDescription("测试商品-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmmss")));
//
//        // 设置测试openid（如果需要JSAPI支付）
//        request.setOpenid("test_openid_" + System.currentTimeMillis());
//
//        // 设置客户端IP
//        request.setClientIp("127.0.0.1");
//
//        // 设置附加数据
//        request.setAttach("test_attach_data");
//
//        // 设置订单失效时间（30分钟）
//        request.setExpireMinutes(30);
//
//        return request;
//    }
//
//    /**
//     * 生成测试订单号
//     */
//    private String generateTestOrderNo() {
//        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
//        int random = new Random().nextInt(9999);
//        return "TEST" + timestamp + String.format("%04d", random);
//    }
//
//    /**
//     * 生成随机的极小金额（0.01-0.05元）
//     */
//    private BigDecimal generateRandomSmallAmount() {
//        Random random = new Random();
//        // 生成1-5分的随机金额，确保测试金额很小
//        int cents = random.nextInt(5) + 1;
//        return new BigDecimal("0.0" + cents);
//    }
//
//    @GetMapping("/quick-test")
//    @Operation(
//        summary = "快速测试支付",
//        description = "一键测试微信支付功能，无需参数，自动生成测试数据",
//        responses = {
//            @ApiResponse(
//                responseCode = "200",
//                description = "测试完成",
//                content = @Content(
//                    mediaType = "application/json",
//                    schema = @Schema(implementation = PaymentResponse.class)
//                )
//            )
//        }
//    )
//    public ResultBean<PaymentResponse> quickTest() {
//        try {
//            log.info("🚀 开始快速测试微信支付功能...");
//
//            // 创建测试数据
//            PaymentRequest request = createTestPaymentRequest();
//            request.setOpenid(null); // 使用Native支付，不需要openid
//            request.setDescription("快速测试-洗衣服务");
//
//            log.info("📋 测试订单信息:");
//            log.info("   订单号: {}", request.getOrderNo());
//            log.info("   金额: {}元", request.getAmount());
//            log.info("   描述: {}", request.getDescription());
//            log.info("   支付方式: {}", request.getPaymentType().getDesc());
//
//            // 调用支付接口
//            PaymentResponse response = wechatPayService.createPayment(request);
//
//            log.info("✅ 支付订单创建成功!");
//            log.info("   支付单号: {}", response.getPaymentNo());
//            log.info("   支付状态: {}", response.getPaymentStatus());
//
//            if (response.getCodeUrl() != null) {
//                log.info("   二维码链接: {}", response.getCodeUrl());
//                log.info("💡 请使用微信扫描二维码完成支付测试");
//            }
//
//            return ResultBean.success(response);
//
//        } catch (Exception e) {
//            log.error("❌ 快速测试失败", e);
//            return ResultBean.fail("快速测试失败: " + e.getMessage());
//        }
//    }
//
//    @GetMapping("/test-config")
//    @Operation(
//        summary = "测试配置信息",
//        description = "检查微信支付配置是否正确",
//        responses = {
//            @ApiResponse(
//                responseCode = "200",
//                description = "检查完成",
//                content = @Content(
//                    mediaType = "application/json",
//                    schema = @Schema(implementation = String.class)
//                )
//            )
//        }
//    )
//    public ResultBean<String> testConfig() {
//        try {
//            // 这里可以添加配置检查逻辑
//            StringBuilder configInfo = new StringBuilder();
//            configInfo.append("微信支付配置检查:\n");
//            configInfo.append("- 当前时间: ").append(LocalDateTime.now()).append("\n");
//            configInfo.append("- 测试订单号: ").append(generateTestOrderNo()).append("\n");
//            configInfo.append("- 测试金额: ").append(generateRandomSmallAmount()).append("元\n");
//            configInfo.append("- 配置状态: 正常");
//
//            return ResultBean.success(configInfo.toString());
//
//        } catch (Exception e) {
//            log.error("配置检查失败", e);
//            return ResultBean.fail("配置检查失败: " + e.getMessage());
//        }
//    }
//
//
//
//    @Autowired
//    private WechatPayService service;
//
//    /**
//     * 创建JSAPI支付订单
//
//     * @return PaymentResponse
//     */
//    @PostMapping("/createJsapiOrder")
//    public PaymentResponse createJsapiOrder() {
//        UserPo userPo = SaTokenUtil.getUserPo();
//
//        String openid = userPo.getOpenid();
//        JsapiOrderRequest request = new JsapiOrderRequest();
//        request.setOpenid(openid);
//        request.setDescription("测试商品-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmmss")));
//        request.setOutTradeNo(generateTestOrderNo());
//        request.setTotalAmount(generateRandomSmallAmount().multiply(new BigDecimal(100)).intValue());
//        return service.createJsapiOrder(
//                request.getOutTradeNo(),
//                request.getDescription(),
//                request.getTotalAmount(),
//                request.getOpenid()
//        );
//    }
//
//    /**
//     * 支付请求参数类
//     */
//    public static class JsapiOrderRequest {
//        private String outTradeNo;     // 商户订单号
//        private String description;    // 商品描述
//        private int totalAmount;       // 金额（单位：分）
//        private String openid;         // 用户openid
//
//        // Getter & Setter
//        public String getOutTradeNo() {
//            return outTradeNo;
//        }
//
//        public void setOutTradeNo(String outTradeNo) {
//            this.outTradeNo = outTradeNo;
//        }
//
//        public String getDescription() {
//            return description;
//        }
//
//        public void setDescription(String description) {
//            this.description = description;
//        }
//
//        public int getTotalAmount() {
//            return totalAmount;
//        }
//
//        public void setTotalAmount(int totalAmount) {
//            this.totalAmount = totalAmount;
//        }
//
//        public String getOpenid() {
//            return openid;
//        }
//
//        public void setOpenid(String openid) {
//            this.openid = openid;
//        }
//    }
}
