package com.demon.giraffe.modules.payment.model.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * JSAPI支付参数
 */
@Data
@Schema(description = "JSAPI支付参数")
public class JsapiPayData {
    @Schema(description = "应用ID")
    private String appId;

    @Schema(description = "时间戳")
    private String timeStamp;

    @Schema(description = "随机字符串")
    private String nonceStr;

    @Schema(description = "订单详情扩展字符串")
    private String packageValue;

    @Schema(description = "签名方式")
    private String signType;

    @Schema(description = "签名")
    private String paySign;
}