package com.demon.giraffe.modules.payment.constants;

import com.demon.giraffe.framework.redis.expiration.constants.RedisExpirationConstants;

/**
 * 支付模块常量
 */
public class PayConstants {

    // ================= 支付核心配置 =================
    public static final Integer PAY_EXPIRE_MINUTES = 30;
    public static final String DEFAULT_CURRENCY = "CNY";
    public static final String SIGN_ALGORITHM = "SHA256withRSA";
    public static final String SIGN_TYPE = "RSA";

    // ================= Redis配置 =================
    public static final String REDIS_KEY_PREFIX = "giraffe:payment";
    public static final String PAYMENT_EXPIRE_KEY_PREFIX = RedisExpirationConstants.Payment.ORDER_PAYMENT;

    // ================= 接口路径 =================
    public static final String BASE_PAYMENT_PATH = "/payment";
    public static final String WECHAT_PAY_NOTIFY_PATH = "/api/payment/wechat/notify";
    public static final String WECHAT_REFUND_NOTIFY_PATH = "/api/payment/wechat/refund-notify";
    public static final String QUERY_PATH = "/query";
    public static final String CLOSE_PATH = "/close";
    public static final String REFUND_PATH = "/refund";
    public static final String REFUND_QUERY_PATH = "/refund/query";
    public static final String WECHAT_CALLBACK_PATH = "/callback/wechat";
    public static final String WECHAT_REFUND_CALLBACK_PATH = "/callback/wechat/refund";

    // ================= 响应处理 =================
    public static final String CALLBACK_FAIL_RESPONSE = "FAIL";
    public static final String CALLBACK_SUCCESS_RESPONSE = "SUCCESS";
    public static final String HTTPS_PREFIX = "https://";
    public static final String PATH_SEPARATOR = "/";

    // ================= 错误消息 =================
    public static final String QUERY_PAYMENT_ERROR = "查询支付状态失败：";
    public static final String CLOSE_PAYMENT_ERROR = "关闭支付失败：";
    public static final String REFUND_ERROR = "申请退款失败：";
    public static final String QUERY_REFUND_ERROR = "查询退款状态失败：";
    public static final String WECHAT_CALLBACK_ERROR = "处理微信支付回调失败";
    public static final String WECHAT_REFUND_CALLBACK_ERROR = "处理微信退款回调失败";
}