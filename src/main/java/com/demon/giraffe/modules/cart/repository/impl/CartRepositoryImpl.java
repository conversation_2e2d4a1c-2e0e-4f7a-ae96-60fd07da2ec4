package com.demon.giraffe.modules.cart.repository.impl;

import com.demon.giraffe.framework.redis.service.RedisService;
import com.demon.giraffe.framework.redis.util.RedisUtils;
import com.demon.giraffe.modules.cart.Constants;
import com.demon.giraffe.modules.cart.model.entity.CartEntity;
import com.demon.giraffe.modules.cart.repository.CartRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 购物车Repository实现类
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class CartRepositoryImpl implements CartRepository {

    private final RedisService redisService;

    /**
     * 生成Redis Key
     */
    private String generateRedisKey(Long memberId) {
        return RedisUtils.redisAssembleKey(
            Constants.MEMBER_CART_KEY_PREFIX,
            memberId.toString()
        );
    }

    @Override
    public boolean saveOrUpdate(CartEntity cart) {
        try {
            String key = generateRedisKey(cart.getMemberId());
            boolean result = redisService.set(key, cart);
            if (result) {
                log.debug("保存购物车成功，会员ID：{}", cart.getMemberId());
            }
            return result;
        } catch (Exception e) {
            log.error("保存购物车失败，会员ID：{}", cart.getMemberId(), e);
            return false;
        }
    }

    @Override
    public Optional<CartEntity> getByMemberId(Long memberId) {
        try {
            String key = generateRedisKey(memberId);
            CartEntity cart = redisService.getValue(key, CartEntity.class);
            return Optional.ofNullable(cart);
        } catch (Exception e) {
            log.error("获取购物车失败，会员ID：{}", memberId, e);
            return Optional.empty();
        }
    }

    @Override
    public boolean deleteByMemberId(Long memberId) {
        try {
            String key = generateRedisKey(memberId);
            boolean result = redisService.deleteKey(key);
            if (result) {
                log.debug("删除购物车成功，会员ID：{}", memberId);
            }
            return result;
        } catch (Exception e) {
            log.error("删除购物车失败，会员ID：{}", memberId, e);
            return false;
        }
    }

    @Override
    public boolean existsByMemberId(Long memberId) {
        try {
            String key = generateRedisKey(memberId);
            return redisService.exist(key);
        } catch (Exception e) {
            log.error("检查购物车存在性失败，会员ID：{}", memberId, e);
            return false;
        }
    }
}
