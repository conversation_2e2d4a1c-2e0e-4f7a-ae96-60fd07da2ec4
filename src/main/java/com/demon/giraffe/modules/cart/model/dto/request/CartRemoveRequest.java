package com.demon.giraffe.modules.cart.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 从购物车移除商品请求
 */
@Data
@Schema(description = "从购物车移除商品请求")
public class CartRemoveRequest {

    @NotNull(message = "服务项ID不能为空")
    @Schema(description = "服务项ID", required = true)
    private Long serviceItemId;
}
