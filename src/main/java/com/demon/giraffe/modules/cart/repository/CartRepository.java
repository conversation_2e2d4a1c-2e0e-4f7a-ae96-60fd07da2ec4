package com.demon.giraffe.modules.cart.repository;

import com.demon.giraffe.modules.cart.model.entity.CartEntity;

import java.util.Optional;

/**
 * 购物车Repository接口
 */
public interface CartRepository {

    /**
     * 保存或更新购物车
     *
     * @param cart 购物车实体
     * @return 是否成功
     */
    boolean saveOrUpdate(CartEntity cart);

    /**
     * 根据会员ID获取购物车
     *
     * @param memberId 会员ID
     * @return 购物车实体
     */
    Optional<CartEntity> getByMemberId(Long memberId);

    /**
     * 删除购物车
     *
     * @param memberId 会员ID
     * @return 是否成功
     */
    boolean deleteByMemberId(Long memberId);

    /**
     * 检查购物车是否存在
     *
     * @param memberId 会员ID
     * @return 是否存在
     */
    boolean existsByMemberId(Long memberId);
}
