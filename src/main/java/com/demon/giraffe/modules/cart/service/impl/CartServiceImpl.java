package com.demon.giraffe.modules.cart.service.impl;

import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.modules.cart.Constants;
import com.demon.giraffe.modules.cart.model.dto.request.CartRemoveRequest;
import com.demon.giraffe.modules.cart.model.dto.request.CartUpdateAllRequest;
import com.demon.giraffe.modules.cart.model.dto.response.CartResponse;
import com.demon.giraffe.modules.cart.model.entity.CartEntity;
import com.demon.giraffe.modules.cart.repository.CartRepository;
import com.demon.giraffe.modules.cart.service.CartService;
import com.demon.giraffe.modules.order.model.dto.request.OrderItemDetail;
import com.demon.giraffe.modules.order.model.entity.OrderServiceItemEntity;

import com.demon.giraffe.modules.service.model.entity.CleaningItem;
import com.demon.giraffe.modules.service.model.enums.ProductCategory;
import com.demon.giraffe.modules.service.model.entity.ServiceItemEntity;
import com.demon.giraffe.modules.service.service.ServiceItemService;
import com.demon.giraffe.modules.user.service.impl.MemberRoleServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 购物车服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CartServiceImpl implements CartService {

    private final MemberRoleServiceImpl memberRoleService;
    private final CartRepository cartRepository;
    private final ServiceItemService serviceItemService;


    @Override
    public CartResponse updateCart(CartUpdateAllRequest request) {
        Long memberId = memberRoleService.getCurrent().getId();
        try {
            // 验证商品数量限制
            if (request.getOrderItems().size() > Constants.MAX_CART_ITEMS) {
                throw new BusinessException("购物车商品种类不能超过" + Constants.MAX_CART_ITEMS);
            }

            // 验证每个商品的数量限制
            for (OrderItemDetail item : request.getOrderItems()) {
                if (item.getQuantity() <= 0) {
                    throw new BusinessException("商品数量必须大于0");
                }
                if (item.getQuantity() > Constants.MAX_ITEM_QUANTITY) {
                    throw new BusinessException("单个商品数量不能超过" + Constants.MAX_ITEM_QUANTITY);
                }
            }

            // 创建或获取购物车实体
            CartEntity cart = CartEntity.builder()
                    .memberId(memberId)
                    .orderItems(new ArrayList<>(request.getOrderItems()))
                    .build();

            // 更新购物车统计信息
            updateCartStatistics(cart);

            // 保存购物车（完全覆盖）
            cartRepository.saveOrUpdate(cart);

            return convertToResponse(cart);
        } catch (Exception e) {
            log.error("全量更新购物车失败，会员ID：{}", memberId, e);
            throw new BusinessException("更新购物车失败");
        }
    }

    @Override
    public CartResponse removeFromCart(CartRemoveRequest request) {
        Long memberId = memberRoleService.getCurrent().getId();
        try {


            Optional<CartEntity> cartOpt = cartRepository.getByMemberId(memberId);
            if (cartOpt.isEmpty()) {
                throw new BusinessException("购物车不存在");
            }

            CartEntity cart = cartOpt.get();

            // 移除指定商品
            boolean removed = cart.getOrderItems().removeIf(
                    item -> item.getServiceItemId().equals(request.getServiceItemId())
            );

            if (!removed) {
                throw new BusinessException("商品不存在于购物车中");
            }

            // 更新购物车统计信息
            updateCartStatistics(cart);

            // 保存购物车
            cartRepository.saveOrUpdate(cart);

            return convertToResponse(cart);
        } catch (Exception e) {
            log.error("从购物车移除商品失败，会员ID：{}，服务项ID：{}",
                    memberId, request.getServiceItemId(), e);
            throw new BusinessException("从购物车移除商品失败");
        }
    }

    @Override
    public CartResponse getCart() {
        Long memberId = memberRoleService.getCurrent().getId();

        Optional<CartEntity> cartOpt = cartRepository.getByMemberId(memberId);
        if (cartOpt.isEmpty()) {
            // 返回空购物车
            return createEmptyCartResponse(memberId);
        }

        CartEntity cart = cartOpt.get();
        return convertToResponse(cart);
    }

    @Override
    public boolean clearCart() {
        Long memberId = memberRoleService.getCurrent().getId();
        try {
            return cartRepository.deleteByMemberId(memberId);
        } catch (Exception e) {
            log.error("清空购物车失败，会员ID：{}", memberId, e);
            return false;
        }
    }



    /**
     * 更新购物车统计信息
     */
    private void updateCartStatistics(CartEntity cart) {
        cart.setTotalQuantity(cart.getOrderItems().stream()
                .mapToInt(OrderItemDetail::getQuantity)
                .sum());
    }

    /**
     * 转换为响应对象
     */
    private CartResponse convertToResponse(CartEntity cart) {
        // 转换服务项列表
        List<OrderServiceItemEntity> orderServiceItems = convertToOrderServiceItems(cart.getOrderItems());

        return CartResponse.builder()
                .memberId(cart.getMemberId())
                .orderServiceItems(orderServiceItems)
                .totalQuantity(cart.getTotalQuantity())
                .build();
    }

    /**
     * 将OrderItemDetail列表转换为OrderServiceItemVO列表
     */
    private List<OrderServiceItemEntity> convertToOrderServiceItems(List<OrderItemDetail> orderItems) {
        if (CollectionUtils.isEmpty(orderItems)) {
            return new ArrayList<>();
        }

        // 获取所有服务项ID
        Set<Long> serviceItemIds = orderItems.stream()
                .map(OrderItemDetail::getServiceItemId)
                .collect(Collectors.toSet());

        // 批量获取服务项详情
        List<ServiceItemEntity> serviceItems = serviceItemService.batchGetServiceItemDetails(serviceItemIds);

        // 构建服务项映射
        Map<Long, ServiceItemEntity> serviceMap = serviceItems.stream()
                .collect(Collectors.toMap(ServiceItemEntity::getServiceItemId, e -> e));

        // 转换为OrderServiceItemVO
        return orderItems.stream()
                .map(item -> convertSingleServiceItem(item, serviceMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 转换单个服务项
     */
    private OrderServiceItemEntity convertSingleServiceItem(OrderItemDetail item, Map<Long, ServiceItemEntity> serviceMap) {
        ServiceItemEntity service = serviceMap.get(item.getServiceItemId());
        if (service == null) {
            log.warn("服务项不存在，ID：{}", item.getServiceItemId());
            return null;
        }

        // 购物车中默认不设置加急和精洗，使用基础价格
        Boolean isUrgent = false;
        Boolean isPremium = false;

        return null;
    }

    /**
     * 计算单个服务项总价
     */
    private BigDecimal calculateItemTotalPrice(ServiceItemEntity service, OrderItemDetail item, Boolean isUrgent, Boolean isPremium) {
        BigDecimal unitPrice = service.getMarketReferencePrice(); // 使用服务定价作为基础价格

        if (isPremium && service.getPremiumWashExtraFee() != null) {
            unitPrice = unitPrice.add(service.getPremiumWashExtraFee());
        }
        if (isUrgent && service.getExpeditedServiceFee() != null) {
            unitPrice = unitPrice.add(service.getExpeditedServiceFee());
        }

        return unitPrice.multiply(BigDecimal.valueOf(item.getQuantity()));
    }

    /**
     * 转换服务详情
     */
    private List<ServiceItemEntity> convertServiceDetail(ServiceItemEntity service) {
        if (service.getDetailConfig() == null ||
                CollectionUtils.isEmpty(service.getDetailConfig().getCleaningItems())) {
            return Collections.emptyList();
        }

        // 统计各品类数量
        Map<ProductCategory, Integer> categoryCountMap = service.getDetailConfig()
                .getCleaningItems()
                .stream()
                .collect(Collectors.toMap(
                        CleaningItem::getProductCategory,
                        e -> 1,
                        Integer::sum
                ));

        // 转换为服务详情VO
        return Collections.emptyList();
    }

    /**
     * 创建空购物车响应
     */
    private CartResponse createEmptyCartResponse(Long memberId) {
        return CartResponse.builder()
                .memberId(memberId)
                .orderServiceItems(new ArrayList<>())
                .totalQuantity(0)
                .build();
    }
}
