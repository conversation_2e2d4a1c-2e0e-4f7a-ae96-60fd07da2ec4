package com.demon.giraffe.modules.cart.model.dto.request;

import com.demon.giraffe.modules.order.model.dto.request.OrderItemDetail;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 购物车全量更新请求
 *
 * 重要说明：这是全量覆盖操作！
 * - 传入的商品列表会完全替换现有购物车内容
 * - 如果某个商品不在新列表中，该商品会被删除
 * - 如果传入空列表，购物车会被清空
 */
@Data
@Schema(description = "购物车全量更新请求 - 完全覆盖现有购物车内容")
public class CartUpdateAllRequest {

    @NotNull(message = "购物车商品列表不能为null")
    @Size(max = 50, message = "购物车商品数量不能超过50种")
    @Valid
    @Schema(
        description = "完整的购物车商品列表 - 会完全覆盖现有购物车内容。" +
                     "传入空列表将清空购物车。" +
                     "不在此列表中的现有商品将被删除。",
        required = true
    )
    private List<OrderItemDetail> orderItems;
}
