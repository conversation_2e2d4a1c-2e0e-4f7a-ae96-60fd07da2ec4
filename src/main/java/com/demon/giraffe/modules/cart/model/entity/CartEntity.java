package com.demon.giraffe.modules.cart.model.entity;

import com.demon.giraffe.modules.order.model.dto.request.OrderItemDetail;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 购物车实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "购物车实体")
public class CartEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "会员ID（唯一标识）")
    private Long memberId;

    @Schema(description = "购物车商品列表")
    private List<OrderItemDetail> orderItems;

    @Schema(description = "商品总数量")
    private Integer totalQuantity;
}
