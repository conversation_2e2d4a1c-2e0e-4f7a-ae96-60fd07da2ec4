package com.demon.giraffe.modules.cart.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.cart.model.dto.request.CartRemoveRequest;
import com.demon.giraffe.modules.cart.model.dto.request.CartUpdateAllRequest;
import com.demon.giraffe.modules.cart.model.dto.response.CartResponse;
import com.demon.giraffe.modules.cart.service.CartService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 购物车控制器
 */
@RestController
@RequestMapping("/cart")
@RequiredArgsConstructor
@Tag(name = "购物车管理", description = "会员购物车相关接口")
@SaCheckRole(value = {"CUSTOMER"}, mode = SaMode.OR)
public class CartController {

    private final CartService cartService;

    @PostMapping("/update")
    @Operation(
        summary = "全量更新购物车",
        description = "⚠️ 重要：这是全量覆盖操作，不是增量更新！\n\n" +
                     "功能说明：\n" +
                     "• 完全替换当前会员的购物车内容\n" +
                     "• 前端需要传入完整的购物车商品列表\n" +
                     "• 后端会直接覆盖整个购物车，删除所有现有商品\n\n" +
                     "使用场景：\n" +
                     "• 传入空列表 → 清空购物车\n" +
                     "• 传入完整列表 → 替换为新的购物车内容\n" +
                     "• 某商品不在新列表中 → 该商品会被删除\n\n" +
                     "限制：\n" +
                     "• 最多50种商品\n" +
                     "• 单个商品数量不超过99个"
    )
    public ResultBean<CartResponse> updateCart(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "购物车全量更新参数 - 包含完整的购物车商品列表，会完全覆盖现有内容",
                    required = true,
                    content = @Content(schema = @Schema(implementation = CartUpdateAllRequest.class))
            )
            @Valid @RequestBody CartUpdateAllRequest request) {
        CartResponse response = cartService.updateCart(request);
        return ResultBean.success(response);
    }

    @PostMapping("/remove")
    @Operation(
        summary = "从购物车移除单个商品",
        description = "从当前会员购物车中移除指定的单个商品。\n\n" +
                     "注意：如果需要批量操作或复杂的购物车修改，建议使用全量更新接口 PUT /cart/update"
    )
    public ResultBean<CartResponse> removeFromCart(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "移除购物车商品参数 - 指定要删除的商品ID",
                    required = true,
                    content = @Content(schema = @Schema(implementation = CartRemoveRequest.class))
            )
            @Valid @RequestBody CartRemoveRequest request) {
        CartResponse response = cartService.removeFromCart(request);
        return ResultBean.success(response);
    }

    @GetMapping
    @Operation(summary = "获取购物车详情", description = "获取当前会员的购物车详情")
    public ResultBean<CartResponse> getCart() {
        CartResponse response = cartService.getCart();
        return ResultBean.success(response);
    }

    @PostMapping("/clear")
    @Operation(
        summary = "清空购物车",
        description = "清空当前会员的购物车，删除所有商品。\n\n" +
                     "等效操作：调用 PUT /cart/update 并传入空的商品列表"
    )
    public ResultBean<Boolean> clearCart() {
        boolean result = cartService.clearCart();
        return ResultBean.success(result);
    }


}
