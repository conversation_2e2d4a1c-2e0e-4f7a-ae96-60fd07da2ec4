package com.demon.giraffe.modules.cart.model.dto.response;

import com.demon.giraffe.modules.order.model.entity.OrderServiceItemEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 购物车响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "购物车响应")
public class CartResponse {

    @Schema(description = "会员ID")
    private Long memberId;

    @Schema(description = "购物车服务项列表")
    private List<OrderServiceItemEntity> orderServiceItems;

    @Schema(description = "商品总数量")
    private Integer totalQuantity;
}
