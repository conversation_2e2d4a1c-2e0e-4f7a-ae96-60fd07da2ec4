package com.demon.giraffe.modules.cart;

/**
 * 购物车模块常量类
 */
public class Constants {
    
    /**
     * Redis Key 前缀
     */
    public static final String REDIS_KEY_PREFIX = "giraffe:cart";
    
    /**
     * 会员购物车 Redis Key 前缀
     */
    public static final String MEMBER_CART_KEY_PREFIX = REDIS_KEY_PREFIX + ":member";
    
    /**
     * 购物车最大商品数量
     */
    public static final int MAX_CART_ITEMS = 50;
    
    /**
     * 单个商品最大数量
     */
    public static final int MAX_ITEM_QUANTITY = 99;
    
    private Constants() {
        // 私有构造函数，防止实例化
    }
}
