package com.demon.giraffe.modules.cart.service;

import com.demon.giraffe.modules.cart.model.dto.request.CartRemoveRequest;
import com.demon.giraffe.modules.cart.model.dto.request.CartUpdateAllRequest;
import com.demon.giraffe.modules.cart.model.dto.response.CartResponse;

/**
 * 购物车服务接口
 */
public interface CartService {

    /**
     * 全量更新购物车
     * 每次调用都会完全覆盖当前购物车内容，不是增量更新
     * 前端需要传入完整的购物车商品列表，后端会直接替换整个购物车
     *
     * @param request 包含完整购物车商品列表的请求
     * @return 购物车响应
     */
    CartResponse updateCart(CartUpdateAllRequest request);

    /**
     * 从购物车移除商品
     *
     * @param request 移除请求
     * @return 购物车响应
     */
    CartResponse removeFromCart(CartRemoveRequest request);

    /**
     * 获取当前会员的购物车详情
     *
     * @return 购物车响应
     */
    CartResponse getCart();

    /**
     * 清空当前会员的购物车
     *
     * @return 是否成功
     */
    boolean clearCart();


}
