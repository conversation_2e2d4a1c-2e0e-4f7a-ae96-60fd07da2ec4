package com.demon.giraffe.modules.settlement.model.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 投资人收益统计响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "InvestorRevenueStatisticsResponse", description = "投资人收益统计响应")
public class InvestorRevenueStatisticsResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "投资人ID")
    private Long investorId;

    @Schema(description = "投资人姓名")
    private String investorName;

    @Schema(description = "投资人编号")
    private String investorNo;

    @Schema(description = "总收益金额")
    private BigDecimal totalRevenue;

    @Schema(description = "已确认收益金额")
    private BigDecimal confirmedRevenue;

    @Schema(description = "已结算收益金额")
    private BigDecimal settledRevenue;

    @Schema(description = "待确认收益金额")
    private BigDecimal pendingRevenue;

    @Schema(description = "总订单数")
    private Integer totalOrders;

    @Schema(description = "已完成订单数")
    private Integer completedOrders;

    @Schema(description = "通过柜子获得的收益")
    private BigDecimal cabinetRevenue;

    @Schema(description = "通过距离获得的收益")
    private BigDecimal distanceRevenue;

    @Schema(description = "平均订单收益")
    private BigDecimal avgOrderRevenue;

    @Schema(description = "本月收益")
    private BigDecimal monthlyRevenue;

    @Schema(description = "本年收益")
    private BigDecimal yearlyRevenue;
}
