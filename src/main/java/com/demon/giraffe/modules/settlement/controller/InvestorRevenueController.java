package com.demon.giraffe.modules.settlement.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.settlement.model.dto.response.InvestorRevenueStatisticsResponse;
import com.demon.giraffe.modules.settlement.service.OrderRevenueService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 投资人收益统计控制器
 */
@Slf4j
@RestController
@RequestMapping("/settlement/investor/revenue")
@Tag(name = "投资人收益统计接口", description = "投资人收益统计和管理")
@RequiredArgsConstructor
public class InvestorRevenueController {

    private final OrderRevenueService orderRevenueService;

    @GetMapping("/my/statistics")
    @Operation(summary = "获取我的收益统计", description = "获取当前投资人的收益统计信息")
    @SaCheckRole(value = {"INVESTOR"}, mode = SaMode.OR)
    public ResultBean<InvestorRevenueStatisticsResponse> getMyRevenueStatistics() {
        try {
            InvestorRevenueStatisticsResponse statistics = orderRevenueService.getMyRevenueStatistics();
            return ResultBean.success(statistics);
        } catch (Exception e) {
            log.error("获取我的收益统计失败", e);
            return ResultBean.fail("获取收益统计失败：" + e.getMessage());
        }
    }

    @GetMapping("/statistics/{investorId}")
    @Operation(summary = "获取指定投资人收益统计", description = "管理员查看指定投资人的收益统计信息")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<InvestorRevenueStatisticsResponse> getInvestorRevenueStatistics(
            @Parameter(description = "投资人ID", required = true)
            @PathVariable Long investorId) {
        try {
            InvestorRevenueStatisticsResponse statistics = orderRevenueService.getInvestorRevenueStatistics(investorId);
            return ResultBean.success(statistics);
        } catch (Exception e) {
            log.error("获取投资人收益统计失败，投资人ID：{}", investorId, e);
            return ResultBean.fail("获取收益统计失败：" + e.getMessage());
        }
    }

    @PostMapping("/confirm/{orderId}")
    @Operation(summary = "确认订单收益", description = "确认指定订单的收益记录")
    @SaCheckRole(value = {"ROOT", "INVESTOR"}, mode = SaMode.OR)
    public ResultBean<Boolean> confirmRevenue(
            @Parameter(description = "订单ID", required = true)
            @PathVariable Long orderId) {
        try {
            boolean result = orderRevenueService.confirmRevenue(orderId);
            return ResultBean.success("收益确认成功", result);
        } catch (Exception e) {
            log.error("确认订单收益失败，订单ID：{}", orderId, e);
            return ResultBean.fail("确认收益失败：" + e.getMessage());
        }
    }

    @PostMapping("/settle/{orderId}")
    @Operation(summary = "结算订单收益", description = "结算指定订单的收益记录")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<Boolean> settleRevenue(
            @Parameter(description = "订单ID", required = true)
            @PathVariable Long orderId) {
        try {
            boolean result = orderRevenueService.settleRevenue(orderId);
            return ResultBean.success("收益结算成功", result);
        } catch (Exception e) {
            log.error("结算订单收益失败，订单ID：{}", orderId, e);
            return ResultBean.fail("结算收益失败：" + e.getMessage());
        }
    }

    @PostMapping("/create/{orderId}")
    @Operation(summary = "创建订单收益记录", description = "为完成的订单创建收益记录（通常由系统自动调用）")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<Boolean> createRevenueRecord(
            @Parameter(description = "订单ID", required = true)
            @PathVariable Long orderId) {
        try {
            boolean result = orderRevenueService.createRevenueRecord(orderId);
            return ResultBean.success("收益记录创建成功", result);
        } catch (Exception e) {
            log.error("创建订单收益记录失败，订单ID：{}", orderId, e);
            return ResultBean.fail("创建收益记录失败：" + e.getMessage());
        }
    }

    @PostMapping("/rollback/{orderId}")
    @Operation(summary = "回退订单收益记录", description = "撤销订单时回退收益记录")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<Boolean> rollbackRevenueRecord(
            @Parameter(description = "订单ID", required = true)
            @PathVariable Long orderId) {
        try {
            boolean result = orderRevenueService.rollbackRevenueRecord(orderId);
            return ResultBean.success("收益记录回退成功", result);
        } catch (Exception e) {
            log.error("回退订单收益记录失败，订单ID：{}", orderId, e);
            return ResultBean.fail("回退收益记录失败：" + e.getMessage());
        }
    }
}
