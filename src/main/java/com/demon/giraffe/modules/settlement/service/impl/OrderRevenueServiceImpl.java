package com.demon.giraffe.modules.settlement.service.impl;

import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.framework.satoken.util.SaTokenUtil;
import com.demon.giraffe.modules.order.model.po.OrderMainPo;
import com.demon.giraffe.modules.order.repository.OrderMainRepository;
import com.demon.giraffe.modules.settlement.model.dto.response.InvestorRevenueStatisticsResponse;
import com.demon.giraffe.modules.settlement.model.enums.RevenueStatusEnum;
import com.demon.giraffe.modules.settlement.model.po.OrderRevenuePo;
import com.demon.giraffe.modules.settlement.repository.impl.OrderRevenueRepositoryImpl;
import com.demon.giraffe.modules.settlement.service.OrderRevenueService;
import com.demon.giraffe.modules.user.model.dto.response.RegionInvestorResponse;
import com.demon.giraffe.modules.user.model.po.UserPo;
import com.demon.giraffe.modules.user.service.RegionInvestorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.Objects;

/**
 * 订单收益管理服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderRevenueServiceImpl implements OrderRevenueService {

    private final OrderRevenueRepositoryImpl orderRevenueRepository;
    private final OrderMainRepository orderMainRepository;
    private final RegionInvestorService regionInvestorService;

    // 默认收益比例（可以后续配置化）
    private static final BigDecimal DEFAULT_REVENUE_RATE = new BigDecimal("0.10"); // 10%

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createRevenueRecord(Long orderId) {
        try {
            // 1. 检查是否已存在收益记录
            OrderRevenuePo existingRevenue = orderRevenueRepository.getByOrderId(orderId);
            if (existingRevenue != null) {
                log.warn("订单收益记录已存在，订单ID：{}", orderId);
                return true;
            }

            // 2. 获取订单信息
            OrderMainPo order = orderMainRepository.getById(orderId);
            if (order == null) {
                throw new BusinessException("订单不存在，订单ID：" + orderId);
            }

            // 3. 确定受益人
            Long beneficiaryId = determineBeneficiary(orderId);
            if (beneficiaryId == null) {
                log.warn("无法确定订单受益人，订单ID：{}", orderId);
                return false;
            }

            // 4. 计算收益金额
            BigDecimal revenueAmount = order.getPayAmount().multiply(DEFAULT_REVENUE_RATE)
                    .setScale(2, RoundingMode.HALF_UP);

            // 5. 创建收益记录
            OrderRevenuePo revenue = OrderRevenuePo.builder()
                    .orderId(orderId)
                    .orderNo(order.getOrderNo())
                    .totalAmount(order.getPayAmount())
                    .beneficiaryId(beneficiaryId)
                    .beneficiaryType(1) // TODO: 根据实际逻辑设置类型
                    .revenueAmount(revenueAmount)
                    .revenueRate(DEFAULT_REVENUE_RATE)
                    .status(RevenueStatusEnum.CONFIRMED)
                    .remark("订单完成自动生成")
                    .build();

            orderRevenueRepository.save(revenue);
            log.info("创建订单收益记录成功，订单ID：{}，受益人：{}，收益金额：{}", 
                    orderId, beneficiaryId, revenueAmount);
            return true;
        } catch (Exception e) {
            log.error("创建订单收益记录失败，订单ID：{}", orderId, e);
            throw new BusinessException("创建收益记录失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rollbackRevenueRecord(Long orderId) {
        try {
            // 更新收益状态为已撤销
            boolean updated = orderRevenueRepository.updateStatusByOrderId(orderId, RevenueStatusEnum.CANCELLED);
            if (updated) {
                log.info("订单收益记录回退成功，订单ID：{}", orderId);
            }
            return updated;
        } catch (Exception e) {
            log.error("回退订单收益记录失败，订单ID：{}", orderId, e);
            throw new BusinessException("回退收益记录失败：" + e.getMessage());
        }
    }

    @Override
    public boolean confirmRevenue(Long orderId) {
        return orderRevenueRepository.updateStatusByOrderId(orderId, RevenueStatusEnum.CONFIRMED);
    }

    @Override
    public boolean settleRevenue(Long orderId) {
        return orderRevenueRepository.updateStatusByOrderId(orderId, RevenueStatusEnum.SETTLED);
    }

    @Override
    public InvestorRevenueStatisticsResponse getInvestorRevenueStatistics(Long investorId) {
        // 获取投资人信息
        RegionInvestorResponse investor = regionInvestorService.getById(investorId);
        if (investor == null) {
            throw new BusinessException("投资人不存在");
        }

        // 计算各种收益统计
        BigDecimal totalRevenue = orderRevenueRepository.calculateTotalRevenue(investorId, null);
        BigDecimal confirmedRevenue = orderRevenueRepository.calculateTotalRevenue(investorId, RevenueStatusEnum.CONFIRMED);
        BigDecimal settledRevenue = orderRevenueRepository.calculateTotalRevenue(investorId, RevenueStatusEnum.SETTLED);
        BigDecimal pendingRevenue = orderRevenueRepository.calculateTotalRevenue(investorId, RevenueStatusEnum.PENDING);

        Integer totalOrders = orderRevenueRepository.countOrdersByInvestor(investorId, null);
        Integer completedOrders = orderRevenueRepository.countOrdersByInvestor(investorId, RevenueStatusEnum.CONFIRMED);

        // 计算柜子收益和距离收益
        BigDecimal cabinetRevenue = orderRevenueRepository.calculateRevenueByType(investorId, 1, null);
        BigDecimal distanceRevenue = orderRevenueRepository.calculateRevenueByType(investorId, 2, null);

        // 计算平均订单收益
        BigDecimal avgOrderRevenue = totalOrders > 0 ? 
                totalRevenue.divide(new BigDecimal(totalOrders), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO;

        // 计算本月收益
        LocalDateTime monthStart = YearMonth.now().atDay(1).atStartOfDay();
        LocalDateTime monthEnd = YearMonth.now().atEndOfMonth().atTime(23, 59, 59);
        BigDecimal monthlyRevenue = orderRevenueRepository.calculateRevenueByTimeRange(
                investorId, monthStart, monthEnd, null);

        // 计算本年收益
        LocalDateTime yearStart = LocalDateTime.of(LocalDateTime.now().getYear(), 1, 1, 0, 0, 0);
        LocalDateTime yearEnd = LocalDateTime.of(LocalDateTime.now().getYear(), 12, 31, 23, 59, 59);
        BigDecimal yearlyRevenue = orderRevenueRepository.calculateRevenueByTimeRange(
                investorId, yearStart, yearEnd, null);

        return InvestorRevenueStatisticsResponse.builder()
                .investorId(investorId)
                .investorName(investor.getContactPerson())
                .investorNo(investor.getInvestorNo())
                .totalRevenue(totalRevenue)
                .confirmedRevenue(confirmedRevenue)
                .settledRevenue(settledRevenue)
                .pendingRevenue(pendingRevenue)
                .totalOrders(totalOrders)
                .completedOrders(completedOrders)
                .cabinetRevenue(cabinetRevenue)
                .distanceRevenue(distanceRevenue)
                .avgOrderRevenue(avgOrderRevenue)
                .monthlyRevenue(monthlyRevenue)
                .yearlyRevenue(yearlyRevenue)
                .build();
    }

    @Override
    public InvestorRevenueStatisticsResponse getMyRevenueStatistics() {
        // 获取当前用户信息
        UserPo userPo = SaTokenUtil.getUserPo();
        RegionInvestorResponse investor = regionInvestorService.getRoleByUserId(userPo.getId());
        
        if (investor == null) {
            throw new BusinessException("当前用户不是投资人");
        }

        return getInvestorRevenueStatistics(investor.getId());
    }

    @Override
    public Long determineBeneficiary(Long orderId) {
        // TODO: 实现受益人确定逻辑
        // 1. 获取订单信息
        // 2. 检查是否使用了特定柜子，如果是，返回柜子所有者
        // 3. 如果没有使用柜子，根据下单地点找到最近的投资人
        // 4. 返回投资人ID
        
        // 临时实现：返回第一个投资人（实际应该根据业务逻辑实现）
        log.warn("determineBeneficiary 方法需要实现具体的受益人确定逻辑，订单ID：{}", orderId);
        return 1L; // 临时返回
    }
}
