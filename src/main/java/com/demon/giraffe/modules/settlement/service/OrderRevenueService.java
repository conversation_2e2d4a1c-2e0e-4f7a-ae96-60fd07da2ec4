package com.demon.giraffe.modules.settlement.service;

import com.demon.giraffe.modules.settlement.model.dto.response.InvestorRevenueStatisticsResponse;
import com.demon.giraffe.modules.settlement.model.po.OrderRevenuePo;

/**
 * 订单收益管理服务接口
 */
public interface OrderRevenueService {

    /**
     * 订单完成后创建收益记录
     * @param orderId 订单ID
     * @return 是否创建成功
     */
    boolean createRevenueRecord(Long orderId);

    /**
     * 订单撤销后回退收益记录
     * @param orderId 订单ID
     * @return 是否回退成功
     */
    boolean rollbackRevenueRecord(Long orderId);

    /**
     * 确认收益记录
     * @param orderId 订单ID
     * @return 是否确认成功
     */
    boolean confirmRevenue(Long orderId);

    /**
     * 结算收益记录
     * @param orderId 订单ID
     * @return 是否结算成功
     */
    boolean settleRevenue(Long orderId);

    /**
     * 获取投资人收益统计
     * @param investorId 投资人ID
     * @return 收益统计信息
     */
    InvestorRevenueStatisticsResponse getInvestorRevenueStatistics(Long investorId);

    /**
     * 获取当前用户（投资人）的收益统计
     * @return 收益统计信息
     */
    InvestorRevenueStatisticsResponse getMyRevenueStatistics();

    /**
     * 根据订单信息确定受益人
     * @param orderId 订单ID
     * @return 受益人投资人ID
     */
    Long determineBeneficiary(Long orderId);
}
