package com.demon.giraffe.modules.settlement.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 取送员结算实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("courier_settlement")
@Schema(description = "取送员结算表")
public class CourierSettlementPo extends BasePo {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "取送员ID")
    @NotNull
    private Long staffId;

    @Schema(description = "结算单号（自动生成）")
    @NotBlank
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String settlementNo;

    @Schema(description = "结算周期开始")
    @NotNull
    private LocalDate periodStart;

    @Schema(description = "结算周期结束")
    @NotNull
    private LocalDate periodEnd;

    @Schema(description = "工作天数")
    @NotNull
    private Integer workDays;

    @Schema(description = "完成任务数")
    @NotNull
    private Integer completedTasks;

    @Schema(description = "完成订单数")
    @NotNull
    private Integer completedOrders;

    @Schema(description = "基础工资")
    @NotNull
    private BigDecimal baseSalary;

    @Schema(description = "任务提成")
    @NotNull
    private BigDecimal taskCommission;

    @Schema(description = "绩效奖金")
    @NotNull
    private BigDecimal performanceBonus;

    @Schema(description = "扣除金额")
    @NotNull
    private BigDecimal deduction;

    @Schema(description = "最终金额")
    @NotNull
    private BigDecimal finalAmount;

    @Schema(description = "状态 (0-待确认 / 1-已确认 / 2-已支付)")
    @NotNull
    private Integer status;
}
