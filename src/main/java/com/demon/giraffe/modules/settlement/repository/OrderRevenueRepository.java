package com.demon.giraffe.modules.settlement.repository;

import com.demon.giraffe.modules.settlement.model.po.OrderRevenuePo;
import com.demon.giraffe.modules.settlement.model.enums.RevenueStatusEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单收益记录Repository接口
 */
public interface OrderRevenueRepository {



    /**
     * 根据ID查询
     * @param id 主键ID
     * @return 订单收益记录
     */
    OrderRevenuePo getById(Long id);

    /**
     * 根据订单ID查询
     * @param orderId 订单ID
     * @return 订单收益记录
     */
    OrderRevenuePo getByOrderId(Long orderId);

    /**
     * 更新收益状态
     * @param id 主键ID
     * @param status 新状态
     * @return 是否更新成功
     */
    boolean updateStatus(Long id, RevenueStatusEnum status);

    /**
     * 根据订单ID更新状态
     * @param orderId 订单ID
     * @param status 新状态
     * @return 是否更新成功
     */
    boolean updateStatusByOrderId(Long orderId, RevenueStatusEnum status);

    /**
     * 计算投资人总收益
     * @param investorId 投资人ID
     * @param status 收益状态（可选）
     * @return 总收益金额
     */
    BigDecimal calculateTotalRevenue(Long investorId, RevenueStatusEnum status);

    /**
     * 统计投资人订单数量
     * @param investorId 投资人ID
     * @param status 收益状态（可选）
     * @return 订单数量
     */
    Integer countOrdersByInvestor(Long investorId, RevenueStatusEnum status);

    /**
     * 计算投资人时间范围内的收益
     * @param investorId 投资人ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param status 收益状态（可选）
     * @return 收益金额
     */
    BigDecimal calculateRevenueByTimeRange(Long investorId, LocalDateTime startTime, 
                                          LocalDateTime endTime, RevenueStatusEnum status);

    /**
     * 根据受益人类型计算收益
     * @param investorId 投资人ID
     * @param beneficiaryType 受益人类型（1-柜子所有者 2-距离最近的投资人）
     * @param status 收益状态（可选）
     * @return 收益金额
     */
    BigDecimal calculateRevenueByType(Long investorId, Integer beneficiaryType, RevenueStatusEnum status);

    /**
     * 查询投资人的收益记录列表
     * @param investorId 投资人ID
     * @param status 收益状态（可选）
     * @return 收益记录列表
     */
    List<OrderRevenuePo> listByInvestor(Long investorId, RevenueStatusEnum status);

    /**
     * 删除订单收益记录（用于订单撤销）
     * @param orderId 订单ID
     * @return 是否删除成功
     */
    boolean deleteByOrderId(Long orderId);
}
