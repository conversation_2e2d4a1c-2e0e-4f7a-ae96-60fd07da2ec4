package com.demon.giraffe.modules.settlement.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

/**
 * 收益状态枚举
 */
@Getter
public enum RevenueStatusEnum implements IEnum<Integer> {

    /**
     * 待确认
     */
    PENDING(0, "待确认"),

    /**
     * 已确认
     */
    CONFIRMED(1, "已确认"),

    /**
     * 已结算
     */
    SETTLED(2, "已结算"),

    /**
     * 已撤销
     */
    CANCELLED(3, "已撤销");

    @EnumValue
    private final Integer code;
    private final String description;

    RevenueStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getValue() {
        return this.code;
    }


    public String getLabel() {
        return this.description;
    }
}
