package com.demon.giraffe.modules.settlement.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.modules.settlement.model.enums.RevenueStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单收益记录表
 * <p>记录每个订单完成后对应的受益人信息</p>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("order_revenue")
@Schema(description = "订单收益记录实体")
public class OrderRevenuePo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "订单ID不能为空")
    @TableField("order_id")
    private Long orderId;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "订单号不能为空")
    @TableField("order_no")
    private String orderNo;

    @Schema(description = "总金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "总金额不能为空")
    @DecimalMin(value = "0.00", inclusive = true, message = "总金额不能小于0")
    @Digits(integer = 10, fraction = 2, message = "总金额格式错误")
    @TableField("total_amount")
    private BigDecimal totalAmount;

    @Schema(description = "受益人（投资人ID）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "受益人不能为空")
    @TableField("beneficiary_id")
    private Long beneficiaryId;

    @Schema(description = "受益人类型：1-柜子所有者 2-距离最近的投资人")
    @TableField("beneficiary_type")
    private Integer beneficiaryType;

    @Schema(description = "收益金额")
    @DecimalMin(value = "0.00", inclusive = true, message = "收益金额不能小于0")
    @Digits(integer = 10, fraction = 2, message = "收益金额格式错误")
    @TableField("revenue_amount")
    private BigDecimal revenueAmount;

    @Schema(description = "收益比例（百分比）")
    @DecimalMin(value = "0.00", inclusive = true, message = "收益比例不能小于0")
    @Digits(integer = 3, fraction = 2, message = "收益比例格式错误")
    @TableField("revenue_rate")
    private BigDecimal revenueRate;

    @Schema(description = "最终状态", implementation = RevenueStatusEnum.class)
    @TableField("status")
    private RevenueStatusEnum status;

    @Schema(description = "使用的柜子ID")
    @TableField("cabinet_id")
    private Long cabinetId;

    @Schema(description = "下单地址区域编码")
    @TableField("order_region")
    private String orderRegion;

    @Schema(description = "备注")
    @TableField("remark")
    private String remark;
}
