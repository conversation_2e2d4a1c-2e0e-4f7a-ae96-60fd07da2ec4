package com.demon.giraffe.modules.settlement.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.demon.giraffe.modules.settlement.mapper.OrderRevenueMapper;
import com.demon.giraffe.modules.settlement.model.po.OrderRevenuePo;
import com.demon.giraffe.modules.settlement.model.enums.RevenueStatusEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 订单收益记录Repository实现
 */
@Repository
@RequiredArgsConstructor
public class OrderRevenueRepositoryImpl {

    private final OrderRevenueMapper orderRevenueMapper;

    /**
     * 保存订单收益记录
     * @param orderRevenue 订单收益记录
     * @return 保存的记录
     */
    public OrderRevenuePo save(OrderRevenuePo orderRevenue) {
        return saveOrUpdate(orderRevenue);
    }

    /**
     * 保存或更新订单收益记录
     * @param orderRevenue 订单收益记录
     * @return 保存后的记录
     */
    public OrderRevenuePo saveOrUpdate(OrderRevenuePo orderRevenue) {
        if (orderRevenue == null) {
            return null;
        }

        if (orderRevenue.getId() == null) {
            // 新增
            orderRevenueMapper.insert(orderRevenue);
        } else {
            // 更新
            orderRevenueMapper.updateById(orderRevenue);
        }
        return orderRevenue;
    }

    /**
     * 根据ID查询
     * @param id 主键ID
     * @return 订单收益记录
     */
    public OrderRevenuePo getById(Long id) {
        if (id == null) {
            return null;
        }
        return orderRevenueMapper.selectById(id);
    }

    /**
     * 根据订单ID查询
     * @param orderId 订单ID
     * @return 订单收益记录
     */
    public OrderRevenuePo getByOrderId(Long orderId) {
        if (orderId == null) {
            return null;
        }

        LambdaQueryWrapper<OrderRevenuePo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderRevenuePo::getOrderId, orderId);
        return orderRevenueMapper.selectOne(wrapper);
    }

    /**
     * 更新收益状态
     * @param id 主键ID
     * @param status 新状态
     * @return 是否更新成功
     */
    public boolean updateStatus(Long id, RevenueStatusEnum status) {
        if (id == null || status == null) {
            return false;
        }

        LambdaUpdateWrapper<OrderRevenuePo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(OrderRevenuePo::getId, id)
               .set(OrderRevenuePo::getStatus, status);
        return orderRevenueMapper.update(null, wrapper) > 0;
    }

    /**
     * 根据订单ID更新状态
     * @param orderId 订单ID
     * @param status 新状态
     * @return 是否更新成功
     */
    public boolean updateStatusByOrderId(Long orderId, RevenueStatusEnum status) {
        if (orderId == null || status == null) {
            return false;
        }

        LambdaUpdateWrapper<OrderRevenuePo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(OrderRevenuePo::getOrderId, orderId)
               .set(OrderRevenuePo::getStatus, status);
        return orderRevenueMapper.update(null, wrapper) > 0;
    }

    /**
     * 计算投资人总收益
     * @param investorId 投资人ID
     * @param status 收益状态（可选）
     * @return 总收益金额
     */
    public BigDecimal calculateTotalRevenue(Long investorId, RevenueStatusEnum status) {
        if (investorId == null) {
            return BigDecimal.ZERO;
        }

        LambdaQueryWrapper<OrderRevenuePo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderRevenuePo::getBeneficiaryId, investorId);

        if (status != null) {
            wrapper.eq(OrderRevenuePo::getStatus, status);
        }

        List<OrderRevenuePo> revenues = orderRevenueMapper.selectList(wrapper);
        if (revenues == null || revenues.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return revenues.stream()
                .map(OrderRevenuePo::getRevenueAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 统计投资人订单数量
     * @param investorId 投资人ID
     * @param status 收益状态（可选）
     * @return 订单数量
     */
    public Integer countOrdersByInvestor(Long investorId, RevenueStatusEnum status) {
        if (investorId == null) {
            return 0;
        }

        LambdaQueryWrapper<OrderRevenuePo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderRevenuePo::getBeneficiaryId, investorId);

        if (status != null) {
            wrapper.eq(OrderRevenuePo::getStatus, status);
        }

        return Math.toIntExact(orderRevenueMapper.selectCount(wrapper));
    }

    /**
     * 计算投资人时间范围内的收益
     * @param investorId 投资人ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param status 收益状态（可选）
     * @return 收益金额
     */
    public BigDecimal calculateRevenueByTimeRange(Long investorId, LocalDateTime startTime, 
                                                 LocalDateTime endTime, RevenueStatusEnum status) {
        if (investorId == null) {
            return BigDecimal.ZERO;
        }

        LambdaQueryWrapper<OrderRevenuePo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderRevenuePo::getBeneficiaryId, investorId);

        if (startTime != null) {
            wrapper.ge(OrderRevenuePo::getCreateTime, startTime);
        }
        if (endTime != null) {
            wrapper.le(OrderRevenuePo::getCreateTime, endTime);
        }
        if (status != null) {
            wrapper.eq(OrderRevenuePo::getStatus, status);
        }

        List<OrderRevenuePo> revenues = orderRevenueMapper.selectList(wrapper);
        if (revenues == null || revenues.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return revenues.stream()
                .map(OrderRevenuePo::getRevenueAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 根据受益人类型计算收益
     * @param investorId 投资人ID
     * @param beneficiaryType 受益人类型（1-柜子所有者 2-距离最近的投资人）
     * @param status 收益状态（可选）
     * @return 收益金额
     */
    public BigDecimal calculateRevenueByType(Long investorId, Integer beneficiaryType, RevenueStatusEnum status) {
        if (investorId == null || beneficiaryType == null) {
            return BigDecimal.ZERO;
        }

        LambdaQueryWrapper<OrderRevenuePo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderRevenuePo::getBeneficiaryId, investorId)
               .eq(OrderRevenuePo::getBeneficiaryType, beneficiaryType);

        if (status != null) {
            wrapper.eq(OrderRevenuePo::getStatus, status);
        }

        List<OrderRevenuePo> revenues = orderRevenueMapper.selectList(wrapper);
        if (revenues == null || revenues.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return revenues.stream()
                .map(OrderRevenuePo::getRevenueAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 查询投资人的收益记录列表
     * @param investorId 投资人ID
     * @param status 收益状态（可选）
     * @return 收益记录列表
     */
    public List<OrderRevenuePo> listByInvestor(Long investorId, RevenueStatusEnum status) {
        if (investorId == null) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<OrderRevenuePo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderRevenuePo::getBeneficiaryId, investorId);

        if (status != null) {
            wrapper.eq(OrderRevenuePo::getStatus, status);
        }

        wrapper.orderByDesc(OrderRevenuePo::getCreateTime);
        return orderRevenueMapper.selectList(wrapper);
    }

    /**
     * 删除订单收益记录（用于订单撤销）
     * @param orderId 订单ID
     * @return 是否删除成功
     */
    public boolean deleteByOrderId(Long orderId) {
        if (orderId == null) {
            return false;
        }

        LambdaQueryWrapper<OrderRevenuePo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderRevenuePo::getOrderId, orderId);
        return orderRevenueMapper.delete(wrapper) > 0;
    }
}
