package com.demon.giraffe.modules.settlement.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 合伙人分润结算表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("partner_settlement")
@Schema(description = "合伙人分润结算实体")
public class PartnerSettlementPo extends BasePo {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "租户ID")
    @NotNull
    private Long tenantId;

    @Schema(description = "结算单号(自动生成)")
    @NotBlank
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private String settlementNo;

    @Schema(description = "结算周期开始")
    @NotNull
    private LocalDate periodStart;

    @Schema(description = "结算周期结束")
    @NotNull
    private LocalDate periodEnd;

    @Schema(description = "订单总数")
    @NotNull
    private Integer totalOrders;

    @Schema(description = "总营业额")
    @NotNull
    private BigDecimal totalAmount;

    @Schema(description = "平台费率")
    @NotNull
    private BigDecimal platformFeeRate;

    @Schema(description = "平台费用")
    @NotNull
    private BigDecimal platformFee;

    @Schema(description = "佣金比例")
    @NotNull
    private BigDecimal commissionRate;

    @Schema(description = "佣金金额")
    @NotNull
    private BigDecimal commissionAmount;

    @Schema(description = "扣除金额")
    @NotNull
    private BigDecimal deductionAmount;

    @Schema(description = "最终结算金额")
    @NotNull
    private BigDecimal finalAmount;

    @Schema(description = "状态(0待确认/1已确认/2已支付/3已驳回)")
    @NotNull
    private Integer status;

    @Schema(description = "确认时间")
    private LocalDateTime confirmTime;

    @Schema(description = "支付时间")
    private LocalDateTime payTime;

    @Schema(description = "备注")
    private String remark;

    // createTime, updateTime, creator, updater, deleted 由 BasePo 提供
}
