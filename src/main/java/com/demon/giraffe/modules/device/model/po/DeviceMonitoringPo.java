package com.demon.giraffe.modules.device.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 设备状态监控表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("device_monitoring")
@Schema(description = "设备状态监控")
public class DeviceMonitoringPo extends BasePo {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "设备ID")
    @NotNull
    private Long deviceId;

    @Schema(description = "设备类型(1智能柜/2打印机)")
    @NotNull
    private Integer deviceType;

    @Schema(description = "指标类型")
    @NotBlank
    private String metricType;

    @Schema(description = "指标值")
    @NotBlank
    private String metricValue;

    @Schema(description = "正常范围")
    private String normalRange;

    @Schema(description = "告警阈值")
    private String alertThreshold;

    @Schema(description = "状态(0正常/1告警/2故障)")
    @NotNull
    private Integer status;

    @Schema(description = "记录时间")
    @NotNull
    private LocalDateTime recordTime;
}
