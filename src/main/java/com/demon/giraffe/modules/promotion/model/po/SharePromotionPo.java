package com.demon.giraffe.modules.promotion.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 分享推广记录表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("share_promotion")
@Schema(description = "分享推广记录表")
public class SharePromotionPo extends BasePo {


    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "分享者ID")
    @NotNull
    private Long sharerId;

    @Schema(description = "分享类型 (1-邀请注册 / 2-分享订单 / 3-分享活动)")
    @NotNull
    private Integer shareType;

    @Schema(description = "自动生成分享码")
    @NotBlank
    private String shareCode;

    @Schema(description = "分享链接")
    private String shareUrl;

    @Schema(description = "被邀请人ID")
    private Long invitedId;

    @Schema(description = "关联订单ID")
    private Long orderId;

    @Schema(description = "关联活动ID")
    private Long activityId;

    @Schema(description = "点击次数")
    @NotNull
    private Integer clickCount;

    @Schema(description = "注册转化数")
    @NotNull
    private Integer registerCount;

    @Schema(description = "下单转化数")
    @NotNull
    private Integer orderCount;

    @Schema(description = "奖励类型 (1-积分 / 2-优惠券 / 3-现金)")
    private Integer rewardType;

    @Schema(description = "奖励值")
    @NotNull
    private BigDecimal rewardValue;

    @Schema(description = "状态 (0-待确认 / 1-已奖励 / 2-已取消)")
    @NotNull
    private Integer status;

    @Schema(description = "确认时间")
    private LocalDateTime confirmTime;
}
