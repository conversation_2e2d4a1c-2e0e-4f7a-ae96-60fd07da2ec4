package com.demon.giraffe.modules.config.service;

import com.demon.giraffe.modules.config.model.dto.request.ActivityConfigRequest;
import com.demon.giraffe.modules.config.model.dto.response.ActivityConfigResponse;
import com.demon.giraffe.modules.config.model.entity.ActivityConfigEntity;

/**
 * 活动配置服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ActivityConfigService {

    /**
     * 创建活动
     *
     * @param request 活动配置请求
     * @return 活动配置响应
     */
    ActivityConfigResponse createActivity(ActivityConfigRequest request);

    /**
     * 更新活动
     *
     * @param activityId 活动ID
     * @param request 活动配置请求
     * @return 活动配置响应
     */
    ActivityConfigResponse updateActivity(String activityId, ActivityConfigRequest request);

    /**
     * 获取当前活动详情
     * 如果有多个活动，返回最新的正在进行中的活动
     *
     * @return 活动配置响应，如果没有活动则返回null
     */
    ActivityConfigResponse getCurrentActivity();

    /**
     * 根据ID获取活动详情
     *
     * @param activityId 活动ID
     * @return 活动配置响应
     */
    ActivityConfigResponse getActivityById(String activityId);

    /**
     * 取消活动
     *
     * @param activityId 活动ID
     * @return 是否取消成功
     */
    boolean cancelActivity(String activityId);

    /**
     * 检查并处理过期活动
     * 定时任务调用，处理已过期的活动
     */
    void handleExpiredActivities();

    /**
     * 启动活动
     * 修改服务项价格，启动定时器
     *
     * @param activityEntity 活动实体
     */
    void startActivity(ActivityConfigEntity activityEntity);

    /**
     * 结束活动
     * 还原服务项价格
     *
     * @param activityEntity 活动实体
     */
    void endActivity(ActivityConfigEntity activityEntity);
}
