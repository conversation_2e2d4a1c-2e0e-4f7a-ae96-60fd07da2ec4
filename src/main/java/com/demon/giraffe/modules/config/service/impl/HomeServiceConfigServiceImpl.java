package com.demon.giraffe.modules.config.service.impl;

import com.demon.giraffe.framework.redis.service.RedisService;
import com.demon.giraffe.modules.config.model.dto.request.ServiceConfigItem;
import com.demon.giraffe.modules.config.model.entity.HomeServiceConfigEntity;
import com.demon.giraffe.modules.config.service.HomeServiceConfigService;
import com.demon.giraffe.modules.service.model.dto.response.CategoryResponse;
import com.demon.giraffe.modules.service.model.po.ServiceCategoryPo;
import com.demon.giraffe.modules.service.service.ServiceCategoryService;
import com.demon.giraffe.modules.service.service.helper.ServiceCategoryConvertHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.demon.giraffe.modules.config.Constants.HOME_SERVICE_CONFIG_KEY;

/**
 * 首页展示服务配置服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HomeServiceConfigServiceImpl implements HomeServiceConfigService {

    private final RedisService redisService;
    private final ServiceCategoryService service;
    private final ServiceCategoryConvertHelper serviceCategoryConvertHelper;



    @Override
    public List<CategoryResponse> saveHomeServiceConfig(List<ServiceConfigItem> request) {
        try {

            // 构建服务配置列表
            List<HomeServiceConfigEntity.ServiceConfig> serviceConfigs = new ArrayList<>();

            for (ServiceConfigItem item : request) {

                HomeServiceConfigEntity.ServiceConfig config = HomeServiceConfigEntity.ServiceConfig.builder()
                        .serviceId(item.getServiceId())
                        .sortOrder(item.getSortOrder() != null ? item.getSortOrder() : 0)
                        .build();

                serviceConfigs.add(config);
            }

            // 创建配置实体
            HomeServiceConfigEntity entity = HomeServiceConfigEntity.builder()
                    .serviceConfigs(serviceConfigs)

                    .build();

            // 保存到Redis
            redisService.set(HOME_SERVICE_CONFIG_KEY, entity);

            log.info("保存首页服务配置成功，服务数量: {}", serviceConfigs.size());

            // 返回配置的服务列表
            return getHomeServiceConfig();

        } catch (Exception e) {
            log.error("保存首页服务配置失败", e);
            throw new RuntimeException("保存首页服务配置失败: " + e.getMessage());
        }
    }

    @Override
    public List<CategoryResponse> getHomeServiceConfig() {
        try {
            // 从Redis获取配置
            HomeServiceConfigEntity entity = getConfigFromRedis();

            if (entity == null || CollectionUtils.isEmpty(entity.getServiceConfigs())) {
                // 无配置时返回前三个可用服务
                return getDefaultServices();
            }

            // 提取所有服务ID进行批量查询
            Set<Long> serviceIds = entity.getServiceConfigs().stream()
                    .map(HomeServiceConfigEntity.ServiceConfig::getServiceId)
                    .collect(Collectors.toSet());

            // 批量获取服务详情
            Map<Long, CategoryResponse> longCategoryResponseMap = batchGetServiceMap(serviceIds);


            // 按展示顺序排序并构建服务列表

            return entity.getServiceConfigs().stream()
                    .sorted(Comparator.comparing(HomeServiceConfigEntity.ServiceConfig::getSortOrder).reversed())
                    .map(config -> longCategoryResponseMap.get(config.getServiceId()))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取首页服务配置失败", e);
            // 出错时返回默认服务
            return getDefaultServices();
        }
    }

    /**
     * 批量获取服务详情，并转为 Map<Long, CategoryResponse>
     *
     * @param serviceIds 服务 ID 集合
     * @return Map，key 为服务 ID，value 为对应的服务响应对象
     */
    private Map<Long, CategoryResponse> batchGetServiceMap(Set<Long> serviceIds) {
        if (CollectionUtils.isEmpty(serviceIds)) {
            return Collections.emptyMap();
        }

        try {
            // 使用现有的批量查询方法获取服务实体
            List<ServiceCategoryPo> serviceCategoryPos = service.listByIds(serviceIds);
            Map<Long, CategoryResponse> result = serviceCategoryPos.stream()
                    .map(serviceCategoryConvertHelper::convertToResponse)
                    .collect(Collectors.toMap(
                            CategoryResponse::getId,
                            cr -> cr,
                            (v1, v2) -> v1,
                            LinkedHashMap::new
                    ));

            log.debug("批量获取服务详情完成，请求数量: {}, 成功数量: {}", serviceIds.size(), result.size());
            return result;
        } catch (Exception e) {
            log.error("批量获取服务详情失败", e);
        }
        return Collections.emptyMap();
    }


    /**
     * 获取默认服务（前三个可用服务）
     */
    private List<CategoryResponse> getDefaultServices() {
        try {
            log.info("返回默认服务配置（前三个可用服务）");
            // 获取无地域限制的服务项，取前3个作为默认服务
            List<CategoryResponse> allServices = service.listAvailable(3);

            // 取前3个服务作为默认配置
            return allServices.stream()
                    .limit(3)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取默认服务失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 从Redis获取配置
     */
    private HomeServiceConfigEntity getConfigFromRedis() {
        try {
            return redisService.getValue(HOME_SERVICE_CONFIG_KEY, HomeServiceConfigEntity.class);
        } catch (Exception e) {
            log.error("从Redis获取配置失败", e);
            return null;
        }
    }

}
