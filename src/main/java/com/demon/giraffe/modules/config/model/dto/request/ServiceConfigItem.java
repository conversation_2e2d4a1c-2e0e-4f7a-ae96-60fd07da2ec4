package com.demon.giraffe.modules.config.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 首页展示服务配置请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "服务配置项")
public class ServiceConfigItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "服务项ID", required = true)
    @NotNull(message = "服务项ID不能为空")
    private Long serviceId;

    @Schema(description = "展示顺序（不传入时默认为0）", example = "0")
    private Integer sortOrder;
}
