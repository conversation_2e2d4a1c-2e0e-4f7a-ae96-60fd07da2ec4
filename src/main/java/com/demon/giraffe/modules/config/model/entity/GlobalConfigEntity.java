package com.demon.giraffe.modules.config.model.entity;

import com.demon.giraffe.modules.config.model.enums.GlobalConfigKeyEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 全局配置实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "全局配置实体")
public class GlobalConfigEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "配置项Key")
    private GlobalConfigKeyEnum configKey;

    @Schema(description = "配置项标签（中文描述）")
    private String configLabel;

    @Schema(description = "图片URL（兼容原有字段）")
    private String imageUrl;

    @Schema(description = "配置值（新增字段，用于文件内容、链接等）")
    private String configValue;

}
