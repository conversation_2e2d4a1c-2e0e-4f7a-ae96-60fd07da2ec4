package com.demon.giraffe.modules.config.model.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 首页展示服务配置实体类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "首页展示服务配置实体")
public class HomeServiceConfigEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "服务配置列表")
    private List<ServiceConfig> serviceConfigs;

    /**
     * 服务配置项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "服务配置项")
    public static class ServiceConfig implements Serializable {

        private static final long serialVersionUID = 1L;

        @Schema(description = "服务项ID")
        private Long serviceId;

        @Schema(description = "展示顺序")
        private Integer sortOrder;
    }
}
