package com.demon.giraffe.modules.config.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 活动配置实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "活动配置实体")
public class ActivityConfigEntity {

    @Schema(description = "活动ID")
    private String activityId;

    @Schema(description = "活动名称")
    private String activityName;

    @Schema(description = "活动描述")
    private String description;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "持续时间（小时）")
    private Integer durationHours;

    @Schema(description = "活动展示图URL")
    private String imageUrl;

    @Schema(description = "活动状态")
    private ActivityStatus status;

    @Schema(description = "影响的服务项列表")
    private List<ActivityServiceItem> serviceItems;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 活动服务项（简化版，只保存ID和活动价格）
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "活动服务项")
    public static class ActivityServiceItem {

        @Schema(description = "服务项ID")
        private Long serviceItemId;

        @Schema(description = "活动价格")
        private BigDecimal activityPrice;
    }

    /**
     * 活动状态枚举
     */
    public enum ActivityStatus {
        DRAFT("草稿"),
        ACTIVE("进行中"),
        EXPIRED("已过期"),
        CANCELLED("已取消");

        private final String desc;

        ActivityStatus(String desc) {
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 检查活动是否正在进行中
     */
    @JsonIgnore
    public boolean isActive() {
        LocalDateTime now = LocalDateTime.now();
        return status == ActivityStatus.ACTIVE && 
               startTime != null && endTime != null &&
               now.isAfter(startTime) && now.isBefore(endTime);
    }

    /**
     * 检查活动是否已过期
     */
    @JsonIgnore
    public boolean isExpired() {
        LocalDateTime now = LocalDateTime.now();
        return endTime != null && now.isAfter(endTime);
    }
}
