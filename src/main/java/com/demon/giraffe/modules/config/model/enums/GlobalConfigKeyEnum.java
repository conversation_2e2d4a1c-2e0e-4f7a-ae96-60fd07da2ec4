package com.demon.giraffe.modules.config.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 全局配置项枚举
 */
@Getter
@AllArgsConstructor
@Schema(description = "全局配置项枚举")
public enum GlobalConfigKeyEnum {

    @Schema(description = "客服热线")
    CUSTOMER_SERVICE_PHONE("CUSTOMER_SERVICE_PHONE", "客服热线", true),

    @Schema(description = "前后对比")
    BEFORE_AFTER_COMPARE("BEFORE_AFTER_COMPARE", "前后对比", true),

    @Schema(description = "洗护中心实拍")
    CARE_CENTER_PHOTO("CARE_CENTER_PHOTO", "洗护中心实拍", true),

    @Schema(description = "客服二维码")
    CUSTOMER_QR_CODE("CUSTOMER_QR_CODE", "客服二维码", true),

    @Schema(description = "隐私权政策测试")
    PRIVACY_POLICY_TEST("PRIVACY_POLICY_TEST", "隐私权政策测试", false),

    @Schema(description = "付费协议测试")
    PAYMENT_AGREEMENT_TEST("PAYMENT_AGREEMENT_TEST", "付费协议测试", false),

    @Schema(description = "用户协议范本测试")
    USER_AGREEMENT_TEST("USER_AGREEMENT_TEST", "用户协议范本测试", false),

    @Schema(description = "客服连接")
    CUSTOMER_SERVICE_LINK("CUSTOMER_SERVICE_LINK", "客服连接", false);

    @EnumValue
    private final String code;   // 配置项的英文 Key（用于数据库/配置中心等）
    private final String label;  // 中文描述（用于前端展示）
    private final boolean updatable; // 是否可以通过API更新

    /**
     * 根据code获取枚举
     */
    public static GlobalConfigKeyEnum of(String code) {
        for (GlobalConfigKeyEnum key : values()) {
            if (key.code.equals(code)) {
                return key;
            }
        }
        throw new IllegalArgumentException("无效的配置项代码: " + code);
    }

    /**
     * 判断是否是有效的配置项
     */
    public static boolean isValid(String code) {
        try {
            of(code);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }


    public String getValue() {
        return code;
    }
}
