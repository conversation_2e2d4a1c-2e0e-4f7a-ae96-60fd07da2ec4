package com.demon.giraffe.modules.config.repository.impl;

import com.demon.giraffe.framework.redis.service.RedisService;
import com.demon.giraffe.framework.redis.util.RedisUtils;
import com.demon.giraffe.modules.config.Constants;
import com.demon.giraffe.modules.config.model.entity.GlobalConfigEntity;
import com.demon.giraffe.modules.config.model.enums.GlobalConfigKeyEnum;
import com.demon.giraffe.modules.config.repository.GlobalConfigRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 全局配置Repository实现类
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class GlobalConfigRepositoryImpl implements GlobalConfigRepository {

    private final RedisService redisService;

    /**
     * 生成Redis Key
     */
    private String generateRedisKey(GlobalConfigKeyEnum configKey) {
        return RedisUtils.redisAssembleKey(
            Constants.GLOBAL_CONFIG_KEY_PREFIX,
            configKey.getCode()
        );
    }
    
    @Override
    public boolean saveOrUpdate(GlobalConfigEntity entity) {
        try {
            String key = generateRedisKey(entity.getConfigKey());
            return redisService.set(key, entity);
        } catch (Exception e) {
            log.error("保存全局配置失败，配置项Key：{}", entity.getConfigKey(), e);
            return false;
        }
    }

    @Override
    public Optional<GlobalConfigEntity> getByConfigKey(GlobalConfigKeyEnum configKey) {
        try {
            String key = generateRedisKey(configKey);
            GlobalConfigEntity entity = redisService.getValue(key, GlobalConfigEntity.class);
            return Optional.ofNullable(entity);
        } catch (Exception e) {
            log.error("获取全局配置失败，配置项Key：{}", configKey, e);
            return Optional.empty();
        }
    }

    @Override
    public List<GlobalConfigEntity> getAllConfigs() {
        List<GlobalConfigEntity> configs = new ArrayList<>();
        for (GlobalConfigKeyEnum key : GlobalConfigKeyEnum.values()) {
            getByConfigKey(key).ifPresent(configs::add);
        }
        return configs;
    }



    @Override
    public boolean deleteByConfigKey(GlobalConfigKeyEnum configKey) {
        try {
            String key = generateRedisKey(configKey);
            return redisService.deleteKey(key);
        } catch (Exception e) {
            log.error("删除全局配置失败，配置项Key：{}", configKey, e);
            return false;
        }
    }

    @Override
    public boolean existsByConfigKey(GlobalConfigKeyEnum configKey) {
        try {
            String key = generateRedisKey(configKey);
            return redisService.exist(key);
        } catch (Exception e) {
            log.error("检查全局配置存在性失败，配置项Key：{}", configKey, e);
            return false;
        }
    }
}
