package com.demon.giraffe.modules.config.model.dto.request;

import com.demon.giraffe.modules.config.model.enums.GlobalConfigKeyEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 全局配置更新请求类
 */
@Data
@Schema(description = "全局配置更新请求")
public class GlobalConfigUpdateRequest {

    @NotNull(message = "配置项Key不能为空")
    @Schema(description = "配置项Key", required = true)
    private GlobalConfigKeyEnum configKey;

    @NotBlank(message = "图片URL不能为空")
    @Schema(description = "图片URL", required = true)
    private String imageUrl;
}
