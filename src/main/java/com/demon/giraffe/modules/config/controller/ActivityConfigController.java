package com.demon.giraffe.modules.config.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.config.model.dto.request.ActivityConfigRequest;
import com.demon.giraffe.modules.config.model.dto.response.ActivityConfigResponse;
import com.demon.giraffe.modules.config.service.ActivityConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 活动配置控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/config/activity")
@RequiredArgsConstructor
@Tag(name = "活动配置管理", description = "活动配置的增删改查接口")
public class ActivityConfigController {

    private final ActivityConfigService activityConfigService;

    @PostMapping
    @Operation(summary = "创建活动", description = "ROOT权限：创建新的活动配置")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<ActivityConfigResponse> createActivity(
            @Valid @RequestBody ActivityConfigRequest request) {
        try {
            log.info("开始创建活动：{}", request.getActivityName());
            
            ActivityConfigResponse response = activityConfigService.createActivity(request);
            
            log.info("活动创建成功，活动ID：{}", response.getActivityId());
            return ResultBean.success("活动创建成功", response);
            
        } catch (Exception e) {
            log.error("创建活动失败：{}", request.getActivityName(), e);
            return ResultBean.fail("创建活动失败：" + e.getMessage());
        }
    }

    @PutMapping("/{activityId}")
    @Operation(summary = "更新活动", description = "ROOT权限：更新指定活动配置")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<ActivityConfigResponse> updateActivity(
            @Parameter(description = "活动ID", required = true)
            @PathVariable String activityId,
            @Valid @RequestBody ActivityConfigRequest request) {
        try {
            log.info("开始更新活动：{}", activityId);
            
            ActivityConfigResponse response = activityConfigService.updateActivity(activityId, request);
            
            log.info("活动更新成功，活动ID：{}", activityId);
            return ResultBean.success("活动更新成功", response);
            
        } catch (Exception e) {
            log.error("更新活动失败，活动ID：{}", activityId, e);
            return ResultBean.fail("更新活动失败：" + e.getMessage());
        }
    }

    @GetMapping("/current")
    @Operation(summary = "获取当前活动", description = "所有人可访问：获取当前正在进行的活动详情")
    public ResultBean<ActivityConfigResponse> getCurrentActivity() {
        try {
            ActivityConfigResponse response = activityConfigService.getCurrentActivity();
            
            if (response == null) {
                return ResultBean.success("当前没有进行中的活动", null);
            }
            
            return ResultBean.success("获取当前活动成功", response);
            
        } catch (Exception e) {
            log.error("获取当前活动失败", e);
            return ResultBean.fail("获取当前活动失败：" + e.getMessage());
        }
    }

    @GetMapping("/{activityId}")
    @Operation(summary = "获取活动详情", description = "所有人可访问：根据ID获取活动详情")
    public ResultBean<ActivityConfigResponse> getActivityById(
            @Parameter(description = "活动ID", required = true)
            @PathVariable String activityId) {
        try {
            ActivityConfigResponse response = activityConfigService.getActivityById(activityId);
            
            return ResultBean.success("获取活动详情成功", response);
            
        } catch (Exception e) {
            log.error("获取活动详情失败，活动ID：{}", activityId, e);
            return ResultBean.fail("获取活动详情失败：" + e.getMessage());
        }
    }

    @DeleteMapping("/{activityId}")
    @Operation(summary = "删除活动", description = "ROOT权限：删除当前活动（系统只支持一个活动）")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<Boolean> cancelActivity(
            @Parameter(description = "活动ID", required = true)
            @PathVariable String activityId) {
        try {
            log.info("开始删除活动：{}", activityId);

            boolean result = activityConfigService.cancelActivity(activityId);

            log.info("活动删除成功，活动ID：{}", activityId);
            return ResultBean.success("活动删除成功", result);

        } catch (Exception e) {
            log.error("删除活动失败，活动ID：{}", activityId, e);
            return ResultBean.fail("删除活动失败：" + e.getMessage());
        }
    }

    @DeleteMapping("/current")
    @Operation(summary = "删除当前活动", description = "ROOT权限：删除当前活动")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<Boolean> deleteCurrentActivity() {
        try {
            log.info("开始删除当前活动");

            ActivityConfigResponse currentActivity = activityConfigService.getCurrentActivity();
            if (currentActivity == null) {
                return ResultBean.success("当前没有活动", true);
            }

            boolean result = activityConfigService.cancelActivity(currentActivity.getActivityId());

            log.info("当前活动删除成功");
            return ResultBean.success("当前活动删除成功", result);

        } catch (Exception e) {
            log.error("删除当前活动失败", e);
            return ResultBean.fail("删除当前活动失败：" + e.getMessage());
        }
    }

    @PostMapping("/handle-expired")
    @Operation(summary = "处理过期活动", description = "ROOT权限：手动触发过期活动处理")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<String> handleExpiredActivities() {
        try {
            log.info("开始手动处理过期活动");
            
            activityConfigService.handleExpiredActivities();
            
            log.info("过期活动处理完成");
            return ResultBean.success("过期活动处理完成");
            
        } catch (Exception e) {
            log.error("处理过期活动失败", e);
            return ResultBean.fail("处理过期活动失败：" + e.getMessage());
        }
    }
}
