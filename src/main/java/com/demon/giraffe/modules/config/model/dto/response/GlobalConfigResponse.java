package com.demon.giraffe.modules.config.model.dto.response;

import com.demon.giraffe.modules.config.model.enums.GlobalConfigKeyEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 全局配置响应类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "全局配置响应")
public class GlobalConfigResponse {

    @Schema(description = "配置项代码")
    private String configCode;

    @Schema(description = "配置项标签（中文描述）")
    private String configLabel;

    @Schema(description = "图片URL（兼容原有字段）")
    private String imageUrl;

    @Schema(description = "配置值（新增字段，用于文件内容、链接等）")
    private String configValue;

}
