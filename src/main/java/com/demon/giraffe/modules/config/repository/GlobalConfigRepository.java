package com.demon.giraffe.modules.config.repository;

import com.demon.giraffe.modules.config.model.entity.GlobalConfigEntity;
import com.demon.giraffe.modules.config.model.enums.GlobalConfigKeyEnum;

import java.util.List;
import java.util.Optional;

/**
 * 全局配置Repository接口
 */
public interface GlobalConfigRepository {

    /**
     * 保存或更新配置
     *
     * @param entity 配置实体
     * @return 是否成功
     */
    boolean saveOrUpdate(GlobalConfigEntity entity);

    /**
     * 根据配置项Key获取配置
     *
     * @param configKey 配置项Key
     * @return 配置实体
     */
    Optional<GlobalConfigEntity> getByConfigKey(GlobalConfigKeyEnum configKey);

    /**
     * 获取所有配置
     *
     * @return 配置列表
     */
    List<GlobalConfigEntity> getAllConfigs();


    /**
     * 删除配置
     *
     * @param configKey 配置项Key
     * @return 是否成功
     */
    boolean deleteByConfigKey(GlobalConfigKeyEnum configKey);

    /**
     * 检查配置是否存在
     *
     * @param configKey 配置项Key
     * @return 是否存在
     */
    boolean existsByConfigKey(GlobalConfigKeyEnum configKey);
}
