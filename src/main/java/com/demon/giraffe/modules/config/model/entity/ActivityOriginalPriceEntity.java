package com.demon.giraffe.modules.config.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 活动原始价格存储实体
 * 用于存储活动开始前的服务项原始价格，避免使用Map导致的序列化问题
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityOriginalPriceEntity {

    /**
     * 活动ID
     */
    private String activityId;

    /**
     * 原始价格列表
     */
    private List<ServiceItemPrice> originalPrices;

    /**
     * 服务项价格信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ServiceItemPrice {
        
        /**
         * 服务项ID
         */
        private Long serviceItemId;
        
        /**
         * 原始价格
         */
        private BigDecimal originalPrice;
    }
}
