package com.demon.giraffe.modules.config.service.impl;

import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.modules.config.Constants;
import com.demon.giraffe.modules.config.model.dto.request.GlobalConfigUpdateRequest;
import com.demon.giraffe.modules.config.model.dto.response.GlobalConfigResponse;
import com.demon.giraffe.modules.config.model.entity.GlobalConfigEntity;
import com.demon.giraffe.modules.config.model.enums.GlobalConfigKeyEnum;
import com.demon.giraffe.modules.config.repository.GlobalConfigRepository;
import com.demon.giraffe.modules.config.service.GlobalConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;

/**
 * 全局配置服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GlobalConfigServiceImpl implements GlobalConfigService {

    private final GlobalConfigRepository globalConfigRepository;

    @Override
    public boolean updateGlobalConfig(GlobalConfigUpdateRequest request) {
        try {
            // 检查配置项是否允许更新
            if (!request.getConfigKey().isUpdatable()) {
                log.warn("配置项 {} 不允许通过API更新", request.getConfigKey());
                throw new BusinessException("该配置项不允许更新");
            }

            // 检查是否存在现有配置
            Optional<GlobalConfigEntity> existingConfig = globalConfigRepository
                    .getByConfigKey(request.getConfigKey());

            GlobalConfigEntity entity;
            if (existingConfig.isPresent()) {
                // 更新现有配置（仅修改图片）
                entity = existingConfig.get();
                entity.setImageUrl(request.getImageUrl());
            } else {
                // 创建新配置
                entity = createDefaultConfigEntity(request.getConfigKey(), request.getImageUrl(), null);
            }

            return globalConfigRepository.saveOrUpdate(entity);
        } catch (Exception e) {
            log.error("更新全局配置失败，配置项Key：{}", request.getConfigKey(), e);
            throw new BusinessException("更新全局配置失败");
        }
    }

    @Override
    public GlobalConfigResponse getGlobalConfig(GlobalConfigKeyEnum configKey) {
        Optional<GlobalConfigEntity> entity = globalConfigRepository.getByConfigKey(configKey);
        return entity.map(this::convertToResponse).orElse(null);
    }

    @Override
    public List<GlobalConfigResponse> getAllGlobalConfigs() {
        List<GlobalConfigEntity> entities = globalConfigRepository.getAllConfigs();
        return entities.stream()
                .map(this::convertToResponse)
                .toList();
    }


    @Override
    public boolean initializeDefaultConfigs() {
        try {
            // 初始化客服热线配置
            if (!globalConfigRepository.existsByConfigKey(GlobalConfigKeyEnum.CUSTOMER_SERVICE_PHONE)) {
                GlobalConfigEntity config = createDefaultConfigEntity(
                        GlobalConfigKeyEnum.CUSTOMER_SERVICE_PHONE,
                        Constants.DEFAULT_CUSTOMER_SERVICE_PHONE_IMAGE);
                globalConfigRepository.saveOrUpdate(config);
            }

            // 初始化前后对比配置
            if (!globalConfigRepository.existsByConfigKey(GlobalConfigKeyEnum.BEFORE_AFTER_COMPARE)) {
                GlobalConfigEntity config = createDefaultConfigEntity(
                        GlobalConfigKeyEnum.BEFORE_AFTER_COMPARE,
                        Constants.DEFAULT_BEFORE_AFTER_COMPARE_IMAGE
                );
                globalConfigRepository.saveOrUpdate(config);
            }

            // 初始化洗护中心实拍配置
            if (!globalConfigRepository.existsByConfigKey(GlobalConfigKeyEnum.CARE_CENTER_PHOTO)) {
                GlobalConfigEntity config = createDefaultConfigEntity(
                        GlobalConfigKeyEnum.CARE_CENTER_PHOTO,
                        Constants.DEFAULT_CARE_CENTER_PHOTO_IMAGE
                );
                globalConfigRepository.saveOrUpdate(config);
            }

            // 初始化客服二维码配置
            if (!globalConfigRepository.existsByConfigKey(GlobalConfigKeyEnum.CUSTOMER_QR_CODE)) {
                GlobalConfigEntity config = createDefaultConfigEntity(
                        GlobalConfigKeyEnum.CUSTOMER_QR_CODE,
                        Constants.DEFAULT_CUSTOMER_QR_CODE_IMAGE);
                globalConfigRepository.saveOrUpdate(config);
            }

            // 初始化隐私权政策测试配置
            if (!globalConfigRepository.existsByConfigKey(GlobalConfigKeyEnum.PRIVACY_POLICY_TEST)) {
                String fileContent = readConfigFileContent("config/privacy_policy_test.txt");
                GlobalConfigEntity config = createDefaultConfigEntity(
                        GlobalConfigKeyEnum.PRIVACY_POLICY_TEST,
                        null, // 不设置imageUrl
                        fileContent);
                globalConfigRepository.saveOrUpdate(config);
            }

            // 初始化付费协议测试配置
            if (!globalConfigRepository.existsByConfigKey(GlobalConfigKeyEnum.PAYMENT_AGREEMENT_TEST)) {
                String fileContent = readConfigFileContent("config/payment_agreement_test.txt");
                GlobalConfigEntity config = createDefaultConfigEntity(
                        GlobalConfigKeyEnum.PAYMENT_AGREEMENT_TEST,
                        null, // 不设置imageUrl
                        fileContent);
                globalConfigRepository.saveOrUpdate(config);
            }

            // 初始化用户协议范本测试配置
            if (!globalConfigRepository.existsByConfigKey(GlobalConfigKeyEnum.USER_AGREEMENT_TEST)) {
                String fileContent = readConfigFileContent("config/user_agreement_test.txt");
                GlobalConfigEntity config = createDefaultConfigEntity(
                        GlobalConfigKeyEnum.USER_AGREEMENT_TEST,
                        null, // 不设置imageUrl
                        fileContent);
                globalConfigRepository.saveOrUpdate(config);
            }

            // 初始化客服连接配置
            if (!globalConfigRepository.existsByConfigKey(GlobalConfigKeyEnum.CUSTOMER_SERVICE_LINK)) {
                GlobalConfigEntity config = createDefaultConfigEntity(
                        GlobalConfigKeyEnum.CUSTOMER_SERVICE_LINK,
                        null, // 不设置imageUrl
                        Constants.DEFAULT_CUSTOMER_SERVICE_LINK);
                globalConfigRepository.saveOrUpdate(config);
            }

            return true;
        } catch (Exception e) {
            log.error("初始化默认配置失败", e);
            return false;
        }
    }

    /**
     * 创建默认配置实体
     */
    private GlobalConfigEntity createDefaultConfigEntity(GlobalConfigKeyEnum configKey, String imageUrl, String configValue) {
        return GlobalConfigEntity.builder()
                .configKey(configKey)
                .configLabel(configKey.getLabel())
                .imageUrl(imageUrl)
                .configValue(configValue)
                .build();
    }

    /**
     * 创建默认配置实体（仅设置图片URL，向后兼容）
     */
    private GlobalConfigEntity createDefaultConfigEntity(GlobalConfigKeyEnum configKey, String imageUrl) {
        return createDefaultConfigEntity(configKey, imageUrl, null);
    }

    /**
     * 读取配置文件内容
     */
    private String readConfigFileContent(String filePath) {
        try {
            ClassPathResource resource = new ClassPathResource(filePath);
            if (!resource.exists()) {
                log.warn("配置文件不存在: {}", filePath);
                return "配置文件不存在";
            }

            try (InputStream inputStream = resource.getInputStream()) {
                return StreamUtils.copyToString(inputStream, StandardCharsets.UTF_8);
            }
        } catch (IOException e) {
            log.error("读取配置文件失败: {}", filePath, e);
            return "读取配置文件失败";
        }
    }

    /**
     * 转换为响应对象
     */
    private GlobalConfigResponse convertToResponse(GlobalConfigEntity entity) {
        return GlobalConfigResponse.builder()
                .configCode(entity.getConfigKey().getCode())
                .configLabel(entity.getConfigLabel())
                .imageUrl(entity.getImageUrl())
                .configValue(entity.getConfigValue())
                .build();
    }


}
