package com.demon.giraffe.modules.config;

/**
 * 配置模块常量类
 */
public class Constants {

    /**
     * Redis Key 前缀
     */
    public static final String REDIS_KEY_PREFIX = "giraffe:config";

    /**
     * 缓存 Redis Key 前缀
     */
    public static final String CACHE_KEY_PREFIX = "giraffe:cache";

    /**
     * 全局配置 Redis Key 前缀
     */
    public static final String GLOBAL_CONFIG_KEY_PREFIX = REDIS_KEY_PREFIX + ":global";
    public static final String HOME_SERVICE_CONFIG_KEY = REDIS_KEY_PREFIX + ":home:service-config";
    /**
     * 配置类型分隔符
     */
    public static final String CONFIG_TYPE_SEPARATOR = ":";

    /**
     * 默认图片占位符
     */
    public static final String DEFAULT_IMAGE_PLACEHOLDER = "default_placeholder.jpg";

    /**
     * 默认配置图片路径
     */
    public static final String DEFAULT_CUSTOMER_SERVICE_PHONE_IMAGE = "/api-storage/inherent/config/service_tel.png";
    public static final String DEFAULT_BEFORE_AFTER_COMPARE_IMAGE = "/api-storage/inherent/config/cleaning_comparison.png";
    public static final String DEFAULT_CARE_CENTER_PHOTO_IMAGE = "/api-storage/inherent/config/factory_on-site_photos.png";
    public static final String DEFAULT_CUSTOMER_QR_CODE_IMAGE = DEFAULT_IMAGE_PLACEHOLDER;

    /**
     * 默认配置文件路径
     */
    public static final String DEFAULT_PRIVACY_POLICY_TEST_FILE = "config/privacy_policy_test.txt";
    public static final String DEFAULT_PAYMENT_AGREEMENT_TEST_FILE = "config/payment_agreement_test.txt";
    public static final String DEFAULT_USER_AGREEMENT_TEST_FILE = "config/user_agreement_test.txt";

    /**
     * 默认客服连接
     */
    public static final String DEFAULT_CUSTOMER_SERVICE_LINK = "https://work.weixin.qq.com/kfid/kfc92026e9a7c9e9059";

    /**
     * 缓存名称常量
     */
    public static class CacheNames {
        // 用户相关缓存
        public static final String APP_USER = "app_user";
        public static final String APP_USER_BY_PHONE = "app_user_by_phone";
        public static final String APP_USER_BY_WECHAT = "app_user_by_wechat";

        // 会员相关缓存
        public static final String MEMBER = "member";
        public static final String MEMBER_BY_USER_ID = "member_by_user_id";
        public static final String MEMBER_ADDRESS = "member_address";
        public static final String MEMBER_DEFAULT_ADDRESS = "member_default_address";

        // 投资人相关缓存
        public static final String REGION_INVESTOR = "region_investor";
        public static final String REGION_INVESTOR_BY_USER_ID = "region_investor_by_user_id";
        public static final String REGION_INVESTOR_BY_ADDRESS = "region_investor_by_address";

        // 工厂相关缓存
        public static final String FACTORY_INFO = "factory_info";
        public static final String FACTORY_BY_ADDRESS = "factory_by_address";

        // 智能柜相关缓存
        public static final String SMART_CABINET = "smart_cabinet";
        public static final String CABINET_CELLS = "cabinet_cells";
        public static final String CABINET_BY_ADDRESS = "cabinet_by_address";

        public static final String SERVICE_ITEM = "serviceItem";
        public static final String SERVICE_ITEM_BY_REGION = "serviceItemByRegion";
        public static final String SERVICE_ITEM_BY_CATEGORY = "serviceItemByCategory";
        public static final String SERVICE_ITEM_NO_REGION = "serviceItemNoRegion";
        public static final String SERVICE_ITEM_BY_CATEGORY_AND_REGION = "serviceItemByCategoryAndRegion";
        public static final String SERVICE_ITEM_BATCH = "serviceItemBatch";
    }

    private Constants() {
        // 私有构造函数，防止实例化
    }
}
