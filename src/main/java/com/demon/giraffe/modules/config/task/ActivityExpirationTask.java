package com.demon.giraffe.modules.config.task;

import com.demon.giraffe.modules.config.service.ActivityConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 活动过期处理定时任务
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ActivityExpirationTask {

    private final ActivityConfigService activityConfigService;

    /**
     * 每分钟检查一次过期活动
     */
    @Scheduled(cron = "0 * * * * ?")
    public void handleExpiredActivities() {
        try {
            log.debug("开始检查过期活动");
            activityConfigService.handleExpiredActivities();
            log.debug("过期活动检查完成");
        } catch (Exception e) {
            log.error("处理过期活动失败", e);
        }
    }
}
