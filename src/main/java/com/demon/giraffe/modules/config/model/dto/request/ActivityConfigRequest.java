package com.demon.giraffe.modules.config.model.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 活动配置请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "活动配置请求")
public class ActivityConfigRequest {

    @Schema(description = "活动名称", required = true)
    @NotBlank(message = "活动名称不能为空")
    private String activityName;

    @Schema(description = "活动描述")
    private String description;

    @Schema(description = "开始时间", required = true)
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "持续时间（小时）", required = true)
    @NotNull(message = "持续时间不能为空")
    @Positive(message = "持续时间必须大于0")
    private Integer durationHours;

    @Schema(description = "活动展示图URL")
    private String imageUrl;

    @Schema(description = "影响的服务项列表", required = true)
    @NotEmpty(message = "服务项列表不能为空")
    @Valid
    private List<ActivityServiceItemRequest> serviceItems;

    /**
     * 活动服务项请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "活动服务项请求")
    public static class ActivityServiceItemRequest {

        @Schema(description = "服务项ID", required = true)
        @NotNull(message = "服务项ID不能为空")
        private Long serviceItemId;

        @Schema(description = "活动价格", required = true)
        @NotNull(message = "活动价格不能为空")
        @Positive(message = "活动价格必须大于0")
        private BigDecimal activityPrice;
    }
}
