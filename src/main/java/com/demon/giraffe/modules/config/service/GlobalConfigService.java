package com.demon.giraffe.modules.config.service;

import com.demon.giraffe.modules.config.model.dto.request.GlobalConfigUpdateRequest;
import com.demon.giraffe.modules.config.model.dto.response.GlobalConfigResponse;
import com.demon.giraffe.modules.config.model.enums.GlobalConfigKeyEnum;

import java.util.List;

/**
 * 全局配置服务接口
 */
public interface GlobalConfigService {

    /**
     * 更新全局配置（仅修改图片，部分配置项不支持更新）
     *
     * @param request 更新请求
     * @return 是否成功
     */
    boolean updateGlobalConfig(GlobalConfigUpdateRequest request);

    /**
     * 根据配置项Key获取配置
     *
     * @param configKey 配置项Key
     * @return 配置响应
     */
    GlobalConfigResponse getGlobalConfig(GlobalConfigKeyEnum configKey);

    /**
     * 获取所有配置
     *
     * @return 配置列表
     */
    List<GlobalConfigResponse> getAllGlobalConfigs();


    /**
     * 初始化默认配置
     *
     * @return 是否成功
     */
    boolean initializeDefaultConfigs();
}
