package com.demon.giraffe.modules.config.model.dto.response;

import com.demon.giraffe.modules.config.model.entity.ActivityConfigEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 活动配置响应
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "活动配置响应")
public class ActivityConfigResponse {

    @Schema(description = "活动ID")
    private String activityId;

    @Schema(description = "活动名称")
    private String activityName;

    @Schema(description = "活动描述")
    private String description;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "持续时间（小时）")
    private Integer durationHours;

    @Schema(description = "活动展示图URL")
    private String imageUrl;

    @Schema(description = "活动状态")
    private ActivityConfigEntity.ActivityStatus status;

    @Schema(description = "活动状态描述")
    private String statusDesc;

    @Schema(description = "是否正在进行中")
    private Boolean isActive;

    @Schema(description = "剩余时间（分钟）")
    private Long remainingMinutes;

    @Schema(description = "影响的服务项列表")
    private List<ActivityServiceItemResponse> serviceItems;

    @Schema(description = "总优惠金额")
    private BigDecimal totalDiscountAmount;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 活动服务项响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "活动服务项响应")
    public static class ActivityServiceItemResponse {

        @Schema(description = "服务项ID")
        private Long serviceItemId;

        @Schema(description = "服务项名称")
        private String serviceName;

        @Schema(description = "服务项描述")
        private String serviceDescription;

        @Schema(description = "服务项图片URL")
        private String serviceImageUrl;

        @Schema(description = "原价")
        private BigDecimal originalPrice;

        @Schema(description = "活动价格")
        private BigDecimal activityPrice;

        @Schema(description = "优惠金额")
        private BigDecimal discountAmount;

        @Schema(description = "折扣率", example = "0.8")
        private BigDecimal discountRate;

        @Schema(description = "优惠百分比", example = "20%")
        private String discountPercentage;
    }
}
