package com.demon.giraffe.modules.config.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.config.model.dto.request.GlobalConfigUpdateRequest;
import com.demon.giraffe.modules.config.model.dto.request.ServiceConfigItem;
import com.demon.giraffe.modules.config.model.dto.response.GlobalConfigResponse;
import com.demon.giraffe.modules.config.model.enums.GlobalConfigKeyEnum;
import com.demon.giraffe.modules.config.service.GlobalConfigService;
import com.demon.giraffe.modules.config.service.HomeServiceConfigService;
import com.demon.giraffe.modules.service.model.dto.response.CategoryResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 全局配置管理接口
 */
@Slf4j
@RestController
@RequestMapping("/config/global")
@RequiredArgsConstructor
@Tag(name = "全局配置管理接口", description = "全局公共配置的增删改查接口")
public class GlobalConfigController {

    private final GlobalConfigService globalConfigService;
    private final HomeServiceConfigService homeServiceConfigService;

    @PostMapping("/update")
    @Operation(summary = "更新全局配置图片", description = "更新指定配置项的图片（仅支持客服热线、前后对比、洗护中心实拍、客服二维码等可更新配置项）")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<Boolean> updateGlobalConfig(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "全局配置更新参数（仅修改图片，部分配置项不支持更新）",
                    required = true,
                    content = @Content(schema = @Schema(implementation = GlobalConfigUpdateRequest.class))
            )
            @Valid @RequestBody GlobalConfigUpdateRequest request) {
        boolean result = globalConfigService.updateGlobalConfig(request);
        return ResultBean.success(result);
    }

    @GetMapping("/get/{configKey}")
    @Operation(summary = "获取指定配置项", description = "根据配置项Key获取全局配置详情")
    public ResultBean<GlobalConfigResponse> getGlobalConfig(
            @Parameter(name = "configKey", description = "配置项Key", required = true, in = ParameterIn.PATH)
            @PathVariable GlobalConfigKeyEnum configKey) {
        GlobalConfigResponse response = globalConfigService.getGlobalConfig(configKey);
        return ResultBean.success(response);
    }

    @GetMapping("/list")
    @Operation(summary = "获取所有全局配置", description = "获取固定的三个全局配置项列表")
    public ResultBean<List<GlobalConfigResponse>> getAllGlobalConfigs() {
        List<GlobalConfigResponse> responses = globalConfigService.getAllGlobalConfigs();
        return ResultBean.success(responses);
    }


    @PostMapping("/init")
    @Operation(summary = "初始化默认配置", description = "初始化系统默认的全局配置（包括客服热线、前后对比、洗护中心实拍、客服二维码、隐私政策、付费协议、用户协议、客服连接等）")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<Boolean> initializeDefaultConfigs() {
        boolean result = globalConfigService.initializeDefaultConfigs();
        return ResultBean.success(result);
    }

    @GetMapping("/keys")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    @Operation(summary = "获取所有配置项", description = "获取系统支持的所有配置项枚举")
    public ResultBean<GlobalConfigKeyEnum[]> getAllConfigKeys() {
        return ResultBean.success(GlobalConfigKeyEnum.values());
    }

    @GetMapping("/updatable-keys")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    @Operation(summary = "获取可更新的配置项", description = "获取系统支持更新的配置项枚举")
    public ResultBean<GlobalConfigKeyEnum[]> getUpdatableConfigKeys() {
        GlobalConfigKeyEnum[] updatableKeys = java.util.Arrays.stream(GlobalConfigKeyEnum.values())
                .filter(GlobalConfigKeyEnum::isUpdatable)
                .toArray(GlobalConfigKeyEnum[]::new);
        return ResultBean.success(updatableKeys);
    }


    @PostMapping("/home-service/save")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    @Operation(
            summary = "保存首页服务配置",
            description = "创建/更新首页展示服务配置（全量覆盖）",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "保存成功",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = CategoryResponse.class)
                            )
                    )
            }
    )
    public ResultBean<List<CategoryResponse>> saveHomeServiceConfig(
            @Valid @RequestBody List<ServiceConfigItem> request) {
        return ResultBean.success(homeServiceConfigService.saveHomeServiceConfig(request));
    }


    @GetMapping("/home-service/get")
    @Operation(
            summary = "获取首页服务配置",
            description = "获取首页展示服务配置列表（按展示顺序排序）",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "获取成功",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = CategoryResponse.class)
                            )
                    )
            }
    )
    public ResultBean<List<CategoryResponse>> getHomeServiceConfig() {
        return ResultBean.success(homeServiceConfigService.getHomeServiceConfig());
    }

}
