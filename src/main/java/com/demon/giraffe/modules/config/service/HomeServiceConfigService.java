package com.demon.giraffe.modules.config.service;

import com.demon.giraffe.modules.config.model.dto.request.ServiceConfigItem;
import com.demon.giraffe.modules.service.model.dto.response.CategoryResponse;
import com.demon.giraffe.modules.service.model.dto.response.ServiceItemResponse;

import java.util.List;

/**
 * 首页展示服务配置服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface HomeServiceConfigService {

    /**
     * 创建/更新首页服务配置（全量覆盖）
     *
     * @param request 配置请求
     * @return 配置的服务列表
     */
    List<CategoryResponse> saveHomeServiceConfig(List<ServiceConfigItem> request);

    /**
     * 获取首页服务配置
     *
     * @return 配置的服务列表（按展示顺序排序）
     */
    List<CategoryResponse> getHomeServiceConfig();
}
