package com.demon.giraffe.modules.config.service.impl;

import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.common.util.CodeGeneratorUtil;
import com.demon.giraffe.framework.redis.service.RedisService;
import com.demon.giraffe.modules.config.model.dto.request.ActivityConfigRequest;
import com.demon.giraffe.modules.config.model.dto.response.ActivityConfigResponse;
import com.demon.giraffe.modules.config.model.entity.ActivityConfigEntity;
import com.demon.giraffe.modules.config.model.entity.ActivityOriginalPriceEntity;
import com.demon.giraffe.modules.config.service.ActivityConfigService;
import com.demon.giraffe.modules.service.model.po.ServiceItemPo;
import com.demon.giraffe.modules.service.repository.ServiceItemRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ScheduledFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 活动配置服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ActivityConfigServiceImpl implements ActivityConfigService {

    private static final String ACTIVITY_CONFIG_KEY = "global:config:activity";
    private static final String ACTIVITY_ORIGINAL_PRICES_KEY = "global:config:activity:original_prices";

    private final RedisService redisService;
    private final ServiceItemRepository serviceItemRepository;
    private final CodeGeneratorUtil codeGeneratorUtil;
    private final TaskScheduler taskScheduler;

    // 存储定时任务的引用
    private ScheduledFuture<?> activityEndTask;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActivityConfigResponse createActivity(ActivityConfigRequest request) {
        log.info("开始创建活动：{}", request.getActivityName());

        // 1. 检查是否已有活动（只允许一个活动）
        ActivityConfigEntity currentActivity = getCurrentActivityEntity();
        if (currentActivity != null) {
            throw new BusinessException("系统只支持一个活动，请先删除当前活动");
        }

        // 2. 验证服务项是否存在
        Set<Long> serviceItemIds = request.getServiceItems().stream()
                .map(ActivityConfigRequest.ActivityServiceItemRequest::getServiceItemId)
                .collect(Collectors.toSet());

        List<ServiceItemPo> serviceItems = serviceItemRepository.batchGetNormalItemsByIds(serviceItemIds);
        if (serviceItems.size() != serviceItemIds.size()) {
            throw new BusinessException("部分服务项不存在");
        }

        // 3. 构建活动实体
        ActivityConfigEntity activityEntity = buildActivityEntity(request, serviceItems);

        // 4. 保存到Redis
        redisService.set(ACTIVITY_CONFIG_KEY, activityEntity);

        // 5. 如果活动立即开始，启动活动
        if (activityEntity.getStartTime().isBefore(LocalDateTime.now().plusMinutes(1))) {
            startActivity(activityEntity);
        }

        log.info("活动创建成功：{}", activityEntity.getActivityId());
        return convertToResponse(activityEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActivityConfigResponse updateActivity(String activityId, ActivityConfigRequest request) {
        log.info("开始更新活动：{}", activityId);

        // 1. 获取当前活动（系统只有一个活动）
        ActivityConfigEntity currentActivity = getCurrentActivityEntity();
        if (currentActivity == null) {
            throw new BusinessException("活动不存在");
        }

        // 2. 如果活动正在进行中，先结束当前活动
        if (currentActivity.isActive()) {
            endActivity(currentActivity);
        }

        // 3. 验证服务项
        Set<Long> serviceItemIds = request.getServiceItems().stream()
                .map(ActivityConfigRequest.ActivityServiceItemRequest::getServiceItemId)
                .collect(Collectors.toSet());
        
        List<ServiceItemPo> serviceItems = serviceItemRepository.batchGetNormalItemsByIds(serviceItemIds);
        if (serviceItems.size() != serviceItemIds.size()) {
            throw new BusinessException("部分服务项不存在");
        }

        // 4. 构建新的活动实体
        ActivityConfigEntity updatedActivity = buildActivityEntity(request, serviceItems);
        updatedActivity.setActivityId(currentActivity.getActivityId()); // 保持原有ID
        updatedActivity.setCreateTime(currentActivity.getCreateTime());

        // 5. 保存到Redis
        redisService.set(ACTIVITY_CONFIG_KEY, updatedActivity);

        // 6. 如果活动立即开始，启动活动
        if (updatedActivity.getStartTime().isBefore(LocalDateTime.now().plusMinutes(1))) {
            startActivity(updatedActivity);
        }

        log.info("活动更新成功：{}", activityId);
        return convertToResponse(updatedActivity);
    }

    @Override
    public ActivityConfigResponse getCurrentActivity() {
        ActivityConfigEntity activityEntity = getCurrentActivityEntity();
        return activityEntity != null ? convertToResponse(activityEntity) : null;
    }

    @Override
    public ActivityConfigResponse getActivityById(String activityId) {
        ActivityConfigEntity activityEntity = getCurrentActivityEntity();
        if (activityEntity == null) {
            throw new BusinessException("活动不存在");
        }
        // 系统只有一个活动，直接返回当前活动
        return convertToResponse(activityEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelActivity(String activityId) {
        log.info("开始取消活动：{}", activityId);

        ActivityConfigEntity activityEntity = getCurrentActivityEntity();
        if (activityEntity == null) {
            throw new BusinessException("活动不存在");
        }

        // 结束活动
        endActivity(activityEntity);

        // 删除活动（系统只有一个活动，直接删除）
        redisService.deleteKey(ACTIVITY_CONFIG_KEY);

        log.info("活动取消成功：{}", activityEntity.getActivityId());
        return true;
    }

    @Override
    public void handleExpiredActivities() {
        ActivityConfigEntity activityEntity = getCurrentActivityEntity();
        if (activityEntity != null && activityEntity.isExpired() && 
            activityEntity.getStatus() == ActivityConfigEntity.ActivityStatus.ACTIVE) {
            
            log.info("处理过期活动：{}", activityEntity.getActivityId());
            endActivity(activityEntity);
            
            activityEntity.setStatus(ActivityConfigEntity.ActivityStatus.EXPIRED);
            activityEntity.setUpdateTime(LocalDateTime.now());
            redisService.set(ACTIVITY_CONFIG_KEY, activityEntity);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startActivity(ActivityConfigEntity activityEntity) {
        log.info("启动活动：{}", activityEntity.getActivityId());

        try {
            // 1. 保存原始价格
            saveOriginalPrices(activityEntity);

            // 2. 更新服务项价格
            updateServiceItemPrices(activityEntity, true);

            // 3. 更新活动状态
            activityEntity.setStatus(ActivityConfigEntity.ActivityStatus.ACTIVE);
            activityEntity.setUpdateTime(LocalDateTime.now());
            redisService.set(ACTIVITY_CONFIG_KEY, activityEntity);

            // 4. 创建定时任务，在活动结束时自动处理
            scheduleActivityEnd(activityEntity);

            log.info("活动启动成功：{}", activityEntity.getActivityId());

        } catch (Exception e) {
            log.error("启动活动失败：{}", activityEntity.getActivityId(), e);
            throw new BusinessException("启动活动失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void endActivity(ActivityConfigEntity activityEntity) {
        log.info("结束活动：{}", activityEntity.getActivityId());

        try {
            // 1. 还原服务项价格
            restoreServiceItemPrices(activityEntity);

            // 2. 清除原始价格缓存
            redisService.deleteKey(ACTIVITY_ORIGINAL_PRICES_KEY);

            // 3. 取消定时任务
            if (activityEndTask != null && !activityEndTask.isDone()) {
                activityEndTask.cancel(false);
            }

            log.info("活动结束成功：{}", activityEntity.getActivityId());

        } catch (Exception e) {
            log.error("结束活动失败：{}", activityEntity.getActivityId(), e);
            throw new BusinessException("结束活动失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前活动实体
     */
    private ActivityConfigEntity getCurrentActivityEntity() {
        return redisService.getValue(ACTIVITY_CONFIG_KEY, ActivityConfigEntity.class);
    }

    /**
     * 构建活动实体
     */
    private ActivityConfigEntity buildActivityEntity(ActivityConfigRequest request, List<ServiceItemPo> serviceItems) {
        Map<Long, ServiceItemPo> serviceItemMap = serviceItems.stream()
                .collect(Collectors.toMap(ServiceItemPo::getId, Function.identity()));

        // 只保存服务项ID和活动价格，不保存其他服务信息
        List<ActivityConfigEntity.ActivityServiceItem> activityServiceItems = request.getServiceItems().stream()
                .map(item -> {
                    // 验证服务项存在
                    if (!serviceItemMap.containsKey(item.getServiceItemId())) {
                        throw new BusinessException("服务项不存在：" + item.getServiceItemId());
                    }

                    return ActivityConfigEntity.ActivityServiceItem.builder()
                            .serviceItemId(item.getServiceItemId())
                            .activityPrice(item.getActivityPrice())
                            .build();
                })
                .collect(Collectors.toList());

        LocalDateTime startTime = request.getStartTime();
        LocalDateTime endTime = startTime.plusHours(request.getDurationHours());

        return ActivityConfigEntity.builder()
                .activityId(codeGeneratorUtil.generateActivityCode())
                .activityName(request.getActivityName())
                .description(request.getDescription())
                .startTime(startTime)
                .endTime(endTime)
                .durationHours(request.getDurationHours())
                .imageUrl(request.getImageUrl())
                .status(ActivityConfigEntity.ActivityStatus.DRAFT)
                .serviceItems(activityServiceItems)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();
    }

    /**
     * 转换为响应对象（动态查询服务项信息）
     */
    private ActivityConfigResponse convertToResponse(ActivityConfigEntity entity) {
        // 1. 获取所有服务项ID
        List<Long> serviceItemIds = entity.getServiceItems().stream()
                .map(ActivityConfigEntity.ActivityServiceItem::getServiceItemId)
                .collect(Collectors.toList());

        // 2. 动态查询服务项信息
        List<ServiceItemPo> serviceItems = serviceItemRepository.listByIds(serviceItemIds);
        Map<Long, ServiceItemPo> serviceItemMap = serviceItems.stream()
                .collect(Collectors.toMap(ServiceItemPo::getId, Function.identity()));

        // 3. 构建响应对象，包含最新的服务项信息和图片
        List<ActivityConfigResponse.ActivityServiceItemResponse> serviceItemResponses =
                entity.getServiceItems().stream()
                        .map(item -> {
                            ServiceItemPo serviceItem = serviceItemMap.get(item.getServiceItemId());
                            if (serviceItem == null) {
                                log.warn("服务项不存在：{}", item.getServiceItemId());
                                return null;
                            }

                            BigDecimal originalPrice = serviceItem.getMarketReferencePrice();
                            BigDecimal activityPrice = item.getActivityPrice();
                            BigDecimal discountAmount = originalPrice.subtract(activityPrice);
                            BigDecimal discountRate = activityPrice.divide(originalPrice, 4, RoundingMode.HALF_UP);
                            BigDecimal discountPercentage = BigDecimal.ONE.subtract(discountRate)
                                    .multiply(BigDecimal.valueOf(100))
                                    .setScale(0, RoundingMode.HALF_UP);

                            return ActivityConfigResponse.ActivityServiceItemResponse.builder()
                                    .serviceItemId(item.getServiceItemId())
                                    .serviceName(serviceItem.getName())
                                    .serviceDescription(serviceItem.getDescription())
                                    .serviceImageUrl(serviceItem.getSurfacePlot()) // 添加服务图片
                                    .originalPrice(originalPrice)
                                    .activityPrice(activityPrice)
                                    .discountAmount(discountAmount)
                                    .discountRate(discountRate)
                                    .discountPercentage(discountPercentage + "%")
                                    .build();
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        // 4. 计算总优惠金额（从响应对象中计算）
        BigDecimal totalDiscountAmount = serviceItemResponses.stream()
                .map(ActivityConfigResponse.ActivityServiceItemResponse::getDiscountAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        LocalDateTime now = LocalDateTime.now();
        Long remainingMinutes = null;
        if (entity.getEndTime() != null && now.isBefore(entity.getEndTime())) {
            remainingMinutes = Duration.between(now, entity.getEndTime()).toMinutes();
        }

        return ActivityConfigResponse.builder()
                .activityId(entity.getActivityId())
                .activityName(entity.getActivityName())
                .description(entity.getDescription())
                .startTime(entity.getStartTime())
                .endTime(entity.getEndTime())
                .durationHours(entity.getDurationHours())
                .imageUrl(entity.getImageUrl())
                .status(entity.getStatus())
                .statusDesc(entity.getStatus().getDesc())
                .isActive(entity.isActive())
                .remainingMinutes(remainingMinutes)
                .serviceItems(serviceItemResponses)
                .totalDiscountAmount(totalDiscountAmount)
                .createTime(entity.getCreateTime())
                .updateTime(entity.getUpdateTime())
                .build();
    }

    /**
     * 保存原始价格（动态查询当前价格）
     */
    private void saveOriginalPrices(ActivityConfigEntity activityEntity) {
        // 1. 获取服务项ID列表
        List<Long> serviceItemIds = activityEntity.getServiceItems().stream()
                .map(ActivityConfigEntity.ActivityServiceItem::getServiceItemId)
                .collect(Collectors.toList());

        // 2. 查询当前的服务项价格
        List<ServiceItemPo> serviceItems = serviceItemRepository.listByIds(serviceItemIds);

        // 3. 构建原始价格列表
        List<ActivityOriginalPriceEntity.ServiceItemPrice> originalPrices = serviceItems.stream()
                .map(serviceItem -> ActivityOriginalPriceEntity.ServiceItemPrice.builder()
                        .serviceItemId(serviceItem.getId())
                        .originalPrice(serviceItem.getMarketReferencePrice())
                        .build())
                .collect(Collectors.toList());

        ActivityOriginalPriceEntity priceEntity = ActivityOriginalPriceEntity.builder()
                .activityId(activityEntity.getActivityId())
                .originalPrices(originalPrices)
                .build();

        redisService.set(ACTIVITY_ORIGINAL_PRICES_KEY, priceEntity);
    }

    /**
     * 更新服务项价格
     */
    private void updateServiceItemPrices(ActivityConfigEntity activityEntity, boolean useActivityPrice) {
        for (ActivityConfigEntity.ActivityServiceItem item : activityEntity.getServiceItems()) {
            if (useActivityPrice) {
                // 使用活动价格
                serviceItemRepository.updateMarketPrice(item.getServiceItemId(), item.getActivityPrice());
            } else {
                // 还原价格时从Redis中获取原始价格
                log.warn("updateServiceItemPrices with useActivityPrice=false should use restoreServiceItemPrices method");
            }
        }
    }

    /**
     * 还原服务项价格
     */
    private void restoreServiceItemPrices(ActivityConfigEntity activityEntity) {
        ActivityOriginalPriceEntity priceEntity = redisService.getValue(ACTIVITY_ORIGINAL_PRICES_KEY, ActivityOriginalPriceEntity.class);
        if (priceEntity != null && priceEntity.getOriginalPrices() != null) {
            for (ActivityOriginalPriceEntity.ServiceItemPrice priceInfo : priceEntity.getOriginalPrices()) {
                serviceItemRepository.updateMarketPrice(priceInfo.getServiceItemId(), priceInfo.getOriginalPrice());
            }
        }
    }

    /**
     * 安排活动结束任务
     */
    private void scheduleActivityEnd(ActivityConfigEntity activityEntity) {
        if (activityEndTask != null && !activityEndTask.isDone()) {
            activityEndTask.cancel(false);
        }

        activityEndTask = taskScheduler.schedule(
                () -> {
                    try {
                        handleExpiredActivities();
                    } catch (Exception e) {
                        log.error("定时处理活动结束失败", e);
                    }
                },
                java.time.Instant.from(activityEntity.getEndTime().atZone(java.time.ZoneId.systemDefault()))
        );
    }
}
