package com.demon.giraffe.modules.order.model.po.other;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单配送信息表PO
 * <p>存储订单的取件和送件地址信息，以及配送时间安排</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("order_delivery_info")
@Schema(description = "订单配送信息实体")
public class OrderDeliveryInfoPo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /* 数据库字段：order_id */
    @Schema(description = "关联订单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "订单ID不能为空")
    @TableField("order_id")
    private Long orderId;

    /* 取件信息 */
    @Schema(description = "取件地址ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "取件地址ID不能为空")
    @TableField("pickup_address_id")
    private Long pickupAddressId;

    @Schema(description = "取件联系人姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "取件联系人姓名不能为空")
    @Size(max = 30, message = "取件联系人姓名长度不能超过30个字符")
    @TableField("pickup_contact_name")
    private String pickupContactName;

    @Schema(description = "取件联系人电话", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "取件联系人电话不能为空")
    @Size(min = 11, max = 11, message = "取件联系人电话必须为11位")
    @TableField("pickup_contact_phone")
    private String pickupContactPhone;

    @Schema(description = "取件详细地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "取件详细地址不能为空")
    @Size(max = 200, message = "取件详细地址长度不能超过200个字符")
    @TableField("pickup_detail_address")
    private String pickupDetailAddress;

    @Schema(description = "取件地址经度")
    @Digits(integer = 3, fraction = 7, message = "取件地址经度格式不正确")
    @TableField("pickup_longitude")
    private BigDecimal pickupLongitude;

    @Schema(description = "取件地址纬度")
    @Digits(integer = 3, fraction = 7, message = "取件地址纬度格式不正确")
    @TableField("pickup_latitude")
    private BigDecimal pickupLatitude;

    /* 送件信息 */
    @Schema(description = "送件地址ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "送件地址ID不能为空")
    @TableField("delivery_address_id")
    private Long deliveryAddressId;

    @Schema(description = "送件联系人姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "送件联系人姓名不能为空")
    @Size(max = 30, message = "送件联系人姓名长度不能超过30个字符")
    @TableField("delivery_contact_name")
    private String deliveryContactName;

    @Schema(description = "送件联系人电话", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "送件联系人电话不能为空")
    @Size(min = 11, max = 11, message = "送件联系人电话必须为11位")
    @TableField("delivery_contact_phone")
    private String deliveryContactPhone;

    @Schema(description = "送件详细地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "送件详细地址不能为空")
    @Size(max = 200, message = "送件详细地址长度不能超过200个字符")
    @TableField("delivery_detail_address")
    private String deliveryDetailAddress;

    @Schema(description = "送件地址经度")
    @Digits(integer = 3, fraction = 7, message = "送件地址经度格式不正确")
    @TableField("delivery_longitude")
    private BigDecimal deliveryLongitude;

    @Schema(description = "送件地址纬度")
    @Digits(integer = 3, fraction = 7, message = "送件地址纬度格式不正确")
    @TableField("delivery_latitude")
    private BigDecimal deliveryLatitude;

    /* 配送时间信息 */
    @Schema(description = "预计取件时间")
    @TableField("estimated_pickup_time")
    private LocalDateTime estimatedPickupTime;

    @Schema(description = "预计送达时间")
    @TableField("estimated_delivery_time")
    private LocalDateTime estimatedDeliveryTime;

    @Schema(description = "实际取件时间")
    @TableField("actual_pickup_time")
    private LocalDateTime actualPickupTime;

    @Schema(description = "实际送达时间")
    @TableField("actual_delivery_time")
    private LocalDateTime actualDeliveryTime;

    /* 配送选项 */
    @Schema(description = "是否加急：false-普通 true-加急")
    @TableField("is_urgent")
    private Boolean isUrgent;

    @Schema(description = "加急费用")
    @DecimalMin(value = "0.00", inclusive = true, message = "加急费用不能小于0")
    @Digits(integer = 8, fraction = 2, message = "加急费用格式错误")
    @TableField("urgent_fee")
    private BigDecimal urgentFee;

    @Schema(description = "配送费用")
    @DecimalMin(value = "0.00", inclusive = true, message = "配送费用不能小于0")
    @Digits(integer = 8, fraction = 2, message = "配送费用格式错误")
    @TableField("delivery_fee")
    private BigDecimal deliveryFee;
}
