package com.demon.giraffe.modules.order.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "订单状态枚举")
public enum OrderStatusEnum implements IEnum<Integer> {
    @Schema(description = "待支付")
    WAIT_PAY(10, "待支付"),
    
    @Schema(description = "已支付")
    PAID(20, "已支付"),
    
    @Schema(description = "待取件")
    WAIT_PICKUP(30, "待取件"),
    
    @Schema(description = "已取件")
    PICKED_UP(40, "已取件"),
    
    @Schema(description = "清洗中")
    WASHING(50, "清洗中"),
    
    @Schema(description = "待送件")
    WAIT_DELIVERY(60, "待送件"),
    
    @Schema(description = "已送达")
    DELIVERED(70, "已送达"),
    
    @Schema(description = "已完成")
    COMPLETED(80, "已完成"),
    
    @Schema(description = "已取消")
    CANCELLED(90, "已取消");

    @EnumValue
    private final int code;
    private final String desc;

    OrderStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据代码获取枚举
     */
    public static OrderStatusEnum of(int code) {
        for (OrderStatusEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("无效的订单状态代码: " + code);
    }

    @Override
    public Integer getValue() {
        return code;
    }
}