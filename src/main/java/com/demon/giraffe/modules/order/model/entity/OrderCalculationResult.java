package com.demon.giraffe.modules.order.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单计算结果实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderCalculationResult {

    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;

    /**
     * 优惠券优惠金额
     */
    private BigDecimal couponAmount;

    /**
     * 实际支付金额
     */
    private BigDecimal payAmount;

    /**
     * 预计取件时间
     */
    private LocalDateTime estimatedPickupTime;

    /**
     * 预计送达时间
     */
    private LocalDateTime estimatedDeliveryTime;

    /**
     * 订单详情
     */
    private OrderDetailEntity orderDetail;

    /**
     * 使用的优惠券ID
     */
    private Long usedCouponId;
}