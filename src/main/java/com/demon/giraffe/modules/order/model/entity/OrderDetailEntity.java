package com.demon.giraffe.modules.order.model.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订单详情实体")
public class OrderDetailEntity {

    @Schema(description = "金额信息")
    private OrderAmountEntity amountInfo;

    @Schema(description = "服务项列表")
    private List<OrderServiceItemEntity> serviceItems;

    @Schema(description = "是否精洗")
    private Boolean isPremium;

    @Schema(description = "是否加急")
    private Boolean isUrgent;

    @Schema(description = "清洗总时间")
    private Integer totalProcessingHours;

}