package com.demon.giraffe.modules.order.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "支付状态枚举")
public enum PayStatusEnum implements IEnum<Integer> {
    @Schema(description = "未支付")
    UNPAID(0, "未支付"),
    
    @Schema(description = "已支付")
    PAID(1, "已支付"),
    
    @Schema(description = "已退款")
    REFUNDED(2, "已退款");

    @EnumValue
    private final int code;
    private final String desc;

    PayStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据代码获取枚举
     */
    public static PayStatusEnum of(int code) {
        for (PayStatusEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("无效的支付状态代码: " + code);
    }

    @Override
    public Integer getValue() {
        return code;
    }
}