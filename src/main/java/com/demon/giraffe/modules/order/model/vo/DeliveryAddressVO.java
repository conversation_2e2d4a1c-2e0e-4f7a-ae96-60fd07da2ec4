package com.demon.giraffe.modules.order.model.vo;


import com.demon.giraffe.common.domain.enums.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "配送地址信息")
public class DeliveryAddressVO {
    @Schema(description = "地址ID", required = true)
    private Long addressId;

    @Schema(description = "收货人姓名", required = true)
    private String receiverName;

    @Schema(description = "收货人手机号", required = true)
    private String receiverPhone;

    @Schema(description = "省份", required = true)
    private ProvinceEnum province;

    @Schema(description = "城市", required = true)
    private CityEnum city;

    @Schema(description = "区县", required = true)
    private CountyEnum district;

    @Schema(description = "详细地址", required = true)
    private String detailAddress;

    @Schema(description = "标准取送时间（小时）")
    private Integer standardDeliveryHours;


    /**
     * 计算标准取送时间（模拟方法，实际应根据地理位置计算）
     */
    public Integer calculateStandardDeliveryTime() {
        // 模拟逻辑：同城默认3小时，省内不同城6小时，跨省12小时
        if (this.city != null) {
            return 3; // 同城默认3小时
        }
        return 12; // 默认跨省12小时
    }


    /**
     *    地址列表 : 每个地址的时间
     *    创建Vo 把所有地址查出来 ：然后每个地址算一下 取送时间 给出最终时间
     *
     *    计算方式
     *       根据地址匹配最近的柜子->按照柜子的 取送路程时间
     *       加上家到柜子的距离
     *       同城这里给个假的方法 取送时间  都按照 3小时计算
     *
     *
     *
     *
     *    包含几个方法：地址定位该区域的柜子
     *    根据柜子 信息给出 ->柜子到工厂的时间 从柜子送往工厂的时间 （涉及到统计信息等等（后头根据大数据回归具体时间，这里直接返回固定值））
     *
     * */

}