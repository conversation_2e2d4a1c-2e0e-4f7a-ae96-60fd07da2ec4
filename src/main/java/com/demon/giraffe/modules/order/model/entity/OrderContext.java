package com.demon.giraffe.modules.order.model.entity;

import com.demon.giraffe.modules.marketing.model.entity.Coupon;
import com.demon.giraffe.modules.order.model.dto.request.OrderCreateSimpleRequest;
import com.demon.giraffe.modules.order.model.po.OrderMainPo;
import com.demon.giraffe.modules.service.model.entity.ServiceItemEntity;
import com.demon.giraffe.modules.user.model.dto.response.MemberIdentityDetailResponse;
import com.demon.giraffe.modules.user.model.po.MemberAddressPo;
import com.demon.giraffe.modules.user.model.po.UserPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 订单处理上下文
 * <p>
 * 用于在订单创建流程中传递和共享数据的上下文对象。
 * 包含订单创建所需的所有基础数据、计算结果和持久化对象。
 * </p>
 *
 * <p>设计模式：Context Pattern</p>
 * <p>职责：</p>
 * <ul>
 *   <li>封装订单创建流程中的所有相关数据</li>
 *   <li>在验证、计算、持久化等步骤间传递数据</li>
 *   <li>避免方法参数过多的问题</li>
 *   <li>提供统一的数据访问接口</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-07-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订单处理上下文")
public class OrderContext implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单创建请求
     * 包含用户提交的所有订单信息
     */
    @Schema(description = "订单创建请求", required = true)
    private OrderCreateSimpleRequest request;

    /**
     * 当前登录用户信息
     * 用于权限验证和支付信息获取
     */
    @Schema(description = "当前登录用户信息", required = true)
    private UserPo currentUser;

    /**
     * 会员身份详细信息
     * 包含会员等级、权益等信息
     */
    @Schema(description = "会员身份详细信息", required = true)
    private MemberIdentityDetailResponse memberInfo;

    /**
     * 订单包含的服务项目列表
     * 已验证的有效服务项目
     */
    @Schema(description = "订单包含的服务项目列表", required = true)
    private List<ServiceItemEntity> serviceItems;

    /**
     * 配送地址信息
     * 仅在上门服务时有值
     */
    @Schema(description = "配送地址信息（上门服务时必填）")
    private MemberAddressPo memberAddress;

    /**
     * 使用的优惠券信息
     * 仅在使用优惠券时有值
     */
    @Schema(description = "使用的优惠券信息")
    private Coupon coupon;

    /**
     * 订单计算结果
     * 包含金额、时间等计算结果
     */
    @Schema(description = "订单计算结果")
    private OrderCalculationResult calculation;

    /**
     * 持久化后的订单对象
     * 包含数据库生成的ID等信息
     */
    @Schema(description = "持久化后的订单对象")
    private OrderMainPo order;

    /**
     * 检查是否使用优惠券
     *
     * @return true-使用优惠券，false-未使用优惠券
     */
    public boolean hasCoupon() {
        return coupon != null && request != null && request.getCouponId() != null;
    }

    /**
     * 检查是否为上门服务
     *
     * @return true-上门服务，false-其他服务类型
     */
    public boolean isDoorToDoorService() {
        return memberAddress != null && request != null &&
               request.getDeliveryInfo() != null &&
               request.getDeliveryInfo().getAddressId() != null;
    }

    /**
     * 获取会员ID
     *
     * @return 会员ID
     */
    public Long getMemberId() {
        return memberInfo != null ? memberInfo.getMemberId() : null;
    }

    /**
     * 获取用户ID
     *
     * @return 用户ID
     */
    public Long getUserId() {
        return currentUser != null ? currentUser.getId() : null;
    }
}