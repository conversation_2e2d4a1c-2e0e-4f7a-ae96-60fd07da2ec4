package com.demon.giraffe.modules.order.model.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 订单详情展示实体
 * 用于订单列表等场景的简化展示
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订单详情展示实体")
public class OrderDetailDisplayEntity {

    @Schema(description = "是否精洗")
    private Boolean isPremium;

    @Schema(description = "是否加急")
    private Boolean isUrgent;

    @Schema(description = "服务项目列表")
    private List<ServiceItemDisplay> serviceItems;

    /**
     * 服务项目展示信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "服务项目展示信息")
    public static class ServiceItemDisplay {

        @Schema(description = "服务名称")
        private String serviceName;

        @Schema(description = "数量")
        private Integer quantity;
    }
}
