package com.demon.giraffe.modules.order.model.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 订单地址信息响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订单地址信息响应")
public class OrderAddressInfoResponse {

    @Schema(description = "地址ID")
    private Long addressId;

    @Schema(description = "联系人姓名")
    private String contactName;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "省份")
    private String province;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "区县")
    private String district;

    @Schema(description = "详细地址")
    private String detailAddress;


    @Schema(description = "是否为默认地址")
    private Boolean isDefault;

    @Schema(description = "配送时间信息（仅用于用户预估）")
    private LocalDateTime localDateTime;

}
