package com.demon.giraffe.modules.order.model.dto.status;

import com.demon.giraffe.modules.order.model.dto.status.base.OrderStatusBusinessData;
import com.demon.giraffe.modules.order.model.enums.OrderStatusTrackEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 前往配送中状态业务数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "前往配送中状态业务数据")
public class OutForDeliveryData implements OrderStatusBusinessData {
    
    @Schema(description = "配送员ID")
    private Long deliveryPersonId;
    
    @Schema(description = "配送员姓名")
    private String deliveryPersonName;
    
    @Schema(description = "配送员电话")
    private String deliveryPersonPhone;
    
    @Schema(description = "出发时间")
    private LocalDateTime departureTime;
    
    @Schema(description = "预计送达时间")
    private LocalDateTime estimatedDeliveryTime;
    
    @Schema(description = "配送地址")
    private String deliveryAddress;
    
    @Schema(description = "收件人")
    private String recipient;
    
    @Schema(description = "收件人电话")
    private String recipientPhone;
    
    @Schema(description = "配送方式(1-送货上门,2-放置柜子)")
    private Integer deliveryMethod;
    
    @Schema(description = "目标柜子ID(如果是柜子配送)")
    private Long cabinetId;
    
    @Schema(description = "实时位置信息")
    private String currentLocation;
    
    @Override
    public OrderStatusTrackEnum getStatusType() {
        return OrderStatusTrackEnum.OUT_FOR_DELIVERY;
    }
}
