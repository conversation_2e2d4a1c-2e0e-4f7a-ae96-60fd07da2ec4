package com.demon.giraffe.modules.order.model.dto.status;

import com.demon.giraffe.modules.order.model.dto.status.base.OrderStatusBusinessData;
import com.demon.giraffe.modules.order.model.enums.OrderStatusTrackEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 已完成状态业务数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "已完成状态业务数据")
public class CompletedData implements OrderStatusBusinessData {
    
    @Schema(description = "完成时间")
    private LocalDateTime completionTime;
    
    @Schema(description = "实际取件时间")
    private LocalDateTime actualPickupTime;
    
    @Schema(description = "取件人")
    private String pickedUpBy;
    
    @Schema(description = "总耗时(分钟)")
    private Long totalDurationMinutes;
    
    @Schema(description = "客户满意度评分(1-5)")
    private Integer satisfactionRating;
    
    @Schema(description = "是否已评价")
    private Boolean hasReviewed;
    
    @Schema(description = "完成方式(1-柜子取件,2-上门取件)")
    private Integer completionMethod;
    
    @Schema(description = "完成备注")
    private String completionRemark;
    
    @Override
    public OrderStatusTrackEnum getStatusType() {
        return OrderStatusTrackEnum.COMPLETED;
    }
}
