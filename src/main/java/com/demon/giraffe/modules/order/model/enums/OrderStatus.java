package com.demon.giraffe.modules.order.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

@Getter
public enum OrderStatus implements IEnum<Integer> {
    TO_BE_PAID(10, "待支付"),
    PAID(20, "已支付"),
    TO_BE_PICKED_UP(30, "待取件"),
    PICKED_UP(40, "已取件"),
    IN_TRANSIT(50, "运输中"),
    FACTORY_RECEIVED(60, "工厂已接收"),
    PROCESSING(70, "处理中"),
    COMPLETED(80, "已完成"),
    CANCELLED(90, "已取消");

    @EnumValue
    private final Integer value;
    private final String description;

    OrderStatus(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    // 判断订单是否已完成状态
    public boolean isFinalStatus() {
        return this == COMPLETED || this == CANCELLED;
    }

    // 判断订单是否可取消
    public boolean canBeCancelled() {
        return this == TO_BE_PAID || this == PAID || this == TO_BE_PICKED_UP;
    }
}