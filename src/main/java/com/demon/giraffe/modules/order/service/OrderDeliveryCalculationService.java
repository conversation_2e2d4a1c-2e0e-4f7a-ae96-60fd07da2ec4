package com.demon.giraffe.modules.order.service;

import com.demon.giraffe.modules.cabinet.model.entity.SmartCabinet;
import com.demon.giraffe.modules.order.model.vo.DeliveryInfoVO;
import com.demon.giraffe.modules.user.model.po.MemberAddressPo;

import java.util.List;

/**
 * 订单配送计算服务接口
 * 负责计算配送时间、距离、费用等复杂业务逻辑
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface OrderDeliveryCalculationService {

    /**
     * 计算并填充配送信息
     * 
     * @param memberId 会员ID
     * @param isUrgent 是否加急
     * @return 填充完整的配送信息VO
     */
    DeliveryInfoVO calculateDeliveryInfo(Long memberId, Boolean isUrgent);

    /**
     * 根据地址列表计算配送信息
     * 
     * @param addresses 地址列表
     * @param isUrgent 是否加急
     * @return 填充完整的配送信息VO
     */
    DeliveryInfoVO calculateDeliveryInfoByAddresses(List<MemberAddressPo> addresses, Boolean isUrgent);

    /**
     * 为单个地址计算最近柜子和配送时间
     * 
     * @param address 地址信息
     * @return 最近的柜子信息，如果该地址不支持服务则返回null
     */
    SmartCabinet findNearestCabinetForAddress(MemberAddressPo address);

    /**
     * 计算柜子到工厂的距离和时间
     * 
     * @param cabinet 柜子信息
     * @return 到工厂的时间（小时），如果无法计算则返回null
     */
    Integer calculateCabinetToFactoryTime(SmartCabinet cabinet);

    /**
     * 计算总的配送时间
     * 
     * @param userToBoxTime 用户到柜子时间（小时）
     * @param boxToFactoryTime 柜子到工厂时间（小时）
     * @param isUrgent 是否加急
     * @return 总配送时间（小时）
     */
    Integer calculateTotalDeliveryTime(Integer userToBoxTime, Integer boxToFactoryTime, Boolean isUrgent);

    /**
     * 检查地址是否在服务范围内
     * 
     * @param address 地址信息
     * @return 是否支持服务
     */
    boolean isAddressSupported(MemberAddressPo address);
}
