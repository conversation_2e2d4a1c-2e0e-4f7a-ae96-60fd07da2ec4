package com.demon.giraffe.modules.order.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * 退款状态枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Schema(description = "退款状态枚举")
public enum RefundStatusEnum implements IEnum<Integer> {
    @Schema(description = "申请中")
    APPLYING(1, "申请中"),
    
    @Schema(description = "审核通过")
    APPROVED(2, "审核通过"),
    
    @Schema(description = "退款中")
    REFUNDING(3, "退款中"),
    
    @Schema(description = "退款成功")
    SUCCESS(4, "退款成功"),
    
    @Schema(description = "退款失败")
    FAILED(5, "退款失败"),
    
    @Schema(description = "已拒绝")
    REJECTED(6, "已拒绝");

    @EnumValue
    private final int code;
    private final String desc;

    RefundStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据代码获取枚举
     */
    public static RefundStatusEnum of(int code) {
        for (RefundStatusEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("无效的退款状态代码: " + code);
    }

    @Override
    public Integer getValue() {
        return code;
    }
}
