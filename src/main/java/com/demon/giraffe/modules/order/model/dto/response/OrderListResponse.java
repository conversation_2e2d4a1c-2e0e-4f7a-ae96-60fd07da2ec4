package com.demon.giraffe.modules.order.model.dto.response;

import com.demon.giraffe.modules.order.model.entity.OrderDetailDisplayEntity;
import com.demon.giraffe.modules.order.model.enums.DeliveryTypeEnum;
import com.demon.giraffe.modules.order.model.enums.OrderStatusEnum;
import com.demon.giraffe.modules.order.model.enums.PayStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单列表响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订单列表响应")
public class OrderListResponse {

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "订单详情")
    private OrderDetailDisplayEntity orderDetail;

    @Schema(description = "订单状态描述")
    private String orderStatusDesc;

    @Schema(description = "支付状态描述")
    private String payStatusDesc;

    @Schema(description = "订单总金额")
    private BigDecimal totalAmount;

    @Schema(description = "优惠券优惠金额")
    private BigDecimal couponAmount;

    @Schema(description = "实际支付金额")
    private BigDecimal payAmount;

    @Schema(description = "取件方式描述")
    private String pickupTypeDesc;

    @Schema(description = "送件方式描述")
    private String deliveryTypeDesc;

    @Schema(description = "订单创建时间")
    private LocalDateTime createTime;

    @Schema(description = "预计取件时间")
    private LocalDateTime estimatedPickupTime;

    @Schema(description = "预计送达时间")
    private LocalDateTime estimatedDeliveryTime;

    @Schema(description = "订单描述")
    private String description;

    @Schema(description = "服务项目列表")
    private List<OrderItemResponse> serviceItems;

    @Schema(description = "是否可以支付")
    private Boolean canPay;

    @Schema(description = "是否可以申请退款")
    private Boolean canRefund;

    @Schema(description = "支付剩余时间（秒）", example = "600")
    private Long paymentRemainingTime;

}
