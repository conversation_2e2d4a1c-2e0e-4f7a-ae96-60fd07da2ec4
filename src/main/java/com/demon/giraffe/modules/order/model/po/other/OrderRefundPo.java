package com.demon.giraffe.modules.order.model.po.other;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单退款记录表PO
 * <p>记录订单的退款申请、审核和处理过程</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("order_refund")
@Schema(description = "订单退款记录实体")
public class OrderRefundPo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /* 数据库字段：order_id */
    @Schema(description = "关联订单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "订单ID不能为空")
    @TableField("order_id")
    private Long orderId;

    /* 数据库字段：payment_id */
    @Schema(description = "关联支付记录ID")
    @TableField("payment_id")
    private Long paymentId;

    /* 退款基本信息 */
    @Schema(description = "退款单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "退款单号不能为空")
    @Size(max = 32, message = "退款单号长度不能超过32个字符")
    @TableField("refund_no")
    private String refundNo;

    @Schema(description = "退款金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "退款金额不能为空")
    @DecimalMin(value = "0.00", inclusive = true, message = "退款金额不能小于0")
    @Digits(integer = 8, fraction = 2, message = "退款金额格式错误")
    @TableField("refund_amount")
    private BigDecimal refundAmount;

    @Schema(description = "退款原因", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "退款原因不能为空")
    @Size(max = 200, message = "退款原因长度不能超过200个字符")
    @TableField("refund_reason")
    private String refundReason;

    @Schema(description = "退款类型：1-用户申请 2-系统自动 3-客服处理", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "退款类型不能为空")
    @TableField("refund_type")
    private Integer refundType;

    /* 退款状态 */
    @Schema(description = "退款状态：1-申请中 2-审核通过 3-退款中 4-退款成功 5-退款失败 6-已拒绝")
    @NotNull(message = "退款状态不能为空")
    @TableField("refund_status")
    private Integer refundStatus;

    @Schema(description = "申请时间")
    @TableField("apply_time")
    private LocalDateTime applyTime;

    @Schema(description = "审核时间")
    @TableField("approve_time")
    private LocalDateTime approveTime;

    @Schema(description = "退款完成时间")
    @TableField("refund_time")
    private LocalDateTime refundTime;

    /* 第三方信息 */
    @Schema(description = "第三方退款单号")
    @Size(max = 64, message = "第三方退款单号长度不能超过64个字符")
    @TableField("third_party_refund_no")
    private String thirdPartyRefundNo;

    @Schema(description = "退款方式：1-原路退回 2-余额退款")
    @TableField("refund_method")
    private Integer refundMethod;

    /* 审核信息 */
    @Schema(description = "审核人ID")
    @TableField("approver_id")
    private Long approverId;

    @Schema(description = "审核备注")
    @TableField("approve_notes")
    private String approveNotes;

    @Schema(description = "拒绝原因")
    @Size(max = 200, message = "拒绝原因长度不能超过200个字符")
    @TableField("reject_reason")
    private String rejectReason;
}
