package com.demon.giraffe.modules.order.service;

import com.demon.giraffe.modules.marketing.model.po.MemberCouponPo;

/**
 * 订单操作服务接口
 * 提供订单的核心操作方法：取消、成功下单、退单
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface OrderOperationService {

    /**
     * 取消订单
     * 处理订单取消的完整流程，包括：
     * 1. 释放优惠券使用状态
     * 2. 恢复优惠券模板数量
     * 3. 更新订单状态为已取消
     * 4. 更新支付状态为未支付
     * 5. 清除支付过期时间
     *
     * @param orderNo 订单号
     * @param reason 取消原因
     * @return 是否取消成功
     */
    boolean cancelOrder(String orderNo, String reason);

    /**
     * 成功下单后的处理
     * 处理订单支付成功后的业务逻辑，包括：
     * 1. 更新服务项目销量
     * 2. 分配任务（仅针对上门服务）
     * 3. 更新订单状态
     * 4. 其他后续处理
     *
     * @param orderNo 订单号
     * @return 是否处理成功
     */
    boolean handleSuccessfulOrder(String orderNo);

    /**
     * 退单处理
     * 处理订单退款的完整流程，与取消订单逻辑相同：
     * 1. 释放优惠券使用状态
     * 2. 恢复优惠券模板数量
     * 3. 更新订单状态
     * 4. 处理退款逻辑
     *
     * @param orderNo 订单号
     * @param reason 退单原因
     * @return 是否退单成功
     */
    boolean refundOrder(String orderNo, String reason);

    /**
     * 检查订单是否可以取消
     *
     * @param orderNo 订单号
     * @return 是否可以取消
     */
    boolean canCancelOrder(String orderNo);

    /**
     * 检查订单是否可以退单
     *
     * @param orderNo 订单号
     * @return 是否可以退单
     */
    boolean canRefundOrder(String orderNo);

    /**
     * 根据订单ID查找使用的优惠券
     * 可以先根据订单的优惠价格判断是否使用优惠券
     *
     * @param orderId 订单ID
     * @return 使用的优惠券，如果没有使用优惠券则返回null
     */
    MemberCouponPo findCouponByOrderId(Long orderId);
}
