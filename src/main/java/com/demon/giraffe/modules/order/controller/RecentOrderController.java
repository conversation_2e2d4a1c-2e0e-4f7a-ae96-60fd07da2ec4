package com.demon.giraffe.modules.order.controller;

import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.order.model.dto.response.RecentOrderResponse;
import com.demon.giraffe.modules.order.service.RecentOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 最近下单控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/order/recent")
@RequiredArgsConstructor
@Tag(name = "最近下单管理", description = "最近下单信息查询接口")
public class RecentOrderController {

    private final RecentOrderService recentOrderService;

    @GetMapping
    @Operation(summary = "获取最近下单信息", description = "所有人可访问：获取最近5次下单的用户信息")
    public ResultBean<RecentOrderResponse> getRecentOrders() {
        try {
            log.debug("开始获取最近下单信息");
            
            RecentOrderResponse response = recentOrderService.getRecentOrders();
            
            log.debug("获取最近下单信息成功，数量：{}", 
                    response.getRecentOrders() != null ? response.getRecentOrders().size() : 0);
            
            return ResultBean.success("获取最近下单信息成功", response);
            
        } catch (Exception e) {
            log.error("获取最近下单信息失败", e);
            return ResultBean.fail("获取最近下单信息失败：" + e.getMessage());
        }
    }
}
