package com.demon.giraffe.modules.order.model.dto.status;

import com.demon.giraffe.modules.order.model.dto.status.base.OrderStatusBusinessData;
import com.demon.giraffe.modules.order.model.enums.OrderStatusTrackEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 已取消状态业务数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "已取消状态业务数据")
public class CancelledData implements OrderStatusBusinessData {
    
    @Schema(description = "取消时间")
    private LocalDateTime cancelTime;
    
    @Schema(description = "取消原因")
    private String cancelReason;
    
    @Schema(description = "取消类型(1-用户取消,2-系统取消,3-客服取消)")
    private Integer cancelType;
    
    @Schema(description = "取消操作人ID")
    private Long cancelledBy;
    
    @Schema(description = "取消操作人姓名")
    private String cancelledByName;
    
    @Schema(description = "是否需要退款")
    private Boolean needRefund;
    
    @Schema(description = "退款金额")
    private String refundAmount;
    
    @Schema(description = "退款状态(1-待退款,2-已退款,3-退款失败)")
    private Integer refundStatus;
    
    @Schema(description = "取消备注")
    private String cancelRemark;
    
    @Override
    public OrderStatusTrackEnum getStatusType() {
        return OrderStatusTrackEnum.CANCELLED;
    }
}
