package com.demon.giraffe.modules.order.model.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 最近下单响应
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "最近下单响应")
public class RecentOrderResponse {

    @Schema(description = "最近下单信息列表")
    private List<RecentOrderItem> recentOrders;

    /**
     * 最近下单项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "最近下单项")
    public static class RecentOrderItem {

        @Schema(description = "购买时间", example = "2小时之前")
        private String time;

        @Schema(description = "购买信息", example = "138****1234购买了衬衫清洗")
        private String message;
    }
}
