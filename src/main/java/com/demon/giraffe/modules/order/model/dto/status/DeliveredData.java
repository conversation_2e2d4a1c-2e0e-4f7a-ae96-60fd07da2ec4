package com.demon.giraffe.modules.order.model.dto.status;

import com.demon.giraffe.modules.order.model.dto.status.base.OrderStatusBusinessData;
import com.demon.giraffe.modules.order.model.enums.OrderStatusTrackEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 已送达状态业务数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "已送达状态业务数据")
public class DeliveredData implements OrderStatusBusinessData {
    
    @Schema(description = "实际送达时间")
    private LocalDateTime actualDeliveryTime;
    
    @Schema(description = "配送员ID")
    private Long deliveryPersonId;
    
    @Schema(description = "签收人")
    private String signedBy;
    
    @Schema(description = "签收时间")
    private LocalDateTime signTime;
    
    @Schema(description = "签收照片URLs")
    private List<String> signPhotos;
    
    @Schema(description = "配送方式(1-送货上门,2-放置柜子)")
    private Integer deliveryMethod;
    
    @Schema(description = "柜子ID(如果是柜子配送)")
    private Long cabinetId;
    
    @Schema(description = "柜子格口号")
    private String cabinetCompartment;
    
    @Schema(description = "取件码(如果是柜子配送)")
    private String pickupCode;
    
    @Schema(description = "配送备注")
    private String deliveryRemark;
    
    @Override
    public OrderStatusTrackEnum getStatusType() {
        return OrderStatusTrackEnum.DELIVERED;
    }
}
