package com.demon.giraffe.modules.order.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.modules.order.model.dto.query.OrderMainQuery;
import com.demon.giraffe.modules.order.model.dto.request.OrderCreateSimpleRequest;
import com.demon.giraffe.modules.order.model.dto.response.OrderCreateResponse;
import com.demon.giraffe.modules.order.model.dto.response.OrderListResponse;
import com.demon.giraffe.modules.payment.model.dto.response.PaymentResponse;


/**
 * 订单主表服务接口
 */
public interface OrderMainService {


    /**
     * 创建订单并准备支付（新版本）
     * 包含完整的校验、计算、保存和支付准备流程
     * @param request 简化订单创建请求
     * @return 订单创建响应（包含支付信息）
     */
    OrderCreateResponse createOrderWithPayment(OrderCreateSimpleRequest request);

    /**
     * 获取我的订单列表
     * 根据当前登录用户获取其订单列表，支持分页和条件查询
     * @param pageQuery 分页查询条件
     * @return 订单列表分页响应
     */
    IPage<OrderListResponse> getMyOrderList(BasePageQuery<OrderMainQuery> pageQuery);

    /**
     * 重新支付订单
     * 为待支付订单重新创建支付
     * @param orderNo 订单号
     * @return 支付响应信息
     */
    PaymentResponse repayOrder(String orderNo);

}