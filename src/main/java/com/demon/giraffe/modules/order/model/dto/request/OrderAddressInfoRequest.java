package com.demon.giraffe.modules.order.model.dto.request;

import com.demon.giraffe.modules.order.model.enums.AppointmentTimeSlotEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单地址信息请求（仅读取上门服务进行计算）
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订单地址信息请求（仅读取上门服务进行计算）")
public class OrderAddressInfoRequest {

    @NotNull(message = "地址ID不能为空")
    @Schema(description = "配送地址ID（上门服务）", required = true)
    private Long addressId;

    @NotEmpty(message = "订单商品不能为空")
    @Valid
    @Schema(description = "订单商品列表", required = true)
    private List<OrderItemDetail> orderItems;

    @Schema(description = "用户选项（加急、精洗等）")
    private UserOptionRequest userOptions;

    @Schema(description = "预约时间-日期（上门服务时必填）")
    private LocalDate localDate;

    @Schema(description = "预约时间-时间节点（上门服务时必填）")
    private AppointmentTimeSlotEnum appointmentTimeSlotEnum;

}
