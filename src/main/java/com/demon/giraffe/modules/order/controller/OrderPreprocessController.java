package com.demon.giraffe.modules.order.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.order.model.dto.request.OrderAddressInfoRequest;
import com.demon.giraffe.modules.order.model.dto.request.OrderCouponMatchRequest;
import com.demon.giraffe.modules.order.model.dto.response.OrderAddressInfoResponse;
import com.demon.giraffe.modules.order.model.dto.response.OrderCouponInfoResponse;
import com.demon.giraffe.modules.order.service.OrderPreprocessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 订单预处理控制器
 * 用于订单创建前的信息获取和预处理
 */
@RestController
@RequestMapping("/order/preprocess")
@RequiredArgsConstructor
@Tag(name = "订单预处理", description = "订单创建前的信息获取和预处理接口")
@SaCheckRole(value = {"CUSTOMER"}, mode = SaMode.OR)
public class OrderPreprocessController {

    private final OrderPreprocessService orderPreprocessService;

    @PostMapping("/address-info")
    @Operation(
            summary = "获取指定地址的配送信息（仅上门服务）",
            description = "根据地址ID和订单信息计算上门配送时间。\n\n" +
                    "功能包括：\n" +
                    "• 获取指定地址的详细信息\n" +
                    "• 根据订单商品和用户选项计算配送时间（仅供用户参考）\n" +
                    "• 支持预约时间参数（与OrderMainController时间参数一致）\n" +
                    "• 判断地址是否支持配送服务\n\n" +
                    "注意：\n" +
                    "• 仅读取上门服务进行计算\n" +
                    "• 配送时间仅为预估值，用于用户参考\n" +
                    "• 不返回内部的柜子、工厂等具体信息\n" +
                    "• 实际配送时间以订单创建后的计算为准\n" +
                    "• 只返回指定地址的信息，不遍历所有地址"
    )
    @SaCheckRole(value = {"CUSTOMER"}, mode = SaMode.OR)
    public ResultBean<OrderAddressInfoResponse> getAddressDeliveryInfo(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "地址配送信息请求参数",
                    required = true,
                    content = @Content(schema = @Schema(implementation = OrderAddressInfoRequest.class))
            )
            @Valid @RequestBody OrderAddressInfoRequest request) {
        OrderAddressInfoResponse response = orderPreprocessService.getAddressDeliveryInfo(request);
        return ResultBean.success(response);
    }

    @PostMapping("/coupon-match")
    @Operation(
            summary = "匹配订单优惠券",
            description = "计算优惠券优惠金额，返回可用和不可用的优惠券列表。\n\n" +
                    "功能包括：\n" +
                    "• 根据订单商品和用户选项计算总金额\n" +
                    "• 匹配用户可用的优惠券\n" +
                    "• 计算每个优惠券的优惠金额\n" +
                    "• 推荐最优优惠券\n\n" +
                    "注意：\n"
    )
    @SaCheckRole(value = {"CUSTOMER"}, mode = SaMode.OR)
    public ResultBean<OrderCouponInfoResponse> matchCouponsForOrder(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "优惠券匹配请求参数",
                    required = true,
                    content = @Content(schema = @Schema(implementation = OrderCouponMatchRequest.class))
            )
            @Valid @RequestBody OrderCouponMatchRequest request) {
        OrderCouponInfoResponse response = orderPreprocessService.matchCouponsForOrder(request);
        return ResultBean.success(response);
    }
}
