package com.demon.giraffe.modules.order.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * 订单任务状态枚举
 * 用于跟踪订单在整个业务流程中的任务状态
 */
@Getter
@Schema(description = "订单任务状态枚举")
public enum OrderTaskStatusEnum implements IEnum<Integer> {
    
    @Schema(description = "待分配取送员")
    PENDING_COURIER_ASSIGNMENT(10, "待分配取送员"),
    
    @Schema(description = "已分配取送员")
    COURIER_ASSIGNED(20, "已分配取送员"),
    
    @Schema(description = "取送员前往取件")
    COURIER_EN_ROUTE_PICKUP(30, "取送员前往取件"),
    
    @Schema(description = "已取件打包")
    ITEMS_PICKED_UP_PACKAGED(40, "已取件打包"),
    
    @Schema(description = "生成订单二维码")
    ORDER_QR_GENERATED(50, "生成订单二维码"),
    
    @Schema(description = "运输中")
    IN_TRANSIT_TO_FACTORY(60, "运输中"),
    
    @Schema(description = "工厂已接收")
    FACTORY_RECEIVED(70, "工厂已接收"),
    
    @Schema(description = "工厂拆包中")
    FACTORY_UNPACKING(80, "工厂拆包中"),
    
    @Schema(description = "生成衣物二维码")
    CLOTHING_QR_GENERATED(90, "生成衣物二维码"),
    
    @Schema(description = "清洗处理中")
    PROCESSING_IN_FACTORY(100, "清洗处理中"),
    
    @Schema(description = "质检中")
    QUALITY_CHECKING(110, "质检中"),
    
    @Schema(description = "清洗完成打包")
    PROCESSING_COMPLETED_PACKAGED(120, "清洗完成打包"),
    
    @Schema(description = "生成出厂二维码")
    FACTORY_EXIT_QR_GENERATED(130, "生成出厂二维码"),
    
    @Schema(description = "待分配送货员")
    PENDING_DELIVERY_ASSIGNMENT(140, "待分配送货员"),
    
    @Schema(description = "已分配送货员")
    DELIVERY_COURIER_ASSIGNED(150, "已分配送货员"),
    
    @Schema(description = "送货员已取件")
    DELIVERY_COURIER_PICKED_UP(160, "送货员已取件"),
    
    @Schema(description = "配送中")
    OUT_FOR_DELIVERY(170, "配送中"),
    
    @Schema(description = "已送达")
    DELIVERED(180, "已送达"),
    
    @Schema(description = "订单完成")
    COMPLETED(190, "订单完成"),
    
    @Schema(description = "异常")
    EXCEPTION(200, "异常"),
    
    @Schema(description = "已取消")
    CANCELLED(210, "已取消");

    @EnumValue
    private final Integer code;
    private final String desc;

    OrderTaskStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getValue() {
        return code;
    }

    /**
     * 根据代码获取枚举
     */
    public static OrderTaskStatusEnum of(Integer code) {
        for (OrderTaskStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("无效的订单任务状态代码: " + code);
    }

    /**
     * 判断是否是最终状态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == CANCELLED || this == EXCEPTION;
    }

    /**
     * 判断是否是工厂处理阶段
     */
    public boolean isFactoryStage() {
        return code >= FACTORY_RECEIVED.code && code <= FACTORY_EXIT_QR_GENERATED.code;
    }

    /**
     * 判断是否是配送阶段
     */
    public boolean isDeliveryStage() {
        return code >= PENDING_DELIVERY_ASSIGNMENT.code && code <= DELIVERED.code;
    }

    /**
     * 判断是否是取件阶段
     */
    public boolean isPickupStage() {
        return code >= PENDING_COURIER_ASSIGNMENT.code && code <= IN_TRANSIT_TO_FACTORY.code;
    }

    /**
     * 获取下一个状态
     */
    public OrderTaskStatusEnum getNextStatus() {
        switch (this) {
            case PENDING_COURIER_ASSIGNMENT:
                return COURIER_ASSIGNED;
            case COURIER_ASSIGNED:
                return COURIER_EN_ROUTE_PICKUP;
            case COURIER_EN_ROUTE_PICKUP:
                return ITEMS_PICKED_UP_PACKAGED;
            case ITEMS_PICKED_UP_PACKAGED:
                return ORDER_QR_GENERATED;
            case ORDER_QR_GENERATED:
                return IN_TRANSIT_TO_FACTORY;
            case IN_TRANSIT_TO_FACTORY:
                return FACTORY_RECEIVED;
            case FACTORY_RECEIVED:
                return FACTORY_UNPACKING;
            case FACTORY_UNPACKING:
                return CLOTHING_QR_GENERATED;
            case CLOTHING_QR_GENERATED:
                return PROCESSING_IN_FACTORY;
            case PROCESSING_IN_FACTORY:
                return QUALITY_CHECKING;
            case QUALITY_CHECKING:
                return PROCESSING_COMPLETED_PACKAGED;
            case PROCESSING_COMPLETED_PACKAGED:
                return FACTORY_EXIT_QR_GENERATED;
            case FACTORY_EXIT_QR_GENERATED:
                return PENDING_DELIVERY_ASSIGNMENT;
            case PENDING_DELIVERY_ASSIGNMENT:
                return DELIVERY_COURIER_ASSIGNED;
            case DELIVERY_COURIER_ASSIGNED:
                return DELIVERY_COURIER_PICKED_UP;
            case DELIVERY_COURIER_PICKED_UP:
                return OUT_FOR_DELIVERY;
            case OUT_FOR_DELIVERY:
                return DELIVERED;
            case DELIVERED:
                return COMPLETED;
            default:
                return this; // 最终状态或异常状态不变
        }
    }
}
