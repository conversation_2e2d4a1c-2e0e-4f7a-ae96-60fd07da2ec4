package com.demon.giraffe.modules.order.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.order.model.dto.query.OrderMainQuery;
import com.demon.giraffe.modules.order.model.dto.request.OrderCreateSimpleRequest;
import com.demon.giraffe.modules.order.model.dto.response.OrderCreateResponse;
import com.demon.giraffe.modules.order.model.dto.response.OrderListResponse;
import com.demon.giraffe.modules.order.service.OrderMainService;
import com.demon.giraffe.modules.payment.model.dto.response.PaymentResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 订单管理控制器
 */
@RestController
@RequestMapping("/order")
@RequiredArgsConstructor
@Tag(name = "订单管理接口", description = "订单的创建、查询、更新等操作")
@Slf4j
public class OrderMainController {
    private final OrderMainService orderMainService;

    @PostMapping("/create-simple")
    @Operation(
            summary = "创建订单",
            description = "完整的订单创建接口，包含校验、计算、保存和支付准备。\n\n" +
                    "处理流程：\n" +
                    "1. 下单校验：商品合法性、地址合法性、优惠券合法性\n" +
                    "2. 下单计算：金额计算、清洗时间、取送时间、优惠券优惠\n" +
                    "3. 保存状态：创建订单、占用优惠券、设置待支付状态\n" +
                    "4. 返回支付信息：包含支付参数，可直接调起支付\n\n" +
                    "功能特点：\n" +
                    "• 完整的业务校验和计算\n" +
                    "• 自动优惠券占用和超时释放\n" +
                    "• 集成支付创建，返回支付参数\n" +
                    "• 支持主动取消订单"
    )
    @SaCheckRole(value = {"CUSTOMER"}, mode = SaMode.OR)
    public ResultBean<OrderCreateResponse> createSimpleOrder(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "订单创建请求",
                    required = true,
                    content = @Content(schema = @Schema(implementation = OrderCreateSimpleRequest.class))
            )
            @RequestBody @Valid OrderCreateSimpleRequest request) {
        try {
            log.info("开始创建订单，请求参数：{}", request);

            // 调用新的订单创建服务
            OrderCreateResponse response = orderMainService.createOrderWithPayment(request);

            log.info("订单创建成功，订单号：{}，支付金额：{}",
                    response.getOrderNo(), response.getPayAmount());

            return ResultBean.success("订单创建成功", response);
        } catch (Exception e) {
            log.error("订单创建失败：{}", e.getMessage(), e);
            return ResultBean.fail("订单创建失败：" + e.getMessage());
        }
    }

    @PostMapping("/my-list")
    @Operation(
            summary = "获取我的订单列表",
            description = "获取当前登录用户的订单列表，支持分页和条件查询。\n\n" +
                    "查询功能：\n" +
                    "• 自动根据当前登录用户筛选订单\n" +
                    "• 支持按订单状态筛选\n" +
                    "• 支持按支付状态筛选\n" +
                    "• 支持按订单号模糊查询\n" +
                    "• 支持按时间范围查询\n" +
                    "• 支持分页查询\n\n" +
                    "返回信息：\n" +
                    "• 订单基本信息（订单号、状态、金额等）\n" +
                    "• 服务项目列表\n" +
                    "• 操作权限（是否可取消、支付、退款）\n" +
                    "• 时间信息（创建时间、预计时间等）"
    )
    @SaCheckRole(value = {"CUSTOMER"}, mode = SaMode.OR)
    public ResultBean<IPage<OrderListResponse>> getMyOrderList(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "分页查询条件",
                    required = true,
                    content = @Content(schema = @Schema(implementation = BasePageQuery.class))
            )
            @RequestBody @Valid BasePageQuery<OrderMainQuery> pageQuery) {
        try {
            pageQuery.init();

            IPage<OrderListResponse> result = orderMainService.getMyOrderList(pageQuery);

            log.info("获取我的订单列表成功，总记录数：{}", result.getTotal());

            return ResultBean.success("获取订单列表成功", result);
        } catch (Exception e) {
            log.error("获取我的订单列表失败：{}", e.getMessage(), e);
            return ResultBean.fail("获取订单列表失败：" + e.getMessage());
        }
    }

    @PostMapping("/repay/{orderNo}")
    @Operation(summary = "重新支付", description = "为待支付订单重新创建支付")
    @SaCheckRole(value = {"CUSTOMER"}, mode = SaMode.OR)
    public ResultBean<PaymentResponse> repayOrder(
            @Parameter(description = "订单号", required = true)
            @PathVariable String orderNo) {
        try {
            log.info("开始重新支付，订单号：{}", orderNo);

            PaymentResponse result = orderMainService.repayOrder(orderNo);

            log.info("重新支付成功，订单号：{}", orderNo);

            return ResultBean.success("重新支付创建成功", result);
        } catch (Exception e) {
            log.error("重新支付失败，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
            return ResultBean.fail("重新支付失败：" + e.getMessage());
        }
    }

}