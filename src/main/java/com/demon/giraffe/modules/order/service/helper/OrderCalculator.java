package com.demon.giraffe.modules.order.service.helper;

import com.demon.giraffe.modules.order.model.dto.request.*;
import com.demon.giraffe.modules.order.model.entity.*;
import com.demon.giraffe.modules.order.model.enums.DeliveryTypeEnum;
import com.demon.giraffe.modules.order.util.OrderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static com.demon.giraffe.modules.marketing.util.CouponCalculator.calculateDiscountAmount;
import static com.demon.giraffe.modules.order.util.OrderUtil.*;

/**
 * 订单计算器
 * <p>
 * 负责订单相关的所有计算逻辑，包括金额计算、时间计算、优惠券折扣计算等。
 * 采用策略模式设计，确保计算逻辑的准确性和可扩展性。
 * </p>
 *
 * <p>主要功能：</p>
 * <ul>
 *   <li>订单金额计算（基础价格、附加费用、总金额）</li>
 *   <li>优惠券折扣计算</li>
 *   <li>配送时间计算（取件时间、送达时间）</li>
 *   <li>服务处理时间计算</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-07-24
 */
@Slf4j
@Component
public class OrderCalculator {

    /**
     * 执行订单完整计算
     * <p>
     * 按照固定顺序执行所有计算步骤：
     * 1. 计算订单金额和详情
     * 2. 计算优惠券折扣
     * 3. 计算配送时间
     * </p>
     *
     * @param context 订单处理上下文，包含计算所需的所有数据
     */
    public void calculateOrder(OrderContext context) {
        log.info("开始执行订单计算，订单商品数量：{}", context.getRequest().getOrderItems().size());

        // 创建计算结果容器
        OrderCalculationResult calculation = new OrderCalculationResult();

        // 1. 计算订单金额和详情
        OrderDetailEntity orderDetail = calculateOrderAmount(context);
        calculation.setOrderDetail(orderDetail);

        // 2. 计算优惠券折扣
        calculateCouponDiscount(context, calculation);

        // 3. 计算配送时间
        calculateDeliveryTimes(context, calculation);

        // 设置计算结果到上下文
        context.setCalculation(calculation);

        log.info("订单计算完成，总金额：{}，优惠金额：{}，实付金额：{}",
                calculation.getTotalAmount(), calculation.getCouponAmount(), calculation.getPayAmount());
    }

    /**
     * 计算订单金额和详情
     * <p>
     * 构建完整的订单详情，包括服务项目、金额信息、处理时间等。
     * </p>
     *
     * @param context 订单处理上下文
     * @return OrderDetailEntity 订单详情实体
     */
    private OrderDetailEntity calculateOrderAmount(OrderContext context) {
        log.debug("开始计算订单金额和详情");

        // 1. 构建订单服务项目列表
        List<OrderServiceItemEntity> serviceItems = OrderUtil.buildOrderServiceItems(context.getRequest().getOrderItems(), context.getServiceItems());

        // 2. 计算金额信息
        OrderAmountEntity amountInfo = calculateAmountInfo(serviceItems);

        // 3. 计算总处理时间（根据OrderAmountEntity结构计算）
        Integer totalProcessingHours = calculateTotalProcessingHoursFromAmountInfo(amountInfo,
                context.getRequest().getUserOptions());

        // 4. 构建订单详情
        OrderDetailEntity orderDetail = OrderDetailEntity.builder()
                .amountInfo(amountInfo)
                .serviceItems(serviceItems)
                .isPremium(context.getRequest().getUserOptions().getIsPremium())
                .isUrgent(context.getRequest().getUserOptions().getIsUrgent())
                .totalProcessingHours(totalProcessingHours)
                .build();

        log.debug("订单金额和详情计算完成，服务项目数量：{}，总处理时间：{}小时",
                serviceItems.size(), totalProcessingHours);

        return orderDetail;
    }


    private void calculateCouponDiscount(OrderContext context, OrderCalculationResult calculation) {
        BigDecimal couponAmount = BigDecimal.ZERO;
        Long usedCouponId = null;

        if (context.getRequest().getCouponId() != null && context.getCoupon() != null) {
            couponAmount = calculateDiscountAmount(
                    context.getCoupon(),
                    calculation.getOrderDetail().getAmountInfo().getTotalMarketReferencePrice()
            );
            usedCouponId = context.getRequest().getCouponId();
        }

        BigDecimal totalAmount = calculation.getOrderDetail().getAmountInfo().getTotalMarketReferencePrice();

        if (context.getRequest().getUserOptions().getIsUrgent()) {
            totalAmount = totalAmount.add(
                    calculation.getOrderDetail().getAmountInfo().getTotalExpeditedExtraFee());
        }

        if (context.getRequest().getUserOptions().getIsPremium()) {
            totalAmount = totalAmount.add(
                    calculation.getOrderDetail().getAmountInfo().getTotalPremiumWashExtraFee());
        }

        BigDecimal payAmount = totalAmount.subtract(couponAmount).max(BigDecimal.ZERO);

        calculation.setTotalAmount(totalAmount);
        calculation.setCouponAmount(couponAmount);
        calculation.setPayAmount(payAmount);
        calculation.setUsedCouponId(usedCouponId);
    }


    /**
     * 计算配送时间（仅计算上门取件）
     * <p>
     * 根据订单类型计算预计取件时间和送达时间。
     * 目前只支持上门取件服务的时间计算，其他类型不计算时间。
     * </p>
     *
     * @param context     订单处理上下文
     * @param calculation 订单计算结果
     */
    private void calculateDeliveryTimes(OrderContext context, OrderCalculationResult calculation) {
        log.debug("开始计算配送时间，配送类型：{}",
                context.getRequest().getDeliveryInfo().getDeliveryType());

        // 只计算上门取件的时间
        if (isDoorToDoorService(context)) {
            LocalDateTime[] times = calculateDoorToDoorDeliveryTimes(
                    context.getRequest().getDeliveryInfo().getAppointmentTime(),
                    calculation.getOrderDetail().getTotalProcessingHours());

            calculation.setEstimatedPickupTime(times[0]);
            calculation.setEstimatedDeliveryTime(times[1]);

            log.info("上门取件时间计算完成 - 预计取件时间：{}，预计送达时间：{}",
                    times[0], times[1]);
        } else {
            // 非上门服务不设置时间
            calculation.setEstimatedPickupTime(null);
            calculation.setEstimatedDeliveryTime(null);

            log.info("非上门服务，不计算配送时间");
        }
    }

    /**
     * 检查是否为上门服务
     *
     * @param context 订单处理上下文
     * @return true-上门服务，false-其他服务类型
     */
    private boolean isDoorToDoorService(OrderContext context) {
        return context.getRequest().getDeliveryInfo() != null &&
                context.getRequest().getDeliveryInfo().getDeliveryType() == DeliveryTypeEnum.DOOR_TO_DOOR &&
                context.getMemberAddress() != null;
    }


}