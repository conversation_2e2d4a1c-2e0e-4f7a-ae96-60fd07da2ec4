package com.demon.giraffe.modules.order.service.impl;

import com.demon.giraffe.common.domain.dto.request.RegionRequest;
import com.demon.giraffe.common.exception.exception.BusinessException;

import com.demon.giraffe.common.util.CountySupportUtil;
import com.demon.giraffe.modules.marketing.model.entity.Coupon;
import com.demon.giraffe.modules.marketing.model.enums.CouponInapplicableReason;
import com.demon.giraffe.modules.marketing.service.MemberCouponService;
import com.demon.giraffe.modules.order.model.dto.request.*;
import com.demon.giraffe.modules.order.model.dto.response.OrderAddressInfoResponse;
import com.demon.giraffe.modules.order.model.dto.response.OrderCouponInfoResponse;
import com.demon.giraffe.modules.order.model.entity.*;
import com.demon.giraffe.modules.order.service.OrderPreprocessService;
import com.demon.giraffe.modules.order.util.OrderUtil;
import com.demon.giraffe.modules.service.model.entity.ServiceItemEntity;
import com.demon.giraffe.modules.service.service.ServiceItemService;
import com.demon.giraffe.modules.user.model.po.MemberAddressPo;

import com.demon.giraffe.modules.user.repository.MemberAddressRepository;
import com.demon.giraffe.modules.user.repository.MemberDefaultAddressRepository;

import com.demon.giraffe.modules.user.service.impl.MemberRoleServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.demon.giraffe.modules.marketing.util.CouponCalculator.calculateDiscountAmount;
import static com.demon.giraffe.modules.order.util.OrderUtil.*;

/**
 * 订单预处理服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderPreprocessServiceImpl implements OrderPreprocessService {

    private final MemberRoleServiceImpl memberRoleService;
    private final MemberAddressRepository memberAddressRepository;
    private final MemberDefaultAddressRepository memberDefaultAddressRepository;
    private final ServiceItemService serviceItemService;
    private final MemberCouponService memberCouponService;

    @Override
    public OrderAddressInfoResponse getAddressDeliveryInfo(OrderAddressInfoRequest request) {
        // 获取当前用户信息
        Long memberId = memberRoleService.getCurrent().getId();

        // 获取指定地址
        MemberAddressPo address = memberAddressRepository.getById(request.getAddressId());
        if (Objects.isNull(address) || !address.getMemberId().equals(memberId)) {
            throw new BusinessException("无权访问该地址");
        }
        if (!CountySupportUtil.isSupported(address.getAddressCode())) {
            throw new BusinessException("该地址暂不在服务范围内");
        }

        // 检查是否为默认地址
        boolean isDefault = memberDefaultAddressRepository.existsByMemberIdAndAddressId(
                address.getMemberId(), address.getId());


        // 验证服务项目合法性
        Set<Long> serviceItemIds = request.getOrderItems().stream()
                .map(OrderItemDetail::getServiceItemId)
                .collect(Collectors.toSet());

        List<ServiceItemEntity> serviceItems = serviceItemService.batchGetServiceItemDetails(serviceItemIds);
        if (serviceItems.size() != serviceItemIds.size()) {
            throw new BusinessException("订单包含无效的服务项");
        }

        // 构建订单服务项并计算订单金额信息
        List<OrderServiceItemEntity> orderServiceItemEntities =
                OrderUtil.buildOrderServiceItems(request.getOrderItems(), serviceItems);

        OrderAmountEntity amountInfo = calculateAmountInfo(orderServiceItemEntities);

        // 3. 计算总处理时间（根据OrderAmountEntity结构计算）
        Integer totalProcessingHours = calculateTotalProcessingHoursFromAmountInfo(amountInfo,
                request.getUserOptions());


        // 根据时间段枚举获取具体时间（code字段就是小时数）
        int hour = request.getAppointmentTimeSlotEnum().getCode();
        LocalDateTime localDateTime = request.getLocalDate().atTime(hour, 0);


        // 计算配送时间信息（仅读取上门服务进行计算）
        LocalDateTime[] localDateTimes = calculateDoorToDoorDeliveryTimes(localDateTime, totalProcessingHours);

        // 处理区域信息
        RegionRequest regionRequest = new RegionRequest(address.getAddressCode());

        return OrderAddressInfoResponse.builder()
                .addressId(address.getId())
                .contactName(address.getContactName())
                .contactPhone(address.getContactPhone())
                .province(regionRequest.getProvince())
                .city(regionRequest.getCity())
                .district(regionRequest.getDistrict())
                .detailAddress(address.getDetailAddress())
                .isDefault(isDefault)
                .localDateTime(localDateTimes[1])
                .build();
    }

    @Override
    public OrderCouponInfoResponse matchCouponsForOrder(OrderCouponMatchRequest request) {
        OrderCouponInfoResponse response = new OrderCouponInfoResponse();

        // 获取当前用户ID
        Long memberId = memberRoleService.getCurrent().getId();

        // 验证服务项目合法性
        Set<Long> serviceItemIds = request.getOrderItems().stream()
                .map(OrderItemDetail::getServiceItemId)
                .collect(Collectors.toSet());

        List<ServiceItemEntity> serviceItems = serviceItemService.batchGetServiceItemDetails(serviceItemIds);
        if (serviceItems.size() != serviceItemIds.size()) {
            throw new BusinessException("订单包含无效的服务项");
        }

        // 构建订单服务项并计算订单金额信息
        List<OrderServiceItemEntity> orderServiceItemEntities =
                OrderUtil.buildOrderServiceItems(request.getOrderItems(), serviceItems);

        OrderAmountEntity amountInfo = calculateAmountInfo(orderServiceItemEntities);
        BigDecimal totalMarketReferencePrice = amountInfo.getTotalMarketReferencePrice();

        // 查询用户可用优惠券
        List<Coupon> coupons = memberCouponService.listAvailableCouponEntities(memberId);

        List<OrderCouponInfoResponse.ApplicableCouponInfo> applicableCouponInfos = new ArrayList<>();
        List<OrderCouponInfoResponse.InapplicableCouponInfo> inapplicableCouponInfos = new ArrayList<>();

        BigDecimal maxDiscountAmount = BigDecimal.ZERO;
        Long recommendedCouponId = null;

        for (Coupon coupon : coupons) {
            CouponInapplicableReason reason = coupon.checkApplicable(totalMarketReferencePrice);

            if (reason == CouponInapplicableReason.APPLICABLE) {
                // 构造可用优惠券信息
                BigDecimal discountAmount = calculateDiscountAmount(coupon, totalMarketReferencePrice);
                BigDecimal finalPayAmount = totalMarketReferencePrice.subtract(discountAmount);

                OrderCouponInfoResponse.ApplicableCouponInfo info =
                        OrderCouponInfoResponse.ApplicableCouponInfo.builder()
                                .couponId(coupon.getId())
                                .name(coupon.getName())
                                .type(coupon.getType())
                                .discountType(coupon.getDiscountType())
                                .discountValue(coupon.getDiscountValue())
                                .minAmount(coupon.getMinAmount())
                                .maxDiscount(coupon.getMaxDiscount())
                                .estimatedDiscount(discountAmount)
                                .finalPayAmount(finalPayAmount.max(BigDecimal.ZERO))
                                .conditionDescription(coupon.generateConditionDescription())
                                .expireTime(coupon.getExpireTime())
                                .build();

                applicableCouponInfos.add(info);

                // 推荐最大优惠券
                if (discountAmount.compareTo(maxDiscountAmount) > 0) {
                    maxDiscountAmount = discountAmount;
                    recommendedCouponId = coupon.getId();
                }

            } else {
                // 构造不可用优惠券信息
                OrderCouponInfoResponse.InapplicableCouponInfo info =
                        OrderCouponInfoResponse.InapplicableCouponInfo.builder()
                                .couponId(coupon.getId())
                                .inapplicableReason(reason.getDescription())
                                .expireTime(coupon.getExpireTime())
                                .build();

                inapplicableCouponInfos.add(info);
            }
        }

        response.setOrderAmountEntity(amountInfo);
        response.setAvailableCoupons(applicableCouponInfos);
        response.setUnavailableCoupons(inapplicableCouponInfos);
        response.setRecommendedCouponId(recommendedCouponId);
        return response;
    }


}
