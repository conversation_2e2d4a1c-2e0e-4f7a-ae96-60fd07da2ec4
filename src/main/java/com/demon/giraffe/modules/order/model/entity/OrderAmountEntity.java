package com.demon.giraffe.modules.order.model.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订单金额信息Entity")
public class OrderAmountEntity {

    @Schema(description = "标准洗涤总价格")
    private BigDecimal totalStandardWashPrice;

    @Schema(description = "精洗额外费用总计")
    private BigDecimal totalPremiumWashExtraFee;

    @Schema(description = "精洗额外时间总计(小时)")
    private Integer totalPremiumWashExtraHours;

    @Schema(description = "服务定价总价格")
    private BigDecimal totalMarketReferencePrice;

    @Schema(description = "标准处理总时长(小时)")
    private Integer totalStandardProcessingHours;

    @Schema(description = "加急处理总时长(小时)")
    private Integer totalExpeditedProcessingHours;

    @Schema(description = "加急额外费用总计")
    private BigDecimal totalExpeditedExtraFee;
}
