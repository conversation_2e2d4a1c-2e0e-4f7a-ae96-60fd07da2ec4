package com.demon.giraffe.modules.order.model.dto.status;

import com.demon.giraffe.modules.order.model.dto.status.base.OrderStatusBusinessData;
import com.demon.giraffe.modules.order.model.enums.OrderStatusTrackEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 工厂清洗中状态业务数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "工厂清洗中状态业务数据")
public class FactoryWashingData implements OrderStatusBusinessData {
    
    @Schema(description = "开始清洗时间")
    private LocalDateTime washStartTime;
    
    @Schema(description = "负责工人ID")
    private Long workerId;
    
    @Schema(description = "工人姓名")
    private String workerName;
    
    @Schema(description = "清洗工序列表")
    private List<String> washProcesses;
    
    @Schema(description = "当前工序")
    private String currentProcess;
    
    @Schema(description = "工序进度百分比")
    private Integer progressPercentage;
    
    @Schema(description = "预计完成时间")
    private LocalDateTime estimatedCompletionTime;
    
    @Schema(description = "使用的清洗剂")
    private String detergentUsed;
    
    @Schema(description = "清洗温度")
    private String washTemperature;
    
    @Schema(description = "特殊处理记录")
    private String specialTreatment;
    
    @Schema(description = "过程照片URLs")
    private List<String> processPhotos;
    
    @Override
    public OrderStatusTrackEnum getStatusType() {
        return OrderStatusTrackEnum.FACTORY_WASHING;
    }
}
