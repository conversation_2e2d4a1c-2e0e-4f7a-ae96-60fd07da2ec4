package com.demon.giraffe.modules.order.service;

import com.demon.giraffe.modules.order.model.dto.request.OrderTaskUpdateRequest;
import com.demon.giraffe.modules.order.model.dto.response.OrderTaskDetailResponse;
import com.demon.giraffe.modules.order.model.enums.OrderTaskStatusEnum;


import java.util.List;

/**
 * 订单任务服务接口
 * 负责管理订单在整个业务流程中的任务状态和进度
 */
public interface OrderTaskService {

    /**
     * 工厂扫码接收订单
     * 
     * @param qrCodeId 二维码ID
     * @param factoryId 工厂ID
     * @param operatorId 操作员ID
     * @return 是否接收成功
     */
    Boolean factoryReceiveOrder(Long qrCodeId, Long factoryId, Long operatorId);

    /**
     * 工厂开始拆包
     * 
     * @param taskId 任务ID
     * @param operatorId 操作员ID
     * @return 是否成功
     */
    Boolean startUnpacking(Long taskId, Long operatorId);

    /**
     * 完成拆包并生成衣物二维码
     * 
     * @param taskId 任务ID
     * @param operatorId 操作员ID
     * @param clothingItems 衣物清单
     * @return 生成的衣物二维码ID列表
     */
    List<Long> completeUnpackingAndGenerateClothingQR(Long taskId, Long operatorId, List<String> clothingItems);

    /**
     * 开始清洗处理
     * 
     * @param taskId 任务ID
     * @param operatorId 操作员ID
     * @return 是否成功
     */
    Boolean startProcessing(Long taskId, Long operatorId);

    /**
     * 完成清洗处理
     * 
     * @param taskId 任务ID
     * @param operatorId 操作员ID
     * @return 是否成功
     */
    Boolean completeProcessing(Long taskId, Long operatorId);

    /**
     * 开始质检
     * 
     * @param taskId 任务ID
     * @param qualityCheckerId 质检员ID
     * @return 是否成功
     */
    Boolean startQualityCheck(Long taskId, Long qualityCheckerId);

    /**
     * 完成质检
     * 
     * @param taskId 任务ID
     * @param qualityCheckerId 质检员ID
     * @param qualityPassed 质检是否通过
     * @param qualityNotes 质检备注
     * @return 是否成功
     */
    Boolean completeQualityCheck(Long taskId, Long qualityCheckerId, Boolean qualityPassed, String qualityNotes);

    /**
     * 完成打包并生成出厂二维码
     * 
     * @param taskId 任务ID
     * @param operatorId 操作员ID
     * @return 出厂二维码ID
     */
    Long completePackagingAndGenerateExitQR(Long taskId, Long operatorId);

    /**
     * 分配送货快递员
     * 
     * @param taskId 任务ID
     * @return 是否分配成功
     */
    Boolean assignDeliveryCourier(Long taskId);

    /**
     * 手动分配送货快递员
     * 
     * @param taskId 任务ID
     * @param courierId 快递员ID
     * @return 是否分配成功
     */
    Boolean assignDeliveryCourier(Long taskId, Long courierId);

    /**
     * 送货员扫码取件
     * 
     * @param qrCodeId 出厂二维码ID
     * @param courierId 快递员ID
     * @return 是否成功
     */
    Boolean deliveryCourierPickup(Long qrCodeId, Long courierId);

    /**
     * 开始配送
     * 
     * @param taskId 任务ID
     * @param courierId 快递员ID
     * @return 是否成功
     */
    Boolean startDelivery(Long taskId, Long courierId);

    /**
     * 完成配送
     * 
     * @param taskId 任务ID
     * @param courierId 快递员ID
     * @param deliveryType 配送方式：1-上门 2-智能柜
     * @param cabinetId 智能柜ID（如果使用智能柜）
     * @param cellNo 格口号（如果使用智能柜）
     * @return 是否成功
     */
    Boolean completeDelivery(Long taskId, Long courierId, Integer deliveryType, Long cabinetId, String cellNo);

    /**
     * 处理任务异常
     * 
     * @param taskId 任务ID
     * @param exceptionCode 异常代码
     * @param exceptionReason 异常原因
     * @param operatorId 操作员ID
     * @return 是否成功
     */
    Boolean handleTaskException(Long taskId, String exceptionCode, String exceptionReason, Long operatorId);

    /**
     * 取消任务
     * 
     * @param taskId 任务ID
     * @param reason 取消原因
     * @param operatorId 操作员ID
     * @return 是否成功
     */
    Boolean cancelTask(Long taskId, String reason, Long operatorId);

    /**
     * 根据订单ID获取任务详情
     * 
     * @param orderId 订单ID
     * @return 任务详情
     */
    OrderTaskDetailResponse getTaskByOrderId(Long orderId);


    /**
     * 获取快递员的待处理任务列表
     * 
     * @param courierId 快递员ID
     * @return 任务列表
     */
    List<OrderTaskDetailResponse> getPendingTasksByCourier(Long courierId);

    /**
     * 获取工厂的待处理任务列表
     * 
     * @param factoryId 工厂ID
     * @return 任务列表
     */
    List<OrderTaskDetailResponse> getPendingTasksByFactory(Long factoryId);

    /**
     * 批量更新任务状态
     * 
     * @param request 批量更新请求
     * @return 更新成功的任务数量
     */
    Integer batchUpdateTaskStatus(OrderTaskUpdateRequest request);
}
