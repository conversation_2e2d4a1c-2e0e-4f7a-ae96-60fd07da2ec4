package com.demon.giraffe.modules.order.service;

import com.demon.giraffe.modules.order.model.dto.response.RecentOrderResponse;

/**
 * 最近下单服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface RecentOrderService {

    /**
     * 获取最近5次下单信息
     * 直接从数据库查询最近的已支付订单
     * 返回格式：手机号（遮挡）+ "购买了" + 服务名
     * 如果订单包含多个服务，只显示第一个服务名
     *
     * @return 最近下单响应
     */
    RecentOrderResponse getRecentOrders();
}
