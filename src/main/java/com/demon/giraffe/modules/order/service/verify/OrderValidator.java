package com.demon.giraffe.modules.order.service.verify;

import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.common.util.CountySupportUtil;
import com.demon.giraffe.modules.marketing.model.entity.Coupon;
import com.demon.giraffe.modules.marketing.model.enums.CouponStatusEnum;
import com.demon.giraffe.modules.order.model.dto.request.OrderDeliveryRequest;
import com.demon.giraffe.modules.order.model.dto.request.OrderItemDetail;
import com.demon.giraffe.modules.order.model.entity.OrderContext;
import com.demon.giraffe.modules.order.model.enums.DeliveryTypeEnum;
import com.demon.giraffe.modules.service.model.entity.ServiceItemEntity;
import com.demon.giraffe.modules.service.model.enums.CommonStatusEnum;
import com.demon.giraffe.modules.user.model.dto.response.MemberIdentityDetailResponse;
import com.demon.giraffe.modules.user.model.po.MemberAddressPo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单验证器
 * <p>
 * 负责订单创建前的所有业务规则验证，确保订单数据的合法性和完整性。
 * 采用责任链模式，按照业务优先级进行验证。
 * </p>
 *
 * <p>验证范围：</p>
 * <ul>
 *   <li>服务项目有效性验证</li>
 *   <li>配送信息完整性验证</li>
 *   <li>地址权限和服务范围验证</li>
 *   <li>优惠券可用性验证</li>
 *   <li>业务规则合规性验证</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-07-24
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class OrderValidator {

    /**
     * 执行订单上下文完整验证
     * <p>
     * 按照业务优先级顺序执行所有验证规则：
     * 1. 服务项目验证（基础数据）
     * 2. 配送信息验证（业务规则）
     * 3. 优惠券验证（优惠规则）
     * </p>
     *
     * @param context 订单处理上下文，包含所有需要验证的数据
     * @throws BusinessException 验证失败时抛出业务异常
     */
    public void validateOrderContext(OrderContext context) {
        log.info("开始执行订单验证，会员ID：{}，商品数量：{}",
                context.getMemberInfo().getMemberId(),
                context.getRequest().getOrderItems().size());

        try {
            // 1. 验证服务项目（优先级最高）
            validateServiceItems(context.getServiceItems(), context.getRequest().getOrderItems());

            // 2. 验证配送信息
            validateDeliveryInfo(context.getRequest().getDeliveryInfo(),
                    context.getMemberAddress(),
                    context.getMemberInfo());

            // 3. 验证优惠券（如果使用）
            validateCouponIfPresent(context);

            log.info("订单验证通过，会员ID：{}", context.getMemberInfo().getMemberId());

        } catch (BusinessException e) {
            log.warn("订单验证失败，会员ID：{}，原因：{}",
                    context.getMemberInfo().getMemberId(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("订单验证异常，会员ID：{}",
                    context.getMemberInfo().getMemberId(), e);
            throw new BusinessException("订单验证失败：" + e.getMessage());
        }
    }
    
    private void validateServiceItems(List<ServiceItemEntity> serviceItems, List<OrderItemDetail> orderItems) {
        if (orderItems == null || orderItems.isEmpty()) {
            throw new BusinessException("订单商品不能为空");
        }

        Set<Long> requestItemIds = orderItems.stream()
                .map(OrderItemDetail::getServiceItemId)
                .collect(Collectors.toSet());

        if (serviceItems.size() != requestItemIds.size()) {
            throw new BusinessException("订单包含无效或已停用的服务项目");
        }

        for (ServiceItemEntity item : serviceItems) {
            if (CommonStatusEnum.DISABLED.equals(item.getStatus())) {
                throw new BusinessException("服务项目【" + item.getName() + "】已停用");
            }
        }
    }
    
    private void validateDeliveryInfo(OrderDeliveryRequest deliveryInfo, 
                                   MemberAddressPo address, 
                                   MemberIdentityDetailResponse memberInfo) {
        if (deliveryInfo == null) {
            throw new BusinessException("配送信息不能为空");
        }

        switch (deliveryInfo.getDeliveryType()) {
            case DOOR_TO_DOOR:
                validateHomeDeliveryAddress(deliveryInfo, address, memberInfo);
                break;
            case CABINET:
                validateCabinetDelivery();
                break;
            default:
                throw new BusinessException("不支持的配送类型");
        }
    }
    
    private void validateHomeDeliveryAddress(OrderDeliveryRequest deliveryInfo, 
                                          MemberAddressPo memberAddress, 
                                          MemberIdentityDetailResponse memberInfo) {
        if (deliveryInfo.getAddressId() == null) {
            throw new BusinessException("上门服务必须选择地址");
        }

        if (deliveryInfo.getAppointmentTime() == null) {
            throw new BusinessException("上门服务必须选择预约时间");
        }

        if (memberAddress == null) {
            throw new BusinessException("配送地址不存在");
        }

        if (!memberAddress.getMemberId().equals(memberInfo.getMemberId())) {
            throw new BusinessException("配送地址不属于当前用户");
        }

        if (!CountySupportUtil.isSupported(memberAddress.getAddressCode())) {
            throw new BusinessException("该地址暂不在服务范围内");
        }
    }
    
    private void validateCabinetDelivery() {
        log.info("柜子服务校验通过");
    }
    
    private void validateCouponIfPresent(OrderContext context) {
        if (context.getRequest().getCouponId() != null) {
            validateCoupon(context.getRequest().getCouponId(), 
                         context.getCoupon(), 
                         context.getMemberInfo());
        }
    }
    
    private void validateCoupon(Long couponId, Coupon coupon, MemberIdentityDetailResponse memberInfo) {
        if (coupon == null) {
            throw new BusinessException("优惠券不存在");
        }

        if (!coupon.getMemberId().equals(memberInfo.getMemberId())) {
            throw new BusinessException("优惠券不属于当前用户");
        }


        if (Objects.nonNull(coupon.getOrderId())&& coupon.getOrderId() != 0) {
            throw new BusinessException("优惠券已使用");
        }

        if (coupon.getExpireTime() != null && coupon.getExpireTime().isBefore(LocalDateTime.now())) {
            throw new BusinessException("优惠券已过期");
        }

        log.info("优惠券校验通过：{}", couponId);
    }
}