package com.demon.giraffe.modules.order.model.dto.status;

import com.demon.giraffe.modules.order.model.dto.status.base.OrderStatusBusinessData;
import com.demon.giraffe.modules.order.model.enums.OrderStatusTrackEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 待取件(柜子)状态业务数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "待取件(柜子)状态业务数据")
public class PendingPickupFromCabinetData implements OrderStatusBusinessData {
    
    @Schema(description = "柜子ID")
    private Long cabinetId;
    
    @Schema(description = "柜子名称")
    private String cabinetName;
    
    @Schema(description = "柜子地址")
    private String cabinetAddress;
    
    @Schema(description = "格口号")
    private String compartmentNumber;
    
    @Schema(description = "取件码")
    private String pickupCode;
    
    @Schema(description = "存放时间")
    private LocalDateTime storedTime;
    
    @Schema(description = "取件截止时间")
    private LocalDateTime pickupDeadline;
    
    @Schema(description = "已发送通知次数")
    private Integer notificationCount;
    
    @Schema(description = "最后通知时间")
    private LocalDateTime lastNotificationTime;
    
    @Schema(description = "是否已超时")
    private Boolean isOverdue;
    
    @Override
    public OrderStatusTrackEnum getStatusType() {
        return OrderStatusTrackEnum.PENDING_PICKUP_FROM_CABINET;
    }
}
