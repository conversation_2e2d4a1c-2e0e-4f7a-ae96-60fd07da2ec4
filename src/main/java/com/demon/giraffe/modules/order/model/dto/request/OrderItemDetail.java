package com.demon.giraffe.modules.order.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 订单项详情（服务项）
 */
@Data
@Schema(description = "订单项详情（服务项）")
public class OrderItemDetail {

    @NotNull(message = "服务项ID不能为空")
    @Schema(description = "服务项ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long serviceItemId;

    @NotNull(message = "数量不能为空")
    @Schema(description = "数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer quantity;

}