package com.demon.giraffe.modules.order.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * 预约上门时间枚举（按小时段划分）
 */
@Getter
@Schema(description = "预约上门时间枚举")
public enum AppointmentTimeSlotEnum implements IEnum<Integer> {

    @Schema(description = "08:00-09:00")
    H08(8, "08:00-09:00"),

    @Schema(description = "09:00-10:00")
    H09(9, "09:00-10:00"),

    @Schema(description = "10:00-11:00")
    H10(10, "10:00-11:00"),

    @Schema(description = "11:00-12:00")
    H11(11, "11:00-12:00"),

    @Schema(description = "12:00-13:00")
    H12(12, "12:00-13:00"),

    @Schema(description = "13:00-14:00")
    H13(13, "13:00-14:00"),

    @Schema(description = "14:00-15:00")
    H14(14, "14:00-15:00"),

    @Schema(description = "15:00-16:00")
    H15(15, "15:00-16:00"),

    @Schema(description = "16:00-17:00")
    H16(16, "16:00-17:00"),

    @Schema(description = "17:00-18:00")
    H17(17, "17:00-18:00"),

    @Schema(description = "18:00-19:00")
    H18(18, "18:00-19:00"),

    @Schema(description = "19:00-20:00")
    H19(19, "19:00-20:00"),

    @Schema(description = "20:00-21:00")
    H20(20, "20:00-21:00"),

    @Schema(description = "21:00-22:00")
    H21(21, "21:00-22:00"),

    @Schema(description = "22:00-23:00")
    H22(22, "22:00-23:00");

    @EnumValue
    private final int code;

    private final String desc;

    AppointmentTimeSlotEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getValue() {
        return code;
    }
}
