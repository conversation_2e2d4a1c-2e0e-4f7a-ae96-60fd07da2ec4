package com.demon.giraffe.modules.order.service.helper;

import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.common.util.CodeGeneratorUtil;
import com.demon.giraffe.modules.marketing.service.MemberCouponService;
import com.demon.giraffe.modules.order.model.dto.request.OrderDeliveryRequest;
import com.demon.giraffe.modules.order.model.dto.request.UserOptionRequest;
import com.demon.giraffe.modules.order.model.entity.*;
import com.demon.giraffe.modules.order.model.enums.*;
import com.demon.giraffe.modules.order.model.po.OrderMainPo;
import com.demon.giraffe.modules.order.repository.OrderMainRepository;
import com.demon.giraffe.modules.service.model.enums.CleaningTypeEnum;
import com.demon.giraffe.modules.user.model.po.MemberAddressPo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单持久化服务
 * <p>
 * 负责订单数据的持久化操作，包括订单主表保存、优惠券占用等。
 * 确保数据的一致性和完整性，处理持久化过程中的异常情况。
 * </p>
 *
 * <p>主要职责：</p>
 * <ul>
 *   <li>订单主表数据持久化</li>
 *   <li>优惠券占用处理</li>
 *   <li>订单状态初始化</li>
 *   <li>持久化异常处理和回滚</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-07-24
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class OrderPersistenceService {

    /**
     * 订单主表仓储
     * 用于订单数据的持久化操作
     */
    private final OrderMainRepository orderMainRepository;

    /**
     * 会员优惠券服务
     * 用于优惠券的占用和释放操作
     */
    private final MemberCouponService couponService;

    /**
     * 代码生成工具
     * 用于生成订单号等业务编码
     */
    private final CodeGeneratorUtil codeGeneratorUtil;

    /**
     * 执行订单持久化操作
     * <p>
     * 按照事务要求执行订单数据持久化，包括：
     * 1. 创建订单主表记录
     * 2. 保存订单到数据库
     * 3. 占用优惠券（如果使用）
     * </p>
     *
     * @param context 订单处理上下文，包含订单创建所需的所有数据
     * @throws BusinessException 持久化过程中的业务异常
     */
    @Transactional(rollbackFor = Exception.class)
    public void persistOrder(OrderContext context) {
        log.info("开始执行订单持久化，会员ID：{}", context.getMemberInfo().getMemberId());

        try {
            // 1. 创建订单主表对象
            OrderMainPo order = createOrderPo(context);

            // 2. 保存订单到数据库
            OrderMainPo savedOrder = orderMainRepository.save(order);
            log.info("订单主表保存成功，订单ID：{}，订单号：{}",
                    savedOrder.getId(), savedOrder.getOrderNo());

            // 3. 设置保存后的订单到上下文
            context.setOrder(savedOrder);

            // 4. 处理优惠券占用
            occupyCouponIfUsed(context);

            log.info("订单持久化完成，订单ID：{}", savedOrder.getId());

        } catch (Exception e) {
            log.error("订单持久化失败，会员ID：{}", context.getMemberInfo().getMemberId(), e);
            throw new BusinessException("订单保存失败：" + e.getMessage());
        }
    }
    
    /**
     * 创建订单PO对象
     * 根据订单上下文信息创建完整的订单主表对象
     *
     * @param context 订单上下文
     * @return 订单PO对象
     */
    private OrderMainPo createOrderPo(OrderContext context) {
        OrderMainPo orderPo = new OrderMainPo();

        // 1. 生成业务信息丰富的订单编码
        Long couponId = context.getRequest().getCouponId();
        Boolean isPremium = context.getRequest().getUserOptions().getIsPremium();
        Boolean isUrgent = context.getRequest().getUserOptions().getIsUrgent();
        DeliveryTypeEnum deliveryType = context.getRequest().getDeliveryInfo().getDeliveryType();

        orderPo.setOrderNo(codeGeneratorUtil.generateOrderCode(couponId, isPremium, isUrgent, deliveryType));
        orderPo.setMemberId(context.getMemberInfo().getMemberId());

        // 2. 订单状态
        orderPo.setPayStatus(PayStatusEnum.UNPAID);
        orderPo.setOrderStatus(OrderStatusEnum.WAIT_PAY);

        // 3. 金额信息
        orderPo.setTotalAmount(context.getCalculation().getTotalAmount());
        orderPo.setCouponAmount(context.getCalculation().getCouponAmount());
        orderPo.setPayAmount(context.getCalculation().getPayAmount());

        // 4. 服务类型和选项
        setCleaningType(orderPo, context.getRequest().getUserOptions());
        setUserOptions(orderPo, context.getRequest().getUserOptions());

        // 5. 配送信息
        setDeliveryInfo(orderPo, context.getRequest().getDeliveryInfo(), context.getMemberAddress());

        // 6. 时间信息
        orderPo.setEstimatedPickupTime(context.getCalculation().getEstimatedPickupTime());
        orderPo.setEstimatedDeliveryTime(context.getCalculation().getEstimatedDeliveryTime());

        // 7. 订单详情和描述
        OrderDetailEntity orderDetail = context.getCalculation().getOrderDetail();
        orderPo.setOrderDetail(orderDetail);
        orderPo.setDescription(formatOrderServiceItems(orderDetail.getServiceItems()));

        log.info("创建订单PO完成，订单号：{}，会员ID：{}，支付金额：{}",
                orderPo.getOrderNo(), orderPo.getMemberId(), orderPo.getPayAmount());

        return orderPo;
    }
    
    private void setCleaningType(OrderMainPo orderPo, UserOptionRequest userOptions) {
        orderPo.setCleaningType(userOptions.getIsPremium() ? 
            CleaningTypeEnum.FINE : CleaningTypeEnum.STANDARD);
        orderPo.setIsUrgent(userOptions.getIsUrgent());
    }
    
    private void setDeliveryInfo(OrderMainPo orderPo, 
                               OrderDeliveryRequest deliveryInfo, 
                               MemberAddressPo memberAddress) {
        orderPo.setPickupType(deliveryInfo.getDeliveryType());
        orderPo.setDeliveryType(deliveryInfo.getDeliveryType());

        if (deliveryInfo.getDeliveryType() == DeliveryTypeEnum.DOOR_TO_DOOR && memberAddress != null) {
            orderPo.setPickupAddressId(memberAddress.getId());
            orderPo.setDeliveryAddressId(memberAddress.getId());
        }
    }
    
    private void setUserOptions(OrderMainPo orderPo, UserOptionRequest userOptions) {
        if (userOptions != null) {
            orderPo.setRemark(userOptions.getRemark());
            if (userOptions.getPhotoUrls() != null && !userOptions.getPhotoUrls().isEmpty()) {
                orderPo.setItemImages(userOptions.getPhotoUrls());
            }
        }
    }
    
    private String formatOrderServiceItems(List<OrderServiceItemEntity> serviceItems) {
        if (serviceItems == null || serviceItems.isEmpty()) {
            return "【订单服务项】\n无服务记录";
        }

        return serviceItems.stream()
                .map(item -> String.format("%-10s × %d", 
                    item.getServiceItemEntity().getName(), 
                    item.getQuantity()))
                .collect(Collectors.joining("\n", "【订单服务项】\n", ""));
    }
    
    private void occupyCouponIfUsed(OrderContext context) {
        if (context.getCalculation().getUsedCouponId() != null) {
            try {
                boolean success = couponService.useCoupon(
                    context.getCalculation().getUsedCouponId(), 
                    context.getOrder().getId());
                
                if (!success) {
                    throw new BusinessException("优惠券占用失败");
                }
                log.info("优惠券占用成功，优惠券ID：{}，订单ID：{}", 
                    context.getCalculation().getUsedCouponId(), 
                    context.getOrder().getId());
            } catch (Exception e) {
                log.error("优惠券占用失败，优惠券ID：{}，订单ID：{}", 
                    context.getCalculation().getUsedCouponId(), 
                    context.getOrder().getId(), e);
                throw new BusinessException("优惠券占用失败：" + e.getMessage());
            }
        }
    }
}