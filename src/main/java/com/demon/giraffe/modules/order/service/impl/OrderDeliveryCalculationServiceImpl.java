package com.demon.giraffe.modules.order.service.impl;

import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.modules.cabinet.model.entity.SmartCabinet;
import com.demon.giraffe.modules.cabinet.service.SmartCabinetService;
import com.demon.giraffe.modules.common.model.entity.DistanceWrapper;
import com.demon.giraffe.modules.common.service.LocationService;
import com.demon.giraffe.modules.laundry.model.po.FactoryInfoPo;
import com.demon.giraffe.modules.laundry.repository.FactoryInfoRepository;
import com.demon.giraffe.modules.order.model.vo.DeliveryAddressVO;
import com.demon.giraffe.modules.order.model.vo.DeliveryInfoVO;
import com.demon.giraffe.modules.order.service.OrderDeliveryCalculationService;
import com.demon.giraffe.modules.user.model.po.MemberAddressPo;
import com.demon.giraffe.modules.user.model.po.RegionInvestorPo;
import com.demon.giraffe.modules.user.repository.MemberAddressRepository;
import com.demon.giraffe.modules.user.repository.RegionInvestorRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单配送计算服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderDeliveryCalculationServiceImpl implements OrderDeliveryCalculationService {

    private final MemberAddressRepository memberAddressRepository;
    private final SmartCabinetService smartCabinetService;
    private final RegionInvestorRepository regionInvestorRepository;
    private final FactoryInfoRepository factoryInfoRepository;
    private final LocationService locationService;

    // 配送时间计算常量
    private static final int BASE_DELIVERY_HOURS = 3; // 基础配送时间3小时
    private static final int URGENT_REDUCTION_PERCENT = 20; // 加急减少20%时间
    private static final double MAX_DISTANCE_KM = 50.0; // 最大服务距离50公里


    @Override
    public DeliveryInfoVO calculateDeliveryInfo(Long memberId, Boolean isUrgent) {
        // 1. 获取当前登录用户的所有地址
        List<MemberAddressPo> addresses = memberAddressRepository.findAllByMemberId(memberId);
        
        if (addresses.isEmpty()) {
            log.warn("用户[{}]没有配置地址信息", memberId);
            return createEmptyDeliveryInfo();
        }

        return calculateDeliveryInfoByAddresses(addresses, isUrgent);
    }

    @Override
    public DeliveryInfoVO calculateDeliveryInfoByAddresses(List<MemberAddressPo> addresses, Boolean isUrgent) {
        DeliveryInfoVO deliveryInfoVO = new DeliveryInfoVO();
        List<DeliveryAddressVO> deliveryAddressVOS = new ArrayList<>();

        int maxDeliveryHours = 0;
        int urgentDeliveryTime = 0;

        for (MemberAddressPo address : addresses) {
            try {
                // 2. 检查地址是否在服务范围内
                if (!isAddressSupported(address)) {
                    log.info("地址[{}]不在服务范围内，跳过", address.getId());
                    continue;
                }

                // 3. 根据地址定位最近的柜子
                SmartCabinet nearestCabinet = findNearestCabinetForAddress(address);
                if (nearestCabinet == null) {
                    log.info("地址[{}]附近没有可用柜子，跳过", address.getId());
                    continue;
                }

                // 4. 计算用户地址到柜子的时间
                Integer userToBoxTime = calculateUserToBoxTime(address, nearestCabinet);

                // 5. 计算柜子到工厂的时间
                Integer boxToFactoryTime = calculateCabinetToFactoryTime(nearestCabinet);

                // 6. 计算总配送时间
                Integer totalTime = calculateTotalDeliveryTime(userToBoxTime, boxToFactoryTime, isUrgent);

                // 7. 创建地址VO
                DeliveryAddressVO addressVO = createDeliveryAddressVO(address, totalTime);
                deliveryAddressVOS.add(addressVO);

                // 8. 记录最长时间
                maxDeliveryHours = Math.max(maxDeliveryHours, totalTime);

            } catch (Exception e) {
                log.error("计算地址[{}]配送信息失败", address.getId(), e);
                // 继续处理其他地址
            }
        }

        // 9. 计算加急时间
        urgentDeliveryTime = calculateUrgentTime(maxDeliveryHours);

        // 10. 填充结果
        deliveryInfoVO.setDeliveryAddressVOS(deliveryAddressVOS);
        deliveryInfoVO.setUrgentDeliveryHours(maxDeliveryHours);
        deliveryInfoVO.setUrgentDeliveryTime(urgentDeliveryTime);

        return deliveryInfoVO;
    }

    @Override
    public SmartCabinet findNearestCabinetForAddress(MemberAddressPo address) {
        try {
            // 1. 验证地址有经纬度信息
            if (address.getLongitude() == null || address.getLatitude() == null) {
                log.warn("地址[{}]缺少经纬度信息", address.getId());
                return null;
            }

            // 2. 获取该区域所有可用柜子
            CountyEnum addressCode = address.getAddressCode();
            List<SmartCabinet> cabinets = smartCabinetService.getAvailableCabinetsByRegion(addressCode);
            
            if (cabinets.isEmpty()) {
                log.warn("区域[{}]没有可用柜子", addressCode);
                return null;
            }

            // 3. 计算距离并找到最近的柜子
            List<DistanceWrapper<SmartCabinet>> distanceWrappers = locationService.calculateDistancesAndFilterGeneric(
                    address.getLongitude(),
                    address.getLatitude(),
                    cabinets,
                    SmartCabinet::getLongitude,
                    SmartCabinet::getLatitude,
                    MAX_DISTANCE_KM * 1000 // 转换为米
            );

            return distanceWrappers.isEmpty() ? null : distanceWrappers.get(0).getPoint();

        } catch (Exception e) {
            log.error("查找地址[{}]最近柜子失败", address.getId(), e);
            return null;
        }
    }

    @Override
    public Integer calculateCabinetToFactoryTime(SmartCabinet cabinet) {
        try {
            // 1. 根据柜子获取投资人信息
            RegionInvestorPo investor = regionInvestorRepository.getByRegionAndCabinet(
                    cabinet.getAddressCode(), cabinet.getRegionInvestorId());
            
            if (investor == null) {
                log.warn("柜子[{}]没有找到对应的投资人", cabinet.getId());
                return BASE_DELIVERY_HOURS;
            }

            // 2. 根据投资人获取绑定的工厂
            FactoryInfoPo factory = factoryInfoRepository.getById(investor.getFactoryId());
            
            if (factory == null) {
                log.warn("投资人[{}]没有绑定工厂", investor.getId());
                return BASE_DELIVERY_HOURS;
            }

            // 3. 计算柜子到工厂的距离
            if (factory.getLongitude() == null || factory.getLatitude() == null ||
                cabinet.getLongitude() == null || cabinet.getLatitude() == null) {
                log.warn("柜子[{}]或工厂[{}]缺少经纬度信息", cabinet.getId(), factory.getId());
                return BASE_DELIVERY_HOURS;
            }

            double distance = locationService.calculateDistance(
                    cabinet.getLongitude(), cabinet.getLatitude(),
                    factory.getLongitude(), factory.getLatitude()
            );

            // 4. 根据距离计算时间：每10公里1小时，最少1小时
            int transportTime = Math.max(1, (int) Math.ceil(distance / 1000 / 10));
            
            // 5. 加上工厂处理时间
            return transportTime;

        } catch (Exception e) {
            log.error("计算柜子[{}]到工厂时间失败", cabinet.getId(), e);
            return BASE_DELIVERY_HOURS;
        }
    }

    @Override
    public Integer calculateTotalDeliveryTime(Integer userToBoxTime, Integer boxToFactoryTime, Boolean isUrgent) {
        if (userToBoxTime == null || boxToFactoryTime == null) {
            return BASE_DELIVERY_HOURS;
        }

        int totalTime = userToBoxTime + boxToFactoryTime;
        
        // 如果加急，减少20%时间
        if (Boolean.TRUE.equals(isUrgent)) {
            totalTime = (int) (totalTime * (100 - URGENT_REDUCTION_PERCENT) / 100.0);
        }

        return Math.max(1, totalTime); // 最少1小时
    }

    @Override
    public boolean isAddressSupported(MemberAddressPo address) {
        try {
            // 1. 检查地址编码是否有效
            if (address.getAddressCode() == null) {
                return false;
            }

            // 2. 检查该区域是否有可用柜子
            List<SmartCabinet> cabinets = smartCabinetService.getAvailableCabinetsByRegion(address.getAddressCode());
            
            return !cabinets.isEmpty();

        } catch (Exception e) {
            log.error("检查地址[{}]服务支持失败", address.getId(), e);
            return false;
        }
    }

    /**
     * 计算用户地址到柜子的时间
     */
    private Integer calculateUserToBoxTime(MemberAddressPo address, SmartCabinet cabinet) {
        try {
            double distance = locationService.calculateDistance(
                    address.getLongitude(), address.getLatitude(),
                    cabinet.getLongitude(), cabinet.getLatitude()
            );

            // 每5公里1小时，最少0.5小时
            return Math.max(1, (int) Math.ceil(distance / 1000 / 5));

        } catch (Exception e) {
            log.error("计算用户地址[{}]到柜子[{}]时间失败", address.getId(), cabinet.getId(), e);
            return 1; // 默认1小时
        }
    }

    /**
     * 创建地址VO
     */
    private DeliveryAddressVO createDeliveryAddressVO(MemberAddressPo address, Integer deliveryTime) {
        DeliveryAddressVO vo = new DeliveryAddressVO();
        vo.setAddressId(address.getId());
        vo.setReceiverName(address.getContactName());
        vo.setReceiverPhone(address.getContactPhone());
        vo.setProvince(address.getAddressCode().getCityEnum().getProvinceEum());
        vo.setCity(address.getAddressCode().getCityEnum());
        vo.setDistrict(address.getAddressCode());
        vo.setDetailAddress(address.getDetailAddress());
        vo.setStandardDeliveryHours(deliveryTime);
        return vo;
    }

    /**
     * 计算加急时间
     */
    private Integer calculateUrgentTime(Integer standardTime) {
        if (standardTime == null || standardTime <= 0) {
            return 0;
        }
        return (int) (standardTime * (100 - URGENT_REDUCTION_PERCENT) / 100.0);
    }

    /**
     * 创建空的配送信息
     */
    private DeliveryInfoVO createEmptyDeliveryInfo() {
        DeliveryInfoVO vo = new DeliveryInfoVO();
        vo.setDeliveryAddressVOS(new ArrayList<>());
        vo.setUrgentDeliveryHours(0);
        vo.setUrgentDeliveryTime(0);
        return vo;
    }
}
