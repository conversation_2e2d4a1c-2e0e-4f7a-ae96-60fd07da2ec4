package com.demon.giraffe.modules.order.model.entity;

import com.demon.giraffe.modules.service.model.entity.ServiceItemEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订单服务项Entity")
public class OrderServiceItemEntity {

    @Schema(description = "衣物服务明细列表")
    private ServiceItemEntity serviceItemEntity;

    @Schema(description = "服务数量")
    private Integer quantity;

}
