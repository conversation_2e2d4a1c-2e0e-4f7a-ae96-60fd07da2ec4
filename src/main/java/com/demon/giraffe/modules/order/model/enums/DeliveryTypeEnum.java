package com.demon.giraffe.modules.order.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "取件送件方式枚举")
public enum DeliveryTypeEnum implements IEnum<Integer> {
    @Schema(description = "智能柜")
    CABINET(1, "智能柜"),
    
    @Schema(description = "上门服务")
    DOOR_TO_DOOR(3, "上门服务");

    @EnumValue
    private final int code;
    private final String desc;

    DeliveryTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据代码获取枚举
     */
    public static DeliveryTypeEnum of(int code) {
        for (DeliveryTypeEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的配送类型代码: " + code);
    }

    @Override
    public Integer getValue() {
        return code;
    }
}