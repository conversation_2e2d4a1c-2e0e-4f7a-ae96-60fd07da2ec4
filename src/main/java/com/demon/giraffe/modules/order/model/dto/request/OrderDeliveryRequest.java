package com.demon.giraffe.modules.order.model.dto.request;

import com.demon.giraffe.modules.order.model.enums.AppointmentTimeSlotEnum;
import com.demon.giraffe.modules.order.model.enums.DeliveryTypeEnum;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 订单配送请求（新版本）
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订单配送请求")
public class OrderDeliveryRequest {

    @Schema(description = "配送类型", required = true)
    @NotNull(message = "配送类型不能为空")
    private DeliveryTypeEnum deliveryType;

    @Schema(description = "地址ID（上门服务时必填）")
    private Long addressId;

    @Schema(description = "预约时间-日期（上门服务时必填）")
    private LocalDate localDate;

    @Schema(description = "预约时间-时间节点（上门服务时必填）")
    private AppointmentTimeSlotEnum appointmentTimeSlotEnum;

    /**
     * 获取完整的预约时间
     * 根据日期和时间段计算完整的LocalDateTime
     */
    @Schema(hidden = true)
    public LocalDateTime getAppointmentTime() {
        if (localDate == null || appointmentTimeSlotEnum == null) {
            return null;
        }

        // 根据时间段枚举获取具体时间（code字段就是小时数）
        int hour = appointmentTimeSlotEnum.getCode();
        return localDate.atTime(hour, 0);
    }
}
