package com.demon.giraffe.modules.order.model.dto.status;

import com.demon.giraffe.modules.order.model.dto.status.base.OrderStatusBusinessData;
import com.demon.giraffe.modules.order.model.enums.OrderStatusTrackEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 取件完成待送厂状态业务数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "取件完成待送厂状态业务数据")
public class PickupCompletedPendingFactoryData implements OrderStatusBusinessData {
    
    @Schema(description = "实际取件时间")
    private LocalDateTime actualPickupTime;
    
    @Schema(description = "取件快递员ID")
    private Long courierId;
    
    @Schema(description = "衣物件数")
    private Integer itemCount;
    
    @Schema(description = "取件照片URLs")
    private List<String> pickupPhotos;
    
    @Schema(description = "目标工厂ID")
    private Long factoryId;
    
    @Schema(description = "工厂名称")
    private String factoryName;
    
    @Schema(description = "预计到厂时间")
    private LocalDateTime estimatedArrivalTime;
    
    @Schema(description = "运输方式(1-自送,2-物流)")
    private Integer transportType;
    
    @Schema(description = "物流单号")
    private String trackingNumber;
    
    @Schema(description = "取件备注")
    private String pickupRemark;
    
    @Override
    public OrderStatusTrackEnum getStatusType() {
        return OrderStatusTrackEnum.PICKUP_COMPLETED_PENDING_FACTORY;
    }
}
