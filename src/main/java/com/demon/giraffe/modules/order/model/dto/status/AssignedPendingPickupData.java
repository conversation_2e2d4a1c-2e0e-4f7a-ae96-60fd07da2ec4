package com.demon.giraffe.modules.order.model.dto.status;

import com.demon.giraffe.modules.order.model.dto.status.base.OrderStatusBusinessData;
import com.demon.giraffe.modules.order.model.enums.OrderStatusTrackEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 已分配待取件状态业务数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "已分配待取件状态业务数据")
public class AssignedPendingPickupData implements OrderStatusBusinessData {
    
    @Schema(description = "分配的快递员ID")
    private Long courierId;
    
    @Schema(description = "快递员姓名")
    private String courierName;
    
    @Schema(description = "快递员电话")
    private String courierPhone;
    
    @Schema(description = "分配时间")
    private LocalDateTime assignTime;
    
    @Schema(description = "预计取件时间")
    private LocalDateTime estimatedPickupTime;
    
    @Schema(description = "取件地址")
    private String pickupAddress;
    
    @Schema(description = "联系人")
    private String contactPerson;
    
    @Schema(description = "联系电话")
    private String contactPhone;
    
    @Override
    public OrderStatusTrackEnum getStatusType() {
        return OrderStatusTrackEnum.ASSIGNED_PENDING_PICKUP;
    }
}
