package com.demon.giraffe.modules.order.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 订单优惠券匹配请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订单优惠券匹配请求")
public class OrderCouponMatchRequest {
    @NotEmpty(message = "订单商品不能为空")
    @Valid
    @Schema(description = "订单商品列表", required = true)
    private List<OrderItemDetail> orderItems;


}
