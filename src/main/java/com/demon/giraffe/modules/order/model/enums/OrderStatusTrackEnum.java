package com.demon.giraffe.modules.order.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 订单状态跟踪枚举
 * 用于订单状态表的状态字段
 */
@Getter
@Schema(description = "订单状态跟踪枚举")
public enum OrderStatusTrackEnum implements IEnum<Integer> {

    @Schema(description = "待分配")
    PENDING_ASSIGNMENT(1, "待分配"),

    @Schema(description = "已分配待取件")
    ASSIGNED_PENDING_PICKUP(2, "已分配待取件"),

    @Schema(description = "取件完成待送厂")
    PICKUP_COMPLETED_PENDING_FACTORY(3, "取件完成待送厂"),

    @Schema(description = "已到厂待清洗")
    ARRIVED_FACTORY_PENDING_WASH(4, "已到厂待清洗"),

    @Schema(description = "工厂清洗中")
    FACTORY_WASHING(5, "工厂清洗中"),

    @Schema(description = "清洗完成待取送")
    WASH_COMPLETED_PENDING_DELIVERY(6, "清洗完成待取送"),

    @Schema(description = "前往配送中")
    OUT_FOR_DELIVERY(7, "前往配送中"),

    @Schema(description = "已送达")
    DELIVERED(8, "已送达"),

    @Schema(description = "待取件(柜子)")
    PENDING_PICKUP_FROM_CABINET(9, "待取件(柜子)"),

    @Schema(description = "已完成")
    COMPLETED(10, "已完成"),

    @Schema(description = "已取消")
    CANCELLED(11, "已取消"),

    @Schema(description = "异常")
    EXCEPTION(12, "异常");

    @EnumValue
    private final Integer code;
    private final String desc;

    OrderStatusTrackEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getValue() {
        return code;
    }

    /**
     * 根据代码获取枚举
     */
    public static OrderStatusTrackEnum of(Integer code) {
        for (OrderStatusTrackEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("无效的订单状态代码: " + code);
    }



    /**
     * 判断是否是最终状态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == CANCELLED || this == EXCEPTION;
    }

    /**
     * 判断是否是工厂处理阶段
     */
    public boolean isFactoryStage() {
        return this == ARRIVED_FACTORY_PENDING_WASH || this == FACTORY_WASHING || this == WASH_COMPLETED_PENDING_DELIVERY;
    }

    /**
     * 判断是否是配送阶段
     */
    public boolean isDeliveryStage() {
        return this == OUT_FOR_DELIVERY || this == DELIVERED || this == PENDING_PICKUP_FROM_CABINET;
    }

    /**
     * 判断是否可以取消
     */
    public boolean canBeCancelled() {
        return !isFinalStatus() && this != FACTORY_WASHING;
    }

    /**
     * 获取下一个状态
     */
    public OrderStatusTrackEnum getNextStatus() {
        switch (this) {
            case PENDING_ASSIGNMENT:
                return ASSIGNED_PENDING_PICKUP;
            case ASSIGNED_PENDING_PICKUP:
                return PICKUP_COMPLETED_PENDING_FACTORY;
            case PICKUP_COMPLETED_PENDING_FACTORY:
                return ARRIVED_FACTORY_PENDING_WASH;
            case ARRIVED_FACTORY_PENDING_WASH:
                return FACTORY_WASHING;
            case FACTORY_WASHING:
                return WASH_COMPLETED_PENDING_DELIVERY;
            case WASH_COMPLETED_PENDING_DELIVERY:
                return OUT_FOR_DELIVERY;
            case OUT_FOR_DELIVERY:
                return DELIVERED;
            case DELIVERED:
                return PENDING_PICKUP_FROM_CABINET;
            case PENDING_PICKUP_FROM_CABINET:
                return COMPLETED;
            default:
                throw new IllegalStateException("状态 " + this.desc + " 没有下一个状态");
        }
    }


    /**
     * 定义订单状态的有序流程列表
     */
    public static final List<OrderStatusTrackEnum> ORDER_FLOW = Arrays.asList(
            PENDING_ASSIGNMENT,
            ASSIGNED_PENDING_PICKUP,
            PICKUP_COMPLETED_PENDING_FACTORY,
            ARRIVED_FACTORY_PENDING_WASH,
            FACTORY_WASHING,
            WASH_COMPLETED_PENDING_DELIVERY,
            OUT_FOR_DELIVERY,
            DELIVERED,
            PENDING_PICKUP_FROM_CABINET,
            COMPLETED
            // CANCELLED, EXCEPTION 不在流程里
    );
}
