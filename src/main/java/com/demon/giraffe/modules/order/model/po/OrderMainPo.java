package com.demon.giraffe.modules.order.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.demon.giraffe.framework.mybatis.base.BasePo;

import com.demon.giraffe.framework.mybatis.typehandler.OrderDetailTypeHandler;
import com.demon.giraffe.framework.mybatis.typehandler.StringArrayListTypeHandler;
import com.demon.giraffe.modules.order.model.entity.OrderDetailEntity;
import com.demon.giraffe.modules.order.model.enums.*;
import com.demon.giraffe.modules.service.model.enums.CleaningTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单主表PO
 * <p>核心业务实体，整个业务流程的中心，记录了订单的所有关键信息</p>
 * <p>订单生命周期：创建 -> 支付 -> 处理 -> 完成/取消</p>
 *
 * <p>订单取消后再次购买需要生成新的订单信息，原订单状态保持不变</p>
 *
 * <p>主要功能：</p>
 * <ul>
 *   <li>记录订单基本信息</li>
 *   <li>跟踪订单状态变化</li>
 *   <li>管理支付信息</li>
 *   <li>记录物流信息</li>
 *   <li>存储服务明细</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.1.0
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "order_main", autoResultMap = true)
@Schema(description = "订单主表实体，包含订单所有核心信息")
public class OrderMainPo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单编号，系统唯一标识
     */
    @Schema(description = "订单号(系统唯一标识)",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "ORD20230710001",
            maxLength = 32)
    @TableField("order_no")
    private String orderNo;

    /**
     * 关联会员ID
     */
    @Schema(description = "会员ID",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "100001")
    @TableField("member_id")
    private Long memberId;

    /**
     * 支付状态
     */
    @Schema(description = "支付状态",
            implementation = PayStatusEnum.class,
            example = "UNPAID",
            requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("pay_status")
    private PayStatusEnum payStatus;

    /**
     * 订单总金额
     */
    @Schema(description = "订单总金额(元)",
            example = "100.00",
            requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("total_amount")
    private BigDecimal totalAmount;

    /**
     * 优惠金额
     */
    @Schema(description = "优惠金额(元)",
            example = "10.00")
    @TableField("coupon_amount")
    private BigDecimal couponAmount;

    /**
     * 清洗服务类型
     */
    @Schema(description = "清洗类型",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "STANDARD",
            implementation = CleaningTypeEnum.class)
    @NotNull(message = "清洗类型不能为空")
    @TableField("cleaning_type")
    private CleaningTypeEnum cleaningType;

    /**
     * 是否加急服务
     */
    @Schema(description = "是否加急服务",
            example = "false",
            defaultValue = "false")
    @TableField("is_urgent")
    private Boolean isUrgent;

    /**
     * 实际支付金额
     */
    @Schema(description = "实际支付金额(元)",
            example = "85.00",
            requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("pay_amount")
    private BigDecimal payAmount;

    /**
     * 取件方式
     */
    @Schema(description = "取件方式",
            implementation = DeliveryTypeEnum.class,
            example = "SELF_PICKUP",
            requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("pickup_type")
    private DeliveryTypeEnum pickupType;

    /**
     * 送件方式
     */
    @Schema(description = "送件方式",
            implementation = DeliveryTypeEnum.class,
            example = "HOME_DELIVERY",
            requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("delivery_type")
    private DeliveryTypeEnum deliveryType;

    /**
     * 取件地址ID
     */
    @Schema(description = "取件地址ID",
            example = "123",
            requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("pickup_address_id")
    private Long pickupAddressId;

    /**
     * 送件地址ID
     */
    @Schema(description = "送件地址ID",
            example = "456",
            requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("delivery_address_id")
    private Long deliveryAddressId;

    /**
     * 预计取件时间
     */
    @Schema(description = "预计取件时间(ISO格式)",
            example = "2023-07-10T14:00:00",
            requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("estimated_pickup_time")
    private LocalDateTime estimatedPickupTime;

    /**
     * 预计送达时间
     */
    @Schema(description = "预计送达时间(ISO格式)",
            example = "2023-07-12T18:00:00",
            requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("estimated_delivery_time")
    private LocalDateTime estimatedDeliveryTime;

    /**
     * 实际取件时间
     */
    @Schema(description = "实际取件时间(ISO格式)",
            example = "2023-07-10T14:30:00")
    @TableField("actual_pickup_time")
    private LocalDateTime actualPickupTime;

    /**
     * 实际送达时间
     */
    @Schema(description = "实际送达时间(ISO格式)",
            example = "2023-07-12T17:45:00")
    @TableField("actual_delivery_time")
    private LocalDateTime actualDeliveryTime;

    /**
     * 用户备注
     */
    @Schema(description = "用户备注",
            example = "请小心处理我的羊毛大衣",
            maxLength = 500)
    @TableField("remark")
    private String remark;

    /**
     * 物品照片URL列表(JSON格式)
     */
    @Schema(description = "物品照片URL列表(JSON数组)",
            example = "[\"https://example.com/image1.jpg\", \"https://example.com/image2.jpg\"]")
    @TableField(value = "item_images", typeHandler = StringArrayListTypeHandler.class)
    private List<String> itemImages;

    /**
     * 取消原因
     */
    @Schema(description = "取消原因",
            example = "计划有变，暂时不需要了",
            maxLength = 200)
    @TableField("cancel_reason")
    private String cancelReason;

    /**
     * 取消时间
     */
    @Schema(description = "取消时间(ISO格式)",
            example = "2023-07-10T13:30:00")
    @TableField("cancel_time")
    private LocalDateTime cancelTime;

    /**
     * 订单完成时间
     */
    @Schema(description = "完成时间(ISO格式)",
            example = "2023-07-12T17:50:00")
    @TableField("complete_time")
    private LocalDateTime completeTime;

    /**
     * 订单状态
     */
    @Schema(description = "订单状态",
            implementation = OrderStatusEnum.class,
            example = "PROCESSING",
            requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("order_status")
    private OrderStatusEnum orderStatus;

    /**
     * 订单明细：服务项目列表
     * <p>存储为JSON格式，包含订单的所有服务项目信息</p>
     */
    @Schema(description = "订单服务项目明细列表",
            requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField(value = "order_detail", typeHandler = OrderDetailTypeHandler.class)
    private OrderDetailEntity orderDetail;

    /**
     * 订单描述
     * <p>系统生成的订单简要描述，用于展示</p>
     */
    @Schema(description = "订单描述(系统生成)",
            example = "标准洗服务，2件衣物")
    @TableField("description")
    private String description;
}