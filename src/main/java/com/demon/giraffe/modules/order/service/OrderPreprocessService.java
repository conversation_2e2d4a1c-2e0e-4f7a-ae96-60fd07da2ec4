package com.demon.giraffe.modules.order.service;

import com.demon.giraffe.modules.order.model.dto.request.OrderAddressInfoRequest;
import com.demon.giraffe.modules.order.model.dto.request.OrderCouponMatchRequest;
import com.demon.giraffe.modules.order.model.dto.response.OrderAddressInfoResponse;
import com.demon.giraffe.modules.order.model.dto.response.OrderCouponInfoResponse;

/**
 * 订单预处理服务接口
 * 用于订单创建前的信息获取和预处理
 */
public interface OrderPreprocessService {

    /**
     * 获取指定地址的配送信息
     * 根据地址ID和订单信息计算配送时间
     *
     * @param request 地址信息请求
     * @return 地址信息响应
     */
    OrderAddressInfoResponse getAddressDeliveryInfo(OrderAddressInfoRequest request);

    /**
     * 根据订单信息匹配优惠券
     * 返回可用和不可用的优惠券列表
     *
     * @param request 优惠券匹配请求
     * @return 优惠券信息响应
     */
    OrderCouponInfoResponse matchCouponsForOrder(OrderCouponMatchRequest request);
}
