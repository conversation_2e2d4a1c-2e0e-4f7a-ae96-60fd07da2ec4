package com.demon.giraffe.modules.order.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 简化的订单创建请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "简化的订单创建请求")
public class OrderCreateSimpleRequest {

    @NotEmpty(message = "订单商品不能为空")
    @Valid
    @Schema(description = "订单商品列表", required = true)
    private List<OrderItemDetail> orderItems;

    @NotNull(message = "用户选项不能为空")
    @Valid
    @Schema(description = "用户选项（备注、加急、精洗等）", required = true)
    private UserOptionRequest userOptions;

    @NotNull(message = "配送信息不能为空")
    @Valid
    @Schema(description = "配送信息", required = true)
    private OrderDeliveryRequest deliveryInfo;

    @Schema(description = "使用的优惠券ID")
    private Long couponId;
}
