package com.demon.giraffe.modules.order.model.dto.response;

import com.demon.giraffe.modules.marketing.model.enums.CouponTypeEnum;
import com.demon.giraffe.modules.marketing.model.enums.DiscountTypeEnum;
import com.demon.giraffe.modules.order.model.entity.OrderAmountEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单优惠券信息响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订单优惠券信息响应")
public class OrderCouponInfoResponse {

    @Schema(description = "订单基础金额信息")
    private OrderAmountEntity orderAmountEntity;

    @Schema(description = "可用优惠券列表")
    private List<ApplicableCouponInfo> availableCoupons;

    @Schema(description = "不可用优惠券列表")
    private List<InapplicableCouponInfo> unavailableCoupons;

    @Schema(description = "推荐使用的优惠券ID")
    private Long recommendedCouponId;


    /**
     * 可用优惠券信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "可用优惠券信息")
    public static class ApplicableCouponInfo {

        @Schema(description = "优惠券ID")
        private Long couponId;

        @Schema(description = "优惠券名称")
        private String name;

        @Schema(description = "优惠券类型")
        private CouponTypeEnum type;

        @Schema(description = "优惠类型")
        private DiscountTypeEnum discountType;

        @Schema(description = "优惠值")
        private BigDecimal discountValue;

        @Schema(description = "最低消费金额")
        private BigDecimal minAmount;

        @Schema(description = "最大优惠金额")
        private BigDecimal maxDiscount;

        @Schema(description = "预计优惠金额")
        private BigDecimal estimatedDiscount;

        @Schema(description = "使用后的支付金额")
        private BigDecimal finalPayAmount;

        @Schema(description = "使用条件描述")
        private String conditionDescription;

        @Schema(description = "有效期至")
        private LocalDateTime expireTime;

    }

    /**
     * 不可用优惠券信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "不可用优惠券信息")
    public static class InapplicableCouponInfo {

        @Schema(description = "优惠券ID")
        private Long couponId;

        @Schema(description = "不可用原因")
        private String inapplicableReason;

        @Schema(description = "有效期至")
        private LocalDateTime expireTime;
    }
}
