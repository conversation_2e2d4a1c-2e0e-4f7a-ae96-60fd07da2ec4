package com.demon.giraffe.modules.order.model.po.other;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.modules.order.model.enums.OrderTaskStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 订单任务表PO
 * 用于跟踪订单在整个业务流程中的任务状态和进度
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("order_task")
@Schema(description = "订单任务实体")
public class OrderTaskPo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "订单ID不能为空")
    @TableField("order_id")
    @Schema(description = "关联订单ID")
    private Long orderId;

    @Size(max = 32)
    @TableField("task_no")
    @Schema(description = "任务编号（规则：OT+年月日+6位序列）")
    private String taskNo;

    @NotNull(message = "任务状态不能为空")
    @TableField("task_status")
    @Schema(description = "任务状态")
    private OrderTaskStatusEnum taskStatus;

    @TableField("pickup_courier_id")
    @Schema(description = "取件快递员ID")
    private Long pickupCourierId;

    @TableField("pickup_start_time")
    @Schema(description = "取件开始时间")
    private LocalDateTime pickupStartTime;

    @TableField("pickup_completion_time")
    @Schema(description = "取件完成时间")
    private LocalDateTime pickupCompletionTime;

    @TableField("order_qr_code_id")
    @Schema(description = "订单二维码ID")
    private Long orderQrCodeId;

    @TableField("factory_id")
    @Schema(description = "处理工厂ID")
    private Long factoryId;

    @TableField("factory_receive_time")
    @Schema(description = "工厂接收时间")
    private LocalDateTime factoryReceiveTime;

    @TableField("factory_operator_id")
    @Schema(description = "工厂操作员ID")
    private Long factoryOperatorId;

    @TableField("unpacking_time")
    @Schema(description = "拆包时间")
    private LocalDateTime unpackingTime;

    @TableField("clothing_qr_generated_time")
    @Schema(description = "衣物二维码生成时间")
    private LocalDateTime clothingQrGeneratedTime;

    @TableField("processing_start_time")
    @Schema(description = "处理开始时间")
    private LocalDateTime processingStartTime;

    @TableField("processing_completion_time")
    @Schema(description = "处理完成时间")
    private LocalDateTime processingCompletionTime;

    @TableField("quality_check_time")
    @Schema(description = "质检时间")
    private LocalDateTime qualityCheckTime;

    @TableField("quality_checker_id")
    @Schema(description = "质检员ID")
    private Long qualityCheckerId;

    @TableField("packaging_time")
    @Schema(description = "打包时间")
    private LocalDateTime packagingTime;

    @TableField("factory_exit_qr_code_id")
    @Schema(description = "出厂二维码ID")
    private Long factoryExitQrCodeId;

    @TableField("delivery_courier_id")
    @Schema(description = "送货快递员ID")
    private Long deliveryCourierId;

    @TableField("delivery_pickup_time")
    @Schema(description = "送货员取件时间")
    private LocalDateTime deliveryPickupTime;

    @TableField("delivery_start_time")
    @Schema(description = "配送开始时间")
    private LocalDateTime deliveryStartTime;

    @TableField("delivery_completion_time")
    @Schema(description = "配送完成时间")
    private LocalDateTime deliveryCompletionTime;

    @TableField("cabinet_id")
    @Schema(description = "智能柜ID（如果使用智能柜配送）")
    private Long cabinetId;

    @TableField("cabinet_cell_no")
    @Schema(description = "智能柜格口号")
    private String cabinetCellNo;

    @Size(max = 500)
    @TableField("exception_reason")
    @Schema(description = "异常原因")
    private String exceptionReason;

    @Size(max = 20)
    @TableField("exception_code")
    @Schema(description = "异常类型编码")
    private String exceptionCode;

    @TableField("estimated_completion_time")
    @Schema(description = "预计完成时间")
    private LocalDateTime estimatedCompletionTime;

    @TableField("actual_completion_time")
    @Schema(description = "实际完成时间")
    private LocalDateTime actualCompletionTime;

    @Size(max = 1000)
    @TableField("process_notes")
    @Schema(description = "处理备注")
    private String processNotes;

    @TableField("total_clothing_items")
    @Schema(description = "衣物总件数")
    private Integer totalClothingItems;

    @TableField("processed_clothing_items")
    @Schema(description = "已处理衣物件数")
    private Integer processedClothingItems;

    @NotNull(message = "创建人ID不能为空")
    @TableField("creator")
    @Schema(description = "创建人ID")
    private Long creator;

    @Version
    @TableField("version")
    @Schema(description = "乐观锁版本号")
    private Integer version;

    /**
     * 判断任务是否已完成
     */
    public boolean isCompleted() {
        return taskStatus != null && taskStatus.isFinalStatus();
    }

    /**
     * 判断任务是否在工厂处理阶段
     */
    public boolean isInFactoryStage() {
        return taskStatus != null && taskStatus.isFactoryStage();
    }

    /**
     * 判断任务是否在配送阶段
     */
    public boolean isInDeliveryStage() {
        return taskStatus != null && taskStatus.isDeliveryStage();
    }

    /**
     * 判断任务是否在取件阶段
     */
    public boolean isInPickupStage() {
        return taskStatus != null && taskStatus.isPickupStage();
    }

    /**
     * 获取当前处理进度百分比
     */
    public Double getProgressPercentage() {
        if (taskStatus == null) {
            return 0.0;
        }
        
        // 根据状态码计算进度百分比
        double maxCode = OrderTaskStatusEnum.COMPLETED.getCode();
        double currentCode = taskStatus.getCode();
        
        return Math.min(100.0, (currentCode / maxCode) * 100.0);
    }
}
