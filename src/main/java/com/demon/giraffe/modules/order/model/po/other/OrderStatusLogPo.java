package com.demon.giraffe.modules.order.model.po.other;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单状态流转日志表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("order_status_log")
public class OrderStatusLogPo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "日志主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(description = "关联订单ID")
    @NotNull
    @TableField("order_id")
    private Long orderId;

    @Schema(description = "原状态")
    @TableField("from_status")
    private Integer fromStatus;

    @Schema(description = "目标状态")
    @NotNull
    @TableField("to_status")
    private Integer toStatus;

    @Schema(description = "状态分组")
    @TableField("status_group")
    private String statusGroup;

    @Schema(description = "操作人ID")
    @TableField("operator_id")
    private Long operatorId;

    @Schema(description = "操作人类型：0-系统 1-用户 2-取送员 3-工厂 4-客服 5-管理员")
    @NotNull
    @TableField("operator_type")
    private Integer operatorType;

    @Schema(description = "操作人姓名")
    @TableField("operator_name")
    private String operatorName;

    @Schema(description = "操作人角色")
    @TableField("operator_role")
    private UserRole operatorRole;

    @Schema(description = "操作时间")
    @NotNull
    @TableField("operation_time")
    private LocalDateTime operationTime;

    @Schema(description = "操作渠道：1-APP 2-PC 3-API 4-小程序")
    @TableField("operation_channel")
    private Integer operationChannel;

    @Schema(description = "操作IP地址")
    @TableField("operation_ip")
    private String operationIp;

    @Schema(description = "操作位置（GPS或地址）")
    @TableField("location")
    private String location;

    @Schema(description = "经度")
    @Digits(integer = 10, fraction = 7)
    @TableField("longitude")
    private BigDecimal longitude;

    @Schema(description = "纬度")
    @Digits(integer = 10, fraction = 7)
    @TableField("latitude")
    private BigDecimal latitude;

    @Schema(description = "备注说明")
    @Size(max = 200)
    @TableField("remark")
    private String remark;

    @Schema(description = "相关图片（JSON数组）")
    @TableField("images")
    private String images;

    @Schema(description = "扩展数据（JSON格式）")
    @TableField("ext_data")
    private String extData;
}
