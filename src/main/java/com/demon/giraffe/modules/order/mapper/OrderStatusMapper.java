package com.demon.giraffe.modules.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.demon.giraffe.modules.order.model.po.other.OrderStatusPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 订单状态Mapper接口
 */
@Mapper
public interface OrderStatusMapper extends BaseMapper<OrderStatusPo> {

    /**
     * 根据订单ID获取最新状态
     */
    @Select("SELECT * FROM order_status WHERE order_id = #{orderId} AND deleted = 0 ORDER BY create_time DESC LIMIT 1")
    OrderStatusPo selectLatestByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据订单ID获取状态历史
     */
    @Select("SELECT * FROM order_status WHERE order_id = #{orderId} AND deleted = 0 ORDER BY create_time ASC")
    List<OrderStatusPo> selectHistoryByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据状态获取订单列表
     */
    @Select("SELECT DISTINCT order_id FROM order_status WHERE task_status = #{status} AND deleted = 0")
    List<Long> selectOrderIdsByStatus(@Param("status") Integer status);

    /**
     * 统计各状态的订单数量
     */
    @Select("SELECT task_status, COUNT(DISTINCT order_id) as count FROM order_status WHERE deleted = 0 GROUP BY task_status")
    List<java.util.Map<String, Object>> countOrdersByStatus();
}
