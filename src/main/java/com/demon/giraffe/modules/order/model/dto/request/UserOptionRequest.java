package com.demon.giraffe.modules.order.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Schema(description = "用户选项")
public class UserOptionRequest {

    @Schema(description = "用户备注")
    private String remark;

    @Schema(description = "拍照信息（图片URL列表）")
    private List<String> photoUrls;

    @NotNull(message = "是否加急不能为空")
    @Schema(description = "是否加急", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isUrgent;

    @NotNull(message = "是否精洗不能为空")
    @Schema(description = "是否精洗", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isPremium;
}
