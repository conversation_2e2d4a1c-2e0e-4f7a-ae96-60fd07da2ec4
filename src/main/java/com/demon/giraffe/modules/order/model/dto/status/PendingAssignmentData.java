package com.demon.giraffe.modules.order.model.dto.status;

import com.demon.giraffe.modules.order.model.dto.status.base.OrderStatusBusinessData;
import com.demon.giraffe.modules.order.model.enums.OrderStatusTrackEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 待分配状态业务数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "待分配状态业务数据")
public class PendingAssignmentData implements OrderStatusBusinessData {
    
    @Schema(description = "预计分配时间")
    private LocalDateTime estimatedAssignTime;
    
    @Schema(description = "优先级(1-低,2-中,3-高)")
    private Integer priority;
    
    @Schema(description = "分配区域代码")
    private String areaCode;
    
    @Schema(description = "备注信息")
    private String remark;
    
    @Override
    public OrderStatusTrackEnum getStatusType() {
        return OrderStatusTrackEnum.PENDING_ASSIGNMENT;
    }
}
