package com.demon.giraffe.modules.order.model.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "订单项响应")
public class OrderItemResponse {
    private Long serviceItemId;
    private String serviceName;
    private Integer quantity;
    private BigDecimal itemAmount;
    private Boolean isUrgent;
    private Boolean isPremium;
}