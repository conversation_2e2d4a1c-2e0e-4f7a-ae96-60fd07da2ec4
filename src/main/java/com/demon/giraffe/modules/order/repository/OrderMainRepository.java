package com.demon.giraffe.modules.order.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.modules.order.model.dto.query.OrderMainQuery;
import com.demon.giraffe.modules.order.model.enums.OrderStatusEnum;
import com.demon.giraffe.modules.order.model.enums.PayStatusEnum;
import com.demon.giraffe.modules.order.model.po.OrderMainPo;

import java.util.List;


/**
 * 订单主表Repository接口
 */
public interface OrderMainRepository {

    /**
     * 根据ID获取订单
     * @param id 订单ID
     * @return 订单PO对象
     */
    OrderMainPo getById(Long id);

    /**
     * 根据订单号获取订单
     * @param orderNo 订单号
     * @return 订单PO对象
     */
    OrderMainPo getByOrderNo(String orderNo);

    /**
     * 保存订单
     * @param po 订单PO对象
     * @return 保存后的订单PO对象（包含回填的主键）
     */
    OrderMainPo save(OrderMainPo po);

    /**
     * 更新订单
     * @param po 订单PO对象
     * @return 更新是否成功
     */
    boolean update(OrderMainPo po);

    /**
     * 删除订单
     * @param id 订单ID
     * @return 删除是否成功
     */
    boolean deleteById(Long id);

    /**
     * 分页查询订单
     * @param page 分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<OrderMainPo> page(Page<OrderMainPo> page, OrderMainQuery query);

    /**
     * 更新订单状态
     * @param orderId 订单ID
     * @param status 订单状态枚举
     * @return 更新是否成功
     */
    boolean updateStatus(Long orderId, OrderStatusEnum status);

    /**
     * 更新支付状态
     * @param orderId 订单ID
     * @param payStatus 支付状态枚举
     * @return 更新是否成功
     */
    boolean updatePayStatus(Long orderId, PayStatusEnum payStatus);

    /**
     * 根据ID更新订单
     * @param po 订单PO对象
     * @return 更新是否成功
     */
    boolean updateById(OrderMainPo po);

    /**
     * 获取最近的已支付订单
     * @param limit 限制数量
     * @return 最近的已支付订单列表
     */
    List<OrderMainPo> getRecentPaidOrders(int limit);
}