package com.demon.giraffe.modules.order.model.dto.status;

import com.demon.giraffe.modules.order.model.dto.status.base.OrderStatusBusinessData;
import com.demon.giraffe.modules.order.model.enums.OrderStatusTrackEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 清洗完成待取送状态业务数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "清洗完成待取送状态业务数据")
public class WashCompletedPendingDeliveryData implements OrderStatusBusinessData {
    
    @Schema(description = "实际完成时间")
    private LocalDateTime actualCompletionTime;
    
    @Schema(description = "质检员ID")
    private Long inspectorId;
    
    @Schema(description = "质检员姓名")
    private String inspectorName;
    
    @Schema(description = "质检结果")
    private String qualityResult;
    
    @Schema(description = "质检照片URLs")
    private List<String> qualityPhotos;
    
    @Schema(description = "包装完成时间")
    private LocalDateTime packagingTime;
    
    @Schema(description = "包装人员ID")
    private Long packagerId;
    
    @Schema(description = "包装照片URLs")
    private List<String> packagingPhotos;
    
    @Schema(description = "待分配送货员")
    private Boolean pendingDeliveryAssignment;
    
    @Schema(description = "预计出厂时间")
    private LocalDateTime estimatedDepartureTime;
    
    @Schema(description = "完成备注")
    private String completionRemark;
    
    @Override
    public OrderStatusTrackEnum getStatusType() {
        return OrderStatusTrackEnum.WASH_COMPLETED_PENDING_DELIVERY;
    }
}
