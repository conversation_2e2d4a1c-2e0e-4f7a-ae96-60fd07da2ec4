package com.demon.giraffe.modules.order.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.marketing.model.po.MemberCouponPo;
import com.demon.giraffe.modules.order.service.OrderOperationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 订单操作控制器
 * 提供订单的核心操作接口：取消、退单等
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/order/operation")
@RequiredArgsConstructor
@Tag(name = "订单操作管理", description = "订单取消、退单等操作接口")
public class OrderOperationController {

    private final OrderOperationService orderOperationService;

    @PostMapping("/cancel/{orderNo}")
    @Operation(summary = "取消订单", description = "取消指定订单，释放相关资源")
    @SaCheckRole(value = {"ROOT", "CUSTOMER"}, mode = SaMode.OR)
    public ResultBean<Boolean> cancelOrder(
            @Parameter(description = "订单号", required = true)
            @PathVariable String orderNo,
            @Parameter(description = "取消原因")
            @RequestParam(required = false, defaultValue = "用户主动取消") String reason) {
        try {
            boolean result = orderOperationService.cancelOrder(orderNo, reason);
            return ResultBean.success(result);
        } catch (Exception e) {
            log.error("取消订单失败，订单号：{}", orderNo, e);
            return ResultBean.fail("取消订单失败：" + e.getMessage());
        }
    }

    @PostMapping("/refund/{orderNo}")
    @Operation(summary = "退单处理", description = "处理订单退款，释放相关资源")
    @SaCheckRole(value = {"ROOT", "CUSTOMER"}, mode = SaMode.OR)
    public ResultBean<Boolean> refundOrder(
            @Parameter(description = "订单号", required = true)
            @PathVariable String orderNo,
            @Parameter(description = "退单原因")
            @RequestParam(required = false, defaultValue = "用户申请退款") String reason) {
        try {
            boolean result = orderOperationService.refundOrder(orderNo, reason);
            return ResultBean.success(result);
        } catch (Exception e) {
            log.error("退单处理失败，订单号：{}", orderNo, e);
            return ResultBean.fail("退单处理失败：" + e.getMessage());
        }
    }

    @PostMapping("/handle-success/{orderNo}")
    @Operation(summary = "处理成功下单", description = "处理订单支付成功后的业务逻辑")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<Boolean> handleSuccessfulOrder(
            @Parameter(description = "订单号", required = true)
            @PathVariable String orderNo) {
        try {
            boolean result = orderOperationService.handleSuccessfulOrder(orderNo);
            return ResultBean.success(result);
        } catch (Exception e) {
            log.error("处理成功下单失败，订单号：{}", orderNo, e);
            return ResultBean.fail("处理成功下单失败：" + e.getMessage());
        }
    }

    @GetMapping("/can-cancel/{orderNo}")
    @Operation(summary = "检查是否可以取消", description = "检查指定订单是否可以取消")
    @SaCheckRole(value = {"ROOT", "CUSTOMER"}, mode = SaMode.OR)
    public ResultBean<Boolean> canCancelOrder(
            @Parameter(description = "订单号", required = true)
            @PathVariable String orderNo) {
        try {
            boolean result = orderOperationService.canCancelOrder(orderNo);
            return ResultBean.success(result);
        } catch (Exception e) {
            log.error("检查订单是否可以取消失败，订单号：{}", orderNo, e);
            return ResultBean.fail("检查失败：" + e.getMessage());
        }
    }

    @GetMapping("/can-refund/{orderNo}")
    @Operation(summary = "检查是否可以退单", description = "检查指定订单是否可以退单")
    @SaCheckRole(value = {"ROOT", "CUSTOMER"}, mode = SaMode.OR)
    public ResultBean<Boolean> canRefundOrder(
            @Parameter(description = "订单号", required = true)
            @PathVariable String orderNo) {
        try {
            boolean result = orderOperationService.canRefundOrder(orderNo);
            return ResultBean.success(result);
        } catch (Exception e) {
            log.error("检查订单是否可以退单失败，订单号：{}", orderNo, e);
            return ResultBean.fail("检查失败：" + e.getMessage());
        }
    }

    @GetMapping("/find-coupon/{orderId}")
    @Operation(summary = "查找订单使用的优惠券", description = "根据订单ID查找使用的优惠券信息")
    @SaCheckRole(value = {"ROOT", "CUSTOMER"}, mode = SaMode.OR)
    public ResultBean<MemberCouponPo> findCouponByOrderId(
            @Parameter(description = "订单ID", required = true)
            @PathVariable Long orderId) {
        try {
            MemberCouponPo coupon = orderOperationService.findCouponByOrderId(orderId);
            return ResultBean.success(coupon);
        } catch (Exception e) {
            log.error("查找订单使用的优惠券失败，订单ID：{}", orderId, e);
            return ResultBean.fail("查找失败：" + e.getMessage());
        }
    }
}
