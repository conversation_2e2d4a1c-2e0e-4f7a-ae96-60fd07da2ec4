package com.demon.giraffe.modules.order.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demon.giraffe.modules.order.mapper.OrderMainMapper;
import com.demon.giraffe.modules.order.model.dto.query.OrderMainQuery;
import com.demon.giraffe.modules.order.model.enums.OrderStatusEnum;
import com.demon.giraffe.modules.order.model.enums.PayStatusEnum;
import com.demon.giraffe.modules.order.model.po.OrderMainPo;
import com.demon.giraffe.modules.order.repository.OrderMainRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 订单主表Repository实现类
 */
@Repository
public class OrderMainRepositoryImpl implements OrderMainRepository {
    private final OrderMainMapper orderMainMapper;

    public OrderMainRepositoryImpl(OrderMainMapper orderMainMapper) {
        this.orderMainMapper = orderMainMapper;
    }

    @Override
    public OrderMainPo getById(Long id) {
        return orderMainMapper.selectById(id);
    }

    @Override
    public OrderMainPo getByOrderNo(String orderNo) {
        LambdaQueryWrapper<OrderMainPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderMainPo::getOrderNo, orderNo);
        return orderMainMapper.selectOne(wrapper);
    }

    @Override
    public OrderMainPo save(OrderMainPo po) {
        orderMainMapper.insert(po);
        return po;
    }

    @Override
    public boolean update(OrderMainPo po) {
        return orderMainMapper.updateById(po) > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        return orderMainMapper.deleteById(id) > 0;
    }


    @Override
    public IPage<OrderMainPo> page(Page<OrderMainPo> page, OrderMainQuery query) {
        LambdaQueryWrapper<OrderMainPo> wrapper = new LambdaQueryWrapper<>();

        // 构建查询条件
        if (query.getMemberId() != null) {
            wrapper.eq(OrderMainPo::getMemberId, query.getMemberId());
        }
        if (StringUtils.isNotBlank(query.getOrderNo())) {
            wrapper.like(OrderMainPo::getOrderNo, query.getOrderNo());
        }
        if (query.getOrderStatus() != null) {
            wrapper.eq(OrderMainPo::getOrderStatus, query.getOrderStatus());
        }
        if (query.getPayStatus() != null) {
            wrapper.eq(OrderMainPo::getPayStatus, query.getPayStatus());
        }
        if (query.getStartTime() != null) {
            wrapper.ge(OrderMainPo::getCreateTime, query.getStartTime());
        }
        if (query.getEndTime() != null) {
            wrapper.le(OrderMainPo::getCreateTime, query.getEndTime());
        }

        // 排序条件（可根据业务需求添加）
        wrapper.orderByDesc(OrderMainPo::getCreateTime);

        // 正确的分页查询方法
        return orderMainMapper.selectPage(page, wrapper);
    }

    @Override
    public boolean updateStatus(Long orderId, OrderStatusEnum status) {
        OrderMainPo po = new OrderMainPo();
        po.setId(orderId);
        po.setOrderStatus(status);
        return this.update(po);
    }

    @Override
    public boolean updatePayStatus(Long orderId, PayStatusEnum payStatus) {
        OrderMainPo po = new OrderMainPo();
        po.setId(orderId);
        po.setPayStatus(payStatus);
        return this.update(po);
    }

    @Override
    public boolean updateById(OrderMainPo po) {
        return orderMainMapper.updateById(po) > 0;
    }

    @Override
    public List<OrderMainPo> getRecentPaidOrders(int limit) {
        LambdaQueryWrapper<OrderMainPo> wrapper = new LambdaQueryWrapper<>();

        // 查询已支付的订单
        wrapper.eq(OrderMainPo::getPayStatus, PayStatusEnum.PAID);

        // 按创建时间倒序排列
        wrapper.orderByDesc(OrderMainPo::getCreateTime);

        // 限制查询数量
        wrapper.last("LIMIT " + limit);

        return orderMainMapper.selectList(wrapper);
    }
}