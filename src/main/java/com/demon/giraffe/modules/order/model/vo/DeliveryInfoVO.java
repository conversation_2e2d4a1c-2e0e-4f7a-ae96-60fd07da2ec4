package com.demon.giraffe.modules.order.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "取送信息说明")
public class DeliveryInfoVO {


    @Schema(description = "服务需要时间（小时）")
    private Integer urgentDeliveryHours;


    @Schema(description = "如果加急，给出加急服务需要时间（小时")
    private Integer urgentDeliveryTime;

    @Schema(description = "取送信息")
    private List<DeliveryAddressVO> deliveryAddressVOS;

}