package com.demon.giraffe.modules.order.model.dto.response;

import com.demon.giraffe.modules.order.model.entity.OrderDetailEntity;
import com.demon.giraffe.modules.payment.model.dto.response.PaymentResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单创建响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订单创建响应")
public class OrderCreateResponse {

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "订单总金额")
    private BigDecimal totalAmount;

    @Schema(description = "优惠券优惠金额")
    private BigDecimal couponAmount;

    @Schema(description = "实际支付金额")
    private BigDecimal payAmount;

    @Schema(description = "订单状态")
    private String orderStatus;

    @Schema(description = "支付状态")
    private String payStatus;

    @Schema(description = "订单创建时间")
    private LocalDateTime createTime;

    @Schema(description = "订单过期时间（未支付自动取消）")
    private LocalDateTime expireTime;

    @Schema(description = "支付信息（包含支付参数）")
    private PaymentResponse paymentInfo;

    @Schema(description = "使用的优惠券ID")
    private Long usedCouponId;

    @Schema(description = "预计取件时间")
    private LocalDateTime estimatedPickupTime;

    @Schema(description = "预计送达时间")
    private LocalDateTime estimatedDeliveryTime;

    @Schema(description = "订单详情信息")
    private OrderDetailEntity orderDetail;
}

