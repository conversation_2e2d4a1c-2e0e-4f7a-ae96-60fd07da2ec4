package com.demon.giraffe.modules.order.model.dto.query;

import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.modules.order.model.enums.OrderStatusEnum;
import com.demon.giraffe.modules.order.model.enums.PayStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 订单分页查询条件
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "订单分页查询条件")
public class OrderMainQuery extends BasePageQuery<OrderMainQuery> {

    @Schema(description = "会员ID")
    private Long memberId;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "订单状态")
    private OrderStatusEnum orderStatus;

    @Schema(description = "支付状态")
    private PayStatusEnum payStatus;

    @Schema(description = "开始时间(yyyy-MM-dd HH:mm:ss)")
    private LocalDateTime startTime;

    @Schema(description = "结束时间(yyyy-MM-dd HH:mm:ss)")
    private LocalDateTime endTime;
}