package com.demon.giraffe.modules.order.service.impl;

import com.demon.giraffe.modules.order.model.dto.response.RecentOrderResponse;
import com.demon.giraffe.modules.order.model.po.OrderMainPo;
import com.demon.giraffe.modules.order.repository.OrderMainRepository;
import com.demon.giraffe.modules.order.service.RecentOrderService;
import com.demon.giraffe.modules.user.model.po.MemberIdentityPo;
import com.demon.giraffe.modules.user.model.po.UserPo;
import com.demon.giraffe.modules.user.repository.UserRepository;
import com.demon.giraffe.modules.user.service.MemberIdentityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 最近下单服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RecentOrderServiceImpl implements RecentOrderService {

    private static final int MAX_RECENT_ORDERS = 5;

    private final OrderMainRepository orderMainRepository;
    private final MemberIdentityService memberIdentityService;
    private final UserRepository userRepository;

    @Override
    public RecentOrderResponse getRecentOrders() {
        try {
            log.debug("开始获取最近下单信息");

            // 直接从数据库查询最近的已支付订单
            List<RecentOrderResponse.RecentOrderItem> recentOrderItems = loadRecentOrdersFromDatabase();

            return RecentOrderResponse.builder()
                    .recentOrders(recentOrderItems)
                    .build();

        } catch (Exception e) {
            log.error("获取最近下单信息失败", e);
            // 返回空列表，不影响其他功能
            return RecentOrderResponse.builder()
                    .recentOrders(Collections.emptyList())
                    .build();
        }
    }


    /**
     * 从数据库加载最近的订单信息
     */
    private List<RecentOrderResponse.RecentOrderItem> loadRecentOrdersFromDatabase() {
        try {
            // 查询最近的5个已支付订单
            List<OrderMainPo> recentOrders = orderMainRepository.getRecentPaidOrders(MAX_RECENT_ORDERS);

            if (CollectionUtils.isEmpty(recentOrders)) {
                log.debug("没有找到最近的已支付订单");
                return Collections.emptyList();
            }

            List<RecentOrderResponse.RecentOrderItem> orderItems = new ArrayList<>();
            for (OrderMainPo order : recentOrders) {
                RecentOrderResponse.RecentOrderItem orderItem = buildOrderItem(order);
                if (orderItem != null) {
                    orderItems.add(orderItem);
                }
            }

            log.debug("成功加载{}条最近订单信息", orderItems.size());
            return orderItems;

        } catch (Exception e) {
            log.error("从数据库加载最近订单信息失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 构建订单项（包含时间和购买信息）
     */
    private RecentOrderResponse.RecentOrderItem buildOrderItem(OrderMainPo order) {
        try {
            // 1. 获取用户手机号
            String maskedPhone = getMaskedPhoneNumber(order.getMemberId());
            if (!StringUtils.hasText(maskedPhone)) {
                return null;
            }

            // 2. 获取第一个服务名
            String firstServiceName = getFirstServiceName(order);
            if (!StringUtils.hasText(firstServiceName)) {
                return null;
            }

            // 3. 构建购买信息字符串
            String message = maskedPhone + "购买了" + firstServiceName;

            // 4. 格式化时间
            String time = formatOrderTime(order.getCreateTime());

            // 5. 构建订单项
            return RecentOrderResponse.RecentOrderItem.builder()
                    .time(time)
                    .message(message)
                    .build();

        } catch (Exception e) {
            log.error("构建订单项失败，订单号：{}", order.getOrderNo(), e);
            return null;
        }
    }

    /*
     * 格式化订单时间
     *
     * @param createTime 订单创建时间
     * @return 格式化后的时间字符串
     */
    private String formatOrderTime(LocalDateTime createTime) {
        if (createTime == null) {
            return "未知时间";
        }

        Duration duration = Duration.between(createTime, LocalDateTime.now());
        long minutes = duration.toMinutes();
        long hours = duration.toHours();
        long days = duration.toDays();

        if (days > 0) {
            return days + "天前";
        } else if (hours > 0) {
            return hours + "小时前";
        } else if (minutes > 0) {
            return minutes + "分钟前";
        } else {
            return "刚刚";
        }
    }

    /**
     * 获取遮挡的手机号
     */
    private String getMaskedPhoneNumber(Long memberId) {
        try {
            // 1. 获取会员信息
            MemberIdentityPo member = memberIdentityService.getById(memberId);
            if (member == null) {
                return null;
            }

            // 2. 获取用户信息
            UserPo user = userRepository.getById(member.getAppUserId());
            if (user == null || !StringUtils.hasText(user.getPhone())) {
                return null;
            }

            // 3. 遮挡手机号中间4位
            String phone = user.getPhone();
            if (phone.length() != 11) {
                return phone; // 如果不是11位手机号，直接返回
            }

            return phone.substring(0, 3) + "****" + phone.substring(7);

        } catch (Exception e) {
            log.error("获取遮挡手机号失败，会员ID：{}", memberId, e);
            return null;
        }
    }

    /**
     * 获取第一个服务名
     */
    private String getFirstServiceName(OrderMainPo order) {
        try {
            if (order.getOrderDetail() == null ||
                    CollectionUtils.isEmpty(order.getOrderDetail().getServiceItems())) {
                return null;
            }

            // 获取第一个服务项的名称
            var firstServiceItem = order.getOrderDetail().getServiceItems().get(0);
            if (firstServiceItem != null && firstServiceItem.getServiceItemEntity() != null) {
                return firstServiceItem.getServiceItemEntity().getName();
            }

            return null;

        } catch (Exception e) {
            log.error("获取第一个服务名失败，订单号：{}", order.getOrderNo(), e);
            return null;
        }
    }

}
