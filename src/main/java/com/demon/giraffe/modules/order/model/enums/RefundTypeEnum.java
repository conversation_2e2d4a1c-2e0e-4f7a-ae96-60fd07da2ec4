package com.demon.giraffe.modules.order.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * 退款类型枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Schema(description = "退款类型枚举")
public enum RefundTypeEnum implements IEnum<Integer> {
    @Schema(description = "用户申请")
    USER_APPLY(1, "用户申请"),
    
    @Schema(description = "系统自动")
    SYSTEM_AUTO(2, "系统自动"),
    
    @Schema(description = "客服处理")
    CUSTOMER_SERVICE(3, "客服处理");

    @EnumValue
    private final int code;
    private final String desc;

    RefundTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据代码获取枚举
     */
    public static RefundTypeEnum of(int code) {
        for (RefundTypeEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的退款类型代码: " + code);
    }

    @Override
    public Integer getValue() {
        return code;
    }
}
