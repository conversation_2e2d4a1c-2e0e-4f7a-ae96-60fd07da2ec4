package com.demon.giraffe.modules.order.model.dto.status;

import com.demon.giraffe.modules.order.model.dto.status.base.OrderStatusBusinessData;
import com.demon.giraffe.modules.order.model.enums.OrderStatusTrackEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 异常状态业务数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "异常状态业务数据")
public class ExceptionData implements OrderStatusBusinessData {
    
    @Schema(description = "异常发生时间")
    private LocalDateTime exceptionTime;
    
    @Schema(description = "异常类型(1-取件异常,2-运输异常,3-工厂异常,4-配送异常)")
    private Integer exceptionType;
    
    @Schema(description = "异常代码")
    private String exceptionCode;
    
    @Schema(description = "异常描述")
    private String exceptionDescription;
    
    @Schema(description = "异常详情")
    private String exceptionDetails;
    
    @Schema(description = "发现异常的人员ID")
    private Long reportedBy;
    
    @Schema(description = "发现异常的人员姓名")
    private String reportedByName;
    
    @Schema(description = "异常照片URLs")
    private List<String> exceptionPhotos;
    
    @Schema(description = "处理状态(1-待处理,2-处理中,3-已处理)")
    private Integer handlingStatus;
    
    @Schema(description = "处理人员ID")
    private Long handledBy;
    
    @Schema(description = "处理方案")
    private String handlingSolution;
    
    @Schema(description = "预计解决时间")
    private LocalDateTime estimatedResolutionTime;
    
    @Schema(description = "实际解决时间")
    private LocalDateTime actualResolutionTime;
    
    @Override
    public OrderStatusTrackEnum getStatusType() {
        return OrderStatusTrackEnum.EXCEPTION;
    }
}
