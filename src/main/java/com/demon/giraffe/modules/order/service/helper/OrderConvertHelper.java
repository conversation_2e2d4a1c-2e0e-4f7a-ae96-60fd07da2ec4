package com.demon.giraffe.modules.order.service.helper;

import com.demon.giraffe.modules.order.model.dto.response.OrderItemResponse;
import com.demon.giraffe.modules.order.model.dto.response.OrderListResponse;
import com.demon.giraffe.modules.order.model.entity.OrderDetailDisplayEntity;
import com.demon.giraffe.modules.order.model.entity.OrderServiceItemEntity;
import com.demon.giraffe.modules.order.model.enums.OrderStatusEnum;
import com.demon.giraffe.modules.order.model.enums.PayStatusEnum;
import com.demon.giraffe.modules.order.model.po.OrderMainPo;
import com.demon.giraffe.modules.payment.service.WechatPayService;
import com.demon.giraffe.modules.service.model.entity.ServiceItemEntity;
import com.demon.giraffe.modules.service.model.enums.CleaningTypeEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
/**
 * 订单转换工具类
 * 负责订单相关对象的转换和计算
 */
@Component
@RequiredArgsConstructor
public class OrderConvertHelper {

    private final WechatPayService wechatPayService;

    /**
     * 将订单PO转换为订单列表响应DTO
     *
     * @param orderPo 订单PO对象
     * @return 订单列表响应DTO
     */
    public OrderListResponse convertToListResponse(OrderMainPo orderPo) {
        if (orderPo == null) {
            return null;
        }

        return OrderListResponse.builder()
                .orderNo(orderPo.getOrderNo())
                .orderDetail(convertToDisplayOrderDetail(orderPo))
                .orderStatusDesc(orderPo.getOrderStatus() != null ? orderPo.getOrderStatus().getDesc() : null)
                .payStatusDesc(orderPo.getPayStatus() != null ? orderPo.getPayStatus().getDesc() : null)
                .totalAmount(orderPo.getTotalAmount())
                .couponAmount(orderPo.getCouponAmount())
                .payAmount(orderPo.getPayAmount())
                .pickupTypeDesc(orderPo.getPickupType() != null ? orderPo.getPickupType().getDesc() : null)
                .deliveryTypeDesc(orderPo.getDeliveryType() != null ? orderPo.getDeliveryType().getDesc() : null)
                .createTime(orderPo.getCreateTime())
                .estimatedPickupTime(orderPo.getEstimatedPickupTime())
                .estimatedDeliveryTime(orderPo.getEstimatedDeliveryTime())
                .description(orderPo.getDescription())
                .serviceItems(convertServiceItems(orderPo.getOrderDetail().getServiceItems(), orderPo))
                .canPay(canPayOrder(orderPo))
                .canRefund(canRefundOrder(orderPo))
                .paymentRemainingTime(getPaymentRemainingTime(orderPo))
                .build();
    }

    /**
     * 转换服务项目列表
     *
     * @param serviceItems 订单服务项目实体列表
     * @param orderPo 订单PO对象，用于获取加急和精洗信息
     * @return 订单项响应列表
     */
    private List<OrderItemResponse> convertServiceItems(List<OrderServiceItemEntity> serviceItems, OrderMainPo orderPo) {
        if (CollectionUtils.isEmpty(serviceItems)) {
            return Collections.emptyList();
        }

        return serviceItems.stream()
                .map(item -> convertServiceItem(item, orderPo))
                .collect(Collectors.toList());
    }
    /**
     * 转换单个服务项目
     *
     * @param serviceItem 订单服务项目实体
     * @param orderPo 订单PO对象，用于获取加急和精洗信息
     * @return 订单项响应
     */
    private OrderItemResponse convertServiceItem(OrderServiceItemEntity serviceItem, OrderMainPo orderPo) {
        if (serviceItem == null || serviceItem.getServiceItemEntity() == null) {
            return null;
        }

        ServiceItemEntity service = serviceItem.getServiceItemEntity();

        // 从订单PO中获取加急和精洗信息
        Boolean isUrgent = orderPo.getIsUrgent();
        Boolean isPremium = orderPo.getCleaningType() != null &&
                           orderPo.getCleaningType() == CleaningTypeEnum.FINE;

        // 计算单项金额
        BigDecimal itemAmount = calculateItemAmount(service, serviceItem.getQuantity(), isUrgent, isPremium);

        return OrderItemResponse.builder()
                .serviceItemId(service.getServiceItemId())
                .serviceName(service.getName())
                .quantity(serviceItem.getQuantity())
                .itemAmount(itemAmount)
                .isUrgent(isUrgent)
                .isPremium(isPremium)
                .build();
    }

    /**
     * 计算单项金额
     *
     * @param service 服务项目实体
     * @param quantity 数量
     * @param isUrgent 是否加急
     * @param isPremium 是否精洗
     * @return 单项总金额
     */
    private BigDecimal calculateItemAmount(ServiceItemEntity service, Integer quantity, Boolean isUrgent, Boolean isPremium) {
        if (service == null || quantity == null || quantity <= 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal unitPrice = service.getMarketReferencePrice() != null ?
                              service.getMarketReferencePrice() : BigDecimal.ZERO;

        // 加上精洗费用
        if (Boolean.TRUE.equals(isPremium) && service.getPremiumWashExtraFee() != null) {
            unitPrice = unitPrice.add(service.getPremiumWashExtraFee());
        }

        // 加上加急费用
        if (Boolean.TRUE.equals(isUrgent) && service.getExpeditedServiceFee() != null) {
            unitPrice = unitPrice.add(service.getExpeditedServiceFee());
        }

        return unitPrice.multiply(BigDecimal.valueOf(quantity));
    }


    /**
     * 判断订单是否可以支付
     *
     * @param orderPo 订单PO对象
     * @return 是否可以支付
     */
    private Boolean canPayOrder(OrderMainPo orderPo) {
        if (orderPo == null || orderPo.getPayStatus() == null || orderPo.getOrderStatus() == null) {
            return false;
        }

        // 只有待支付状态且订单状态为待支付的订单可以支付
        return orderPo.getPayStatus() == PayStatusEnum.UNPAID &&
               orderPo.getOrderStatus() == OrderStatusEnum.WAIT_PAY;
    }

    /**
     * 判断订单是否可以申请退款
     *
     * @param orderPo 订单PO对象
     * @return 是否可以申请退款
     */
    private Boolean canRefundOrder(OrderMainPo orderPo) {
        if (orderPo == null || orderPo.getPayStatus() == null || orderPo.getOrderStatus() == null) {
            return false;
        }

        // 已支付且订单未完成的订单可以申请退款
        return orderPo.getPayStatus() == PayStatusEnum.PAID &&
               (orderPo.getOrderStatus() == OrderStatusEnum.PAID );
    }

    /**
     * 获取支付剩余时间（秒）
     *
     * @param orderPo 订单PO对象
     * @return 剩余支付时间，如果不需要支付或已过期返回0
     */
    private Long getPaymentRemainingTime(OrderMainPo orderPo) {
        if (orderPo == null || !canPayOrder(orderPo)) {
            return 0L;
        }

        try {
            return wechatPayService.getRemainingPaymentTime(orderPo.getOrderNo());
        } catch (Exception e) {
            return 0L;
        }
    }

    /**
     * 转换为简化的订单详情展示实体
     *
     * @param orderPo 订单PO对象
     * @return 简化的订单详情展示实体
     */
    private OrderDetailDisplayEntity convertToDisplayOrderDetail(OrderMainPo orderPo) {
        if (orderPo == null) {
            return null;
        }

        // 转换服务项目列表
        List<OrderDetailDisplayEntity.ServiceItemDisplay> serviceItemDisplays =
                convertToServiceItemDisplays(orderPo);

        return OrderDetailDisplayEntity.builder()
                .isPremium(orderPo.getCleaningType() != null &&
                          orderPo.getCleaningType() == CleaningTypeEnum.FINE)
                .isUrgent(orderPo.getIsUrgent())
                .serviceItems(serviceItemDisplays)
                .build();
    }

    /**
     * 转换服务项目为展示列表
     *
     * @param orderPo 订单PO对象
     * @return 服务项目展示列表
     */
    private List<OrderDetailDisplayEntity.ServiceItemDisplay> convertToServiceItemDisplays(OrderMainPo orderPo) {
        if (orderPo == null || orderPo.getOrderDetail() == null ||
            orderPo.getOrderDetail().getServiceItems() == null) {
            return Collections.emptyList();
        }

        return orderPo.getOrderDetail().getServiceItems().stream()
                .filter(item -> item != null && item.getServiceItemEntity() != null)
                .map(item -> OrderDetailDisplayEntity.ServiceItemDisplay.builder()
                        .serviceName(item.getServiceItemEntity().getName())
                        .quantity(item.getQuantity())
                        .build())
                .collect(Collectors.toList());
    }

}
