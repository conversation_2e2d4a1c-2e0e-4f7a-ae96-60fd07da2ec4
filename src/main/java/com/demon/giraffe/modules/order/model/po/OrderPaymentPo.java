package com.demon.giraffe.modules.order.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单支付表
 * 对应数据库表：order_payment
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("order_payment")
public class OrderPaymentPo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 关联订单ID，必填 */
    @NotNull(message = "订单ID不能为空")
    @TableField("order_id")
    @Schema(description = "关联订单ID")
    private Long orderId;

    /** 支付单号（规则：PAY+年月日+8位序列），必填且唯一 */
    @NotBlank(message = "支付单号不能为空")
    @Size(max = 32, message = "支付单号长度不能超过32")
    @TableField("payment_no")
    @Schema(description = "支付单号（规则：PAY+年月日+8位序列）")
    private String paymentNo;

    /** 支付方式：1-微信 2-支付宝 3-余额 4-银行卡，必填 */
    @NotNull(message = "支付方式不能为空")
    @TableField("pay_method")
    @Schema(description = "支付方式：1-微信 2-支付宝 3-余额 4-银行卡")
    private Integer payMethod;

    /** 支付金额，必填，默认0.00 */
    @NotNull(message = "支付金额不能为空")
    @DecimalMin(value = "0.00", inclusive = true, message = "支付金额不能小于0")
    @Digits(integer = 8, fraction = 2, message = "支付金额格式错误")
    @TableField("pay_amount")
    @Schema(description = "支付金额")
    private BigDecimal payAmount;

    /** 第三方交易号 */
    @Size(max = 64, message = "第三方交易号长度不能超过64")
    @TableField("third_party_no")
    @Schema(description = "第三方交易号")
    private String thirdPartyNo;

    /** 第三方账户标识 */
    @Size(max = 50, message = "第三方账户标识长度不能超过50")
    @TableField("third_party_account")
    @Schema(description = "第三方账户标识")
    private String thirdPartyAccount;

    /** 支付状态：0-待支付 1-成功 2-失败 3-已退款，默认0 */
    @NotNull(message = "支付状态不能为空")
    @TableField("pay_status")
    @Schema(description = "支付状态：0-待支付 1-成功 2-失败 3-已退款")
    private Integer payStatus;

    /** 支付成功时间 */
    @TableField("pay_time")
    @Schema(description = "支付成功时间")
    private LocalDateTime payTime;

    /** 退款金额，默认0.00 */
    @NotNull(message = "退款金额不能为空")
    @DecimalMin(value = "0.00", inclusive = true, message = "退款金额不能小于0")
    @Digits(integer = 8, fraction = 2, message = "退款金额格式错误")
    @TableField("refund_amount")
    @Schema(description = "退款金额")
    private BigDecimal refundAmount;

    /** 退款时间 */
    @TableField("refund_time")
    @Schema(description = "退款时间")
    private LocalDateTime refundTime;

    /** 退款原因 */
    @Size(max = 200, message = "退款原因长度不能超过200")
    @TableField("refund_reason")
    @Schema(description = "退款原因")
    private String refundReason;
}
