package com.demon.giraffe.modules.order.model.dto.status.base;

import com.demon.giraffe.modules.order.model.enums.OrderStatusTrackEnum;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 订单状态业务数据基础接口
 * 所有状态的业务数据都应该实现此接口
 */
@Schema(description = "订单状态业务数据基础接口")
public interface OrderStatusBusinessData {
    
    /**
     * 获取状态类型
     */
    OrderStatusTrackEnum getStatusType();
    
    /**
     * 验证业务数据的有效性
     */
    default boolean isValid() {
        return true;
    }
}
