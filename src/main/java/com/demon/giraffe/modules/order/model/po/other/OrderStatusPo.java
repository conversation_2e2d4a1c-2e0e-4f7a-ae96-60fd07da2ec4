package com.demon.giraffe.modules.order.model.po.other;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.modules.order.model.enums.OrderStatusTrackEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * 订单状态表PO
 * 用于跟踪订单在整个业务流程中的状态变化
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("order_status")
@Schema(description = "订单状态实体")
public class OrderStatusPo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "订单ID不能为空")
    @TableField("order_id")
    @Schema(description = "关联订单ID")
    private Long orderId;

    @NotNull(message = "任务状态不能为空")
    @TableField("task_status")
    @Schema(description = "订单状态")
    private OrderStatusTrackEnum taskStatus;

    @TableField("business_data")
    @Schema(description = "业务JSON数据")
    private String businessData;


}
