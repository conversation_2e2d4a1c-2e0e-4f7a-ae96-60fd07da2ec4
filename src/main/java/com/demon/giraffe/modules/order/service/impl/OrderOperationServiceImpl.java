package com.demon.giraffe.modules.order.service.impl;

import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.framework.redis.service.RedisService;
import com.demon.giraffe.framework.redis.util.RedisUtils;
import com.demon.giraffe.modules.delivery.service.DeliveryTaskAssignmentService;
import com.demon.giraffe.modules.marketing.model.po.MemberCouponPo;
import com.demon.giraffe.modules.marketing.repository.CouponTemplateRepository;
import com.demon.giraffe.modules.marketing.repository.MemberCouponRepository;
import com.demon.giraffe.modules.order.model.entity.OrderServiceItemEntity;
import com.demon.giraffe.modules.order.model.entity.OrderDetailEntity;

import com.demon.giraffe.modules.order.model.enums.DeliveryTypeEnum;
import com.demon.giraffe.modules.order.model.enums.OrderStatusEnum;
import com.demon.giraffe.modules.order.model.enums.PayStatusEnum;
import com.demon.giraffe.modules.order.model.po.OrderMainPo;
import com.demon.giraffe.modules.order.repository.OrderMainRepository;
import com.demon.giraffe.modules.order.service.OrderOperationService;
import com.demon.giraffe.modules.payment.model.enums.PaymentStatusEnum;
import com.demon.giraffe.modules.payment.model.po.PaymentRecordPo;
import com.demon.giraffe.modules.payment.repository.PaymentRecordRepository;
import com.demon.giraffe.modules.service.service.ServiceItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

import static com.demon.giraffe.modules.payment.constants.PayConstants.PAYMENT_EXPIRE_KEY_PREFIX;

/**
 * 订单操作服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderOperationServiceImpl implements OrderOperationService {

    private final OrderMainRepository orderMainRepository;
    private final PaymentRecordRepository paymentRecordRepository;
    private final MemberCouponRepository memberCouponRepository;
    private final CouponTemplateRepository couponTemplateRepository;
    private final ServiceItemService serviceItemService;
    private final DeliveryTaskAssignmentService deliveryTaskAssignmentService;
    private final RedisService redisService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelOrder(String orderNo, String reason) {
        log.info("开始取消订单，订单号：{}，原因：{}", orderNo, reason);

        try {
            // 1. 查询订单信息
            OrderMainPo order = orderMainRepository.getByOrderNo(orderNo);
            if (order == null) {
                throw new BusinessException("订单不存在：" + orderNo);
            }

            // 2. 检查订单是否可以取消
            if (!canCancelOrder(order)) {
                throw new BusinessException("订单当前状态不允许取消：" + order.getOrderStatus().getDesc());
            }

            // 3. 释放优惠券（如果使用了优惠券）
            releaseCouponIfUsed(order);

            // 4. 更新订单状态
            updateOrderStatusToCancelled(order, reason);

            // 5. 更新支付记录状态
            updatePaymentStatusToClosed(orderNo);

            // 6. 清除支付过期时间
            clearPaymentExpireTime(orderNo);

            log.info("订单取消成功，订单号：{}", orderNo);
            return true;

        } catch (Exception e) {
            log.error("取消订单失败，订单号：{}", orderNo, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleSuccessfulOrder(String orderNo) {
        log.info("开始处理成功下单，订单号：{}", orderNo);

        try {
            // 1. 查询订单信息
            OrderMainPo order = orderMainRepository.getByOrderNo(orderNo);
            if (order == null) {
                throw new BusinessException("订单不存在：" + orderNo);
            }

            // 2. 更新服务项目销量
            updateServiceItemSales(order);

            // 3. 分配任务（仅针对上门服务）
            assignTaskIfNeeded(order);

            // 4. 清除支付过期时间
            clearPaymentExpireTime(orderNo);

            log.info("成功下单处理完成，订单号：{}", orderNo);
            return true;

        } catch (Exception e) {
            log.error("处理成功下单失败，订单号：{}", orderNo, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean refundOrder(String orderNo, String reason) {
        log.info("开始退单处理，订单号：{}，原因：{}", orderNo, reason);

        try {
            // 1. 查询订单信息
            OrderMainPo order = orderMainRepository.getByOrderNo(orderNo);
            if (order == null) {
                throw new BusinessException("订单不存在：" + orderNo);
            }

            // 2. 检查订单是否可以退单
            if (!canRefundOrder(order)) {
                throw new BusinessException("订单当前状态不允许退单：" + order.getOrderStatus().getDesc());
            }

            // 3. 释放优惠券（如果使用了优惠券）
            releaseCouponIfUsed(order);

            // 4. 恢复服务项目销量
            revertServiceItemSales(order);

            // 5. 更新订单状态为已取消
            updateOrderStatusToCancelled(order, reason);

            // 6. 处理退款逻辑（已支付的订单需要退款）
            if (order.getPayStatus() == PayStatusEnum.PAID) {
                processRefund(order, reason);
            }

            log.info("退单处理成功，订单号：{}", orderNo);
            return true;

        } catch (Exception e) {
            log.error("退单处理失败，订单号：{}", orderNo, e);
            throw e;
        }
    }

    @Override
    public boolean canCancelOrder(String orderNo) {
        OrderMainPo order = orderMainRepository.getByOrderNo(orderNo);
        return order != null && canCancelOrder(order);
    }

    @Override
    public boolean canRefundOrder(String orderNo) {
        OrderMainPo order = orderMainRepository.getByOrderNo(orderNo);
        return order != null && canRefundOrder(order);
    }

    @Override
    public MemberCouponPo findCouponByOrderId(Long orderId) {
        if (orderId == null) {
            return null;
        }

        try {
            // 先查询订单信息，通过优惠金额判断是否使用了优惠券
            OrderMainPo order = orderMainRepository.getById(orderId);
            if (order == null) {
                log.warn("订单不存在，订单ID：{}", orderId);
                return null;
            }

            // 如果优惠金额大于0，说明使用了优惠券
            if (order.getCouponAmount() != null && order.getCouponAmount().compareTo(BigDecimal.ZERO) > 0) {
                return memberCouponRepository.getByOrderId(orderId);
            }

            return null;
        } catch (Exception e) {
            log.error("查找订单使用的优惠券失败，订单ID：{}", orderId, e);
            return null;
        }
    }

    /**
     * 检查订单是否可以取消
     */
    private boolean canCancelOrder(OrderMainPo order) {
        OrderStatusEnum status = order.getOrderStatus();
        // 只有待支付、已支付、待取件状态的订单可以取消
        return status == OrderStatusEnum.WAIT_PAY ||
                status == OrderStatusEnum.PAID ||
                status == OrderStatusEnum.WAIT_PICKUP;
    }

    /**
     * 检查订单是否可以退单
     */
    private boolean canRefundOrder(OrderMainPo order) {
        OrderStatusEnum status = order.getOrderStatus();
        // 已支付、待取件、已取件、清洗中的订单可以退单
        return status == OrderStatusEnum.PAID ||
                status == OrderStatusEnum.WAIT_PICKUP ||
                status == OrderStatusEnum.PICKED_UP ||
                status == OrderStatusEnum.WASHING;
    }

    /**
     * 释放优惠券（如果使用了优惠券）
     */
    private void releaseCouponIfUsed(OrderMainPo order) {
        // 通过优惠金额判断是否使用了优惠券
        if (order.getCouponAmount() != null && order.getCouponAmount().compareTo(BigDecimal.ZERO) > 0) {
            log.info("释放优惠券，订单号：{}，优惠金额：{}", order.getOrderNo(), order.getCouponAmount());

            try {
                // 1. 根据订单ID查找使用的优惠券
                MemberCouponPo memberCoupon = memberCouponRepository.getByOrderId(order.getId());
                if (memberCoupon == null) {
                    log.warn("未找到订单使用的优惠券，订单ID：{}", order.getId());
                    return;
                }

                // 2. 取消优惠券使用状态
                boolean couponReleased = memberCouponRepository.cancelCouponUsage(memberCoupon.getId());
                if (!couponReleased) {
                    log.warn("释放优惠券失败，优惠券ID：{}", memberCoupon.getId());
                    return;
                }

                // 3. 恢复优惠券模板使用数量
                boolean templateUpdated = couponTemplateRepository.decrementUsedCount(memberCoupon.getTemplateId());
                if (!templateUpdated) {
                    log.warn("恢复优惠券模板使用数量失败，模板ID：{}", memberCoupon.getTemplateId());
                }

                log.info("优惠券释放成功，优惠券ID：{}，模板ID：{}",
                        memberCoupon.getId(), memberCoupon.getTemplateId());

            } catch (Exception e) {
                log.error("释放优惠券异常，订单ID：{}", order.getId(), e);
            }
        }
    }

    /**
     * 更新订单状态为已取消
     */
    private void updateOrderStatusToCancelled(OrderMainPo order, String reason) {
        order.setOrderStatus(OrderStatusEnum.CANCELLED);
        order.setPayStatus(PayStatusEnum.UNPAID);
        order.setRemark(order.getRemark() + " [取消原因: " + reason + "]");

        boolean updated = orderMainRepository.updateById(order);
        if (!updated) {
            throw new BusinessException("更新订单状态失败");
        }
    }

    /**
     * 更新支付记录状态为已关闭
     */
    private void updatePaymentStatusToClosed(String orderNo) {
        PaymentRecordPo paymentRecord = paymentRecordRepository.getByOrderNo(orderNo);
        if (paymentRecord != null &&
                (paymentRecord.getPaymentStatus() == PaymentStatusEnum.NOTPAY ||
                        paymentRecord.getPaymentStatus() == PaymentStatusEnum.USERPAYING)) {

            paymentRecord.setPaymentStatus(PaymentStatusEnum.CLOSED);
            boolean updated = paymentRecordRepository.updateById(paymentRecord);
            if (!updated) {
                log.warn("更新支付记录状态失败，订单号：{}", orderNo);
            }
        }
    }

    /**
     * 清除支付过期时间
     */
    private void clearPaymentExpireTime(String orderNo) {
        try {
            String redisKey = RedisUtils.redisAssembleKey(PAYMENT_EXPIRE_KEY_PREFIX, orderNo);
            redisService.deleteKey(redisKey);
            log.info("清除支付过期时间，订单号：{}", orderNo);
        } catch (Exception e) {
            log.warn("清除支付过期时间失败，订单号：{}", orderNo, e);
        }
    }

    /**
     * 更新服务项目销量
     */
    private void updateServiceItemSales(OrderMainPo order) {
        OrderDetailEntity orderDetail = order.getOrderDetail();
        if (orderDetail != null && orderDetail.getServiceItems() != null) {
            for (OrderServiceItemEntity serviceItem : orderDetail.getServiceItems()) {
                boolean updated = serviceItemService.increaseSalesCount(
                        serviceItem.getServiceItemEntity().getServiceItemId(),
                        serviceItem.getQuantity()
                );
                if (!updated) {
                    log.warn("更新服务项目销量失败，服务ID：{}，数量：{}",
                            serviceItem.getServiceItemEntity().getServiceItemId(), serviceItem.getQuantity());
                }
            }
        }
    }

    /**
     * 恢复服务项目销量
     */
    private void revertServiceItemSales(OrderMainPo order) {
        OrderDetailEntity orderDetail = order.getOrderDetail();
        if (orderDetail != null && orderDetail.getServiceItems() != null) {
            for (OrderServiceItemEntity serviceItem : orderDetail.getServiceItems()) {
                boolean updated = serviceItemService.decreaseSalesCount(
                        serviceItem.getServiceItemEntity().getServiceItemId(),
                        serviceItem.getQuantity()
                );
                if (!updated) {
                    log.warn("恢复服务项目销量失败，服务ID：{}，数量：{}",
                            serviceItem.getServiceItemEntity().getServiceItemId(), serviceItem.getQuantity());
                }
            }
        }
    }

    /**
     * 分配任务（仅针对上门服务）
     */
    private void assignTaskIfNeeded(OrderMainPo order) {
        if (order.getDeliveryType() == DeliveryTypeEnum.DOOR_TO_DOOR) {
            log.info("开始分配上门取件任务，订单ID：{}", order.getId());

            try {
                Long courierId = deliveryTaskAssignmentService.assignPickupCourier(order.getId());
                if (courierId != null) {
                    log.info("分配上门取件任务成功，订单ID：{}，快递员ID：{}", order.getId(), courierId);
                } else {
                    log.warn("分配上门取件任务失败，暂无可用快递员，订单ID：{}", order.getId());
                }
            } catch (Exception e) {
                log.error("分配上门取件任务异常，订单ID：{}", order.getId(), e);
            }
        }
    }

    /**
     * 处理退款
     */
    private void processRefund(OrderMainPo order, String reason) {
        try {
            log.info("开始处理退款，订单号：{}，退款金额：{}", order.getOrderNo(), order.getPayAmount());

            // TODO: 调用退款服务创建退款申请
            // RefundApplyRequest refundRequest = new RefundApplyRequest();
            // refundRequest.setOrderId(order.getId());
            // refundRequest.setRefundAmount(order.getPayAmount());
            // refundRequest.setRefundReason(reason);
            // orderRefundService.applyRefund(refundRequest);

            log.info("退款处理完成，订单号：{} - 注意：退款服务调用已预留，需要具体实现", order.getOrderNo());

        } catch (Exception e) {
            log.error("处理退款失败，订单号：{}", order.getOrderNo(), e);
            // 退款失败不影响订单取消，但需要记录日志
        }
    }
}
