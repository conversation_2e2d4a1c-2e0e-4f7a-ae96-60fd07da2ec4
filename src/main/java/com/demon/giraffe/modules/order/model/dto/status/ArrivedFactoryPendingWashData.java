package com.demon.giraffe.modules.order.model.dto.status;

import com.demon.giraffe.modules.order.model.dto.status.base.OrderStatusBusinessData;
import com.demon.giraffe.modules.order.model.enums.OrderStatusTrackEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 已到厂待清洗状态业务数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "已到厂待清洗状态业务数据")
public class ArrivedFactoryPendingWashData implements OrderStatusBusinessData {
    
    @Schema(description = "实际到厂时间")
    private LocalDateTime actualArrivalTime;
    
    @Schema(description = "工厂接收人ID")
    private Long receiverId;
    
    @Schema(description = "接收人姓名")
    private String receiverName;
    
    @Schema(description = "接收确认照片URLs")
    private List<String> receivePhotos;
    
    @Schema(description = "衣物检查结果")
    private String inspectionResult;
    
    @Schema(description = "预计开始清洗时间")
    private LocalDateTime estimatedWashStartTime;
    
    @Schema(description = "特殊处理要求")
    private String specialRequirements;
    
    @Schema(description = "衣物分类信息")
    private String categoryInfo;
    
    @Schema(description = "接收备注")
    private String receiveRemark;
    
    @Override
    public OrderStatusTrackEnum getStatusType() {
        return OrderStatusTrackEnum.ARRIVED_FACTORY_PENDING_WASH;
    }
}
