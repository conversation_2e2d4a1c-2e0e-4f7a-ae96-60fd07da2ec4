package com.demon.giraffe.modules.order.model.dto.response;

import com.demon.giraffe.modules.order.model.enums.OrderTaskStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 订单任务详情响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订单任务详情响应")
public class OrderTaskDetailResponse {

    @Schema(description = "任务ID")
    private Long taskId;

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "任务编号")
    private String taskNo;

    @Schema(description = "任务状态")
    private OrderTaskStatusEnum taskStatus;

    @Schema(description = "任务状态描述")
    private String taskStatusDesc;

    @Schema(description = "取件快递员ID")
    private Long pickupCourierId;

    @Schema(description = "取件快递员姓名")
    private String pickupCourierName;

    @Schema(description = "取件开始时间")
    private LocalDateTime pickupStartTime;

    @Schema(description = "取件完成时间")
    private LocalDateTime pickupCompletionTime;

    @Schema(description = "订单二维码ID")
    private Long orderQrCodeId;

    @Schema(description = "订单二维码路径")
    private String orderQrCodePath;

    @Schema(description = "工厂ID")
    private Long factoryId;

    @Schema(description = "工厂名称")
    private String factoryName;

    @Schema(description = "工厂接收时间")
    private LocalDateTime factoryReceiveTime;

    @Schema(description = "工厂操作员ID")
    private Long factoryOperatorId;

    @Schema(description = "工厂操作员姓名")
    private String factoryOperatorName;

    @Schema(description = "拆包时间")
    private LocalDateTime unpackingTime;

    @Schema(description = "衣物二维码生成时间")
    private LocalDateTime clothingQrGeneratedTime;

    @Schema(description = "处理开始时间")
    private LocalDateTime processingStartTime;

    @Schema(description = "处理完成时间")
    private LocalDateTime processingCompletionTime;

    @Schema(description = "质检时间")
    private LocalDateTime qualityCheckTime;

    @Schema(description = "质检员ID")
    private Long qualityCheckerId;

    @Schema(description = "质检员姓名")
    private String qualityCheckerName;

    @Schema(description = "打包时间")
    private LocalDateTime packagingTime;

    @Schema(description = "出厂二维码ID")
    private Long factoryExitQrCodeId;

    @Schema(description = "出厂二维码路径")
    private String factoryExitQrCodePath;

    @Schema(description = "送货快递员ID")
    private Long deliveryCourierId;

    @Schema(description = "送货快递员姓名")
    private String deliveryCourierName;

    @Schema(description = "送货员取件时间")
    private LocalDateTime deliveryPickupTime;

    @Schema(description = "配送开始时间")
    private LocalDateTime deliveryStartTime;

    @Schema(description = "配送完成时间")
    private LocalDateTime deliveryCompletionTime;

    @Schema(description = "智能柜ID")
    private Long cabinetId;

    @Schema(description = "智能柜名称")
    private String cabinetName;

    @Schema(description = "智能柜格口号")
    private String cabinetCellNo;

    @Schema(description = "异常原因")
    private String exceptionReason;

    @Schema(description = "异常类型编码")
    private String exceptionCode;

    @Schema(description = "预计完成时间")
    private LocalDateTime estimatedCompletionTime;

    @Schema(description = "实际完成时间")
    private LocalDateTime actualCompletionTime;

    @Schema(description = "处理备注")
    private String processNotes;

    @Schema(description = "衣物总件数")
    private Integer totalClothingItems;

    @Schema(description = "已处理衣物件数")
    private Integer processedClothingItems;

    @Schema(description = "处理进度百分比")
    private Double progressPercentage;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否已完成")
    private Boolean isCompleted;

    @Schema(description = "是否在工厂处理阶段")
    private Boolean isInFactoryStage;

    @Schema(description = "是否在配送阶段")
    private Boolean isInDeliveryStage;

    @Schema(description = "是否在取件阶段")
    private Boolean isInPickupStage;

    // 订单基本信息
    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "会员ID")
    private Long memberId;

    @Schema(description = "会员姓名")
    private String memberName;

    @Schema(description = "订单金额")
    private String orderAmount;

    @Schema(description = "订单状态")
    private String orderStatus;

    @Schema(description = "配送地址")
    private String deliveryAddress;

    @Schema(description = "联系电话")
    private String contactPhone;
}
