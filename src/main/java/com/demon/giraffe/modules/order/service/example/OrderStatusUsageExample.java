//package com.demon.giraffe.modules.order.service.example;
//
//import com.demon.giraffe.modules.order.model.dto.status.*;
//import com.demon.giraffe.modules.order.model.dto.response.OrderStatusWithBusinessData;
//import com.demon.giraffe.modules.order.service.OrderStatusService;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import java.time.LocalDateTime;
//import java.util.List;
//
///**
// * 订单状态服务使用示例
// * 展示如何在其他业务中调用订单状态服务
// */
//@Slf4j
//@Component
//@RequiredArgsConstructor
//public class OrderStatusUsageExample {
//
//    private final OrderStatusService orderStatusService;
//
//    /**
//     * 示例：订单创建后添加待分配状态
//     */
//    public void handleOrderCreated(Long orderId) {
//        PendingAssignmentData data = PendingAssignmentData.builder()
//                .estimatedAssignTime(LocalDateTime.now().plusHours(1))
//                .priority(2) // 中等优先级
//                .areaCode("BJ001")
//                .remark("新订单待分配")
//                .build();
//
//        // 现在直接返回具体的业务数据实体
//        PendingAssignmentData result = orderStatusService.addPendingAssignmentStatus(orderId, data);
//        log.info("订单 {} 已添加待分配状态，预计分配时间：{}", orderId, result.getEstimatedAssignTime());
//    }
//
//    /**
//     * 示例：工厂开始清洗
//     */
//    public void handleFactoryWashingStart(Long orderId, Long workerId, String workerName) {
//        FactoryWashingData data = FactoryWashingData.builder()
//                .washStartTime(LocalDateTime.now())
//                .workerId(workerId)
//                .workerName(workerName)
//                .currentProcess("清洗")
//                .progressPercentage(20)
//                .estimatedCompletionTime(LocalDateTime.now().plusHours(4))
//                .build();
//
//        // 直接返回具体的业务数据实体
//        FactoryWashingData result = orderStatusService.addFactoryWashingStatus(orderId, data);
//        log.info("订单 {} 开始工厂清洗，负责人：{}，进度：{}%",
//                orderId, result.getWorkerName(), result.getProgressPercentage());
//    }
//
//    /**
//     * 示例：获取订单当前状态（包含业务数据）
//     */
//    public void checkOrderCurrentStatus(Long orderId) {
//        OrderStatusWithBusinessData currentStatus = orderStatusService.getCurrentStatusWithBusinessData(orderId);
//
//        if (currentStatus != null) {
//            log.info("订单 {} 当前状态：{}", orderId, currentStatus.getStatus().getTaskStatus().getDesc());
//
//            // 可以直接访问业务数据
//            if (currentStatus.getBusinessData() instanceof FactoryWashingData) {
//                FactoryWashingData washData = (FactoryWashingData) currentStatus.getBusinessData();
//                log.info("清洗进度：{}%，当前工序：{}", washData.getProgressPercentage(), washData.getCurrentProcess());
//            }
//        } else {
//            log.warn("订单 {} 没有找到状态信息", orderId);
//        }
//    }
//
//    /**
//     * 示例：获取订单状态历史
//     */
//    public void getOrderStatusHistory(Long orderId) {
//        List<OrderStatusWithBusinessData> statusHistory = orderStatusService.getStatusHistoryWithBusinessData(orderId);
//
//        log.info("订单 {} 状态历史：", orderId);
//        for (OrderStatusWithBusinessData statusData : statusHistory) {
//            log.info("- {} ({})",
//                    statusData.getStatus().getTaskStatus().getDesc(),
//                    statusData.getStatus().getCreateTime());
//        }
//    }
//}
