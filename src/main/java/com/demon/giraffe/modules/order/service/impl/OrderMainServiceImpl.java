package com.demon.giraffe.modules.order.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.modules.marketing.model.entity.Coupon;
import com.demon.giraffe.modules.marketing.service.MemberCouponService;
import com.demon.giraffe.modules.order.model.dto.query.OrderMainQuery;
import com.demon.giraffe.modules.order.model.dto.request.*;
import com.demon.giraffe.modules.order.model.dto.response.*;
import com.demon.giraffe.modules.order.model.entity.*;
import com.demon.giraffe.modules.order.model.po.OrderMainPo;
import com.demon.giraffe.modules.order.repository.OrderMainRepository;
import com.demon.giraffe.modules.order.service.OrderMainService;
import com.demon.giraffe.modules.order.service.helper.OrderCalculator;
import com.demon.giraffe.modules.order.service.helper.OrderConvertHelper;
import com.demon.giraffe.modules.order.service.helper.OrderPersistenceService;
import com.demon.giraffe.modules.order.service.verify.OrderValidator;
import com.demon.giraffe.modules.payment.model.dto.response.PaymentResponse;
import com.demon.giraffe.modules.payment.model.entity.CreateOrderRequest;
import com.demon.giraffe.modules.payment.model.enums.PaymentTypeEnum;
import com.demon.giraffe.modules.payment.service.WechatPayService;
import com.demon.giraffe.modules.service.model.entity.ServiceItemEntity;
import com.demon.giraffe.modules.service.service.ServiceItemService;
import com.demon.giraffe.modules.user.model.dto.response.MemberIdentityDetailResponse;
import com.demon.giraffe.modules.user.model.po.MemberAddressPo;
import com.demon.giraffe.modules.user.model.po.UserPo;
import com.demon.giraffe.modules.user.repository.MemberAddressRepository;
import com.demon.giraffe.modules.user.service.MemberIdentityService;
import com.demon.giraffe.framework.satoken.util.SaTokenUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 订单主服务实现类
 * <p>
 * 负责订单创建的核心业务逻辑，采用分层架构设计，通过协调各个专业组件完成订单的完整生命周期管理。
 * 主要包括订单验证、金额计算、数据持久化和支付创建等核心流程。
 * </p>
 *
 * <p>主要功能：</p>
 * <ul>
 *   <li>订单创建与支付准备</li>
 *   <li>订单上下文构建</li>
 *   <li>业务验证协调</li>
 *   <li>金额计算协调</li>
 *   <li>数据持久化协调</li>
 *   <li>支付订单创建</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-07-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderMainServiceImpl implements OrderMainService {

    /**
     * 会员身份服务
     * 用于获取当前用户的会员身份信息
     */
    private final MemberIdentityService memberIdentityService;

    /**
     * 服务项目服务
     * 用于获取和验证订单中的服务项目信息
     */
    private final ServiceItemService serviceItemService;

    /**
     * 会员地址仓储
     * 用于获取会员的配送地址信息
     */
    private final MemberAddressRepository memberAddressRepository;

    /**
     * 微信支付服务
     * 用于创建微信支付订单
     */
    private final WechatPayService wechatPayService;

    /**
     * 会员优惠券服务
     * 用于获取和验证优惠券信息
     */
    private final MemberCouponService couponService;

    /**
     * 订单验证器
     * 负责订单创建前的业务规则验证
     */
    private final OrderValidator orderValidator;

    /**
     * 订单计算器
     * 负责订单金额、时间等相关计算
     */
    private final OrderCalculator orderCalculator;

    /**
     * 订单持久化服务
     * 负责订单数据的持久化操作
     */
    private final OrderPersistenceService orderPersistenceService;

    /**
     * 订单主表仓储
     * 用于订单数据的查询操作
     */
    private final OrderMainRepository orderMainRepository;

    /**
     * 订单转换助手
     * 负责订单对象的转换操作
     */
    private final OrderConvertHelper orderConvertHelper;

    /**
     * 创建订单并准备支付
     * <p>
     * 订单创建的主流程方法，按照固定步骤执行订单创建的完整流程：
     * 1. 构建订单上下文
     * 2. 执行业务验证
     * 3. 执行订单计算
     * 4. 执行数据持久化
     * 5. 创建支付订单
     * 6. 构建响应结果
     * </p>
     *
     * @param request 订单创建请求，包含订单的所有基础信息
     * @return OrderCreateResponse 订单创建响应，包含订单信息和支付信息
     * @throws BusinessException 当业务验证失败或处理异常时抛出
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderCreateResponse createOrderWithPayment(OrderCreateSimpleRequest request) {

        // 1. 构建订单处理上下文
        OrderContext context = buildOrderContext(request);

        // 2. 执行业务验证
        orderValidator.validateOrderContext(context);

        // 3. 执行订单计算
        orderCalculator.calculateOrder(context);

        // 4. 执行数据持久化
        orderPersistenceService.persistOrder(context);

        // 5. 创建支付订单
        PaymentResponse paymentInfo = createPayment(context);

        // 6. 构建响应结果
        return buildResponse(context, paymentInfo);
    }

    /**
     * 构建订单处理上下文
     * <p>
     * 收集订单创建所需的所有基础数据，包括用户信息、会员信息、服务项目、地址信息和优惠券信息，
     * 构建完整的订单处理上下文对象，供后续流程使用。
     * </p>
     *
     * @param request 订单创建请求
     * @return OrderContext 订单处理上下文
     * @throws BusinessException 当用户信息或会员信息异常时抛出
     */
    private OrderContext buildOrderContext(OrderCreateSimpleRequest request) {
        // 获取当前登录用户信息
        UserPo currentUser = SaTokenUtil.getUserPo();
        if (currentUser == null) {
            throw new BusinessException("用户信息异常");
        }

        // 获取会员身份详细信息
        MemberIdentityDetailResponse memberInfo = memberIdentityService.getMyMemberIdentityDetail();
        if (memberInfo == null) {
            throw new BusinessException("会员信息异常");
        }

        // 构建订单上下文
        return OrderContext.builder()
                .request(request)
                .currentUser(currentUser)
                .memberInfo(memberInfo)
                .serviceItems(getServiceItems(request))
                .memberAddress(getAddress(request))
                .coupon(getCoupon(request))
                .build();
    }

    /**
     * 获取服务项目列表
     * <p>
     * 根据订单请求中的服务项目ID列表，批量获取服务项目的详细信息。
     * </p>
     *
     * @param request 订单创建请求
     * @return List<ServiceItemEntity> 服务项目实体列表
     */
    private List<ServiceItemEntity> getServiceItems(OrderCreateSimpleRequest request) {
        // 提取所有服务项目ID
        Set<Long> serviceItemIds = request.getOrderItems().stream()
                .map(OrderItemDetail::getServiceItemId)
                .collect(Collectors.toSet());

        // 批量获取服务项目详情
        return serviceItemService.batchGetServiceItemDetails(serviceItemIds);
    }

    /**
     * 获取配送地址信息
     * <p>
     * 根据订单请求中的地址ID获取会员地址信息，如果没有地址ID则返回null（非上门服务）。
     * </p>
     *
     * @param request 订单创建请求
     * @return MemberAddressPo 会员地址信息，可能为null
     */
    private MemberAddressPo getAddress(OrderCreateSimpleRequest request) {
        return request.getDeliveryInfo().getAddressId() != null ?
                memberAddressRepository.getById(request.getDeliveryInfo().getAddressId()) : null;
    }

    /**
     * 获取优惠券信息
     * <p>
     * 根据订单请求中的优惠券ID获取优惠券信息，如果没有优惠券ID则返回null。
     * </p>
     *
     * @param request 订单创建请求
     * @return Coupon 优惠券信息，可能为null
     */
    private Coupon getCoupon(OrderCreateSimpleRequest request) {
        return request.getCouponId() != null ?
                couponService.getCouponById(request.getCouponId()) : null;
    }

    /**
     * 创建支付订单
     * <p>
     * 调用微信支付服务创建JSAPI支付订单，用于小程序支付。
     * 支付金额需要转换为分（乘以100），并使用用户的openid。
     * </p>
     *
     * @param context 订单处理上下文，包含订单信息和用户信息
     * @return PaymentResponse 支付响应信息，包含支付参数
     * @throws BusinessException 当支付订单创建失败时抛出
     */
    private PaymentResponse createPayment(OrderContext context) {
        try {
            CreateOrderRequest createOrderRequest = new CreateOrderRequest();

            CreateOrderRequest.OrderInfo orderInfo = new CreateOrderRequest.OrderInfo();
            orderInfo.setOrderId(context.getOrder().getId());
            orderInfo.setOrderNo(context.getOrder().getOrderNo());
            orderInfo.setDescription(context.getOrder().getDescription());
            orderInfo.setPayAmount(context.getOrder().getPayAmount());
            createOrderRequest.setOrderInfo(orderInfo);

            CreateOrderRequest.MemberInfo memberInfo = new CreateOrderRequest.MemberInfo();
            memberInfo.setMemberId(context.getMemberInfo().getMemberId());
            memberInfo.setOpenid(context.getCurrentUser().getOpenid());
            createOrderRequest.setMemberInfo(memberInfo);


            CreateOrderRequest.PaymentInfo paymentInfo = new CreateOrderRequest.PaymentInfo();
            paymentInfo.setPaymentType(PaymentTypeEnum.WECHAT);
            createOrderRequest.setPaymentInfo(paymentInfo);

            // 调用微信支付服务创建JSAPI订单
            return wechatPayService.createPayment(createOrderRequest);
        } catch (Exception e) {
            log.error("创建支付订单失败，订单号：{}", context.getOrder().getOrderNo(), e);
            throw new BusinessException("创建支付订单失败：" + e.getMessage());
        }
    }

    /**
     * 构建订单创建响应
     * <p>
     * 根据订单处理上下文和支付信息，构建完整的订单创建响应对象，
     * 包含订单基本信息、金额信息、状态信息、时间信息和支付信息等。
     * </p>
     *
     * @param context 订单处理上下文，包含订单和计算结果信息
     * @param paymentInfo 支付响应信息，包含支付参数
     * @return OrderCreateResponse 订单创建响应
     */
    private OrderCreateResponse buildResponse(OrderContext context, PaymentResponse paymentInfo) {
        return OrderCreateResponse.builder()
                .orderId(context.getOrder().getId())                                    // 订单ID
                .orderNo(context.getOrder().getOrderNo())                              // 订单号
                .totalAmount(context.getCalculation().getTotalAmount())                // 订单总金额
                .couponAmount(context.getCalculation().getCouponAmount())              // 优惠券金额
                .payAmount(context.getCalculation().getPayAmount())                    // 实付金额
                .orderStatus(context.getOrder().getOrderStatus().getDesc())           // 订单状态
                .payStatus(context.getOrder().getPayStatus().getDesc())               // 支付状态
                .createTime(context.getOrder().getCreateTime())                       // 创建时间
                .paymentInfo(paymentInfo)                                             // 支付信息
                .usedCouponId(context.getCalculation().getUsedCouponId())             // 使用的优惠券ID
                .estimatedPickupTime(context.getCalculation().getEstimatedPickupTime()) // 预计取件时间
                .estimatedDeliveryTime(context.getCalculation().getEstimatedDeliveryTime()) // 预计送达时间
                .orderDetail(context.getCalculation().getOrderDetail())               // 订单详情
                .build();
    }

    /**
     * 获取我的订单列表
     * <p>
     * 根据当前登录用户获取其订单列表，支持分页和条件查询。
     * 自动设置查询条件中的会员ID为当前登录用户的会员ID。
     * </p>
     *
     * @param pageQuery 分页查询条件
     * @return 订单列表分页响应
     * @throws BusinessException 当用户信息或会员信息异常时抛出
     */
    @Override
    public IPage<OrderListResponse> getMyOrderList(BasePageQuery<OrderMainQuery> pageQuery) {
        log.info("开始获取我的订单列表，分页参数：{}", pageQuery);

        try {
            // 1. 获取当前登录用户的会员信息
            MemberIdentityDetailResponse memberInfo = memberIdentityService.getMyMemberIdentityDetail();
            if (memberInfo == null) {
                throw new BusinessException("会员信息异常");
            }

            // 2. 初始化分页参数
            pageQuery.init();

            // 3. 设置查询条件中的会员ID
            OrderMainQuery query = pageQuery.getQuery();
            if (query == null) {
                query = new OrderMainQuery();
                pageQuery.setQuery(query);
            }
            query.setMemberId(memberInfo.getMemberId());

            // 4. 创建分页对象
            Page<OrderMainPo> page = new Page<>(pageQuery.getPage(), pageQuery.getPerPage());

            // 5. 执行分页查询
            IPage<OrderMainPo> orderPoPage = orderMainRepository.page(page, query);

            // 6. 转换为响应DTO
            IPage<OrderListResponse> result = orderPoPage.convert(orderConvertHelper::convertToListResponse);

            log.info("获取我的订单列表成功，会员ID：{}，总记录数：{}",
                    memberInfo.getMemberId(), result.getTotal());

            return result;

        } catch (BusinessException e) {
            log.error("获取我的订单列表失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("获取我的订单列表异常", e);
            throw new BusinessException("获取订单列表失败：" + e.getMessage());
        }
    }

    @Override
    public PaymentResponse repayOrder(String orderNo) {
        log.info("开始重新支付，订单号：{}", orderNo);

        try {
            // 1. 获取当前登录用户的会员信息
            MemberIdentityDetailResponse memberInfo = memberIdentityService.getMyMemberIdentityDetail();
            if (memberInfo == null) {
                throw new BusinessException("会员信息异常");
            }

            // 2. 查询订单信息
            OrderMainPo order = orderMainRepository.getByOrderNo(orderNo);
            if (order == null) {
                throw new BusinessException("订单不存在");
            }

            // 3. 验证订单所有权
            if (!order.getMemberId().equals(memberInfo.getMemberId())) {
                throw new BusinessException("无权限操作此订单");
            }

            // 4. 检查订单状态是否可以支付
            if (!canRepayOrder(order)) {
                throw new BusinessException("订单当前状态不允许支付");
            }

            // 5. 调用简化的重新支付接口
            PaymentResponse paymentResponse = wechatPayService.repayOrder(orderNo);

            log.info("重新支付创建成功，订单号：{}，支付ID：{}", orderNo, paymentResponse.getPaymentId());

            return paymentResponse;

        } catch (BusinessException e) {
            log.error("重新支付失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("重新支付异常", e);
            throw new BusinessException("重新支付失败：" + e.getMessage());
        }
    }

    /**
     * 检查订单是否可以重新支付
     */
    private boolean canRepayOrder(OrderMainPo order) {
        return order.getOrderStatus() == com.demon.giraffe.modules.order.model.enums.OrderStatusEnum.WAIT_PAY &&
               order.getPayStatus() == com.demon.giraffe.modules.order.model.enums.PayStatusEnum.UNPAID;
    }
}