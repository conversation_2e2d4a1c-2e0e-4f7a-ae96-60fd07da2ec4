package com.demon.giraffe.modules.order.model.dto.request;

import com.demon.giraffe.modules.order.model.enums.OrderTaskStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 订单任务批量更新请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订单任务批量更新请求")
public class OrderTaskUpdateRequest {

    @NotEmpty(message = "任务ID列表不能为空")
    @Schema(description = "任务ID列表", required = true)
    private List<Long> taskIds;

    @NotNull(message = "新状态不能为空")
    @Schema(description = "新状态", required = true)
    private OrderTaskStatusEnum newStatus;

    @NotNull(message = "操作员ID不能为空")
    @Schema(description = "操作员ID", required = true)
    private Long operatorId;

    @Size(max = 500, message = "备注长度不能超过500字符")
    @Schema(description = "批量更新备注")
    private String notes;

    @Schema(description = "是否强制更新（忽略状态转换验证）")
    private Boolean forceUpdate ;
}
