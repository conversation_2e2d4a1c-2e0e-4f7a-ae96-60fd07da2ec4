package com.demon.giraffe.modules.order.util;

import com.demon.giraffe.modules.order.model.dto.request.*;
import com.demon.giraffe.modules.order.model.entity.OrderAmountEntity;
import com.demon.giraffe.modules.order.model.entity.OrderServiceItemEntity;
import com.demon.giraffe.modules.service.model.entity.ServiceItemEntity;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单状态业务数据解析工具类
 */
@Slf4j
public class OrderUtil {

    public static List<OrderServiceItemEntity> buildOrderServiceItems(List<OrderItemDetail> orderItems, List<ServiceItemEntity> serviceItems) {
        return orderItems.stream()
                .map(orderItem -> {
                    ServiceItemEntity serviceItem = serviceItems.stream()
                            .filter(item -> item.getServiceItemId().equals(orderItem.getServiceItemId()))
                            .findFirst()
                            .orElseThrow(() -> new IllegalArgumentException("服务项目不存在: " + orderItem.getServiceItemId()));

                    return OrderServiceItemEntity.builder()
                            .serviceItemEntity(serviceItem)
                            .quantity(orderItem.getQuantity())
                            .build();
                })
                .collect(Collectors.toList());
    }

    /**
     * 计算订单金额和时间信息
     * <p>
     * 根据服务项目和数量计算订单的完整金额和时间信息，包括：
     * - 价格信息：标准洗价格、市场价格、精洗费用、加急费用
     * - 时间信息：标准处理时间、加急处理时间、精洗额外时间
     * </p>
     *
     * @param serviceItems 订单服务项目列表
     * @return OrderAmountEntity 包含完整金额和时间信息的实体
     */
    public static OrderAmountEntity calculateAmountInfo(List<OrderServiceItemEntity> serviceItems) {
        log.debug("开始计算订单金额和时间信息，服务项目数量：{}", serviceItems.size());

        // 初始化金额累计变量
        BigDecimal totalStandardPrice = BigDecimal.ZERO;
        BigDecimal totalMarketPrice = BigDecimal.ZERO;
        BigDecimal totalPremiumFee = BigDecimal.ZERO;
        BigDecimal totalExpeditedServiceFee = BigDecimal.ZERO;

        // 初始化时间累计变量
        int totalPremiumHours = 0;      // 精洗额外时间总计
        int totalStandardHours = 0;     // 标准处理时间总计
        int totalExpeditedHours = 0;    // 加急处理时间总计

        // 遍历所有服务项目，累计金额和时间
        for (OrderServiceItemEntity item : serviceItems) {
            ServiceItemEntity service = item.getServiceItemEntity();
            Integer quantity = item.getQuantity();

            // 累计价格信息
            totalStandardPrice = accumulate(totalStandardPrice, service.getStandardWashPrice(), quantity);
            totalMarketPrice = accumulate(totalMarketPrice, service.getMarketReferencePrice(), quantity);
            totalPremiumFee = accumulate(totalPremiumFee, service.getPremiumWashExtraFee(), quantity);
            totalExpeditedServiceFee = accumulate(totalExpeditedServiceFee,
                    service.getExpeditedServiceFee(), quantity);

            // 累计时间信息
            totalPremiumHours += safeMultiply(service.getPremiumWashExtraHours(), quantity);
            totalStandardHours += safeMultiply(service.getStandardProcessingHours(), quantity);
            totalExpeditedHours += safeMultiply(service.getExpeditedProcessingHours(), quantity);
        }

        log.debug("金额和时间计算完成 - 标准时间：{}h，加急时间：{}h，精洗额外时间：{}h",
                totalStandardHours, totalExpeditedHours, totalPremiumHours);

        return OrderAmountEntity.builder()
                .totalStandardWashPrice(totalStandardPrice)
                .totalPremiumWashExtraFee(totalPremiumFee)
                .totalPremiumWashExtraHours(totalPremiumHours)
                .totalMarketReferencePrice(totalMarketPrice)
                .totalStandardProcessingHours(totalStandardHours)
                .totalExpeditedProcessingHours(totalExpeditedHours)
                .totalExpeditedExtraFee(totalExpeditedServiceFee)
                .build();
    }

    private static BigDecimal accumulate(BigDecimal total, BigDecimal unitPrice, Integer quantity) {
        if (unitPrice == null) {
            return total;
        }
        return total.add(unitPrice.multiply(BigDecimal.valueOf(quantity != null ? quantity : 0)));
    }

    private static int safeMultiply(Integer value, Integer quantity) {
        return (value != null ? value : 0) * (quantity != null ? quantity : 0);
    }


    /**
     * 计算上门取件的配送时间
     * <p>
     * 专门用于上门取件服务的时间计算，包括：
     * 1. 确定取件时间（预约时间或默认时间）
     * 2. 计算服务处理时间
     * 3. 计算上门服务通勤时间
     * 4. 计算最终送达时间
     * </p>
     *
     * @param localDateTime 预约时间
     * @param totalProcessingHour 订单详情
     * @return 时间数组 [取件时间, 送达时间]
     */
    public static LocalDateTime[] calculateDoorToDoorDeliveryTimes(LocalDateTime localDateTime,
                                                             Integer totalProcessingHour) {
        LocalDateTime now = LocalDateTime.now();

        // 1. 确定取件时间
        LocalDateTime pickupTime = determineDoorToDoorPickupTime(localDateTime, now);

        // 2. 计算服务处理时间
        int serviceProcessingHours = calculateServiceProcessingTime(totalProcessingHour);

        // 3. 计算上门服务通勤时间（固定4小时：去2小时+回2小时）
        int commutingHours = calculateDoorToDoorCommutingTime();

        // 4. 计算送达时间 = 取件时间 + 服务处理时间 + 通勤时间
        LocalDateTime deliveryTime = pickupTime.plusHours(serviceProcessingHours + commutingHours);

        log.debug("上门取件时间计算详情 - 取件时间：{}，服务处理：{}小时，通勤时间：{}小时，送达时间：{}",
                pickupTime, serviceProcessingHours, commutingHours, deliveryTime);

        return new LocalDateTime[]{pickupTime, deliveryTime};
    }





    /**
     * 确定上门取件的取件时间
     * <p>
     * 优先使用用户预约的时间，如果没有预约则使用默认时间（当前时间+2小时）。
     * </p>
     *
     * @param localDateTime 订单创建请求
     * @param now 当前时间
     * @return 确定的取件时间
     */
    public static LocalDateTime determineDoorToDoorPickupTime(LocalDateTime localDateTime, LocalDateTime now) {


        if (localDateTime != null) {
            log.debug("使用用户预约时间作为取件时间：{}", localDateTime);
            return localDateTime;
        } else {
            LocalDateTime defaultPickupTime = now.plusHours(2);
            log.debug("使用默认取件时间（当前时间+2小时）：{}", defaultPickupTime);
            return defaultPickupTime;
        }
    }

    /**
     * 计算服务处理时间
     * <p>
     * 获取订单的服务处理时间，如果订单详情中已计算则直接使用，
     * 否则使用默认的服务时间计算逻辑。
     * </p>
     *
     * @param totalProcessingHours 总情绪时间
     * @return 服务处理时间（小时）
     */
    public static int calculateServiceProcessingTime(Integer totalProcessingHours) {
        log.debug("使用订单详情中的处理时间：{}小时", totalProcessingHours);
        return totalProcessingHours;
    }


    /**
     * 计算上门服务通勤时间
     * <p>
     * 上门服务的通勤时间包括：
     * - 去程时间：2小时（从服务点到用户地址）
     * - 回程时间：2小时（从用户地址回到服务点）
     * - 总计：4小时
     * </p>
     *
     * @return 上门服务通勤时间（小时）
     */
    public static int calculateDoorToDoorCommutingTime() {
        int commutingHours = 4; // 上门服务固定4小时通勤时间
        log.debug("上门服务通勤时间：{}小时（去程2小时+回程2小时）", commutingHours);
        return commutingHours;
    }


    /**
     * 根据OrderAmountEntity结构计算总处理时间
     * <p>
     * 计算逻辑：
     * 1. 基础时间：标准处理时间 或 加急处理时间（二选一）
     * 2. 精洗时间：如果选择精洗，在基础时间上加上精洗额外时间
     * </p>
     *
     * @param amountInfo 订单金额信息，包含各种时间数据
     * @param userOptions 用户选项（精洗、加急等）
     * @return 总处理时间（小时）
     */
    public static Integer calculateTotalProcessingHoursFromAmountInfo(OrderAmountEntity amountInfo,
                                                                UserOptionRequest userOptions) {
        log.debug("开始计算总处理时间，用户选项 - 精洗：{}，加急：{}",
                userOptions.getIsPremium(), userOptions.getIsUrgent());

        // 1. 确定基础处理时间（标准洗 或 加急洗）
        Integer baseProcessingHours;
        if (Boolean.TRUE.equals(userOptions.getIsUrgent())) {
            // 加急洗：使用加急处理时间
            baseProcessingHours = amountInfo.getTotalExpeditedProcessingHours();
            log.debug("选择加急洗，基础处理时间：{}小时", baseProcessingHours);
        } else {
            // 标准洗：使用标准处理时间
            baseProcessingHours = amountInfo.getTotalStandardProcessingHours();
            log.debug("选择标准洗，基础处理时间：{}小时", baseProcessingHours);
        }

        // 2. 如果选择精洗，在基础时间上加上精洗额外时间
        Integer totalProcessingHours = baseProcessingHours != null ? baseProcessingHours : 0;
        if (Boolean.TRUE.equals(userOptions.getIsPremium())) {
            Integer premiumExtraHours = amountInfo.getTotalPremiumWashExtraHours();
            if (premiumExtraHours != null) {
                totalProcessingHours += premiumExtraHours;
                log.debug("选择精洗，增加精洗时间：{}小时，总时间：{}小时",
                        premiumExtraHours, totalProcessingHours);
            }
        }

        log.info("总处理时间计算完成：{}小时（基础：{}小时，精洗额外：{}小时）",
                totalProcessingHours,
                baseProcessingHours,
                Boolean.TRUE.equals(userOptions.getIsPremium()) ?
                        amountInfo.getTotalPremiumWashExtraHours() : 0);

        return totalProcessingHours;
    }
}
