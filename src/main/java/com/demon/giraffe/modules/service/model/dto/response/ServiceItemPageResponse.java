package com.demon.giraffe.modules.service.model.dto.response;

import com.demon.giraffe.modules.service.model.enums.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 服务项目分页响应
 */
@Data
@Schema(name = "ServiceItemPageResponse", description = "服务项目分页响应")
public class ServiceItemPageResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "服务ID")
    private Long id;

    @Schema(description = "服务名称")
    private String name;

    @Schema(description = "服务编码")
    private String code;

    @Schema(description = "服务展示图片URL")
    private String imageUrl;

    @Schema(description = "计价单位")
    private PriceUnit unit;

    @Schema(description = "标准洗涤价格(单价加起来的价格，较贵)")
    private BigDecimal standardWashPrice;

    @Schema(description = "服务标签")
    private ServiceTag tags;

    @Schema(description = "状态")
    private CommonStatusEnum status;

    @Schema(description = "销售数量统计")
    private Integer salesCount;
}