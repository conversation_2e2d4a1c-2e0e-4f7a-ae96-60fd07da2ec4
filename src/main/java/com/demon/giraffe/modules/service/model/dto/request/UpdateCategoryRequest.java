package com.demon.giraffe.modules.service.model.dto.request;

import com.demon.giraffe.modules.service.model.enums.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@Schema(description = "更新分类请求参数")
public class UpdateCategoryRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @NotBlank(message = "分类名称不能为空")
    @Schema(description = "分类名称", required = true)
    private String name;
    
    @Schema(description = "分类描述")
    private String description;
    
    @Schema(description = "分类图标URL")
    private String icon;
    
    @Schema(description = "分类状态")
    private CommonStatusEnum status;
}