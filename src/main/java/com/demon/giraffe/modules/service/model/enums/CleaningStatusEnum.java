package com.demon.giraffe.modules.service.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@Schema(description = "衣物清洗状态枚举")
public enum CleaningStatusEnum implements IEnum<Integer> {

    @Schema(description = "待收衣")
    WAITING_RECEIVE(0, "待收衣"),

    @Schema(description = "已收衣")
    RECEIVED(1, "已收衣"),

    @Schema(description = "清洗中")
    CLEANING(2, "清洗中"),

    @Schema(description = "烘干中")
    DRYING(3, "烘干中"),

    @Schema(description = "熨烫中")
    IRONING(4, "熨烫中"),

    @Schema(description = "质检中")
    QUALITY_CHECK(5, "质检中"),

    @Schema(description = "已打包")
    PACKAGED(6, "已打包"),

    @Schema(description = "派送中")
    OUT_FOR_DELIVERY(7, "派送中"),

    @Schema(description = "已完成")
    COMPLETED(8, "已完成");

    @EnumValue
    private final Integer code;
    private final String description;

    public static CleaningStatusEnum of(Integer code) {
        for (CleaningStatusEnum e : values()) {
            if (e.code.equals(code)) return e;
        }
        return WAITING_RECEIVE;
    }

    @Override
    public Integer getValue() {
        return code;
    }
}
