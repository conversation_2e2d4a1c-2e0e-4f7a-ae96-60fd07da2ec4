package com.demon.giraffe.modules.service.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.service.model.dto.request.*;
import com.demon.giraffe.modules.service.model.dto.response.CategoryResponse;
import com.demon.giraffe.modules.service.service.ServiceCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/service/category")
@RequiredArgsConstructor
@Tag(name = "服务分类管理", description = "服务分类管理接口")
public class ServiceCategoryController {
    private final ServiceCategoryService service;

    @PostMapping
    @Operation(summary = "创建分类", description = "创建新的服务分类")
    @ApiResponse(responseCode = "200", description = "创建成功",
            content = @Content(schema = @Schema(implementation = CategoryResponse.class)))
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<CategoryResponse> create(
            @Valid @RequestBody CreateCategoryRequest request) {
        return ResultBean.success(service.create(request));
    }

    @PostMapping("/{id}")
    @Operation(summary = "更新分类", description = "根据ID更新分类信息")
    @ApiResponse(responseCode = "200", description = "更新成功",
            content = @Content(schema = @Schema(implementation = CategoryResponse.class)))
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<CategoryResponse> update(
            @PathVariable Long id,
            @Valid @RequestBody UpdateCategoryRequest request) {
        return ResultBean.success(service.update(id, request));
    }

    @PostMapping("/sort")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    @Operation(summary = "更新排序", description = "批量更新分类排序")
    @ApiResponse(responseCode = "200", description = "排序更新成功")
    public ResultBean<Void> updateSort(@Valid @RequestBody UpdateSortRequest request) {
        service.updateSort(request);
        return ResultBean.success();
    }

    @PostMapping("/batch-delete")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    @Operation(summary = "批量删除", description = "批量删除分类")
    @ApiResponse(responseCode = "200", description = "删除成功")
    public ResultBean<Void> batchDelete(@RequestBody List<Long> ids) {
        service.batchDelete(ids);
        return ResultBean.success();
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取分类详情", description = "根据ID获取分类详情")
    @ApiResponse(responseCode = "200", description = "成功返回",
            content = @Content(schema = @Schema(implementation = CategoryResponse.class)))
    public ResultBean<CategoryResponse> getById(@PathVariable Long id) {
        return ResultBean.success(service.getById(id));
    }

    @GetMapping("/list")
    @Operation(summary = "获取全部分类", description = "获取所有分类（按排序值降序）")
    @ApiResponse(responseCode = "200", description = "成功返回",
            content = @Content(schema = @Schema(implementation = CategoryResponse.class)))
    public ResultBean<List<CategoryResponse>> listAll() {
        return ResultBean.success(service.listAll());
    }
}