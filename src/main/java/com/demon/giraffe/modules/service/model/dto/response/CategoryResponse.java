package com.demon.giraffe.modules.service.model.dto.response;

import com.demon.giraffe.modules.service.model.enums.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Schema(description = "分类响应数据")
public class CategoryResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "分类ID")
    private Long id;

    @Schema(description = "分类名称")
    private String name;

    @Schema(description = "分类描述")
    private String description;

    @Schema(description = "分类图标URL")
    private String icon;

    @Schema(description = "排序值")
    private Integer sort;

    @Schema(description = "状态")
    private CommonStatusEnum status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}