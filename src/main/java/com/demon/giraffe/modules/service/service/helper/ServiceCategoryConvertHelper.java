package com.demon.giraffe.modules.service.service.helper;


import com.demon.giraffe.modules.service.model.dto.response.CategoryResponse;
import com.demon.giraffe.modules.service.model.po.ServiceCategoryPo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Component
public class ServiceCategoryConvertHelper {
    public CategoryResponse convertToResponse(ServiceCategoryPo po) {
        CategoryResponse response = new CategoryResponse();
        BeanUtils.copyProperties(po, response);
        return response;
    }
}