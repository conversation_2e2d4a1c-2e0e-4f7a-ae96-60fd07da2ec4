package com.demon.giraffe.modules.service.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 服务标签枚举
 */
@Getter
public enum ServiceTag implements IEnum<Integer> {
    RECOMMEND(1, "recommend", "推荐", "#FF4D4F"),
    HOT(2, "hot", "热门", "#FF7D00"),
    NEW(3, "new", "新服务", "#13C2C2"),
    DISCOUNT(4, "discount", "特惠", "#F5222D"),
    ECO_FRIENDLY(5, "eco", "环保", "#52C41A"),
    FAST_DELIVERY(6, "fast", "快速", "#1890FF"),
    PREMIUM(7, "premium", "高端", "#722ED1"),
    MEMBER_EXCLUSIVE(8, "member", "会员专享", "#EB2F96"),
    SEASONAL(9, "seasonal", "季节限定", "#FAAD14"),
    GROUP_DISCOUNT(10, "group", "团购优惠", "#FA8C16");

    @EnumValue
    private final Integer value;
    private final String code;
    private final String label;
    private final String color;  // 标签显示颜色

    private static final Map<Integer, ServiceTag> VALUE_MAP = Collections.unmodifiableMap(
            Arrays.stream(values()).collect(Collectors.toMap(ServiceTag::getValue, e -> e))
    );
    private static final Map<String, ServiceTag> CODE_MAP = Collections.unmodifiableMap(
            Arrays.stream(values()).collect(Collectors.toMap(ServiceTag::getCode, e -> e))
    );

    ServiceTag(Integer value, String code, String label, String color) {
        this.value = value;
        this.code = code;
        this.label = label;
        this.color = color;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    public static ServiceTag fromValue(Integer value) {
        return VALUE_MAP.get(value);
    }

    public static ServiceTag fromCode(String code) {
        return CODE_MAP.get(code);
    }

    /**
     * 获取用于前端显示的标签样式
     */
    public String getStyle() {
        return String.format("color: %s; background-color: %s20; border-color: %s10",
                color, color, color);
    }
}