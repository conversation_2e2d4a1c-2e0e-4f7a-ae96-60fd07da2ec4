package com.demon.giraffe.modules.service.model.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "服务详情配置")
public class ServiceDetailConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "衣物清洗选项")
    private List<CleaningItem> cleaningItems;

}