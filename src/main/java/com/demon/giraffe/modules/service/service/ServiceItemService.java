package com.demon.giraffe.modules.service.service;

import com.demon.giraffe.modules.service.model.dto.request.ServiceItemCreateRequest;
import com.demon.giraffe.modules.service.model.dto.request.ServiceItemUpdateRequest;
import com.demon.giraffe.modules.service.model.dto.response.ServiceItemResponse;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.modules.service.model.entity.ServiceItemEntity;

import java.util.List;
import java.util.Set;

/**
 * 服务项业务接口
 */
public interface ServiceItemService {

    /**
     * 创建服务项
     * @param request 创建请求
     * @return 创建的服务项ID
     */
    Long createServiceItem(ServiceItemCreateRequest request);

    /**
     * 更新服务项
     * @param request 更新请求
     * @return 是否更新成功
     */
    boolean updateServiceItem(ServiceItemUpdateRequest request);

    /**
     * 获取服务项详情
     * @param id 服务项ID
     * @return 服务项详情
     */
    ServiceItemResponse getServiceItemDetail(Long id);


    /**
     * 批量获取服务项详情
     * @param ids 服务项ID列表
     * @return 服务项详情列表（保持与入参相同的顺序，不存在的ID对应位置为null）
     */
    List<ServiceItemEntity> batchGetServiceItemDetails(Set<Long> ids);

    /**
     * 删除服务项
     * @param id 服务项ID
     * @return 是否删除成功
     */
    boolean deleteServiceItem(Long id);

    /**
     * 增加销售数量
     * @param id 服务项ID
     * @param count 增加的数量
     * @return 是否更新成功
     */
    boolean increaseSalesCount(Long id, Integer count);

    /**
     * 减少销售数量
     * @param id 服务项ID
     * @param count 减少的数量
     * @return 是否更新成功
     */
    boolean decreaseSalesCount(Long id, Integer count);

    /**
     * 根据地域获取服务项列表
     * @param county 县级地域枚举

     * @return 服务项列表
     */
    List<ServiceItemResponse> getServiceItemsByRegion(CountyEnum county);

    /**
     * 根据标签获取服务项列表
     * @param categoryId 分类ID
     * @param countyEnum 服务状态(可选)
     * @return 服务项列表
     */
    List<ServiceItemResponse> getServiceItemsByCategory(Long categoryId, CountyEnum countyEnum);


}