package com.demon.giraffe.modules.service.model.entity;


import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.modules.service.model.enums.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 服务项业务实体（领域对象）
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "服务项业务实体")
public class ServiceItemEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "服务项ID")
    private Long serviceItemId;

    @Schema(description = "服务分类IDs")
    private List<Long> categoryIds;

    @Schema(description = "服务名称")
    private String name;

    @Schema(description = "标准洗涤价格(单价加起来的价格，较贵)")
    private BigDecimal standardWashPrice;

    @Schema(description = "精洗额外费用")
    private BigDecimal premiumWashExtraFee;

    @Schema(description = "精洗额外时间(小时)")
    private Integer premiumWashExtraHours;

    @Schema(description = "服务定价")
    private BigDecimal marketReferencePrice;

    @Schema(description = "标准处理时长(小时)")
    private Integer standardProcessingHours;

    @Schema(description = "加急处理时长(小时)")
    private Integer expeditedProcessingHours;

    @Schema(description = "加急服务费用")
    private BigDecimal expeditedServiceFee;

    @Schema(description = "区域编码")
    private CountyEnum addressCode;

    @Schema(description = "服务详情配置")
    private ServiceDetailConfig detailConfig;

    @Schema(description = "状态")
    private CommonStatusEnum status;

}