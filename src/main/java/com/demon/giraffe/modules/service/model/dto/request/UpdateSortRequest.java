package com.demon.giraffe.modules.service.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "更新排序请求参数")
public class UpdateSortRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @NotNull(message = "排序列表不能为空")
    @Schema(description = "分类ID和排序值列表", required = true)
    private List<CategorySortItem> sortList;
    
    @Data
    public static class CategorySortItem implements Serializable {
        private static final long serialVersionUID = 1L;
        
        @Schema(description = "分类ID", required = true)
        private Long id;
        
        @Schema(description = "排序值", required = true)
        private Integer sort;
    }
}