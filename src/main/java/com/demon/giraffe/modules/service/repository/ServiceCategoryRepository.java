package com.demon.giraffe.modules.service.repository;

import com.demon.giraffe.modules.service.model.po.ServiceCategoryPo;

import java.util.*;

public interface ServiceCategoryRepository {
    ServiceCategoryPo save(ServiceCategoryPo po);

    ServiceCategoryPo update(ServiceCategoryPo po);

    Optional<ServiceCategoryPo> findById(Long id);

    List<ServiceCategoryPo> findAll();

    void deleteById(Long id);

    void batchDelete(List<Long> ids);

    List<ServiceCategoryPo> findAllOrderBySort();

    void batchUpdateSort(List<ServiceCategoryPo> categories);

    /**
     * 根据 ID 集合查询对应的分类记录。
     *
     * @param ids 分类主键集合
     * @return 匹配的分类实体列表
     */
    List<ServiceCategoryPo> findByIds(Set<Long> ids);

    /**
     * 获取启用状态的服务分类，按排序值降序，最多返回 limit 条
     *
     * @param limit 限制条数
     * @return 分类实体列表
     */
    List<ServiceCategoryPo> findAvailableOrderBySort(int limit);


}