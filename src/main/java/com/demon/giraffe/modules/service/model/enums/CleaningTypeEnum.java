package com.demon.giraffe.modules.service.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@Schema(description = "清洗方式类型枚举")
public enum CleaningTypeEnum implements IEnum<Integer> {

    @Schema(description = "标准洗")
    STANDARD(0, "标洗"),

    @Schema(description = "精洗")
    FINE(1, "精洗");

    @EnumValue
    private final Integer code;
    private final String description;

    public static CleaningTypeEnum of(Integer code) {
        for (CleaningTypeEnum e : values()) {
            if (e.code.equals(code)) return e;
        }
        return STANDARD;
    }

    @Override
    public Integer getValue() {
        return code;
    }
}
