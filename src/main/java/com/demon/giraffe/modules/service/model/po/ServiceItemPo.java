package com.demon.giraffe.modules.service.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.framework.mybatis.typehandler.*;
import com.demon.giraffe.modules.service.model.entity.ServiceDetailConfig;
import com.demon.giraffe.modules.service.model.enums.*;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 核心服务项目
 */

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "service_item", autoResultMap = true)
public class ServiceItemPo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "服务分类IDs")
    @NotNull(message = "可以划分多个")
    @TableField(value = "category_ids", typeHandler = LongArrayListTypeHandler.class)
    private List<Long> categoryIds;

    @Schema(description = "服务名称")
    @NotBlank(message = "服务名称不能为空")
    @Size(max = 50, message = "服务名称长度不能超过50字符")
    @TableField("name")
    private String name;

    @Schema(description = "服务详细描述")
    @TableField("description")
    private String description;

    @Schema(description = "服务封面")
    @Size(max = 255)
    @TableField(value = "surface_plot")
    private String surfacePlot;

    @Schema(description = "标准洗涤价格(单价加起来的价格，较贵)")
    @NotNull(message = "标准洗涤价格不能为空")
    @Digits(integer = 6, fraction = 2, message = "标准洗涤价格格式错误")
    @TableField("base_price")
    private BigDecimal standardWashPrice;

    @Schema(description = "精洗额外费用")
    @NotNull(message = "精洗额外费用不能为空")
    @Digits(integer = 6, fraction = 2, message = "精洗额外费用格式错误")
    @TableField("precise_price")
    private BigDecimal premiumWashExtraFee;

    @Schema(description = "精洗额外时间(小时)")
    @TableField("premium_extra_hours")
    private Integer premiumWashExtraHours;

    @Schema(description = "服务定价")
    @Digits(integer = 6, fraction = 2, message = "服务定价格式错误")
    @TableField("market_price")
    private BigDecimal marketReferencePrice;

    @Schema(description = "标准处理时长(小时)", required = true)
    @NotNull(message = "标准处理时长不能为空")
    @Min(value = 1, message = "标准处理时长不能小于1")
    @TableField("process_hours")
    private Integer standardProcessingHours;

    @Schema(description = "清洗前后对比图")
    @TableField(value = "compare_image")
    private String compareImage;


    @Schema(description = "加急处理时长(小时)")
    @TableField("rush_hours")
    private Integer expeditedProcessingHours;

    @Schema(description = "加急服务费用")
    @Digits(integer = 6, fraction = 2, message = "加急服务费用格式错误")
    @TableField("rush_fee")
    private BigDecimal expeditedServiceFee;


    @Schema(description = "区域ID")
    @TableField("address_code")
    private CountyEnum addressCode;

    @Schema(description = "服务详情配置(JSON)")
    @TableField(value = "detail_config", typeHandler = ServiceDetailConfigTypeHandler.class)
    private ServiceDetailConfig detailConfig;

    @Schema(description = "状态(0正常/1停用)")
    @TableField("status")
    private CommonStatusEnum status;

    @Schema(description = "排序权重")
    @TableField("sort")
    private Integer sort;

    @Schema(description = "销售数量统计")
    @TableField("sales_count")
    private Integer salesCount;

}