package com.demon.giraffe.modules.service.model.dto.response;

import com.demon.giraffe.modules.service.model.entity.ServiceDetailConfig;
import com.demon.giraffe.modules.service.model.enums.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 服务项目详情响应
 */
@Data
@Schema(name = "ServiceItemDetailResponse", description = "服务项目详情响应")
public class ServiceItemDetailResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "服务ID")
    private Long id;

    @Schema(description = "服务类型")
    private ServiceType serviceType;

    @Schema(description = "服务分类ID")
    private Long categoryId;

    @Schema(description = "服务名称")
    private String name;

    @Schema(description = "服务编码")
    private String code;

    @Schema(description = "服务详细描述")
    private String description;

    @Schema(description = "服务展示图片URL")
    private String imageUrl;

    @Schema(description = "计价单位")
    private PriceUnit unit;

    @Schema(description = "标准洗涤价格(单价加起来的价格，较贵)")
    private BigDecimal standardWashPrice;

    @Schema(description = "服务定价")
    private BigDecimal marketReferencePrice;

    @Schema(description = "标准处理天数")
    private Integer processDays;

    @Schema(description = "加急处理天数")
    private Integer rushDays;

    @Schema(description = "加急服务费用")
    private BigDecimal expeditedServiceFee;

    @Schema(description = "服务标签")
    private ServiceTag tags;

    @Schema(description = "服务详情配置")
    private ServiceDetailConfig detailConfig;

    @Schema(description = "状态")
    private CommonStatusEnum status;

    @Schema(description = "排序权重")
    private Integer sort;

    @Schema(description = "销售数量统计")
    private Integer salesCount;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}