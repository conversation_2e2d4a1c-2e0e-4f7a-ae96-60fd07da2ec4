package com.demon.giraffe.modules.service.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

@Getter
public enum TopCategory implements IEnum<Integer> {
    UPPER(1, "上装类"),
    LOWER(2, "下装类"),
    FOOTWEAR(3, "鞋履类");

    @EnumValue
    private final Integer code;
    private final String name;

    TopCategory(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public Integer getValue() {
        return code;
    }
}