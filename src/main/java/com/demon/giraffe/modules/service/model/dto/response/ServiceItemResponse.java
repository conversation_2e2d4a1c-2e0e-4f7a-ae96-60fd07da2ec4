package com.demon.giraffe.modules.service.model.dto.response;

import com.demon.giraffe.modules.common.model.vo.CommonBannerVo;
import com.demon.giraffe.modules.service.model.enums.CommonStatusEnum;
import com.demon.giraffe.modules.service.model.entity.ServiceDetailConfig;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 服务项响应数据
 */
@Data
@Schema(name = "ServiceItemResponse", description = "服务项响应数据")
public class ServiceItemResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "服务ID")
    private Long id;

    @Schema(description = "服务分类IDs")
    private List<Long> categoryIds;

    @Schema(description = "服务名称")
    private String name;


    @Schema(description = "服务详细描述")
    private String description;

    @Schema(description = "服务封面")
    private String surfacePlot;

    @Schema(description = "清洗前后对比图")
    private String compareImage;

    @Schema(description = "轮播图")
    private List<CommonBannerVo> bannerVos;

    @Schema(description = "标准洗涤价格(单价加起来的价格，较贵)")
    private BigDecimal standardWashPrice;

    @Schema(description = "精洗额外费用")
    private BigDecimal premiumWashExtraFee;

    @Schema(description = "精洗额外时间(小时)")
    private Integer premiumWashExtraHours;

    @Schema(description = "服务定价")
    private BigDecimal marketReferencePrice;

    @Schema(description = "标准处理时长(小时)")
    private Integer standardProcessingHours;

    @Schema(description = "加急处理时长(小时)")
    private Integer expeditedProcessingHours;

    @Schema(description = "加急服务费用")
    private BigDecimal expeditedServiceFee;

    @Schema(description = "服务区域(县级)")
    private CountyEnum addressCode;

    @Schema(description = "服务详情配置")
    private ServiceDetailConfig detailConfig;

    @Schema(description = "状态(0正常/1停用)")
    private CommonStatusEnum status;

    @Schema(description = "排序权重")
    private Integer sort;

    @Schema(description = "销售数量统计")
    private Integer salesCount;

}