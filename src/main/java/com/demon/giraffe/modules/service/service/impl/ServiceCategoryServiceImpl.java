package com.demon.giraffe.modules.service.service.impl;

import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.modules.service.model.dto.request.*;
import com.demon.giraffe.modules.service.model.dto.response.CategoryResponse;
import com.demon.giraffe.modules.service.model.po.ServiceCategoryPo;
import com.demon.giraffe.modules.service.repository.ServiceCategoryRepository;
import com.demon.giraffe.modules.service.service.ServiceCategoryService;
import com.demon.giraffe.modules.service.model.enums.CommonStatusEnum;
import com.demon.giraffe.modules.service.service.helper.ServiceCategoryConvertHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ServiceCategoryServiceImpl implements ServiceCategoryService {
    private final ServiceCategoryRepository repository;
    private final ServiceCategoryConvertHelper convertHelper;
    @Override
    public CategoryResponse create(CreateCategoryRequest request) {
        ServiceCategoryPo po = new ServiceCategoryPo();
        BeanUtils.copyProperties(request, po);
        po.setSort(0); // 默认排序值为0
        po.setStatus(CommonStatusEnum.NORMAL); // 默认启用状态
        ServiceCategoryPo saved = repository.save(po);
        return convertHelper.convertToResponse(saved);
    }

    @Override
    public CategoryResponse update(Long id, UpdateCategoryRequest request) {
        ServiceCategoryPo po = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("分类不存在"));
        BeanUtils.copyProperties(request, po);
        repository.update(po);
        return convertHelper.convertToResponse(po);
    }

    @Override
    public void updateSort(UpdateSortRequest request) {
        List<ServiceCategoryPo> categories = request.getSortList().stream()
                .map(item -> {
                    ServiceCategoryPo po = new ServiceCategoryPo();
                    po.setId(item.getId());
                    po.setSort(item.getSort());
                    return po;
                }).collect(Collectors.toList());
        repository.batchUpdateSort(categories);
    }

    @Override
    public void batchDelete(List<Long> ids) {
        repository.batchDelete(ids);
    }


    @Override
    public List<CategoryResponse> listAll() {
        return repository.findAllOrderBySort().stream()
                .map(convertHelper::convertToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public CategoryResponse getById(Long id) {
        return repository.findById(id)
                .map(convertHelper::convertToResponse)
                .orElseThrow(() -> new BusinessException("分类不存在"));
    }


    @Override
    public List<ServiceCategoryPo> listByIds(Set<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return List.of();
        }
        return repository.findByIds(ids);
    }

    @Override
    public List<CategoryResponse> listAvailable(int limit) {
        // 查询可用状态的服务分类
        List<ServiceCategoryPo> availableList = repository.findAvailableOrderBySort(limit);

        return availableList.stream()
                .map(convertHelper::convertToResponse)
                .collect(Collectors.toList());
    }

}