package com.demon.giraffe.modules.service.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@Schema(description = "通用状态")
public enum CommonStatusEnum implements IEnum<Integer> {
    NORMAL(0, "正常"),
    DISABLED(1, "停用");

    @EnumValue
    private final Integer code;
    private final String description;

    public static CommonStatusEnum of(Integer code) {
        for (CommonStatusEnum status : values()) {
            if (status.code.equals(code)) return status;
        }
        return NORMAL;
    }

    @Override
    public Integer getValue() {
        return code;
    }
}
