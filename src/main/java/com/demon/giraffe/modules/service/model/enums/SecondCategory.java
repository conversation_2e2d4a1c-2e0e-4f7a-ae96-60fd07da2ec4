package com.demon.giraffe.modules.service.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

@Getter
public enum SecondCategory implements IEnum<Integer> {
    // 上装类二级分类
    OUTERWEAR(101, TopCategory.UPPER, "外套类"),
    KNITWEAR(102, TopCategory.UPPER, "针织类"),
    SHIRTS(103, TopCategory.UPPER, "衬衫类"),
    CASUAL_WEAR(104, TopCategory.UPPER, "休闲类"),
    FUNCTIONAL_WEAR(105, TopCategory.UPPER, "功能类"),
    HOME_WEAR(106, TopCategory.UPPER, "家居类"),

    // 下装类二级分类
    PANTS(201, TopCategory.LOWER, "裤装类"),
    FUNCTIONAL_PANTS(202, TopCategory.LOWER, "功能裤装"),
    SHORTS(203, TopCategory.LOWER, "短裤"),
    SKIRTS(204, TopCategory.LOWER, "裙装类"),
    HALF_SKIRTS(205, TopCategory.LOWER, "半身裙"),

    // 鞋履类二级分类
    SNEAKERS(301, TopCategory.FOOTWEAR, "运动鞋"),
    CASUAL_SHOES(302, TopCategory.FOOTWEAR, "休闲鞋"),
    SPECIAL_SHOES(303, TopCategory.FOOTWEAR, "特殊鞋");

    @EnumValue
    private final Integer code;
    private final TopCategory topCategory;
    private final String name;

    SecondCategory(Integer code, TopCategory topCategory, String name) {
        this.code = code;
        this.topCategory = topCategory;
        this.name = name;
    }


    @Override
    public Integer getValue() {
        return code;
    }
}