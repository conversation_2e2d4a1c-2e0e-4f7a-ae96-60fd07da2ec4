package com.demon.giraffe.modules.service.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@Schema(description = "服务分类层级")
public enum CategoryLevelEnum implements IEnum<Integer> {
    FIRST(1, "一级分类"),
    SECOND(2, "二级分类"),
    THIRD(3, "三级分类");
    @EnumValue
    private final Integer code;
    private final String description;

    public static CategoryLevelEnum of(Integer code) {
        for (CategoryLevelEnum level : values()) {
            if (level.code.equals(code)) return level;
        }
        return FIRST;
    }

    @Override
    public Integer getValue() {
        return code;
    }
}
