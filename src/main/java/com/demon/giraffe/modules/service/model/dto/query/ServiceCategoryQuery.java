package com.demon.giraffe.modules.service.model.dto.query;

import com.demon.giraffe.modules.service.model.enums.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 服务分类查询条件
 */
@Data
@Schema(description = "服务分类查询条件")
public class ServiceCategoryQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "分类名称")
    private String name;

    @Schema(description = "分类类型")
    private String type;

    @Schema(description = "状态")
    private CommonStatusEnum status;
}