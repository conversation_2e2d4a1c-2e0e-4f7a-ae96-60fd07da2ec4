package com.demon.giraffe.modules.service.service;

import com.demon.giraffe.modules.service.model.dto.request.*;
import com.demon.giraffe.modules.service.model.dto.response.CategoryResponse;
import com.demon.giraffe.modules.service.model.po.ServiceCategoryPo;

import java.util.List;
import java.util.Set;

/**
 * 服务分类管理接口，提供分类的增删改查与排序操作。
 */
public interface ServiceCategoryService {

    /**
     * 创建新的服务分类。
     *
     * @param request 创建分类请求参数
     * @return 创建后的分类信息
     */
    CategoryResponse create(CreateCategoryRequest request);

    /**
     * 更新指定 ID 的服务分类信息。
     *
     * @param id 分类主键 ID
     * @param request 更新分类请求参数
     * @return 更新后的分类信息
     */
    CategoryResponse update(Long id, UpdateCategoryRequest request);

    /**
     * 批量更新分类的排序信息。
     *
     * @param request 排序更新请求参数，包含分类 ID 与排序值的映射列表
     */
    void updateSort(UpdateSortRequest request);

    /**
     * 批量逻辑删除指定的服务分类。
     *
     * @param ids 要删除的分类 ID 列表
     */
    void batchDelete(List<Long> ids);

    /**
     * 获取所有有效的服务分类（按排序字段升序）。
     *
     * @return 分类列表
     */
    List<CategoryResponse> listAll();

    /**
     * 根据分类 ID 查询对应的分类信息。
     *
     * @param id 分类主键 ID
     * @return 分类信息
     */
    CategoryResponse getById(Long id);

    /**
     * 根据分类主键 ID 集合批量获取分类实体列表。
     *
     * @param ids 分类主键 ID 集合
     * @return 分类实体列表
     */
    List<ServiceCategoryPo> listByIds(Set<Long> ids);


    /**
     * 获取指定数量的可用服务（启用状态），按排序值倒序。
     *
     * @param limit 最大返回条数
     * @return 可用服务列表
     */
    List<CategoryResponse> listAvailable(int limit);

}
