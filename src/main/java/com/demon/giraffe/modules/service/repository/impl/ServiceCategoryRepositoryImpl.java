package com.demon.giraffe.modules.service.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.demon.giraffe.modules.service.mapper.ServiceCategoryMapper;
import com.demon.giraffe.modules.service.model.enums.CommonStatusEnum;
import com.demon.giraffe.modules.service.model.po.ServiceCategoryPo;
import com.demon.giraffe.modules.service.repository.ServiceCategoryRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.*;

@Repository
@RequiredArgsConstructor
public class ServiceCategoryRepositoryImpl implements ServiceCategoryRepository {
    private final ServiceCategoryMapper mapper;

    @Override
    public ServiceCategoryPo save(ServiceCategoryPo po) {
        mapper.insert(po);
        return po;
    }

    @Override
    public ServiceCategoryPo update(ServiceCategoryPo po) {
        mapper.updateById(po);
        return po;
    }

    @Override
    public Optional<ServiceCategoryPo> findById(Long id) {
        return Optional.ofNullable(mapper.selectById(id));
    }

    @Override
    public List<ServiceCategoryPo> findAll() {
        return mapper.selectList(new LambdaQueryWrapper<>());
    }

    @Override
    public void deleteById(Long id) {
        mapper.deleteById(id);
    }

    @Override
    public void batchDelete(List<Long> ids) {
        mapper.deleteBatchIds(ids);
    }

    @Override
    public List<ServiceCategoryPo> findAllOrderBySort() {
        return mapper.selectList(new LambdaQueryWrapper<ServiceCategoryPo>()
                .orderByDesc(ServiceCategoryPo::getSort));
    }

    @Override
    public void batchUpdateSort(List<ServiceCategoryPo> categories) {
        categories.forEach(category -> {
            mapper.update(null, new LambdaUpdateWrapper<ServiceCategoryPo>()
                    .eq(ServiceCategoryPo::getId, category.getId())
                    .set(ServiceCategoryPo::getSort, category.getSort()));
        });
    }

    @Override
    public List<ServiceCategoryPo> findByIds(Set<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return List.of();
        }
        return mapper.selectBatchIds(ids);
    }

    @Override
    public List<ServiceCategoryPo> findAvailableOrderBySort(int limit) {
        return mapper.selectList(new LambdaQueryWrapper<ServiceCategoryPo>()
                .eq(ServiceCategoryPo::getStatus, CommonStatusEnum.NORMAL)
                .orderByDesc(ServiceCategoryPo::getSort)
                .last("LIMIT " + limit));
    }

}