package com.demon.giraffe.modules.service.model.entity;

import com.demon.giraffe.modules.service.model.enums.ProductCategory;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Schema(description = "衣物清洗项")
public class CleaningItem implements Serializable {
    @Schema(description = "衣物类型", required = true)
    private ProductCategory productCategory;

    @Schema(description = "标准洗价格", required = true)
    private BigDecimal standardPrice;

    @Schema(description = "精洗价格差值", required = true)
    private BigDecimal finePriceDifference;

    @Schema(description = "标准洗时间(小时)", required = true)
    private Integer standardHours;

    @Schema(description = "精洗时间(小时)", required = true)
    private Integer fineHours;

    @Schema(description = "特殊处理说明")
    private String specialInstructions;
}