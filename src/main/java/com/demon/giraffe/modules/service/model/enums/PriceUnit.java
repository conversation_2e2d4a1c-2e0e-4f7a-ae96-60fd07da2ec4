package com.demon.giraffe.modules.service.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

@Getter
public enum PriceUnit implements IEnum<String> {
    PIECE("piece", "件"),
    SET("set", "套"),
    KILOGRAM("kg", "公斤"),
    SQUARE_METER("m2", "平方米");

    @EnumValue
    private final String value;
    private final String description;

    PriceUnit(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String getValue() {
        return value;
    }
}