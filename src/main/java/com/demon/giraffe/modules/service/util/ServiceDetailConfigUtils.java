package com.demon.giraffe.modules.service.util;

import com.demon.giraffe.common.util.MoneyCalculator;
import com.demon.giraffe.modules.service.model.entity.CleaningItem;
import com.demon.giraffe.modules.service.model.entity.ServiceDetailConfig;

import com.demon.giraffe.modules.service.model.enums.CleaningTypeEnum;
import com.demon.giraffe.modules.service.model.enums.ProductCategory;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

public class ServiceDetailConfigUtils {

    /**
     * 根据ProductCategory枚举创建ServiceDetailConfig
     */
    public static ServiceDetailConfig createFromCategories(List<ProductCategory> productCategories) {
        ServiceDetailConfig config = new ServiceDetailConfig();
        config.setCleaningItems(createCleaningItems(productCategories));
        return config;
    }

    /**
     * 创建CleaningItem列表
     */
    public static List<CleaningItem> createCleaningItems(List<ProductCategory> productCategories) {
        return productCategories.stream()
                .map(ServiceDetailConfigUtils::createCleaningItem)
                .collect(Collectors.toList());
    }

    /**
     * 创建单个CleaningItem
     */
    public static CleaningItem createCleaningItem(ProductCategory category) {
        CleaningItem item = new CleaningItem();
        item.setProductCategory(category);
        item.setStandardPrice(category.getStandardPrice());
        item.setFinePriceDifference(
                MoneyCalculator.subtract(
                        category.getFinePrice(),
                        category.getStandardPrice()
                )
        );
        item.setStandardHours(category.getDurationHours(CleaningTypeEnum.STANDARD));
        item.setFineHours(category.getDurationHours(CleaningTypeEnum.FINE));
        return item;
    }

    /**
     * 计算总标准洗价格
     */
    public static BigDecimal calculateTotalStandardPrice(List<CleaningItem> cleaningItems) {
        if (cleaningItems == null) {
            return MoneyCalculator.valueOf(0);
        }
        return cleaningItems.stream()
                .map(CleaningItem::getStandardPrice)
                .reduce(MoneyCalculator.valueOf(0), MoneyCalculator::add);
    }

    /**
     * 计算总精洗价格差值
     */
    public static BigDecimal calculateTotalFinePriceDifference(List<CleaningItem> cleaningItems) {
        if (cleaningItems == null) {
            return MoneyCalculator.valueOf(0);
        }
        return cleaningItems.stream()
                .map(CleaningItem::getFinePriceDifference)
                .reduce(MoneyCalculator.valueOf(0), MoneyCalculator::add);
    }

    /**
     * 计算总标准洗时间
     */
    public static int calculateTotalStandardHours(List<CleaningItem> cleaningItems) {
        if (cleaningItems == null) {
            return 0;
        }
        return cleaningItems.stream()
                .mapToInt(CleaningItem::getStandardHours)
                .sum();
    }

    /**
     * 计算总精洗时间
     */
    public static int calculateTotalFineHours(List<CleaningItem> cleaningItems) {
        if (cleaningItems == null) {
            return 0;
        }
        return cleaningItems.stream()
                .mapToInt(CleaningItem::getFineHours)
                .sum();
    }

    /**
     * 计算总精洗价格
     */
    public static BigDecimal calculateTotalFinePrice(List<CleaningItem> cleaningItems) {
        return MoneyCalculator.add(
                calculateTotalStandardPrice(cleaningItems),
                calculateTotalFinePriceDifference(cleaningItems)
        );
    }

    /**
     * 计算精洗额外时间（精洗时间 - 标洗时间）
     */
    public static int calculateTotalPremiumExtraHours(List<CleaningItem> cleaningItems) {
        if (cleaningItems == null) {
            return 0;
        }
        return cleaningItems.stream()
                .mapToInt(item -> item.getProductCategory().getDurationHours(CleaningTypeEnum.FINE)
                        - item.getProductCategory().getDurationHours(CleaningTypeEnum.STANDARD))
                .sum();
    }

}