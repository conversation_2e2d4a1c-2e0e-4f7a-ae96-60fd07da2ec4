package com.demon.giraffe.modules.service.service.impl;

import com.demon.giraffe.modules.common.model.vo.CommonBannerVo;
import com.demon.giraffe.modules.common.service.BannerConfigService;
import com.demon.giraffe.modules.service.model.dto.request.ServiceItemCreateRequest;
import com.demon.giraffe.modules.service.model.dto.request.ServiceItemUpdateRequest;
import com.demon.giraffe.modules.service.model.dto.response.ServiceItemResponse;
import com.demon.giraffe.modules.service.model.entity.ServiceItemEntity;
import com.demon.giraffe.modules.service.model.enums.CommonStatusEnum;
import com.demon.giraffe.modules.service.model.po.ServiceItemPo;
import com.demon.giraffe.modules.service.repository.ServiceItemRepository;
import com.demon.giraffe.modules.service.service.ServiceItemService;
import com.demon.giraffe.modules.service.service.helper.ServiceItemConvertHelper;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务项业务实现
 */
@Service
@RequiredArgsConstructor
public class ServiceItemServiceImpl implements ServiceItemService {

    private final ServiceItemRepository serviceItemRepository;
    private final ServiceItemConvertHelper serviceItemConvertHelper;
    private final BannerConfigService bannerConfigService;
    ;

    @Override
    public Long createServiceItem(ServiceItemCreateRequest request) {
        return serviceItemRepository.create(serviceItemConvertHelper.convertToPo(request));
    }

    @Override
    public boolean updateServiceItem(ServiceItemUpdateRequest request) {
        return serviceItemRepository.update(serviceItemConvertHelper.convertToPo(request));
    }

    @Override
    public ServiceItemResponse getServiceItemDetail(Long id) {
        ServiceItemPo po = serviceItemRepository.getById(id);
        if (po == null) {
            return null;
        }
        List<CommonBannerVo> bannerConfigByServiceId = bannerConfigService.getBannerConfigByServiceId(id);
        return serviceItemConvertHelper.convertToResponse(po, bannerConfigByServiceId);
    }

    @Override
    public List<ServiceItemEntity> batchGetServiceItemDetails(Set<Long> ids) {
        // 1. 参数校验
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        // 2. 批量查询PO
        List<ServiceItemPo> pos = serviceItemRepository.batchGetNormalItemsByIds(ids);
        return serviceItemConvertHelper.toEntityList(pos);
    }


    @Override
    public boolean deleteServiceItem(Long id) {
        return serviceItemRepository.deleteById(id);
    }

    @Override
    public boolean increaseSalesCount(Long id, Integer count) {
        return serviceItemRepository.increaseSalesCount(id, count);
    }

    @Override
    public boolean decreaseSalesCount(Long id, Integer count) {
        return serviceItemRepository.decreaseSalesCount(id, count);
    }

    @Override
    public List<ServiceItemResponse> getServiceItemsByRegion(CountyEnum county) {
        // 1. 获取服务项列表
        List<ServiceItemPo> regionalItems = serviceItemRepository.listByRegion(county, CommonStatusEnum.NORMAL);
        List<ServiceItemPo> nonRegionalItems = serviceItemRepository.listWithoutRegion(CommonStatusEnum.NORMAL);

        // 合并并去重
        Set<ServiceItemPo> allItems = new LinkedHashSet<>(); // 保持顺序
        allItems.addAll(regionalItems);
        allItems.addAll(nonRegionalItems);

        // 2. 批量获取关联的轮播图
        List<Long> serviceIds = allItems.stream()
                .map(ServiceItemPo::getId)
                .collect(Collectors.toList());
        Map<Long, List<CommonBannerVo>> bannersMap = bannerConfigService.getBannerConfigByServiceIds(serviceIds);

        // 3. 转换为响应对象
        return allItems.stream()
                .map(po -> serviceItemConvertHelper.convertToResponse(
                        po,
                        bannersMap.getOrDefault(po.getId(), Collections.emptyList())
                ))
                .collect(Collectors.toList());
    }

    @Override
    public List<ServiceItemResponse> getServiceItemsByCategory(Long categoryId, CountyEnum countyEnum) {
        // 1. 获取服务项列表
        List<ServiceItemPo> serviceItems = serviceItemRepository.listByCategoryAndRegion(
                categoryId, countyEnum, CommonStatusEnum.NORMAL);

        // 2. 批量获取关联的轮播图
        List<Long> serviceIds = serviceItems.stream()
                .map(ServiceItemPo::getId)
                .collect(Collectors.toList());
        Map<Long, List<CommonBannerVo>> bannersMap = bannerConfigService.getBannerConfigByServiceIds(serviceIds);

        // 3. 转换为响应对象
        return serviceItems.stream()
                .map(po -> serviceItemConvertHelper.convertToResponse(
                        po,
                        bannersMap.getOrDefault(po.getId(), Collections.emptyList())
                ))
                .collect(Collectors.toList());
    }


}