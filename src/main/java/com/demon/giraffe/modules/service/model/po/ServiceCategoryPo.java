package com.demon.giraffe.modules.service.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.modules.service.model.enums.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * 服务分类标签实体类
 * 表名：service_category
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "service_category", autoResultMap = true)
@Schema(description = "服务分类标签")
public class ServiceCategoryPo extends BasePo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分类名称（标签名）
     */
    @TableField("name")
    @Schema(description = "分类名称")
    private String name;

    /**
     * 分类描述
     */
    @TableField("description")
    @Schema(description = "分类描述")
    private String description;

    /**
     * 图标 URL
     */
    @TableField("icon")
    @Schema(description = "分类图标URL")
    private String icon;

    /**
     * 排序值，越大越靠前
     */
    @TableField("sort")
    @Schema(description = "排序值（越大越靠前）")
    private Integer sort;

    /**
     * 状态：0-启用，1-禁用
     */
    @TableField("status")
    @Schema(description = "分类状态（0-启用，1-禁用）")
    private CommonStatusEnum status;

}
