package com.demon.giraffe.modules.service.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.service.model.dto.request.ServiceItemCreateRequest;
import com.demon.giraffe.modules.service.model.dto.request.ServiceItemUpdateRequest;
import com.demon.giraffe.modules.service.model.dto.response.ServiceItemResponse;
import com.demon.giraffe.modules.service.service.ServiceItemService;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 服务项管理接口
 */
@RestController
@RequestMapping("/service/item")
@RequiredArgsConstructor
@Tag(name = "服务项管理接口", description = "服务项的增删改查接口")
public class ServiceItemController {

    private final ServiceItemService serviceItemService;

    @PostMapping("/create")
    @Operation(summary = "创建服务项", description = "创建一个新的服务项")
    @ApiResponse(responseCode = "200", description = "创建成功",
            content = @Content(schema = @Schema(implementation = Long.class)))
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<Long> createServiceItem(
            @RequestBody(description = "服务项创建参数", required = true,
                    content = @Content(schema = @Schema(implementation = ServiceItemCreateRequest.class)))
            @Valid @org.springframework.web.bind.annotation.RequestBody ServiceItemCreateRequest request) {
        Long id = serviceItemService.createServiceItem(request);
        return ResultBean.success(id);
    }

    @PostMapping("/update")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    @Operation(summary = "更新服务项", description = "更新已有的服务项信息")
    @ApiResponse(responseCode = "200", description = "更新成功",
            content = @Content(schema = @Schema(implementation = Boolean.class)))
    public ResultBean<Boolean> updateServiceItem(
            @RequestBody(description = "服务项更新参数", required = true,
                    content = @Content(schema = @Schema(implementation = ServiceItemUpdateRequest.class)))
            @Valid @org.springframework.web.bind.annotation.RequestBody ServiceItemUpdateRequest request) {
        boolean result = serviceItemService.updateServiceItem(request);
        return ResultBean.success(result);
    }

    @GetMapping("/detail")
    @Operation(summary = "获取服务项详情", description = "根据ID获取服务项详细信息")
    @ApiResponse(responseCode = "200", description = "成功返回",
            content = @Content(schema = @Schema(implementation = ServiceItemResponse.class)))
    public ResultBean<ServiceItemResponse> getServiceItemDetail(
            @Parameter(description = "服务项ID", required = true)
            @RequestParam Long id) {
        ServiceItemResponse response = serviceItemService.getServiceItemDetail(id);
        return ResultBean.success(response);
    }


    @PostMapping("/delete")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    @Operation(summary = "删除服务项", description = "根据ID逻辑删除服务项")
    @ApiResponse(responseCode = "200", description = "删除成功",
            content = @Content(schema = @Schema(implementation = Boolean.class)))
    public ResultBean<Boolean> deleteServiceItem(
            @Parameter(description = "服务项ID", required = true)
            @RequestParam Long id) {
        boolean result = serviceItemService.deleteServiceItem(id);
        return ResultBean.success(result);
    }


    @GetMapping("/by-region")
    @Operation(summary = "根据地域获取服务项", description = "根据县级地域获取服务项列表")
    @ApiResponse(responseCode = "200", description = "成功返回",
            content = @Content(schema = @Schema(implementation = ServiceItemResponse.class)))
    public ResultBean<List<ServiceItemResponse>> getByRegion(
            @Parameter(description = "县级地域编码", required = true)
            @RequestParam CountyEnum county) {
        List<ServiceItemResponse> result = serviceItemService.getServiceItemsByRegion(county);
        return ResultBean.success(result);
    }

    @GetMapping("/by-category")
    @Operation(summary = "根据分类获取服务项", description = "根据分类ID获取服务项列表")
    @ApiResponse(responseCode = "200", description = "成功返回",
            content = @Content(schema = @Schema(implementation = ServiceItemResponse.class)))
    public ResultBean<List<ServiceItemResponse>> getByCategory(

            @Parameter(description = "县级地域编码", required = true)
            @RequestParam CountyEnum county,
            @Parameter(description = "分类ID", required = true)
            @RequestParam Long categoryId) {
        List<ServiceItemResponse> result = serviceItemService.getServiceItemsByCategory(categoryId, county);
        return ResultBean.success(result);
    }
}