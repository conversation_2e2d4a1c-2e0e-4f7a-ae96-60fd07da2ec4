package com.demon.giraffe.modules.service.service.helper;

import com.demon.giraffe.modules.common.model.vo.CommonBannerVo;
import com.demon.giraffe.modules.service.model.entity.ServiceItemEntity;
import com.demon.giraffe.modules.service.util.ServiceDetailConfigUtils;
import com.demon.giraffe.modules.service.model.dto.request.ServiceItemCreateRequest;
import com.demon.giraffe.modules.service.model.dto.request.ServiceItemUpdateRequest;
import com.demon.giraffe.modules.service.model.dto.response.ServiceItemResponse;
import com.demon.giraffe.modules.service.model.entity.ServiceDetailConfig;
import com.demon.giraffe.modules.service.model.enums.CommonStatusEnum;
import com.demon.giraffe.modules.service.model.enums.ProductCategory;
import com.demon.giraffe.modules.service.model.po.ServiceItemPo;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 服务项目转换工具
 */
@Component
public class ServiceItemConvertHelper {

    /**
     * 将 ServiceItemPo 转换为 ServiceItemResponse
     */
    public ServiceItemResponse convertToResponse(ServiceItemPo po, List<CommonBannerVo> bannerConfigByServiceId) {
        if (po == null) {
            return null;
        }
        ServiceItemResponse response = new ServiceItemResponse();
        response.setId(po.getId());
        response.setCategoryIds(po.getCategoryIds());
        response.setName(po.getName());
        response.setDescription(po.getDescription());
        response.setSurfacePlot(po.getSurfacePlot());
        response.setCompareImage(po.getCompareImage());
        response.setStandardWashPrice(po.getStandardWashPrice());
        response.setPremiumWashExtraFee(po.getPremiumWashExtraFee());
        response.setPremiumWashExtraHours(po.getPremiumWashExtraHours());
        response.setMarketReferencePrice(po.getMarketReferencePrice());
        response.setStandardProcessingHours(po.getStandardProcessingHours());
        response.setExpeditedProcessingHours(po.getExpeditedProcessingHours());
        response.setExpeditedServiceFee(po.getExpeditedServiceFee());
        response.setAddressCode(po.getAddressCode());
        response.setDetailConfig(po.getDetailConfig());
        response.setStatus(po.getStatus());
        response.setSort(po.getSort());
        response.setSalesCount(po.getSalesCount());
        response.setBannerVos(bannerConfigByServiceId);
        return response;
    }

    /**
     * 将 ServiceItemCreateRequest 转换为 ServiceItemPo
     */
    public ServiceItemPo convertToPo(ServiceItemCreateRequest request) {
        if (request == null) {
            return null;
        }

        ServiceItemPo po = new ServiceItemPo();
        List<ProductCategory> productCategories = request.getProductCategories();
        ServiceDetailConfig serviceDetailConfig = ServiceDetailConfigUtils.createFromCategories(productCategories);
        // 设置价格和时间相关字段
        po.setStandardWashPrice(ServiceDetailConfigUtils.calculateTotalStandardPrice(serviceDetailConfig.getCleaningItems()));
        po.setStandardProcessingHours(ServiceDetailConfigUtils.calculateTotalStandardHours(serviceDetailConfig.getCleaningItems()));
        po.setPremiumWashExtraFee(ServiceDetailConfigUtils.calculateTotalFinePriceDifference(serviceDetailConfig.getCleaningItems()));
        po.setPremiumWashExtraHours(ServiceDetailConfigUtils.calculateTotalPremiumExtraHours(serviceDetailConfig.getCleaningItems()));
        po.setDetailConfig(serviceDetailConfig);
        po.setCategoryIds(request.getCategoryIds());
        po.setName(request.getName());
        po.setDescription(request.getDescription());
        po.setSurfacePlot(request.getSurfacePlot());
        po.setCompareImage(request.getCompareImage());
        po.setMarketReferencePrice(request.getMarketReferencePrice());
        po.setExpeditedProcessingHours(request.getExpeditedProcessingHours());
        po.setExpeditedServiceFee(request.getExpeditedServiceFee());
        po.setAddressCode(request.getAddressCode());

        po.setStatus(CommonStatusEnum.NORMAL);
        po.setSort(0);
        po.setSalesCount(0);
        return po;
    }


    /**
     * 将 ServiceItemUpdateRequest 转换为 ServiceItemPo
     */
    public ServiceItemPo convertToPo(ServiceItemUpdateRequest request) {
        if (request == null) {
            return null;
        }

        ServiceItemPo po = new ServiceItemPo();
        // 设置ID（更新请求特有）
        po.setId(request.getId());

        // 设置基础字段（与CreateRequest相同的处理逻辑）
        if (request.getCategoryIds() != null) {
            po.setCategoryIds(request.getCategoryIds());
        }
        if (request.getName() != null) {
            po.setName(request.getName());
        }
        if (request.getDescription() != null) {
            po.setDescription(request.getDescription());
        }
        if (request.getSurfacePlot() != null) {
            po.setSurfacePlot(request.getSurfacePlot());
        }
        if (request.getCompareImage() != null) {
            po.setCompareImage(request.getCompareImage());
        }
        if (request.getMarketReferencePrice() != null) {
            po.setMarketReferencePrice(request.getMarketReferencePrice());
        }
        if (request.getExpeditedProcessingHours() != null) {
            po.setExpeditedProcessingHours(request.getExpeditedProcessingHours());
        }
        if (request.getExpeditedServiceFee() != null) {
            po.setExpeditedServiceFee(request.getExpeditedServiceFee());
        }
        if (request.getAddressCode() != null) {
            po.setAddressCode(request.getAddressCode());
        }

        // 处理服务详情配置和计算字段
        if (request.getProductCategories() != null) {
            ServiceDetailConfig serviceDetailConfig = ServiceDetailConfigUtils.createFromCategories(request.getProductCategories());
            po.setDetailConfig(serviceDetailConfig);

            po.setPremiumWashExtraFee(ServiceDetailConfigUtils.calculateTotalFinePriceDifference(serviceDetailConfig.getCleaningItems()));
            po.setPremiumWashExtraHours(ServiceDetailConfigUtils.calculateTotalPremiumExtraHours(serviceDetailConfig.getCleaningItems()));

            // 设置市场参考价格和标准处理时长
            po.setStandardWashPrice(ServiceDetailConfigUtils.calculateTotalStandardPrice(serviceDetailConfig.getCleaningItems()));
            po.setStandardProcessingHours(ServiceDetailConfigUtils.calculateTotalStandardHours(serviceDetailConfig.getCleaningItems()));
        }
        // 更新特有字段
        if (request.getStatus() != null) {
            po.setStatus(request.getStatus());
        }
        if (request.getSort() != null) {
            po.setSort(request.getSort());
        }
        return po;
    }


    /**
     * PO转Entity
     */
    public ServiceItemEntity toEntity(ServiceItemPo po) {
        if (po == null) {
            return null;
        }

        return ServiceItemEntity.builder()
                .serviceItemId(po.getId())
                .categoryIds(po.getCategoryIds())
                .name(po.getName())
                .standardWashPrice(po.getStandardWashPrice())
                .premiumWashExtraFee(po.getPremiumWashExtraFee())
                .premiumWashExtraHours(po.getPremiumWashExtraHours())
                .marketReferencePrice(po.getMarketReferencePrice())
                .standardProcessingHours(po.getStandardProcessingHours())
                .expeditedProcessingHours(po.getExpeditedProcessingHours())
                .expeditedServiceFee(po.getExpeditedServiceFee())
                .addressCode(po.getAddressCode())
                .detailConfig(po.getDetailConfig())
                .status(po.getStatus())
                .build();
    }

    /**
     * PO列表转Entity列表
     */
    public List<ServiceItemEntity> toEntityList(List<ServiceItemPo> pos) {
        return pos.stream()
                .map(this::toEntity)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

}