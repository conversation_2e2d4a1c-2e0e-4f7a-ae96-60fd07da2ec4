package com.demon.giraffe.modules.service.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.Duration;

@Getter
public enum ProductCategory implements IEnum<Integer> {
    // 外套类
    CASHMERE_COAT(10101, SecondCategory.OUTERWEAR, "羊绒大衣",
            new BigDecimal("150.00"), new BigDecimal("200.00"),
            Duration.ofHours(2), Duration.ofHours(3)),
    WOOL_COAT(10102, SecondCategory.OUTERWEAR, "羊毛大衣",
            new BigDecimal("120.00"), new BigDecimal("180.00"),
            Duration.ofHours(3), Duration.ofHours(4)),
    COTTON_COAT(10103, SecondCategory.OUTERWEAR, "棉衣",
            new BigDecimal("80.00"), new BigDecimal("120.00"),
            Duration.ofHours(4), Duration.ofHours(5)),

    TRENCH_COAT(10104, SecondCategory.OUTERWEAR, "风衣", new BigDecimal("100.00"), new BigDecimal("150.00")),
    JACKET(10105, SecondCategory.OUTERWEAR, "夹克", new BigDecimal("70.00"), new BigDecimal("100.00")),
    DOWN_COAT(10106, SecondCategory.OUTERWEAR, "羽绒服", new BigDecimal("90.00"), new BigDecimal("130.00")),
    HOME_COAT(10107, SecondCategory.OUTERWEAR, "家居大衣", new BigDecimal("60.00"), new BigDecimal("90.00")),

    // 针织类
    CASHMERE_SWEATER(10201, SecondCategory.KNITWEAR, "羊绒衫/羊毛衫", new BigDecimal("60.00"), new BigDecimal("90.00")),
    REGULAR_SWEATER(10202, SecondCategory.KNITWEAR, "普通毛衣", new BigDecimal("40.00"), new BigDecimal("60.00")),
    HOODIE(10203, SecondCategory.KNITWEAR, "卫衣", new BigDecimal("35.00"), new BigDecimal("50.00")),
    KNIT_SHIRT(10204, SecondCategory.KNITWEAR, "针织衫", new BigDecimal("45.00"), new BigDecimal("70.00")),

    // 衬衫类
    SHIRT(10301, SecondCategory.SHIRTS, "衬衫", new BigDecimal("30.00"), new BigDecimal("45.00")),
    POLO_SHIRT(10302, SecondCategory.SHIRTS, "POLO衫", new BigDecimal("25.00"), new BigDecimal("40.00")),
    WARM_SHIRT(10303, SecondCategory.SHIRTS, "保暖衬衫", new BigDecimal("35.00"), new BigDecimal("50.00")),
    ;
    @EnumValue
    private final Integer code;
    private final SecondCategory secondCategory;
    private final String name;
    private final BigDecimal standardPrice;    // 标洗价格
    private final BigDecimal finePrice;       // 精洗价格
    private final Duration standardDuration;  // 标洗时间
    private final Duration fineDuration;     // 精洗时间

    ProductCategory(Integer code, SecondCategory secondCategory, String name,
                    BigDecimal standardPrice, BigDecimal finePrice,
                    Duration standardDuration, Duration fineDuration) {
        this.code = code;
        this.secondCategory = secondCategory;
        this.name = name;
        this.standardPrice = standardPrice;
        this.finePrice = finePrice;
        this.standardDuration = standardDuration;
        this.fineDuration = fineDuration;
    }

    ProductCategory(Integer code, SecondCategory secondCategory, String name,
                    BigDecimal standardPrice, BigDecimal finePrice
    ) {
        this.code = code;
        this.secondCategory = secondCategory;
        this.name = name;
        this.standardPrice = standardPrice;
        this.finePrice = finePrice;
        this.standardDuration = Duration.ofHours(2);
        this.fineDuration = Duration.ofHours(3);
    }

    @Override
    public Integer getValue() {
        return code;
    }

    /**
     * 获取价格信息
     *
     * @param cleaningType 清洗类型
     * @return 对应价格
     */
    public BigDecimal getPrice(CleaningTypeEnum cleaningType) {
        return cleaningType == CleaningTypeEnum.FINE ? finePrice : standardPrice;
    }

    /**
     * 获取清洗时间
     *
     * @param cleaningType 清洗类型
     * @return 对应清洗时间
     */
    public Duration getDuration(CleaningTypeEnum cleaningType) {
        return cleaningType == CleaningTypeEnum.FINE ? fineDuration : standardDuration;
    }

    /**
     * 获取小时格式的清洗时间
     *
     * @param cleaningType 清洗类型
     * @return 清洗小时数
     */
    public int getDurationHours(CleaningTypeEnum cleaningType) {
        return (int) getDuration(cleaningType).toHours();
    }
}
