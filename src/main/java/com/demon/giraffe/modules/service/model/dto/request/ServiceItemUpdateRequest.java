package com.demon.giraffe.modules.service.model.dto.request;

import com.demon.giraffe.modules.service.model.enums.CommonStatusEnum;
import com.demon.giraffe.modules.service.model.enums.ProductCategory;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 服务项更新请求
 */
@Data
@Schema(name = "ServiceItemUpdateRequest", description = "更新服务项请求参数")
public class ServiceItemUpdateRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "服务ID", required = true)
    @NotNull(message = "服务ID不能为空")
    private Long id;

    // 其他字段与CreateRequest相同
    @Schema(description = "服务分类IDs")
    private List<Long> categoryIds;

    @Schema(description = "服务名称", example = "基础保洁服务")
    @Size(max = 50, message = "服务名称长度不能超过50字符")
    private String name;


    @Schema(description = "服务详细描述")
    private String description;

    @Schema(description = "服务封面")
    private String surfacePlot;

    @Schema(description = "清洗前后对比图")
    private String compareImage;

    @Schema(description = "服务定价", example = "100.00")
    @Digits(integer = 6, fraction = 2, message = "服务定价价格格式错误")
    private BigDecimal marketReferencePrice;

    @Schema(description = "加急处理时长(小时)", example = "1")
    private Integer expeditedProcessingHours;

    @Schema(description = "加急服务费用", example = "50.00")
    @Digits(integer = 6, fraction = 2, message = "加急服务费用格式错误")
    private BigDecimal expeditedServiceFee;

    @Schema(description = "服务区域(县级)")
    private CountyEnum addressCode;

    @Schema(description = "包含的衣物")
    private List<ProductCategory> productCategories;

    @Schema(description = "状态(0正常/1停用)", example = "0")
    private CommonStatusEnum status;

    @Schema(description = "排序权重", example = "1")
    private Integer sort;
}