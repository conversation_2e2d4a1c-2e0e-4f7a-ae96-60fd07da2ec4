package com.demon.giraffe.modules.service.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

@Getter
public enum ServiceType implements IEnum<Integer> {
    SINGLE_ITEM(1, "单件服务"),
    MULTI_ITEM(2, "多件服务");

    @EnumValue
    private final Integer value;
    private final String description;

    ServiceType(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}