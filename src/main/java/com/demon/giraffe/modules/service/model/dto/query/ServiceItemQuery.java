package com.demon.giraffe.modules.service.model.dto.query;

import com.demon.giraffe.modules.service.model.enums.CommonStatusEnum;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 服务项查询条件
 */
@Data
@Schema(name = "ServiceItemQuery", description = "服务项查询条件")
public class ServiceItemQuery implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "服务分类ID")
    private Long categoryId;

    @Schema(description = "服务名称(模糊查询)")
    private String name;

    @Schema(description = "服务编码")
    private String code;

    @Schema(description = "价格区间-最小值")
    private BigDecimal minPrice;

    @Schema(description = "价格区间-最大值")
    private BigDecimal maxPrice;

    @Schema(description = "服务区域(县级)")
    private CountyEnum addressCode;

    @Schema(description = "状态(0正常/1停用)")
    private CommonStatusEnum status;

    @Schema(description = "是否包含停用项")
    private Boolean includeDisabled;
}