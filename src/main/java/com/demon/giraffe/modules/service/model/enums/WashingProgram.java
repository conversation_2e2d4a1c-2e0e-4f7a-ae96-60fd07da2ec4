package com.demon.giraffe.modules.service.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 清洗方案枚举
 */
@Getter
public enum WashingProgram implements IEnum<Integer> {
    STANDARD_WASH(1, "SW", "标准水洗", 1),
    DELICATE_WASH(2, "DW", "精洗水洗", 2),
    DRY_CLEAN(3, "DC", "专业干洗", 3),
    HAND_WASH(4, "HW", "手洗", 4),
    STEAM_WASH(5, "STW", "蒸汽洗", 5),
    STERILIZATION_WASH(6, "SIW", "杀菌洗", 6),
    COLOR_PROTECT(7, "CP", "护色洗", 7),
    ANTI_SHRINK(8, "AS", "防缩水洗", 8),
    LEATHER_CARE(9, "LC", "皮具护理", 9),
    FUR_CARE(10, "FC", "皮草护理", 10),
    WEDDING_DRESS_CARE(11, "WDC", "婚纱护理", 11);

    @EnumValue
    private final Integer value;
    private final String code;
    private final String name;
    private final int difficultyLevel;

    WashingProgram(Integer value, String code, String name, int difficultyLevel) {
        this.value = value;
        this.code = code;
        this.name = name;
        this.difficultyLevel = difficultyLevel;
    }

    private static final Map<String, WashingProgram> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(WashingProgram::getCode, e -> e));

    public static WashingProgram fromCode(String code) {
        return CODE_MAP.get(code);
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}