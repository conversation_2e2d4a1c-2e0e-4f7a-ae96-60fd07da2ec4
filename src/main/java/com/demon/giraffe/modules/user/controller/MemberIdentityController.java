package com.demon.giraffe.modules.user.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.user.model.dto.response.MemberIdentityDetailResponse;
import com.demon.giraffe.modules.user.service.MemberIdentityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/user/member/identity")
@Tag(name = "会员身份管理接口", description = "会员身份信息管理")
@RequiredArgsConstructor
public class MemberIdentityController {

    private final MemberIdentityService memberIdentityService;

    @PostMapping("/remove")
    @Operation(summary = "注销我的会员信息")
    @SaCheckRole(value = {"CUSTOMER"}, mode = SaMode.OR)
    public ResultBean<Boolean> delete() {

        Boolean result = memberIdentityService.deleteMyMemberIdentity();
        return ResultBean.success(result);
    }

    @GetMapping("/info")
    @Operation(summary = "获取我的会员信息")
    @SaCheckRole(value = {"CUSTOMER"}, mode = SaMode.OR)
    public ResultBean<MemberIdentityDetailResponse> getDetail() {

        MemberIdentityDetailResponse response = memberIdentityService.getMyMemberIdentityDetail();
        return ResultBean.success(response);
    }
}
