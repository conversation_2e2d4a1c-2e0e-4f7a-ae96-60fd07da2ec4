package com.demon.giraffe.modules.user.model.dto.request;

import com.demon.giraffe.common.domain.dto.request.RegionRequest;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "MemberAddressUpdateRequest", description = "地址更新请求")
public class MemberAddressUpdateRequest {

    @NotNull(message = "地址ID不能为空")
    @Schema(description = "地址ID", required = true)
    private Long id;

    /**
     * 区域信息（统一处理，可选更新）
     */
    @Valid
    @Schema(description = "区域信息，留空表示不更新")
    private RegionRequest region;

    @Size(max = 200, message = "详细地址长度不能超过200个字符")
    @Schema(description = "详细地址")
    private String detailAddress;

    @Size(max = 30, message = "联系人姓名长度不能超过30个字符")
    @Schema(description = "联系人姓名")
    private String contactName;

    @Size(min = 11, max = 11, message = "联系电话必须为11位")
    @Schema(description = "联系电话")
    private String contactPhone;


    @Schema(description = "是否默认地址")
    private Boolean isDefault;

    /**
     * 获取区域编码（兼容性方法）
     */
    public CountyEnum getAddressCode() {
        return region != null ? region.getAddressCode() : null;
    }

    /**
     * 设置区域编码（兼容性方法）
     */
    public void setAddressCode(CountyEnum addressCode) {
        if (region == null) {
            region = new RegionRequest();
        }
        region.setAddressCode(addressCode);
    }

    /**
     * 获取完整地址描述
     */
    public String getFullAddress() {
        return region != null ? region.getFullAddress() + (detailAddress != null ? detailAddress : "") :
               (detailAddress != null ? detailAddress : "");
    }
}