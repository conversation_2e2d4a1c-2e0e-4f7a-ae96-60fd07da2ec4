package com.demon.giraffe.modules.user.model.dto.query;

import io.swagger.v3.oas.annotations.media.Schema;
import com.demon.giraffe.modules.user.model.enums.MemberLevelEnum;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(name = "MemberIdentityQueryRequest", description = "会员身份查询条件")
public class MemberIdentityQueryRequest implements Serializable {

    @Schema(description = "关联app_user用户ID")
    private Long appUserId;

    @Schema(description = "会员编号")
    private String memberNo;

    @Schema(description = "会员等级（1普通/2银卡/3金卡/4钻石）")
    private MemberLevelEnum level;

    @Schema(description = "绑定手机号")
    private String phone;

    @Schema(description = "状态（0正常/1冻结）")
    private Integer status;
}