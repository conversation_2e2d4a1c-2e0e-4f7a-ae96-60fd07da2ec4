package com.demon.giraffe.modules.user.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 会员收发货地址表
 * 对应数据库表：member_address
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("member_address")
public class MemberAddressPo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 关联会员ID， 这里关联会员 是前端 给到得id 都是 用户id  ——>找到会员id ->找到地址信息 必填 */
    @NotNull(message = "会员ID不能为空")
    @TableField("member_id")
    private Long memberId;

    /** 区域编码(使用CountyEnum枚举)，必填 */
    @NotNull(message = "区域编码不能为空")
    @TableField("address_code")
    private CountyEnum addressCode;

    /** 详细地址，必填 */
    @NotBlank(message = "详细地址不能为空")
    @Size(max = 200, message = "详细地址长度不能超过200个字符")
    @TableField("detail_address")
    private String detailAddress;

    /** 联系人姓名，必填 */
    @NotBlank(message = "联系人姓名不能为空")
    @Size(max = 30, message = "联系人姓名长度不能超过30个字符")
    @TableField("contact_name")
    private String contactName;

    /** 联系电话，必填 */
    @NotBlank(message = "联系电话不能为空")
    @Size(min = 11, max = 11, message = "联系电话必须为11位")
    @TableField("contact_phone")
    private String contactPhone;

    /** 经度，非必填 */
    @Digits(integer = 3, fraction = 7, message = "经度格式不正确")
    @TableField("longitude")
    private BigDecimal longitude;

    /** 纬度，非必填 */
    @Digits(integer = 3, fraction = 7, message = "纬度格式不正确")
    @TableField("latitude")
    private BigDecimal latitude;

}