package com.demon.giraffe.modules.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.demon.giraffe.modules.user.model.po.MemberIdentityPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.*;

@Mapper
public interface MemberIdentityMapper extends BaseMapper<MemberIdentityPo> {

    /**
     * 根据用户ID查询会员信息（包括已逻辑删除的记录）
     */
    @Select("SELECT * FROM member_identity WHERE app_user_id = #{appUserId}")
    MemberIdentityPo selectByAppUserIdIgnoreDeleted(@Param("appUserId") Long appUserId);

    /**
     * 根据主键ID恢复已逻辑删除的会员信息
     */
    @Update("UPDATE member_identity SET deleted = 0 WHERE id = #{id} AND deleted = 1")
    int restoreById(@Param("id") Long id);


}