package com.demon.giraffe.modules.user.service.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.framework.satoken.util.SaTokenUtil;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import com.demon.giraffe.modules.user.model.po.UserPo;

import java.io.Serializable;

/**
 * 统一角色管理基础服务接口
 * 所有角色服务都应该实现此接口，提供统一的管理能力
 *
 * @param <T> 角色实体类型
 * @param <R> 角色请求类型
 * @param <S> 角色响应类型
 * @param <Q> 角色查询类型
 */
public interface BaseRoleService<T, R, S, Q extends Serializable> {

    /**
     * 获取当前服务管理的角色类型
     *
     * @return 角色枚举
     */
    UserRole getRoleType();

    /**
     * 统一登录处理
     *
     * @param userId 用户ID
     * @return 登录成功后的角色信息
     */
    S login(Long userId);

    /**
     * 统一注销处理
     *
     * @param userId 用户ID
     * @return 是否成功
     */
    Boolean logout(Long userId);

    /**
     * 创建角色
     *
     * @param request 创建请求
     * @return 角色ID
     */
    Long createRole(R request);

    /**
     * 更新角色信息
     *
     * @param request 更新请求
     * @return 是否成功
     */
    Boolean updateRole(R request);

    /**
     * 获取角色详情
     *
     * @param id 角色ID
     * @return 角色详情
     */
    S getRoleDetail(Long id);

    /**
     * 根据用户ID获取角色信息
     *
     * @param userId 用户ID
     * @return 角色信息
     */
    S getRoleByUserId(Long userId);

    /**
     * 分页查询角色列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<S> queryRolePage(BasePageQuery<Q> query);

    /**
     * 删除角色（根据用户ID）
     *
     * @param userId 用户ID
     * @return 是否成功
     */
    Boolean deleteByUserId(Long userId);

    /**
     * 保存角色实体
     *
     * @param entity 角色实体
     * @return 是否成功
     */
    Boolean saveRole(T entity);

    /**
     * 角色是否存在
     *
     * @param id 角色ID
     * @return 是否存在
     */
    Boolean existsById(Long id);


    default S getCurrent() {
        UserPo userPo = SaTokenUtil.getUserPo();
        S roleByUserId = getRoleByUserId(userPo.getId());
        if (roleByUserId == null) {
            UserRole roleType = getRoleType();
            throw new RuntimeException("当前用户没有角色: " + roleType.getName());
        }
        return roleByUserId;
    }


    /**
     * 角色状态变更处理
     *
     * @param userId    用户ID
     * @param newStatus 新状态
     * @return 是否成功
     */
    default Boolean changeStatus(Long userId, Object newStatus) {
        return true; // 默认成功，子类可重写
    }
}
