package com.demon.giraffe.modules.user.model.dto.request;

import com.demon.giraffe.modules.user.model.enums.DirectorStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
@Schema(name = "FactoryDirectorRequest", description = "厂长创建/更新请求")
public class FactoryDirectorRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "厂长ID(更新时必填)")
    private Long id;

    @NotNull(message = "关联用户ID不能为空")
    @Schema(description = "关联用户ID", required = true)
    private Long appUserId;

    @NotNull(message = "工厂ID不能为空")
    @Schema(description = "工厂ID", required = true)
    private Long factoryId;

    @Schema(description = "厂长状态")
    private DirectorStatusEnum status;
}