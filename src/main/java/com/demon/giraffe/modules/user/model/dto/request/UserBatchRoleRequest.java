package com.demon.giraffe.modules.user.model.dto.request;

import com.demon.giraffe.modules.user.model.enums.UserRole;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
@Schema(name = "UserBatchRoleRequest", description = "修改权限入参")
public class UserBatchRoleRequest {

    @Schema(description = "会员ID列表", required = true)
    private List<Long> userIds;

    @Schema(description = "角色", required = true)
    @NotNull(message = "角色不能为空")
    private UserRole role;
}