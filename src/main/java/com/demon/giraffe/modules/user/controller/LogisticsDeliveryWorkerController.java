package com.demon.giraffe.modules.user.controller;

import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.user.model.enums.DeliveryWorkerStatusEnum;
import com.demon.giraffe.modules.user.service.LogisticsDeliveryWorkerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

@Slf4j
@RestController
@RequestMapping("/user/logistics/delivery-worker")
@RequiredArgsConstructor
@Tag(name = "配送员管理接口", description = "物流配送员相关操作")
public class LogisticsDeliveryWorkerController {

    private final LogisticsDeliveryWorkerService deliveryWorkerService;


    @PostMapping("/update-status/{id}")
    @Operation(summary = "更新配送员状态", description = "更新指定配送员的工作状态")
    public ResultBean<Boolean> updateStatus(
            @Parameter(description = "配送员ID", required = true) @PathVariable Long id,
            @Parameter(description = "目标状态", required = true) @RequestParam DeliveryWorkerStatusEnum status) {
        try {
            Boolean result = deliveryWorkerService.updateDeliveryWorkerStatus(id, status);
            return ResultBean.success("状态更新成功", result);
        } catch (Exception e) {
            log.error("更新配送员状态失败", e);
            return ResultBean.fail("状态更新失败：" + e.getMessage());
        }
    }

    @PostMapping("/update-location/{id}")
    @Operation(summary = "更新配送员位置", description = "更新指定配送员的实时位置坐标")
    public ResultBean<Boolean> updateLocation(
            @Parameter(description = "配送员ID", required = true) @PathVariable Long id,
            @Parameter(description = "经度", required = true) @RequestParam BigDecimal longitude,
            @Parameter(description = "纬度", required = true) @RequestParam BigDecimal latitude) {
        try {
            Boolean result = deliveryWorkerService.updateDeliveryWorkerLocation(id, longitude, latitude);
            return ResultBean.success("位置更新成功", result);
        } catch (Exception e) {
            log.error("更新配送员位置失败", e);
            return ResultBean.fail("位置更新失败：" + e.getMessage());
        }
    }
}