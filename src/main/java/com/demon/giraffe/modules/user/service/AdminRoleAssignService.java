package com.demon.giraffe.modules.user.service;

import com.demon.giraffe.modules.user.model.dto.request.*;

/**
 * 管理员角色分配服务
 * 专注于单个用户角色分配逻辑，支持RequestBody入参
 */
public interface AdminRoleAssignService {

    /**
     * 分配洗护工角色
     * @param request 洗护工角色分配请求
     * @return 分配结果
     */
    Boolean assignCleanerRole(AssignLaundryWorkerRequest request);

    /**
     * 分配配送员角色
     * @param request 配送员角色分配请求
     * @return 分配结果
     */
    Boolean assignDeliveryRole(AssignDeliveryRequest request);

    /**
     * 分配工厂管理员角色
     * @param request 工厂管理员角色分配请求
     * @return 分配结果
     */
    Boolean assignFactoryManagerRole(AssignFactoryManagerRequest request);

    /**
     * 分配区域投资人角色
     * @param request 区域投资人角色分配请求
     * @return 分配结果
     */
    Boolean assignInvestorRole(AssignInvestorRequest request);
}