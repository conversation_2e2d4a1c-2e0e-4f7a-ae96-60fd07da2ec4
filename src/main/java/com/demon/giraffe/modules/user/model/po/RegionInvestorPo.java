package com.demon.giraffe.modules.user.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;

@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("region_investor")
public class RegionInvestorPo extends BasePo {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "关联用户ID不能为空")
    @TableField("app_user_id")
    private Long appUserId;

    @NotBlank(message = "投资人编号不能为空")
    @Size(max = 20, message = "投资人编号长度不能超过20个字符")
    @TableField("investor_no")
    private String investorNo;

    @NotBlank(message = "联系人不能为空")
    @Size(max = 30, message = "联系人长度不能超过30个字符")
    @TableField("contact_person")
    private String contactPerson;

    @NotBlank(message = "联系电话不能为空")
    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    @TableField("contact_phone")
    private String contactPhone;

    /** 区域编码(使用CountyEnum枚举)，必填 */
    @NotNull(message = "区域编码不能为空")
    @TableField("address_code")
    private CountyEnum addressCode;


    @NotNull(message = "工厂ID不能为空")
    @TableField("factory_id")
    private Long factoryId;

}