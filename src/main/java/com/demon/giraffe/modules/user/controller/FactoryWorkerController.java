package com.demon.giraffe.modules.user.controller;

import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.user.model.enums.FactoryPositionTypeEnum;
import com.demon.giraffe.modules.user.service.FactoryWorkerService;
import com.demon.giraffe.modules.user.service.UnifiedRoleManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/user/factory/worker")
@RequiredArgsConstructor
@Tag(name = "工厂员工管理接口", description = "工厂员工相关操作")
public class FactoryWorkerController {

    private final FactoryWorkerService factoryWorkerService;


    @PostMapping("/change-position/{id}")
    @Operation(summary = "变更员工职位", description = "变更指定员工的职位类型")
    public ResultBean<Boolean> changePosition(
            @Parameter(description = "员工ID", required = true) @PathVariable Long id,
            @Parameter(description = "目标职位", required = true) @RequestParam FactoryPositionTypeEnum positionType) {
        try {
            Boolean result = factoryWorkerService.changeWorkerPosition(id, positionType);
            return ResultBean.success("职位变更成功", result);
        } catch (Exception e) {
            log.error("变更员工职位失败", e);
            return ResultBean.fail("职位变更失败：" + e.getMessage());
        }
    }

    @GetMapping("/check-worker-no")
    @Operation(summary = "检查员工编号", description = "检查员工编号是否已存在")
    public ResultBean<Boolean> checkWorkerNo(
            @Parameter(description = "员工编号", required = true) @RequestParam String workerNo) {
        try {
            Boolean exists = factoryWorkerService.checkWorkerNoExist(workerNo);
            return ResultBean.success("检查完成", exists);
        } catch (Exception e) {
            log.error("检查员工编号失败", e);
            return ResultBean.fail("检查失败：" + e.getMessage());
        }
    }
}