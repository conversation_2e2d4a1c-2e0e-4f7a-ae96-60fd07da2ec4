package com.demon.giraffe.modules.user.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import com.demon.giraffe.modules.user.service.UnifiedRoleManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 统一角色管理控制器
 * 提供统一的角色管理API入口
 */
@Slf4j
@RestController
@RequestMapping("/user/role")
@RequiredArgsConstructor
@Tag(name = "统一角色管理", description = "统一的角色登录、分配、删除等操作")
public class UnifiedRoleController {

    private final UnifiedRoleManagementService roleManagementService;


    @PostMapping("/logout/{userId}")
    @Operation(summary = "统一角色退出登录", description = "注销用户当前角色")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<Boolean> logout(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId) {
        try {
            Boolean result = roleManagementService.logout(userId);
            return ResultBean.success("注销成功", result);
        } catch (Exception e) {
            log.error("用户[{}]注销失败", userId, e);
            return ResultBean.fail("注销失败：" + e.getMessage());
        }
    }

    @PostMapping("/assign")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    @Operation(summary = "分配角色", description = "为用户分配新角色")
    public ResultBean<Boolean> assignRole(
            @Parameter(description = "用户ID", required = true)
            @RequestParam Long userId,
            @Parameter(description = "目标角色", required = true)
            @RequestParam UserRole targetRole,
            @Parameter(description = "角色创建请求", required = true)
            @RequestBody Object roleRequest) {
        try {
            Boolean result = roleManagementService.assignRole(userId, targetRole, roleRequest);
            return ResultBean.success("角色分配成功", result);
        } catch (Exception e) {
            log.error("用户[{}]角色分配失败", userId, e);
            return ResultBean.fail("角色分配失败：" + e.getMessage());
        }
    }

    @PostMapping("/remove/{userId}")
    @Operation(summary = "删除角色", description = "删除用户当前角色,变为会员")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<Boolean> removeRole(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId) {
        try {
            Boolean result = roleManagementService.removeRole(userId);
            return ResultBean.success("角色删除成功", result);
        } catch (Exception e) {
            log.error("用户[{}]角色删除失败", userId, e);
            return ResultBean.fail("角色删除失败：" + e.getMessage());
        }
    }


    @PostMapping("/status/{userId}")
    @Operation(summary = "变更角色状态", description = "变更用户角色的状态")
    public ResultBean<Boolean> changeRoleStatus(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId,
            @Parameter(description = "新状态", required = true)
            @RequestBody Object newStatus) {
        try {
            Boolean result = roleManagementService.changeRoleStatus(userId, newStatus);
            return ResultBean.success("状态变更成功", result);
        } catch (Exception e) {
            log.error("用户[{}]角色状态变更失败", userId, e);
            return ResultBean.fail("状态变更失败：" + e.getMessage());
        }
    }


}
