package com.demon.giraffe.modules.user.service;

import com.demon.giraffe.modules.user.model.dto.query.RegionInvestorQueryRequest;
import com.demon.giraffe.modules.user.model.dto.request.RegionInvestorRequest;
import com.demon.giraffe.modules.user.model.dto.response.RegionInvestorResponse;
import com.demon.giraffe.modules.user.model.po.RegionInvestorPo;
import com.demon.giraffe.modules.user.service.base.BaseRoleService;

import java.util.List;
import java.util.Map;

/**
 * 区域投资人服务接口
 * 继承基础角色服务，获得通用角色管理能力
 */
public interface RegionInvestorService extends BaseRoleService<RegionInvestorPo, RegionInvestorRequest, RegionInvestorResponse, RegionInvestorQueryRequest> {

    /**
     * 根据用户ID获取投资人信息
     * @param appUserId 用户ID
     * @return 投资人信息
     */
    RegionInvestorResponse getByAppUserId(Long appUserId);


     RegionInvestorResponse getById(Long id) ;

    /**
     * 根据用户ID删除投资人信息
     * @param appUserId 用户ID
     * @return 是否删除成功
     */
    boolean deleteByAppUserId(Long appUserId);

    /**
     * 批量获取投资人信息
     * @param investorIds 投资人ID列表
     * @return 投资人信息Map，key为投资人ID，value为投资人响应对象
     */
    Map<Long, RegionInvestorResponse> batchGetInvestors(List<Long> investorIds);

    /**
     * 根据投资人ID列表获取投资人基本信息
     * @param investorIds 投资人ID列表
     * @return 投资人基本信息列表
     */
    List<RegionInvestorResponse> listInvestorsByIds(List<Long> investorIds);

    /**
     * 批量删除投资人
     * @param ids 投资人ID列表
     * @return 是否删除成功
     */
    Boolean batchDeleteInvestors(List<Long> ids);

}