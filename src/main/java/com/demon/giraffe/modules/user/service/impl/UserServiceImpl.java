package com.demon.giraffe.modules.user.service.impl;

import cn.dev33.satoken.stp.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.framework.satoken.util.SaEncryptionUtil;
import com.demon.giraffe.framework.satoken.util.SaTokenUtil;
import com.demon.giraffe.modules.user.model.dto.query.UserQueryRequest;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import com.demon.giraffe.modules.user.model.po.UserPo;
import com.demon.giraffe.modules.user.model.vo.UserVO;
import com.demon.giraffe.modules.user.repository.UserRepository;
import com.demon.giraffe.modules.user.service.*;
import com.demon.giraffe.modules.user.service.helper.UserConvertHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Value("${user.password.check.key.private}")
    private String privateKey;

    @Value("${user.password.check.key.public}")
    private String publicKey;

    private final UserConvertHelper userConvertHelper;
    private final UserRepository userRepository;

    public UserServiceImpl(UserConvertHelper userConvertHelper, UserRepository userRepository) {
        this.userConvertHelper = userConvertHelper;
        this.userRepository = userRepository;
    }


    @Override
    public Boolean logout() {
        StpUtil.logoutByTokenValue(StpUtil.getTokenValue());
        return true;
    }

    @Override
    public UserPo getUserInfo(Long userId) {
        return userRepository.getById(userId);
    }


    @Override
    public void updateUserRoles(Long targetId, UserRole role) {
        UserPo userPo = new UserPo();
        userPo.setId(targetId.longValue());
        userPo.setRole(role);
        userPo.setUpdater(SaTokenUtil.getLoginIdAsLong());

        if(!userRepository.updateById(userPo)){
            log.error("更新用户角色失败");
            throw new RuntimeException("更新用户角色失败");
        }
        SaTokenUtil.invalidateTokenByUserId(targetId);
    }

    @Override
    public UserPo getUserByOpenId(String openId) {
        return userRepository.getByOpenid(openId);
    }

    /**
     * 校验用户密码是否正确 true为不正确，false为正确
     *
     * @param existsPwd 实际密码
     * @param inputPwd  用户输入密码
     * @return 是否正确标识 true为不正确，false为正确
     */
    private boolean checkPassword(String existsPwd, String inputPwd) {
        String decryptUserInputPwd = SaEncryptionUtil.rsaDecryptByPrivate(privateKey, inputPwd);
        String decryptUserExistsPwd = SaEncryptionUtil.rsaDecryptByPrivate(privateKey, existsPwd);
        return !decryptUserInputPwd.equals(decryptUserExistsPwd);
    }


    @Override
    public IPage<UserVO> queryUserPage(BasePageQuery<UserQueryRequest> request) {
        request.init(); // 初始化分页参数
        IPage<UserPo> userPoIPage = userRepository.pageByQuery(request);
        return userPoIPage.convert(userConvertHelper::convertToVO);
    }

}