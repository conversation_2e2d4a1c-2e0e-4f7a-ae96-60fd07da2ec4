package com.demon.giraffe.modules.user.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.modules.user.model.enums.FactoryPositionTypeEnum;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;

@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("factory_worker")
public class FactoryWorkerPo extends BasePo {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableField("app_user_id")
    private Long appUserId;

    @Size(max = 20, message = "员工编号长度不能超过20个字符")
    @TableField("worker_no")
    private String workerNo;


    @TableField("factory_id")
    private Long factoryId;


    @TableField("phone")
    private String phone;


    @TableField("real_name")
    private String realName;


    @TableField("position_type")
    private FactoryPositionTypeEnum positionType;
}