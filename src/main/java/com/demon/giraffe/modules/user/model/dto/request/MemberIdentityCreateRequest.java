package com.demon.giraffe.modules.user.model.dto.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.demon.giraffe.modules.user.model.enums.UserStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import com.demon.giraffe.modules.user.model.enums.MemberLevelEnum;
import jakarta.validation.constraints.*;
import lombok.Data;



@Data
@Schema(name = "MemberIdentityCreateRequest", description = "会员身份创建请求")
public class MemberIdentityCreateRequest {

    @NotNull(message = "关联用户ID不能为空")
    @Schema(description = "关联app_user用户ID", required = true)
    private Long appUserId;

    @NotBlank(message = "会员编号不能为空")
    @Schema(description = "会员编号（M+时间戳+随机数）", required = true)
    private String memberNo;

    @NotNull(message = "会员等级不能为空")
    @Schema(description = "会员等级（1普通/2银卡/3金卡/4钻石）", required = true)
    private MemberLevelEnum level;

    @Schema(description = "当前可用积分", defaultValue = "0")
    private Integer points = 0;

    @Schema(description = "累计获得积分", defaultValue = "0")
    private Integer totalPoints = 0;

    @Schema(description = "累计消费金额", defaultValue = "0.0")
    private Double totalConsumed = 0.0;

    @NotBlank(message = "手机号不能为空")
    @Size(min = 11, max = 11, message = "手机号长度必须为11位")
    @Schema(description = "绑定手机号", required = true)
    private String phone;

    @Schema(description = "实名认证姓名")
    private String realName;

    @Schema(description = "身份证号（加密存储）")
    private String idCard;

    @NotNull
    @TableField("status")
    private UserStatus status;
}