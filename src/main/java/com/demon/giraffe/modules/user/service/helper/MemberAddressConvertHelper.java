package com.demon.giraffe.modules.user.service.helper;

import com.demon.giraffe.common.domain.dto.request.RegionRequest;
import com.demon.giraffe.common.domain.dto.response.RegionResponse;
import com.demon.giraffe.common.domain.enums.*;
import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.common.service.RegionProcessingService;
import com.demon.giraffe.modules.common.service.LocationService;
import com.demon.giraffe.modules.user.model.dto.request.MemberAddressCreateRequest;
import com.demon.giraffe.modules.user.model.dto.request.MemberAddressUpdateRequest;
import com.demon.giraffe.modules.user.model.dto.response.MemberAddressDetailResponse;
import com.demon.giraffe.modules.user.model.entity.MemberAddress;
import com.demon.giraffe.modules.user.model.po.MemberAddressPo;
import com.demon.giraffe.modules.user.repository.MemberDefaultAddressRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;

import static com.demon.giraffe.modules.common.service.impl.LocationServiceImpl.*;

@Slf4j
@Component
@RequiredArgsConstructor
public class MemberAddressConvertHelper {

    private final LocationService locationService;
    private final RegionProcessingService regionProcessingService;
    private final MemberDefaultAddressRepository defaultAddressRepository;

    /**
     * 将创建请求转换为PO
     */
    public MemberAddressPo toPo(MemberAddressCreateRequest request, Long memberId) {
        Objects.requireNonNull(request, "创建请求不能为空");
        Objects.requireNonNull(memberId, "会员ID不能为空");

        MemberAddressPo po = new MemberAddressPo();
        setBasicInfo(po, request.getContactName(), request.getContactPhone(),
                request.getDetailAddress(), memberId);

        // 使用统一区域处理
        if (request.getRegion() != null) {
            setAddressInfoFromRegion(po, request.getRegion(), request.getDetailAddress());
        }
        return po;
    }

    /**
     * 将更新请求转换为PO
     */
    public MemberAddressPo toPo(MemberAddressUpdateRequest request, MemberAddressPo existingPo) {
        Objects.requireNonNull(request, "更新请求不能为空");
        Objects.requireNonNull(existingPo, "现有地址不能为空");

        // 更新基础信息
        if (StringUtils.isNotBlank(request.getContactName())) {
            existingPo.setContactName(request.getContactName());
        }
        if (StringUtils.isNotBlank(request.getContactPhone())) {
            existingPo.setContactPhone(request.getContactPhone());
        }
        if (StringUtils.isNotBlank(request.getDetailAddress())) {
            existingPo.setDetailAddress(request.getDetailAddress());
        }

        // 处理地址变更（使用统一区域处理）
        if (request.getRegion() != null) {
            setAddressInfoFromRegion(existingPo, request.getRegion(), existingPo.getDetailAddress());
        }

        return existingPo;
    }

    /**
     * 将PO转换为响应DTO
     */
    public MemberAddressDetailResponse toResponse(MemberAddressPo po) {
        Objects.requireNonNull(po, "地址信息不能为空");

        // 转换地区信息
        RegionResponse regionResponse = null;
        if (po.getAddressCode() != null) {
            regionResponse = new RegionResponse(po.getAddressCode());
        }

        MemberAddressDetailResponse response = new MemberAddressDetailResponse();
        response.setId(po.getId());
        response.setRegion(regionResponse);
        response.setDetailAddress(po.getDetailAddress());
        response.setContactName(po.getContactName());
        response.setContactPhone(po.getContactPhone());
        response.setIsDefault(defaultAddressRepository.existsByMemberIdAndAddressId(po.getMemberId(), po.getId()));
        return response;
    }

    /**
     * 设置基础信息
     */
    private void setBasicInfo(MemberAddressPo po, String contactName, String contactPhone,
                              String detailAddress, Long memberId) {
        po.setContactName(contactName);
        po.setContactPhone(contactPhone);
        po.setDetailAddress(detailAddress);
        po.setMemberId(memberId);
    }

    /**
     * 使用统一区域处理设置地址信息
     */
    private void setAddressInfoFromRegion(MemberAddressPo po, RegionRequest region, String detailAddress) {
        try {
            // 使用统一区域处理服务验证和解析区域
            CountyEnum countyEnum = region.getAddressCode();
            po.setAddressCode(countyEnum);

            // 处理经纬度
            Map<String, BigDecimal> coordinate = resolveCoordinate(
                    region.getProvince(), region.getCity(), region.getDistrict(), detailAddress);
            po.setLongitude(coordinate.get(LONGITUDE_KEY));
            po.setLatitude(coordinate.get(LATITUDE_KEY));
        } catch (Exception e) {
            log.error("地址信息转换失败", e);
            throw new BusinessException("地址信息转换失败", e);
        }
    }

    /**
     * 设置地址相关信息（编码、经纬度等）- 兼容性方法
     */
    private void setAddressInfo(MemberAddressPo po, String province, String city,
                                String district, String detailAddress) {
        validateAddressFields(province, city, district);

        try {
            // 处理地址编码
            CountyEnum countyEnum = resolveCountyCode(province, city, district);
            po.setAddressCode(countyEnum);

            // 处理经纬度
            Map<String, BigDecimal> coordinate = resolveCoordinate(province, city, district, detailAddress);
            po.setLongitude(coordinate.get(LONGITUDE_KEY));
            po.setLatitude(coordinate.get(LATITUDE_KEY));
        } catch (Exception e) {
            throw new BusinessException("地址信息转换失败", e);
        }
    }

    /**
     * 解析区县编码
     */
    private CountyEnum resolveCountyCode(String province, String city, String district) {
        return locationService.resolveCountyCode(province, city, district);
    }

    /**
     * 解析经纬度坐标
     */
    private Map<String, BigDecimal> resolveCoordinate(String province, String city, String district, String detailAddress) {
        try {
            return locationService.convertAddressToCoordinate(province, city, district, detailAddress);
        } catch (Exception e) {
            throw new BusinessException("区域转化异常");
        }
    }


    /**
     * 验证省市区字段是否完整
     */
    public void validateAddressFields(String province, String city, String district) {
        if (StringUtils.isBlank(province)) {
            throw new IllegalArgumentException("省份信息不能为空");
        }
        if (StringUtils.isBlank(city)) {
            throw new IllegalArgumentException("城市信息不能为空");
        }
        if (StringUtils.isBlank(district)) {
            throw new IllegalArgumentException("区县信息不能为空");
        }
    }

    /**
     * 自定义地址转换异常
     */
    public static class AddressConversionException extends RuntimeException {
        public AddressConversionException(String message, Throwable cause) {
            super(message, cause);
        }
    }




    /**
     * 将会员地址PO转换为完整地址实体（包含所有字段）
     * @param po 持久化对象
     * @return 完整的地址实体
     */
    public MemberAddress convertPoToFullAddressEntity(MemberAddressPo po) {
        if (po == null) {
            return null;
        }

        // 使用统一区域处理服务提取区域信息
        RegionRequest region = null;
        if (po.getAddressCode() != null) {
            region = regionProcessingService.extractRegionInfo(po.getAddressCode());
        }

        MemberAddress address = new MemberAddress();
        address.setId(po.getId());
        address.setCountyEnum(po.getAddressCode());
        address.setRegion(region);
        address.setDetailAddress(po.getDetailAddress());
        address.setContactName(po.getContactName());
        address.setContactPhone(po.getContactPhone());
        address.setLongitude(po.getLongitude());
        address.setLatitude(po.getLatitude());
        return address;
    }



}