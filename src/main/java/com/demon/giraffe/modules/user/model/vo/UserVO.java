package com.demon.giraffe.modules.user.model.vo;


import com.demon.giraffe.modules.user.model.enums.SexEnum;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import com.demon.giraffe.modules.user.model.enums.UserStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @className com.demon.giraffe.modules.user.model.vo UserVO
 * @date 30/6/2025 下午10:19
 * @description TODO
 */
@Data
@Builder
@Schema(name = "UserVO", description = "用户信息响应体")
public class UserVO {

    @Schema(description = "用户ID", example = "123456", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;

    @Schema(description = "用户编号(唯一业务ID)", example = "U202406000001", requiredMode = Schema.RequiredMode.REQUIRED)
    private String userCode;


    @Schema(description = "用户昵称", example = "张三")
    private String nickName;

    @Schema(description = "用户头像URL", example = "https://example.com/avatar.jpg")
    private String avatarUrl;

    @Schema(description = "用户性别", example = "MALE", allowableValues = {"MALE", "FEMALE", "UNKNOWN"})
    private SexEnum gender;

    @Schema(description = "手机号", example = "13800138000")
    private String phone;

    @Schema(description = "角色权限", example = "ADMIN", requiredMode = Schema.RequiredMode.REQUIRED)
    private UserRole userRole;

    @Schema(description = "用户状态", example = "0", allowableValues = {"0", "1", "2", "3"})
    private UserStatus status;


    @Schema(description = "注册时间", example = "2024-06-01 12:00:00")
    private LocalDateTime registerTime;

    @Schema(description = "最后登录时间", example = "2024-06-15 08:30:45")
    private LocalDateTime lastLoginTime;

    @Schema(description = "登录次数", example = "42")
    private Integer loginCount;
}
