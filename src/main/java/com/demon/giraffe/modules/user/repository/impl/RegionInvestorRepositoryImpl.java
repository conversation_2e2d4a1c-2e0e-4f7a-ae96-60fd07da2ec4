package com.demon.giraffe.modules.user.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.modules.user.mapper.RegionInvestorMapper;
import com.demon.giraffe.modules.user.model.dto.query.RegionInvestorQueryRequest;
import com.demon.giraffe.modules.user.model.po.RegionInvestorPo;
import com.demon.giraffe.modules.user.repository.RegionInvestorRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
@RequiredArgsConstructor
public class RegionInvestorRepositoryImpl implements RegionInvestorRepository {

    private final RegionInvestorMapper investorMapper;

    @Override
    public RegionInvestorPo saveOrUpdate(RegionInvestorPo po) {
        // 1. 参数校验
        if (po == null || po.getAppUserId() == null) {
            throw new IllegalArgumentException("投资人信息或用户ID不能为空");
        }

        // 2. 检查是否已存在记录（根据appUserId查询）
        RegionInvestorPo existing = investorMapper.selectByAppUserIdIgnoreDeleted(po.getAppUserId());

        if (existing != null) {
            investorMapper.restoreById(existing.getId());
            // 3. 存在则更新
            po.setId(existing.getId()); // 保持相同ID
            int result = investorMapper.updateById(po);
            if (result <= 0) {
                throw new BusinessException("投资人信息更新失败");
            }
        } else {
            // 4. 不存在则新增
            int result = investorMapper.insert(po);
            if (result <= 0) {
                throw new BusinessException("投资人信息保存失败");
            }
        }

        return po;
    }

    @Override
    public boolean save(RegionInvestorPo po) {
        if (po == null) {
            throw new IllegalArgumentException("投资人信息不能为空");
        }
        int result = investorMapper.insert(po);
        return result > 0;
    }

    @Override
    public boolean updateById(RegionInvestorPo po) {
        return investorMapper.updateById(po) > 0;
    }

    @Override
    public RegionInvestorPo getById(Long id) {
        return investorMapper.selectById(id);
    }

    @Override
    public IPage<RegionInvestorPo> queryPage(Page<RegionInvestorPo> page, RegionInvestorQueryRequest query) {
        LambdaQueryWrapper<RegionInvestorPo> wrapper = new LambdaQueryWrapper<>();
        if (query != null) {
            wrapper.eq(query.getFactoryId() != null, RegionInvestorPo::getFactoryId, query.getFactoryId())
                    .like(query.getInvestorNo() != null, RegionInvestorPo::getInvestorNo, query.getInvestorNo())
                    .like(query.getContactPerson() != null, RegionInvestorPo::getContactPerson, query.getContactPerson())
                    .like(query.getContactPhone() != null, RegionInvestorPo::getContactPhone, query.getContactPhone());
        }
        return investorMapper.selectPage(page, wrapper);
    }

    @Override
    public boolean deleteByUserId(Long userId) {
        LambdaUpdateWrapper<RegionInvestorPo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(RegionInvestorPo::getAppUserId, userId);
        return investorMapper.delete(wrapper) > 0;
    }

    @Override
    public RegionInvestorPo getByAppUserId(Long appUserId) {
        LambdaQueryWrapper<RegionInvestorPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RegionInvestorPo::getAppUserId, appUserId);
        return investorMapper.selectOne(wrapper);
    }

    @Override
    public boolean deleteByAppUserId(Long appUserId) {
        LambdaUpdateWrapper<RegionInvestorPo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(RegionInvestorPo::getAppUserId, appUserId);
        return investorMapper.delete(wrapper) > 0;
    }

    @Override
    public List<RegionInvestorPo> listByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return List.of();
        }

        LambdaQueryWrapper<RegionInvestorPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(RegionInvestorPo::getId, ids);
        return investorMapper.selectList(wrapper);
    }

    @Override
    public List<RegionInvestorPo> listByAppUserIds(Set<Long> appUserIds) {
        if (appUserIds == null || appUserIds.isEmpty()) {
            return List.of();
        }

        LambdaQueryWrapper<RegionInvestorPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(RegionInvestorPo::getAppUserId, appUserIds);
        return investorMapper.selectList(wrapper);
    }

    @Override
    public boolean existsById(Long id) {
        LambdaQueryWrapper<RegionInvestorPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RegionInvestorPo::getId, id);
        return investorMapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean existsByAppUserId(Long appUserId) {
        LambdaQueryWrapper<RegionInvestorPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RegionInvestorPo::getAppUserId, appUserId);
        return investorMapper.selectCount(wrapper) > 0;
    }

    @Override
    public RegionInvestorPo getByRegionAndCabinet(CountyEnum addressCode, Long regionInvestorId) {
        LambdaQueryWrapper<RegionInvestorPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RegionInvestorPo::getAddressCode, addressCode)
               .eq(regionInvestorId != null, RegionInvestorPo::getId, regionInvestorId);
        return investorMapper.selectOne(wrapper);
    }

    @Override
    public List<RegionInvestorPo> listByAddressCode(CountyEnum addressCode) {
        LambdaQueryWrapper<RegionInvestorPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RegionInvestorPo::getAddressCode, addressCode);
        return investorMapper.selectList(wrapper);
    }

    @Override
    public boolean batchDelete(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        return investorMapper.deleteBatchIds(ids) > 0;
    }
}