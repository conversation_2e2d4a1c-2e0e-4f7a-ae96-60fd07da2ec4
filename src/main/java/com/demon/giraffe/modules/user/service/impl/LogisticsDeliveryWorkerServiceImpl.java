package com.demon.giraffe.modules.user.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.modules.user.model.dto.query.DeliveryWorkerQueryRequest;
import com.demon.giraffe.modules.user.service.helper.LogisticsDeliveryWorkerConvertHelper;
import com.demon.giraffe.modules.user.model.dto.request.DeliveryWorkerRequest;
import com.demon.giraffe.modules.user.model.dto.response.DeliveryWorkerResponse;
import com.demon.giraffe.modules.user.model.enums.DeliveryWorkerStatusEnum;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import com.demon.giraffe.modules.user.model.po.LogisticsDeliveryWorkerPo;
import com.demon.giraffe.modules.user.repository.LogisticsDeliveryWorkerRepository;
import com.demon.giraffe.modules.user.service.LogisticsDeliveryWorkerService;
import com.demon.giraffe.modules.user.service.RegionInvestorService;
import com.demon.giraffe.modules.user.service.UserService;
import com.demon.giraffe.modules.user.service.base.AbstractRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;

/**
 * 物流配送员服务实现
 * 基于统一角色管理架构重写
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LogisticsDeliveryWorkerServiceImpl extends AbstractRoleService<
        LogisticsDeliveryWorkerPo,
        DeliveryWorkerRequest,
        DeliveryWorkerResponse,
        DeliveryWorkerQueryRequest> implements LogisticsDeliveryWorkerService {

    private final LogisticsDeliveryWorkerRepository workerRepository;
    private final RegionInvestorService regionInvestorService;
    private final LogisticsDeliveryWorkerConvertHelper convertHelper;

    // ========== 统一角色管理架构实现 ==========

    @Override
    public UserRole getRoleType() {
        return UserRole.DELIVERY;
    }

    @Override
    protected Long doCreateRole(DeliveryWorkerRequest request) {
        // 使用ConvertHelper统一处理编码生成和对象转换
        LogisticsDeliveryWorkerPo po = convertHelper.convertRequestToPoForCreate(request);
        po.setCurrentStatus(DeliveryWorkerStatusEnum.ONLINE);

        workerRepository.save(po);
        return po.getId();
    }

    @Override
    protected Boolean doDeleteRole(Long userId) {
        return workerRepository.deleteByUserId(userId);
    }

    @Override
    protected Long extractUserIdFromRequest(DeliveryWorkerRequest request) {
        return request.getAppUserId();
    }

    @Override
    protected void validateCreateRequest(DeliveryWorkerRequest request) {
        Assert.notNull(request.getAppUserId(), "用户ID不能为空");
        Assert.notNull(request.getRegionInvestorId(), "所属投资人ID不能为空");
        Assert.hasText(request.getPhone(), "手机号不能为空");
        Assert.hasText(request.getRealName(), "姓名不能为空");
    }

    @Override
    protected void validateBusinessRules(DeliveryWorkerRequest request) {
        // 验证投资人是否存在
        Assert.isTrue(regionInvestorService.existsById(request.getRegionInvestorId()),
                "所属投资人不存在");
    }

    @Override
    protected void doRoleSpecificLogin(Long userId, DeliveryWorkerResponse roleInfo) {
        log.info("取送员[{}]登录", roleInfo.getRegionInvestorId());
        // 登录时设置为在线状态
        updateDeliveryWorkerStatus(roleInfo.getId(), DeliveryWorkerStatusEnum.ONLINE);
    }

    @Override
    protected void doRoleSpecificLogout(Long userId) {
        // 注销时设置为离线状态
        DeliveryWorkerResponse worker = getRoleByUserId(userId);
        if (worker != null) {
            updateDeliveryWorkerStatus(worker.getId(), DeliveryWorkerStatusEnum.OFFLINE);
        }
    }

    @Override
    protected void doAfterRoleCreated(Long roleId, DeliveryWorkerRequest request) {
        log.info("取送员创建成功，ID：{}，所属投资人：{}", roleId, request.getRegionInvestorId());
    }

    // ========== 业务特定方法实现 ==========

    // ========== 统一角色管理架构方法重写 ==========

    @Override
    public Boolean updateRole(DeliveryWorkerRequest request) {
        // 使用ConvertHelper处理更新（不会重新生成编码）
        LogisticsDeliveryWorkerPo po = convertHelper.convertRequestToPoForUpdate(request);
        return workerRepository.updateById(po);
    }

    @Override
    public DeliveryWorkerResponse getRoleDetail(Long id) {
        LogisticsDeliveryWorkerPo po = workerRepository.getById(id);
        Assert.notNull(po, "配送员不存在");

        // 使用ConvertHelper统一转换
        return convertHelper.convertPoToResponse(po);
    }

    @Override
    public DeliveryWorkerResponse getRoleByUserId(Long userId) {
        LogisticsDeliveryWorkerPo po = workerRepository.getByAppUserId(userId);
        if (po == null) {
            return null;
        }

        // 使用ConvertHelper统一转换
        return convertHelper.convertPoToResponse(po);
    }

    @Override
    public IPage<DeliveryWorkerResponse> queryRolePage(BasePageQuery<DeliveryWorkerQueryRequest> pageQuery) {
        // 初始化分页参数
        pageQuery.init();

        // 构建分页对象
        Page<LogisticsDeliveryWorkerPo> page = new Page<>(pageQuery.getPage(), pageQuery.getPerPage());

        // 执行查询
        IPage<LogisticsDeliveryWorkerPo> poPage = workerRepository.queryPage(
                page,
                pageQuery.getQuery()
        );

        // 使用ConvertHelper统一转换为Response对象
        return poPage.convert(convertHelper::convertPoToResponse);
    }

    @Override
    public Boolean saveRole(LogisticsDeliveryWorkerPo entity) {
        return workerRepository.save(entity);
    }

    @Override
    public Boolean existsById(Long id) {
        return id != null && workerRepository.getById(id) != null;
    }





    @Override
    public Boolean changeStatus(Long userId, Object newStatus) {
        if (newStatus instanceof DeliveryWorkerStatusEnum) {
            DeliveryWorkerResponse worker = getRoleByUserId(userId);
            if (worker != null) {
                return updateDeliveryWorkerStatus(worker.getId(), (DeliveryWorkerStatusEnum) newStatus);
            }
        }
        return false;
    }

    // ========== 业务特定方法实现 ==========

    @Override
    public Boolean updateDeliveryWorkerStatus(Long id, DeliveryWorkerStatusEnum status) {
        return workerRepository.updateStatus(id, status);
    }

    @Override
    public Boolean updateDeliveryWorkerLocation(Long id, BigDecimal longitude, BigDecimal latitude) {
        return workerRepository.updateLocation(id, longitude, latitude);
    }

    // ========== 业务特定方法 ==========

    // generateWorkerNo方法已移除，编码生成统一由ConvertHelper处理

    public IPage<DeliveryWorkerResponse> getWorkersByRegionInvestor(Long regionInvestorId) {
        // 根据投资人ID查询取送员列表
        // 这里需要实现具体的查询逻辑
        return null;
    }
}