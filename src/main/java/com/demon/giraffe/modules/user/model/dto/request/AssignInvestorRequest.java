package com.demon.giraffe.modules.user.model.dto.request;

import com.demon.giraffe.common.domain.dto.request.RegionRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Schema(name = "AssignInvestorRequest", description = "分配区域投资人角色请求")
public class AssignInvestorRequest implements Serializable {

    @Schema(description = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "工厂ID", required = true)
    @NotNull(message = "工厂ID不能为空")
    private Long factoryId;

    /**
     * 区域信息（统一处理，可选更新）
     */
    @Valid
    @Schema(description = "区域信息，留空表示不更新")
    private RegionRequest region;
}