package com.demon.giraffe.modules.user.model.dto.request;

import com.demon.giraffe.modules.user.model.enums.FactoryPositionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
@Schema(name = "FactoryWorkerRequest", description = "工厂员工创建/更新请求")
public class FactoryWorkerRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "员工ID(更新时必填)")
    private Long id;

    @NotNull(message = "关联用户ID不能为空")
    @Schema(description = "关联用户ID", required = true)
    private Long appUserId;

    @NotNull(message = "工厂ID不能为空")
    @Schema(description = "工厂ID", required = true)
    private Long factoryId;

    @NotBlank(message = "手机号不能为空")
    @Size(min = 11, max = 11, message = "手机号必须为11位")
    @Schema(description = "手机号", required = true, minLength = 11, maxLength = 11)
    private String phone;

    @Size(max = 30, message = "姓名长度不能超过30个字符")
    @Schema(description = "真实姓名", maxLength = 30)
    private String realName;

    @Size(max = 18, message = "身份证号长度不能超过18个字符")
    @Schema(description = "身份证号", maxLength = 18)
    private String idCard;

    @Schema(description = "职位类型")
    private FactoryPositionTypeEnum positionType;
}