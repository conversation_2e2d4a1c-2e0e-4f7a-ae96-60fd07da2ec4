package com.demon.giraffe.modules.user.model.dto.query;

import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.modules.user.model.enums.DeliveryWorkerStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "DeliveryWorkerQueryRequest", description = "配送员查询条件")
public class DeliveryWorkerQueryRequest extends BasePageQuery<DeliveryWorkerQueryRequest> implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "关联用户ID")
    private Long appUserId;

    @Schema(description = "所属投资人ID")
    private Long regionInvestorId;

    @Schema(description = "当前状态")
    private DeliveryWorkerStatusEnum currentStatus;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "真实姓名")
    private String realName;
}