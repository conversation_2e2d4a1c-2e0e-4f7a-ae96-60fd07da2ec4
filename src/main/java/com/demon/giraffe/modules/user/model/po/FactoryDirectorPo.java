package com.demon.giraffe.modules.user.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.modules.user.model.enums.DirectorStatusEnum;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;

@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("factory_director")
public class FactoryDirectorPo extends BasePo {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "关联用户ID不能为空")
    @TableField("app_user_id")
    private Long appUserId;

    @NotBlank(message = "厂长编号不能为空")
    @Size(max = 20, message = "厂长编号长度不能超过20个字符")
    @TableField("director_no")
    private String directorNo;

    @NotNull(message = "工厂ID不能为空")
    @TableField("factory_id")
    private Long factoryId;

    @TableField("status")
    private DirectorStatusEnum status;
}