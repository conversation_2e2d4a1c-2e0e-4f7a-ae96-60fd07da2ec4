package com.demon.giraffe.modules.user.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.modules.user.mapper.MemberIdentityMapper;
import com.demon.giraffe.modules.user.model.po.MemberIdentityPo;
import com.demon.giraffe.modules.user.repository.MemberIdentityRepository;
import com.demon.giraffe.modules.config.Constants;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Set;

@Repository
@RequiredArgsConstructor
public class MemberIdentityRepositoryImpl implements MemberIdentityRepository {

    private final MemberIdentityMapper mapper;

    @Override
    @Caching(
            put = {
                    @CachePut(value = Constants.CacheNames.MEMBER, key = "#result.id", condition = "#result != null"),
                    @CachePut(value = Constants.CacheNames.MEMBER_BY_USER_ID, key = "#result.appUserId", condition = "#result != null")
            }
    )
    public MemberIdentityPo save(MemberIdentityPo po) {
        if (po == null || po.getAppUserId() == null) {
            throw new BusinessException("会员信息或用户ID不能为空");
        }
        // 2. 检查是否已存在记录
        MemberIdentityPo existing = mapper.selectByAppUserIdIgnoreDeleted(po.getAppUserId());
        if (existing != null) {
            mapper.restoreById(existing.getId());
            po.setId(existing.getId()); // 保持相同ID
            po.setDeleted(false);
            int result = mapper.updateById(po);
            if (result <= 0) {
                throw new BusinessException("会员信息更新失败");
            }
        } else {
            // 4. 如果不存在则新增
            int result = mapper.insert(po);
            if (result <= 0) {
                throw new BusinessException("会员信息保存失败");
            }
        }
        return po;
    }

    @Override
    @Caching(
            evict = {
                    @CacheEvict(value = Constants.CacheNames.MEMBER, key = "#po.id"),
                    @CacheEvict(value = Constants.CacheNames.MEMBER_BY_USER_ID, key = "#po.appUserId")
            }
    )
    public boolean updateById(MemberIdentityPo po) {
        return mapper.updateById(po) > 0;
    }

    @Override
    @Caching(
            evict = {
                    @CacheEvict(value = Constants.CacheNames.MEMBER, key = "#result.id", condition = "#result != null"),
                    @CacheEvict(value = Constants.CacheNames.MEMBER_BY_USER_ID, key = "#appUserId")
            }
    )
    public boolean removeByAppUserId(Long appUserId) {
        MemberIdentityPo po = getByAppUserId(appUserId);
        if (po == null) return false;

        LambdaQueryWrapper<MemberIdentityPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberIdentityPo::getAppUserId, appUserId);
        return mapper.delete(wrapper) > 0;
    }

    @Override
    @CacheEvict(value = Constants.CacheNames.MEMBER, key = "#id")
    public boolean updateStatus(Long id, Integer status) {
        LambdaUpdateWrapper<MemberIdentityPo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MemberIdentityPo::getId, id).set(MemberIdentityPo::getStatus, status);
        return mapper.update(null, wrapper) > 0;
    }

    @Override
    @Cacheable(value = Constants.CacheNames.MEMBER_BY_USER_ID, key = "#appUserId", unless = "#result == null")
    public MemberIdentityPo getByAppUserId(Long appUserId) {
        LambdaQueryWrapper<MemberIdentityPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberIdentityPo::getAppUserId, appUserId);
        return mapper.selectOne(wrapper);
    }

    @Override
    @Cacheable(value = Constants.CacheNames.MEMBER, key = "#id", unless = "#result == null")
    public MemberIdentityPo getById(Long id) {
        return mapper.selectById(id);
    }

    @Override
    @Cacheable(value = Constants.CacheNames.MEMBER, key = "'listByAppUserIds:' + #appUserIds.hashCode()")
    public List<MemberIdentityPo> listByAppUserIds(Set<Long> appUserIds) {
        if (appUserIds == null || appUserIds.isEmpty()) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<MemberIdentityPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MemberIdentityPo::getAppUserId, appUserIds);
        return mapper.selectList(wrapper);
    }
}