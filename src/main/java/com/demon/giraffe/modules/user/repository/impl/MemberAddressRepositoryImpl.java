package com.demon.giraffe.modules.user.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.modules.user.mapper.MemberAddressMapper;
import com.demon.giraffe.modules.user.model.dto.query.MemberAddressQueryRequest;
import com.demon.giraffe.modules.user.model.po.MemberAddressPo;
import com.demon.giraffe.modules.user.repository.MemberAddressRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 会员地址信息仓储实现类
 * <p>
 * 使用MyBatis-Plus实现会员地址信息的持久化操作
 * </p>
 */
@Repository
@RequiredArgsConstructor
public class MemberAddressRepositoryImpl implements MemberAddressRepository {

    private final MemberAddressMapper addressMapper;

    /**
     * 保存会员地址信息
     *
     * @param po 会员地址持久化对象
     * @return 保存后的会员地址信息
     * @throws BusinessException 当保存失败时抛出
     */
    @Override
    public MemberAddressPo save(MemberAddressPo po) {
        int result = addressMapper.insert(po);
        if (result <= 0) {
            throw new BusinessException("会员地址信息保存失败");
        }
        return po;
    }

    /**
     * 根据ID更新会员地址信息
     *
     * @param po 会员地址持久化对象
     * @return 是否更新成功
     */
    @Override
    public boolean updateById(MemberAddressPo po) {
        return addressMapper.updateById(po) > 0;
    }

    /**
     * 根据ID查询会员地址信息
     *
     * @param id 地址ID
     * @return 会员地址信息
     */
    @Override
    public MemberAddressPo getById(Long id) {
        return addressMapper.selectById(id);
    }

    @Override
    public boolean removeById(Long id) {
         addressMapper.realDeleteById(id);
         return true;
    }

    /**
     * 根据ID和会员ID查询会员地址信息
     *
     * @param id       地址ID
     * @param memberId 会员ID
     * @return 会员地址信息
     * @throws BusinessException 当地址不存在或不属于该会员时抛出
     */
    @Override
    public MemberAddressPo findByIdAndMemberId(Long id, Long memberId) {
        return addressMapper.selectOne(new LambdaQueryWrapper<MemberAddressPo>()
                .eq(MemberAddressPo::getId, id)
                .eq(MemberAddressPo::getMemberId, memberId));
    }

    /**
     * 检查地址是否存在且属于指定会员
     *
     * @param id       地址ID
     * @param memberId 会员ID
     * @return 是否存在且属于该会员
     */
    @Override
    public boolean existsByIdAndMemberId(Long id, Long memberId) {
        return addressMapper.selectCount(new LambdaQueryWrapper<MemberAddressPo>()
                .eq(MemberAddressPo::getId, id)
                .eq(MemberAddressPo::getMemberId, memberId)) > 0;
    }


    @Override
    public List<MemberAddressPo> findAllByMemberId(Long memberId) {
        return addressMapper.selectList(new LambdaQueryWrapper<MemberAddressPo>()
                .eq(MemberAddressPo::getMemberId, memberId));
    }
}