package com.demon.giraffe.modules.user.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.modules.laundry.repository.FactoryInfoRepository;
import com.demon.giraffe.modules.user.model.dto.query.FactoryDirectorQueryRequest;
import com.demon.giraffe.modules.user.service.helper.FactoryDirectorConvertHelper;
import com.demon.giraffe.modules.user.model.dto.request.FactoryDirectorRequest;
import com.demon.giraffe.modules.user.model.dto.response.FactoryDirectorResponse;
import com.demon.giraffe.modules.user.model.enums.DirectorStatusEnum;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import com.demon.giraffe.modules.user.model.po.FactoryDirectorPo;
import com.demon.giraffe.modules.user.repository.FactoryDirectorRepository;
import com.demon.giraffe.modules.user.service.FactoryDirectorService;
import com.demon.giraffe.modules.user.service.base.AbstractRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Objects;

/**
 * 工厂管理员服务实现
 * 基于统一角色管理架构重写
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FactoryDirectorServiceImpl extends AbstractRoleService<
        FactoryDirectorPo,
        FactoryDirectorRequest,
        FactoryDirectorResponse,
        FactoryDirectorQueryRequest> implements FactoryDirectorService {

    private final FactoryDirectorRepository directorRepository;
    private final FactoryInfoRepository factoryInfoRepository;
    private final FactoryDirectorConvertHelper convertHelper;

    // ========== 统一角色管理架构实现 ==========

    @Override
    public UserRole getRoleType() {
        return UserRole.FACTORY_MANAGER;
    }

    @Override
    protected Long doCreateRole(FactoryDirectorRequest request) {
        // 使用ConvertHelper统一处理编码生成和对象转换
        FactoryDirectorPo po = convertHelper.convertRequestToPoForCreate(request);
        po.setStatus(DirectorStatusEnum.IN_OFFICE);

        directorRepository.save(po);
        return po.getId();
    }

    @Override
    protected Boolean doDeleteRole(Long userId) {
        return directorRepository.deleteByUserId(userId);
    }

    @Override
    protected Long extractUserIdFromRequest(FactoryDirectorRequest request) {
        return request.getAppUserId();
    }

    @Override
    protected void validateCreateRequest(FactoryDirectorRequest request) {
        Assert.notNull(request.getAppUserId(), "用户ID不能为空");
        Assert.notNull(request.getFactoryId(), "工厂ID不能为空");
        Assert.isTrue(existsFactoryById(request.getFactoryId()), "工厂不存在");
    }

    @Override
    protected void doRoleSpecificLogin(Long userId, FactoryDirectorResponse roleInfo) {
        log.info("工厂管理员[{}]登录，工厂ID：{}", roleInfo.getDirectorNo(), roleInfo.getFactoryId());
    }

    @Override
    protected void doAfterRoleCreated(Long roleId, FactoryDirectorRequest request) {
        log.info("工厂管理员创建成功，ID：{}，工厂：{}", roleId, request.getFactoryId());
    }

    // ========== 业务特定方法实现 ==========

    // ========== 统一角色管理架构方法重写 ==========

    @Override
    public Boolean updateRole(FactoryDirectorRequest request) {
        // 使用ConvertHelper处理更新（不会重新生成编码）
        FactoryDirectorPo po = convertHelper.convertRequestToPoForUpdate(request);
        return directorRepository.updateById(po);
    }

    @Override
    public FactoryDirectorResponse getRoleDetail(Long id) {
        FactoryDirectorPo po = directorRepository.getById(id);
        Assert.notNull(po, "厂长不存在");

        // 使用ConvertHelper统一转换
        return convertHelper.convertPoToResponse(po);
    }

    @Override
    public FactoryDirectorResponse getRoleByUserId(Long userId) {
        FactoryDirectorPo po = directorRepository.getByAppUserId(userId);
        if (po == null) {
            return null;
        }

        // 使用ConvertHelper统一转换
        return convertHelper.convertPoToResponse(po);
    }

    @Override
    public IPage<FactoryDirectorResponse> queryRolePage(BasePageQuery<FactoryDirectorQueryRequest> pageQuery) {
        // 初始化分页参数
        pageQuery.init();

        // 构建分页对象
        Page<FactoryDirectorPo> page = new Page<>(pageQuery.getPage(), pageQuery.getPerPage());

        // 执行查询
        IPage<FactoryDirectorPo> poPage = directorRepository.queryPage(
                page,
                pageQuery.getQuery()
        );

        // 使用ConvertHelper统一转换为Response对象
        return poPage.convert(convertHelper::convertPoToResponse);
    }

    @Override
    public Boolean saveRole(FactoryDirectorPo entity) {
        return directorRepository.save(entity);
    }

    @Override
    public Boolean existsById(Long id) {
        return id != null && directorRepository.getById(id) != null;
    }





    @Override
    public Boolean changeStatus(Long userId, Object newStatus) {
        if (newStatus instanceof DirectorStatusEnum) {
            FactoryDirectorResponse director = getRoleByUserId(userId);
            if (director != null) {
                return changeDirectorStatus(director.getId(), (DirectorStatusEnum) newStatus);
            }
        }
        return false;
    }

    // ========== 业务特定方法实现 ==========

    @Override
    public Boolean changeDirectorStatus(Long id, DirectorStatusEnum status) {
        return directorRepository.updateStatus(id, status);
    }

    @Override
    public boolean existsFactoryById(Long factoryId) {
        if (factoryId == null) {
            return false;
        }
        return Objects.nonNull(factoryInfoRepository.getById(factoryId));
    }
}