package com.demon.giraffe.modules.user.model.po;

import com.baomidou.mybatisplus.annotation.*;

import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.modules.user.model.enums.MemberLevelEnum;
import com.demon.giraffe.modules.user.model.enums.UserStatus;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 会员身份管理表
 * 对应数据库表：member_identity
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("member_identity")
public class MemberIdentityPo extends BasePo {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 关联app_user用户ID
     */
    @NotNull(message = "关联用户ID不能为空")
    @TableField("app_user_id")
    private Long appUserId;

    /**
     * 会员编号（M+时间戳+随机数）
     */
    @NotBlank(message = "会员编号不能为空")
    @TableField("member_no")
    private String memberNo;

    /**
     * 会员等级（1.普通/2.银卡/3.金卡/4.钻石）
     */
    @NotNull(message = "会员等级不能为空")
    @TableField("level")
    private MemberLevelEnum level;

    /**
     * 当前可用积分
     */
    @TableField("points")
    private Integer points;

    /**
     * 累计获得积分
     */
    @TableField("total_points")
    private Integer totalPoints;

    /**
     * 累计消费金额
     */
    @TableField("total_consumed")
    private BigDecimal totalConsumed;

    /**
     * 绑定手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Size(min = 11, max = 11, message = "手机号长度必须为11位")
    @TableField("phone")
    private String phone;

    /**
     * 实名认证姓名
     */
    @TableField("real_name")
    private String realName;

    /**
     * 状态（0正常/1冻结）
     */
    @NotNull
    @TableField("status")
    private UserStatus status;
}