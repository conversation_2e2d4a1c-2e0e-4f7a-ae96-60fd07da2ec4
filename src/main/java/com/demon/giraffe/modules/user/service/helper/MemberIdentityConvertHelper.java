package com.demon.giraffe.modules.user.service.helper;

import com.demon.giraffe.common.util.CodeGeneratorUtil;
import com.demon.giraffe.modules.user.model.dto.response.MemberIdentityDetailResponse;
import com.demon.giraffe.modules.user.model.enums.MemberLevelEnum;
import com.demon.giraffe.modules.user.model.enums.UserStatus;
import com.demon.giraffe.modules.user.model.po.MemberIdentityPo;
import com.demon.giraffe.modules.user.model.po.UserPo;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 会员身份转换工具类
 * <p>手动实现DTO/PO转换逻辑，避免反射性能损耗</p>
 */
@Component
public class MemberIdentityConvertHelper {

    private final CodeGeneratorUtil codeGeneratorUtil;

    public MemberIdentityConvertHelper(CodeGeneratorUtil codeGeneratorUtil) {
        this.codeGeneratorUtil = codeGeneratorUtil;
    }

    public MemberIdentityDetailResponse convertToDetailResponse(MemberIdentityPo po) {
        if (po == null) {
            return null;
        }

        MemberIdentityDetailResponse response = new MemberIdentityDetailResponse();
        response.setMemberId(po.getId());
        response.setAppUserId(po.getAppUserId());
        response.setMemberNo(po.getMemberNo());
        response.setLevel(po.getLevel());
        response.setPoints(po.getPoints());
        response.setTotalPoints(po.getTotalPoints());
        response.setTotalConsumed(po.getTotalConsumed());
        response.setPhone(po.getPhone());
        response.setRealName(po.getRealName());
        response.setCreateTime(po.getCreateTime());
        return response;
    }


    /**
     * 根据用户信息初始化会员身份
     *
     * @param user 用户信息
     * @return 初始化的会员身份信息
     */
    public MemberIdentityPo initializeMember(UserPo user) {
        if (user == null) {
            throw new IllegalArgumentException("用户信息不能为空");
        }

        return MemberIdentityPo.builder()
                .appUserId(user.getId())
                .memberNo(codeGeneratorUtil.generateMemberCode(user.getId()))
                .level(MemberLevelEnum.NORMAL)
                .points(0)
                .totalPoints(0)
                .totalConsumed(new BigDecimal(0))
                .phone(user.getPhone())
                .realName(null) // 实名认证初始为空
                .status(UserStatus.NORMAL)
                .build();
    }
}