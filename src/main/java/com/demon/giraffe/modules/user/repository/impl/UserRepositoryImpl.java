package com.demon.giraffe.modules.user.repository.impl;

import com.demon.giraffe.common.constants.DateFormatConstants;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.modules.user.mapper.UserMapper;
import com.demon.giraffe.modules.user.model.dto.query.UserQueryRequest;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import com.demon.giraffe.modules.user.model.po.UserPo;
import com.demon.giraffe.modules.user.model.enums.UserStatus;
import com.demon.giraffe.modules.user.repository.UserRepository;
import com.demon.giraffe.modules.config.Constants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
/**
 * 用户持久化实现类
 * 实现所有对用户数据的操作
 */
@Repository
public class UserRepositoryImpl implements UserRepository {

    private final UserMapper userMapper;

    public UserRepositoryImpl(UserMapper userMapper) {
        this.userMapper = userMapper;
    }

    @Override
    @Cacheable(value = Constants.CacheNames.APP_USER, key = "#userId", unless = "#result == null")
    public UserPo getById(Long userId) {
        return userMapper.selectById(userId);
    }

    @Override
    @Cacheable(value = Constants.CacheNames.APP_USER, key = "'listByIds:' + #userIds.hashCode()")
    public List<UserPo> listByIds(List<Long> userIds) {
        return userMapper.selectBatchIds(userIds);
    }

    @Override
    @Cacheable(value = Constants.CacheNames.APP_USER_BY_WECHAT, key = "'openid:' + #openid", unless = "#result == null")
    public UserPo getByOpenid(String openid) {
        LambdaQueryWrapper<UserPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserPo::getOpenid, openid);
        return userMapper.selectOne(wrapper);
    }

    @Override
    @Caching(
            put = {
                    @CachePut(value = Constants.CacheNames.APP_USER, key = "#result.id", condition = "#result != null"),
                    @CachePut(value = Constants.CacheNames.APP_USER_BY_PHONE, key = "#result.phone", condition = "#result != null && #result.phone != null"),
                    @CachePut(value = Constants.CacheNames.APP_USER_BY_WECHAT, key = "'openid:' + #result.openid", condition = "#result != null"),
                    @CachePut(value = Constants.CacheNames.APP_USER_BY_WECHAT, key = "'unionId:' + #result.unionId", condition = "#result != null && #result.unionId != null")
            }
    )
    public UserPo save(UserPo userPo) {
        // 先查询是否存在（包括已删除的）
        UserPo existing = userMapper.selectByOpenidIgnoreDeleted(userPo.getOpenid());
        if (existing != null) {
            userMapper.restoreById(existing.getId());
            userPo.setId(existing.getId());
            userMapper.updateById(userPo);
            return userPo;
        }

        int result = userMapper.insert(userPo);
        if (result <= 0) {
            throw new BusinessException("用户保存失败");
        }
        return userPo;
    }

    @Override
    @Caching(
            evict = {
                    @CacheEvict(value = Constants.CacheNames.APP_USER, key = "#userPo.id"),
                    @CacheEvict(value = Constants.CacheNames.APP_USER_BY_PHONE, key = "#userPo.phone", condition = "#userPo.phone != null"),
                    @CacheEvict(value = Constants.CacheNames.APP_USER_BY_WECHAT, key = "'openid:' + #userPo.openid"),
                    @CacheEvict(value = Constants.CacheNames.APP_USER_BY_WECHAT, key = "'unionId:' + #userPo.unionId", condition = "#userPo.unionId != null")
            }
    )
    public boolean updateById(UserPo userPo) {
        return userMapper.updateById(userPo) > 0;
    }

    @Override
    @Caching(
            evict = {
                    @CacheEvict(value = Constants.CacheNames.APP_USER, key = "#userId"),
                    @CacheEvict(value = Constants.CacheNames.APP_USER_BY_PHONE, key = "#result.phone", condition = "#result != null && #result.phone != null"),
                    @CacheEvict(value = Constants.CacheNames.APP_USER_BY_WECHAT, key = "'openid:' + #result.openid", condition = "#result != null"),
                    @CacheEvict(value = Constants.CacheNames.APP_USER_BY_WECHAT, key = "'unionId:' + #result.unionId", condition = "#result != null && #result.unionId != null")
            }
    )
    public boolean deleteById(Long userId) {
        UserPo user = getById(userId);
        if (user == null) return false;

        return userMapper.deleteById(userId) > 0;
    }

    @Override
    public IPage<UserPo> pageByQuery(BasePageQuery<UserQueryRequest> pageQuery) {
        // 初始化分页参数
        pageQuery.init();

        // 创建分页对象
        Page<UserPo> page = new Page<>(
                Objects.nonNull(pageQuery.getPage()) ? pageQuery.getPage() : 1,
                Objects.nonNull(pageQuery.getPerPage()) ? pageQuery.getPerPage() : 10
        );

        // 获取查询条件
        UserQueryRequest query = pageQuery.getQuery();
        LambdaQueryWrapper<UserPo> wrapper = new LambdaQueryWrapper<>();

        // 构建查询条件
        if (query != null) {
            // 角色筛选
            if (query.getRole() != null) {
                wrapper.eq(UserPo::getRole, query.getRole());
            }

            // 关键词搜索 (支持用户ID或用户编号模糊查询)
            if (StringUtils.isNotBlank(query.getKeyword())) {
                wrapper.and(q -> q
                        .like(UserPo::getUserCode, query.getKeyword())
                        .or()
                        .eq(UserPo::getId, query.getKeyword())
                );
            }

            // 注册时间范围查询
            if (StringUtils.isNotBlank(query.getRegisterTimeStart())) {
                LocalDateTime startTime = LocalDateTime.parse(query.getRegisterTimeStart(), DateFormatConstants.NORMAL_DATE_TIME_FORMATTER);
                wrapper.ge(UserPo::getRegisterTime, startTime);
            }
            if (StringUtils.isNotBlank(query.getRegisterTimeEnd())) {
                LocalDateTime endTime = LocalDateTime.parse(query.getRegisterTimeEnd(), DateFormatConstants.NORMAL_DATE_TIME_FORMATTER);
                wrapper.le(UserPo::getRegisterTime, endTime);
            }
        }

        // 按注册时间倒序
        wrapper.orderByDesc(UserPo::getRegisterTime);

        // 执行分页查询
        return userMapper.selectPage(page, wrapper);
    }
}