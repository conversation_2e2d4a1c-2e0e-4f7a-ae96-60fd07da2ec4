package com.demon.giraffe.modules.user.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.modules.user.model.enums.*;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 微信用户信息表
 * 对应数据库表：app_user
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("app_user")
public class UserPo extends BasePo {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 微信openid，唯一，必填 */
    @NotBlank(message = "openid不能为空")
    @TableField("openid")
    private String openid;

    /** 微信unionId，唯一，非必填 */
    @TableField("unionid")
    private String unionId;

    /** 用户编号，唯一业务ID（如USR20230001） */
    @NotBlank
    @TableField("user_code")
    private String userCode;

    /** 手机号，唯一，非必填 */
    @TableField("phone")
    private String phone;

    /** 微信昵称，非必填 */
    @TableField("nickname")
    private String nickname;

    /** 微信头像URL，非必填 */
    @TableField("avatar_url")
    private String avatarUrl;

    /** 性别 (0未知/1男/2女)，默认0 */
    @TableField("gender")
    private SexEnum gender;

    /** 语言，默认zh_CN */
    @TableField("language")
    private String language;

    /** 城市，非必填 */
    @TableField("city")
    private String city;

    /** 省份，非必填 */
    @TableField("province")
    private String province;

    /** 国家，非必填 */
    @TableField("country")
    private String country;

    /** 注册来源 (1小程序/2公众号/3APP/4H5/5PC)，必填，默认1 */
    @NotNull
    @TableField("register_source")
    private RegisterSourceEnum registerSource;

    /** 注册时间，必填，默认当前时间 */
    @NotNull
    @TableField("register_time")
    private LocalDateTime registerTime;

    /** 最后登录时间，非必填 */
    @TableField("last_login_time")
    private LocalDateTime lastLoginTime;

    /** 角色编码 */
    @NotBlank
    @TableField(value = "role")
    private UserRole role;

    /** 登录次数，必填，默认0 */
    @NotNull
    @TableField("login_count")
    private Integer loginCount;

    /** 状态 (0正常/1禁用/2注销/3冻结)，必填，默认0 */
    @NotNull
    @TableField("status")
    private UserStatus status;

    /** 设备信息，JSON字符串，非必填 */
    @TableField("device_info")
    private String deviceInfo;

    /** 备注，非必填 */
    @Size(max = 200)
    @TableField("remark")
    private String remark;

}