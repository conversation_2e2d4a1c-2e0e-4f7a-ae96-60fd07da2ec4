package com.demon.giraffe.modules.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.demon.giraffe.modules.user.model.po.MemberAddressPo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface MemberAddressMapper extends BaseMapper<MemberAddressPo> {

    /**
     * 根据ID真实删除地址记录
     * @param id 地址ID
     * @return 影响的行数
     */
    @Delete("DELETE FROM member_address WHERE id = #{id}")
    int realDeleteById(Long id);
}