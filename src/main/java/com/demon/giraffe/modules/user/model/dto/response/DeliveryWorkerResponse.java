package com.demon.giraffe.modules.user.model.dto.response;

import com.demon.giraffe.modules.user.model.enums.DeliveryWorkerStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "DeliveryWorkerResponse", description = "配送员响应信息")
public class DeliveryWorkerResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "配送员ID")
    private Long id;

    @Schema(description = "关联用户ID")
    private Long appUserId;

    @Schema(description = "所属投资人ID")
    private Long regionInvestorId;

    @Schema(description = "当前状态")
    private DeliveryWorkerStatusEnum currentStatus;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "真实姓名")
    private String realName;

    @Schema(description = "身份证号")
    private String idCard;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}