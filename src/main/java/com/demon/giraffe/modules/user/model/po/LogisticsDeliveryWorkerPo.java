package com.demon.giraffe.modules.user.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.modules.user.model.enums.DeliveryWorkerStatusEnum;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("logistics_delivery_worker")
public class LogisticsDeliveryWorkerPo extends BasePo {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "关联用户ID不能为空")
    @TableField("app_user_id")
    private Long appUserId;

    @NotNull(message = "所属投资人ID不能为空")
    @TableField("region_investor_id")
    private Long regionInvestorId;

    @NotBlank(message = "员工编号不能为空")
    @Size(max = 20, message = "员工编号长度不能超过20个字符")
    @TableField("worker_no")
    private String workerNo;

    @TableField("current_status")
    private DeliveryWorkerStatusEnum currentStatus;


    @NotBlank(message = "手机号不能为空")
    @Size(min = 11, max = 11, message = "手机号必须为11位")
    @TableField("phone")
    private String phone;

    @Size(max = 30, message = "姓名长度不能超过30个字符")
    @TableField("real_name")
    private String realName;


}