package com.demon.giraffe.modules.user.service.helper;

import com.demon.giraffe.common.util.CodeGeneratorUtil;
import com.demon.giraffe.modules.user.model.dto.request.FactoryDirectorRequest;
import com.demon.giraffe.modules.user.model.dto.response.FactoryDirectorResponse;
import com.demon.giraffe.modules.user.model.po.FactoryDirectorPo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 工厂管理员转换助手
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FactoryDirectorConvertHelper {

    private final CodeGeneratorUtil codeGeneratorUtil;

    /**
     * 将请求对象转换为PO（用于创建）
     * @param request 请求对象
     * @return 转换后的PO对象
     */
    public FactoryDirectorPo convertRequestToPo(FactoryDirectorRequest request) {
        if (request == null) {
            return null;
        }

        // 生成厂长编码
        String directorNo = codeGeneratorUtil.generateFactoryDirectorCode(request.getFactoryId());

        FactoryDirectorPo po = FactoryDirectorPo.builder()
                .appUserId(request.getAppUserId())
                .directorNo(directorNo)
                .factoryId(request.getFactoryId())
                .status(request.getStatus())
                .build();

        // 只有在更新时才设置ID
        if (request.getId() != null) {
            po.setId(request.getId());
        }

        return po;
    }

    /**
     * 将PO转换为响应对象
     * @param po 持久化对象
     * @return 转换后的响应对象
     */
    public FactoryDirectorResponse convertPoToResponse(FactoryDirectorPo po) {
        if (po == null) {
            return null;
        }

        return FactoryDirectorResponse.builder()
                .id(po.getId())
                .appUserId(po.getAppUserId())
                .directorNo(po.getDirectorNo())
                .factoryId(po.getFactoryId())
                .createTime(po.getCreateTime())
                .build();
    }

    /**
     * 更新PO对象属性
     * @param po 目标PO对象
     * @param request 来源请求对象
     */
    public void updatePoFromRequest(FactoryDirectorPo po, FactoryDirectorRequest request) {
        if (po == null || request == null) {
            return;
        }

        if (request.getAppUserId() != null) {
            po.setAppUserId(request.getAppUserId());
        }
        if (request.getFactoryId() != null) {
            po.setFactoryId(request.getFactoryId());
        }
        if (request.getStatus() != null) {
            po.setStatus(request.getStatus());
        }
        // 注意：directorNo不允许更新，因为它是系统生成的唯一标识
    }

    /**
     * 将PO列表转换为响应对象列表
     * @param pos PO列表
     * @return 响应对象列表
     */
    public List<FactoryDirectorResponse> convertPosToResponses(List<FactoryDirectorPo> pos) {
        if (pos == null) {
            return null;
        }

        return pos.stream()
                .map(this::convertPoToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 统一转换方法（兼容性）
     */
    public FactoryDirectorResponse convertToResponse(FactoryDirectorPo po) {
        return convertPoToResponse(po);
    }

    /**
     * 将请求对象转换为PO（专门用于创建，不设置ID）
     * @param request 请求对象
     * @return 转换后的PO对象
     */
    public FactoryDirectorPo convertRequestToPoForCreate(FactoryDirectorRequest request) {
        if (request == null) {
            return null;
        }

        // 生成厂长编码
        String directorNo = codeGeneratorUtil.generateFactoryDirectorCode(request.getFactoryId());

        return FactoryDirectorPo.builder()
                .appUserId(request.getAppUserId())
                .directorNo(directorNo)
                .factoryId(request.getFactoryId())
                .status(request.getStatus())
                .build();
    }

    /**
     * 将请求对象转换为PO（专门用于更新，包含ID）
     * @param request 请求对象
     * @return 转换后的PO对象
     */
    public FactoryDirectorPo convertRequestToPoForUpdate(FactoryDirectorRequest request) {
        if (request == null) {
            return null;
        }

        return FactoryDirectorPo.builder()
//                .id(request.getId())
                .appUserId(request.getAppUserId())
                .factoryId(request.getFactoryId())
                .status(request.getStatus())
                .build();
        // 注意：更新时不重新生成directorNo
    }

    /**
     * 统一转换方法（兼容性）
     */
    public FactoryDirectorPo convertToEntity(FactoryDirectorRequest request) {
        return convertRequestToPo(request);
    }
}
