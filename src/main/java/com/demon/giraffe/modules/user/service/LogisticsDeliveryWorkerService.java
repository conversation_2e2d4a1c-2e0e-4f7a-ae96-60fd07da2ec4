package com.demon.giraffe.modules.user.service;

import com.demon.giraffe.modules.user.model.dto.query.DeliveryWorkerQueryRequest;
import com.demon.giraffe.modules.user.model.dto.request.DeliveryWorkerRequest;
import com.demon.giraffe.modules.user.model.dto.response.DeliveryWorkerResponse;
import com.demon.giraffe.modules.user.model.enums.DeliveryWorkerStatusEnum;
import com.demon.giraffe.modules.user.model.po.LogisticsDeliveryWorkerPo;
import com.demon.giraffe.modules.user.service.base.BaseRoleService;

import java.math.BigDecimal;

/**
 * 物流配送员服务接口
 * 继承基础角色服务，获得通用角色管理能力
 */
public interface LogisticsDeliveryWorkerService extends BaseRoleService<LogisticsDeliveryWorkerPo, DeliveryWorkerRequest, DeliveryWorkerResponse, DeliveryWorkerQueryRequest> {

    /**
     * 更新配送员状态
     * @param id 配送员ID
     * @param status 新状态
     * @return 是否更新成功
     */
    Boolean updateDeliveryWorkerStatus(Long id, DeliveryWorkerStatusEnum status);

    /**
     * 更新配送员位置
     * @param id 配送员ID
     * @param longitude 经度
     * @param latitude 纬度
     * @return 是否更新成功
     */
    Boolean updateDeliveryWorkerLocation(Long id, BigDecimal longitude, BigDecimal latitude);
}