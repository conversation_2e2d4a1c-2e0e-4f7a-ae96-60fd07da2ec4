package com.demon.giraffe.modules.user.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Schema(name = "AssignFactoryManagerRequest", description = "分配工厂管理员角色请求")
public class AssignFactoryManagerRequest implements Serializable {

    @Schema(description = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "工厂ID", required = true)
    @NotNull(message = "工厂ID不能为空")
    private Long factoryId;
}