package com.demon.giraffe.modules.user.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户状态枚举
 * 对应数据库字段：status (0正常/1禁用/2注销)
 */
@Getter
@AllArgsConstructor
@Schema(description = "用户状态枚举")
public enum UserStatus implements IEnum<Integer> {

    NORMAL(0, "正常", "账号可正常使用"),
    DISABLED(1, "禁用", "账号被管理员禁用"),
    CANCELLED(2, "注销", "用户主动注销账号"),
    FROZEN(3, "冻结", "账号因安全原因被临时冻结");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String description;

    private static final Map<Integer, UserStatus> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(UserStatus::getCode, Function.identity()));

    /**
     * 根据code获取枚举
     */
    public static UserStatus of(Integer code) {
        return CODE_MAP.getOrDefault(code, NORMAL);
    }

    /**
     * 判断是否是可用的状态（正常状态）
     */
    public boolean isActive() {
        return this == NORMAL;
    }

    /**
     * 判断是否是禁用状态
     */
    public boolean isDisabled() {
        return this == DISABLED;
    }

    /**
     * 判断是否是注销状态
     */
    public boolean isCancelled() {
        return this == CANCELLED;
    }

    /**
     * 判断是否是冻结状态
     */
    public boolean isFrozen() {
        return this == FROZEN;
    }

    /**
     * 获取所有不可用状态
     */
    public static UserStatus[] getInactiveStatuses() {
        return new UserStatus[]{DISABLED, CANCELLED, FROZEN};
    }

    @Override
    public Integer getValue() {
        return code;
    }
}