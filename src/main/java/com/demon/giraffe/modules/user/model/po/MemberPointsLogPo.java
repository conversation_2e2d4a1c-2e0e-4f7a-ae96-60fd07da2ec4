package com.demon.giraffe.modules.user.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 积分变动记录表（用户激励机制）
 * 对应表：member_points_log
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("member_points_log")
public class MemberPointsLogPo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull
    @Schema(description = "会员ID")
    @TableField("member_id")
    private Long memberId;

    @NotNull
    @Schema(description = "积分类型(1获得/2消费/3过期/4退回)")
    @TableField("type")
    private Integer type;

    @NotNull
    @Schema(description = "积分数量(正负数)")
    @Min(0)
    @TableField("points")
    private Integer points;

    @NotNull
    @Schema(description = "变动后余额")
    @Min(0)
    @TableField("balance")
    private Integer balance;

    @NotNull
    @Schema(description = "来源类型(1下单/2分享/3签到/4消费/5系统赠送)")
    @TableField("source_type")
    private Integer sourceType;

    @Schema(description = "来源关联ID")
    @TableField("source_id")
    private Long sourceId;

    @NotBlank
    @Size(max = 100)
    @Schema(description = "变动说明")
    @TableField("description")
    private String description;

    @Schema(description = "过期时间")
    @TableField("expire_time")
    private LocalDateTime expireTime;

}
