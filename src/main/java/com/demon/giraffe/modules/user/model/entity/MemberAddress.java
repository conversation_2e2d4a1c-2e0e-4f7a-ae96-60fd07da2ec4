package com.demon.giraffe.modules.user.model.entity;

import com.demon.giraffe.common.domain.dto.request.RegionRequest;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 会员地址实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "会员地址实体")
public class MemberAddress {
    @Schema(description = "地址ID")
    private Long id;

    @Schema(description = "区域编码")
    private CountyEnum countyEnum;

    /**
     * 区域信息（统一处理）
     */
    @Schema(description = "区域信息")
    private RegionRequest region;

    /**
     * 省份/直辖市（兼容性字段）
     */
    @Schema(description = "省份/直辖市名称", example = "广东省")
    public String getProvince() {
        return region != null ? region.getProvince() : null;
    }

    /**
     * 城市（兼容性字段）
     */
    @Schema(description = "城市名称", example = "深圳市")
    public String getCity() {
        return region != null ? region.getCity() : null;
    }

    /**
     * 区县（兼容性字段）
     */
    @Schema(description = "区县名称", example = "南山区")
    public String getDistrict() {
        return region != null ? region.getDistrict() : null;
    }

    @Schema(description = "详细地址")
    private String detailAddress;

    @Schema(description = "联系人姓名")
    private String contactName;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "经度")
    private BigDecimal longitude;

    @Schema(description = "纬度")
    private BigDecimal latitude;


    @Schema(description = "预计取送时间（分钟）")
    private Integer estimatedDeliveryTime;
}