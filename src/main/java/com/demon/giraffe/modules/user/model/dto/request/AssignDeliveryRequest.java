package com.demon.giraffe.modules.user.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Schema(name = "AssignDeliveryRequest", description = "分配配送员角色请求")
public class AssignDeliveryRequest implements Serializable {

    @Schema(description = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "区域投资人ID", required = true)
    @NotNull(message = "区域投资人ID不能为空")
    private Long regionInvestorId;
}