package com.demon.giraffe.modules.user.model.dto.request;

import com.demon.giraffe.common.domain.dto.request.RegionRequest;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "RegionInvestorRequest", description = "区域投资人创建/更新请求")
public class RegionInvestorRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "投资人ID（更新时必填）")
    private Long id;

    /**
     * 区域信息（统一处理）
     */
    @Valid
    @Schema(description = "区域信息", required = true)
    private RegionRequest region;

    @NotNull(message = "关联用户ID不能为空")
    @Schema(description = "关联用户ID", required = true)
    private Long appUserId;

    @NotBlank(message = "联系人不能为空")
    @Size(max = 30, message = "联系人长度不能超过30个字符")
    @Schema(description = "联系人", required = true, maxLength = 30)
    private String contactPerson;

    @NotBlank(message = "联系电话不能为空")
    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    @Schema(description = "联系电话", required = true, maxLength = 20)
    private String contactPhone;

    @NotNull(message = "工厂ID不能为空")
    @Schema(description = "工厂ID", required = true)
    private Long factoryId;

    /**
     * 获取区域编码（兼容性方法）
     */
    public CountyEnum getAddressCode() {
        return region != null ? region.getAddressCode() : null;
    }

    /**
     * 设置区域编码（兼容性方法）
     */
    public void setAddressCode(CountyEnum addressCode) {
        if (region == null) {
            region = new RegionRequest();
        }
        region.setAddressCode(addressCode);
    }

    /**
     * 获取完整地址描述
     */
    public String getFullAddress() {
        return region != null ? region.getFullAddress() : "";
    }
}