package com.demon.giraffe.modules.user.model.dto.query;

import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.modules.user.model.enums.DirectorStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "FactoryDirectorQueryRequest", description = "厂长查询条件")
public class FactoryDirectorQueryRequest extends BasePageQuery<FactoryDirectorQueryRequest> implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "关联用户ID")
    private Long appUserId;

    @Schema(description = "工厂ID")
    private Long factoryId;

    @Schema(description = "厂长编号")
    private String directorNo;

    @Schema(description = "厂长状态")
    private DirectorStatusEnum status;
}