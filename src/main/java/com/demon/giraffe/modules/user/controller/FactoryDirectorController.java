package com.demon.giraffe.modules.user.controller;

import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.user.model.enums.DirectorStatusEnum;
import com.demon.giraffe.modules.user.service.FactoryDirectorService;
import com.demon.giraffe.modules.user.service.UnifiedRoleManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/user/factory/director")
@RequiredArgsConstructor
@Tag(name = "厂长管理接口", description = "工厂厂长相关操作")
public class FactoryDirectorController {


    private final FactoryDirectorService factoryDirectorService;


    @PostMapping("/change-status/{id}")
    @Operation(summary = "变更厂长状态", description = "变更指定厂长的状态")
    public ResultBean<Boolean> changeStatus(
            @Parameter(description = "厂长ID", required = true) @PathVariable Long id,
            @Parameter(description = "目标状态", required = true) @RequestParam DirectorStatusEnum status) {
        try {
            Boolean result = factoryDirectorService.changeDirectorStatus(id, status);
            return ResultBean.success("状态变更成功", result);
        } catch (Exception e) {
            log.error("变更厂长状态失败", e);
            return ResultBean.fail("状态变更失败：" + e.getMessage());
        }
    }

    @GetMapping("/check-factory/{factoryId}")
    @Operation(summary = "检查工厂是否存在", description = "检查指定工厂是否存在")
    public ResultBean<Boolean> checkFactory(
            @Parameter(description = "工厂ID", required = true) @PathVariable Long factoryId) {
        try {
            Boolean exists = factoryDirectorService.existsFactoryById(factoryId);
            return ResultBean.success("检查完成", exists);
        } catch (Exception e) {
            log.error("检查工厂是否存在失败", e);
            return ResultBean.fail("检查失败：" + e.getMessage());
        }
    }
}