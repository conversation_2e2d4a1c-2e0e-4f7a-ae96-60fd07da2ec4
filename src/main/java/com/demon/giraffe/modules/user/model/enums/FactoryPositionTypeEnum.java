package com.demon.giraffe.modules.user.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "工厂岗位类型枚举")
public enum FactoryPositionTypeEnum implements IEnum<Integer> {

    WASHING(3, "洗涤"),
    ;
    @EnumValue
    private final Integer code;
    private final String desc;

    FactoryPositionTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getValue() {
        return code;
    }
}