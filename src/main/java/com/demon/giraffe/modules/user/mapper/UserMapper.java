package com.demon.giraffe.modules.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.demon.giraffe.modules.user.model.po.UserPo;
import org.apache.ibatis.annotations.*;

/**
 * 用户Mapper接口，继承 MyBatis-Plus 提供的基础 CRUD 接口
 */
@Mapper
public interface UserMapper extends BaseMapper<UserPo> {

    /**
     * 根据 openid 查询用户（包括已逻辑删除的记录）
     *
     * @param openid 用户微信openid
     * @return 用户信息
     */
    @Select("SELECT * FROM app_user WHERE openid = #{openid}")
    UserPo selectByOpenidIgnoreDeleted(@Param("openid") String openid);


    /**
     * 根据主键ID恢复已逻辑删除的用户信息
     *
     * @param id 用户ID
     * @return 更新成功的记录数（1表示恢复成功，0表示无可恢复记录）
     */
    @Update("UPDATE app_user SET deleted = 0 WHERE id = #{id} AND deleted = 1")
    int restoreById(@Param("id") Long id);

}
