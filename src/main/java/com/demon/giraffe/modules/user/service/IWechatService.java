package com.demon.giraffe.modules.user.service;

import com.demon.giraffe.modules.user.model.dto.request.WechatLoginRequest;
import com.demon.giraffe.modules.user.model.dto.response.EnhancedLoginResponse;
import com.demon.giraffe.modules.user.model.enums.UserRole;

/**
 * 统一微信服务接口
 * 专注于微信登录功能，角色管理功能通过组合CoreRoleManagementService实现
 */
public interface IWechatService {

    /**
     * 微信登录
     */
    EnhancedLoginResponse login(WechatLoginRequest dto);

    /**
     * 增强微信登录（包含角色信息）
     */
    EnhancedLoginResponse enhancedLogin(WechatLoginRequest dto);


    /**
     * 增强简单登录（包含角色信息）
     */
    EnhancedLoginResponse enhancedSimpleLogin(String openid);

}
