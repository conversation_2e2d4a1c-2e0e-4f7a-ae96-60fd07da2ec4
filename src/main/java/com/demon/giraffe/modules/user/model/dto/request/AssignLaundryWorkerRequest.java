package com.demon.giraffe.modules.user.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Schema(name = "AssignLaundryWorkerRequest", description = "分配洗护工角色请求")
public class AssignLaundryWorkerRequest implements Serializable {

    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", required = true)
    private Long userId;

    @NotNull(message = "工厂ID不能为空")
    @Schema(description = "工厂ID", required = true)
    private Long factoryId;
}
