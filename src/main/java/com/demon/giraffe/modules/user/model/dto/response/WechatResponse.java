package com.demon.giraffe.modules.user.model.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "微信登录响应结果")
public class WechatResponse {
    @Schema(description = "用户唯一标识", example = "o7K2t5axxxxxxx")
    private String openid;

    @Schema(description = "会话密钥", example = "HyVFkGl5F5OQWJzzalRHdg==")
    private String session_key;

    @Schema(description = "错误码", example = "0")
    private Integer errcode;

    @Schema(description = "错误信息", example = "ok")
    private String errmsg;
}