package com.demon.giraffe.modules.user.service.impl;

import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.dev33.satoken.stp.StpUtil;
import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.common.exception.exception.UserLoginException;
import com.demon.giraffe.modules.user.model.dto.entity.WxSessionDto;
import com.demon.giraffe.modules.user.model.dto.request.WechatLoginRequest;
import com.demon.giraffe.modules.user.model.dto.response.*;
import com.demon.giraffe.modules.user.model.po.UserPo;
import com.demon.giraffe.modules.user.repository.UserRepository;
import com.demon.giraffe.modules.user.service.*;
import com.demon.giraffe.modules.user.service.helper.UserConvertHelper;
import com.demon.giraffe.modules.user.util.WxApiUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

import static com.demon.giraffe.common.constants.UserConstants.*;

/**
 * 增强的微信登录服务
 * 集成统一角色管理功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EnhancedWechatService implements IWechatService {

    private final WxApiUtil wxApiUtil;
    private final UserRepository userRepository;
    private final UserService userService;
    private final UserConvertHelper userConvertHelper;
    private final MemberIdentityService memberIdentityService;
    private final CoreRoleManagementService coreRoleManagementService;

    @Override
    @Transactional
    public EnhancedLoginResponse login(WechatLoginRequest dto) {
        // 直接返回增强登录响应
        return enhancedLogin(dto);
    }


    /**
     * 增强的微信登录，包含角色信息
     */
    @Override
    @Transactional
    public EnhancedLoginResponse enhancedLogin(WechatLoginRequest dto) {
        if (StringUtils.isBlank(dto.getCode())) {
            throw new IllegalArgumentException("code不能为空");
        }

        // 1. 获取微信用户信息
        WxSessionDto wxSession = wxApiUtil.getWxSession(dto.getCode());
        if (Objects.isNull(wxSession)) {
            throw new UserLoginException("Code异常获取不到用户信息");
        }

        String openid = wxSession.getOpenid();
        String phoneNumber = null;

        // 2. 获取手机号
        if (dto.getUserInfo() != null && StringUtils.isNotBlank(dto.getUserInfo().getPhoneCode())) {
            WxMaPhoneNumberInfo wxMaPhoneNumberInfo = wxApiUtil.getPhoneNumber(dto.getUserInfo().getPhoneCode());
            phoneNumber = wxMaPhoneNumberInfo.getPhoneNumber();
        }

        // 3. 处理用户信息
        UserPo user = userService.getUserByOpenId(openid);

        if (user == null) {
            // 新用户注册
            user = userConvertHelper.fromWechatLoginRequest(dto.getUserInfo(), openid, phoneNumber);
            UserPo userPo = userRepository.save(user);
            //创建会员信息
            memberIdentityService.createMemberIdentity(userPo);
            log.info("新用户注册成功，用户ID：{}, OpenID：{}", userPo.getId(), openid);
        } else {
            // 老用户登录
            //        如果需要保存在redis中
//            user.setLastLoginTime(LocalDateTime.now());
//            user.setLoginCount(user.getLoginCount() + 1);
//            if (StringUtils.isNotBlank(phoneNumber)) {
//                user.setPhone(phoneNumber);
//            }
            userRepository.updateById(user);
        }

        // 4. 执行登录
        performLogin(user);
        String token = StpUtil.getTokenValue();

        // 5. 获取角色信息（优化版本，避免重复查询用户）
        Object roleInfo = null;
        try {
            roleInfo = coreRoleManagementService.getUserRoleInfo(user);
            log.info("用户[{}]角色信息获取成功：{}", user.getId(), roleInfo != null ? "有角色" : "无角色");
        } catch (Exception e) {
            log.warn("用户[{}]角色信息获取失败：{}", user.getId(), e.getMessage());
        }

        // 6. 构建增强登录响应
        EnhancedLoginResponse response = EnhancedLoginResponse.builder()
                .token(token)
                .userRole(user.getRole())
                .userCode(user.getUserCode())
                .userId(user.getId())
                .nickName(user.getNickname())
                .avatarUrl(user.getAvatarUrl())
                .gender(user.getGender())
                .roleInfo(roleInfo)
                .build();

        response.setCurrentLoginTime();
        return response;
    }


    /**
     * 增强的简单登录，包含角色信息
     */
    @Override
    @Transactional
    public EnhancedLoginResponse enhancedSimpleLogin(String openid) {
        // 1. 校验参数
        if (StringUtils.isBlank(openid)) {
            throw new IllegalArgumentException("openid不能为空");
        }

        // 2. 查找用户
        UserPo user = userService.getUserByOpenId(openid);

        // 老用户登录
//        如果需要保存在redis中
//        user.setLastLoginTime(LocalDateTime.now());
//        user.setLoginCount(user.getLoginCount() + 1);
//        userRepository.updateById(user);


        // 4. 执行登录
        performLogin(user);
        String token = StpUtil.getTokenValue();
        // 5. 获取角色信息（优化版本，避免重复查询用户）
        Object roleInfo = null;
        try {
            roleInfo = coreRoleManagementService.getUserRoleInfo(user);
            log.info("用户[{}]角色信息获取成功：{}", user.getId(), roleInfo != null ? "有角色" : "无角色");
        } catch (Exception e) {
            log.warn("用户[{}]角色信息获取失败：{}", user.getId(), e.getMessage());
        }

        // 6. 构建增强登录响应
        EnhancedLoginResponse response = EnhancedLoginResponse.builder()
                .token(token)
                .userRole(user.getRole())
                .userCode(user.getUserCode())
                .userId(user.getId())
                .nickName(user.getNickname())
                .avatarUrl(user.getAvatarUrl())
                .gender(user.getGender())
                .roleInfo(roleInfo)
                .build();

        response.setCurrentLoginTime();
        return response;
    }


    /**
     * 执行用户登录逻辑，并设置对应的 Token 会话信息。
     *
     * @param user 当前登录用户，不能为空
     * @throws BusinessException 登录失败或系统异常
     */
    private void performLogin(UserPo user) {
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        try {
            // 登录
            StpUtil.login(user.getId());

            // 设置会话信息
            StpUtil.getTokenSession()
                    .set(USER_SESSION_LOGIN_ID, user.getId().toString())
                    .set(USER_SESSION_OPENID, user.getOpenid())
                    .set(USER_SESSION_INFO, user)
                    .set(USER_SESSION_EXPIRATION, System.currentTimeMillis() + StpUtil.getTokenTimeout() * 1000);

            log.info("用户登录成功, ID: {}, OpenID: {}", user.getId(), user.getOpenid());

        } catch (Exception e) {
            log.error("用户登录失败, ID: {}, 错误信息: {}", user.getId(), e.getMessage(), e);
            throw new BusinessException("登录系统异常，请稍后再试");
        }
    }


}
