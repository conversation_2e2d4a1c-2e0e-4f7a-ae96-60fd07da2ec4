package com.demon.giraffe.modules.user.config;

import com.demon.giraffe.modules.user.service.base.BaseRoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;

import java.util.Map;

/**
 * 角色管理配置类
 * 负责角色服务的自动发现和注册
 */
@Slf4j
@Configuration
public class RoleManagementConfig {
    
    /**
     * 在Spring容器启动完成后，自动发现并注册所有角色服务
     */
    @EventListener(ContextRefreshedEvent.class)
    public void onApplicationEvent(ContextRefreshedEvent event) {
        Map<String, BaseRoleService> roleServices = event.getApplicationContext()
                .getBeansOfType(BaseRoleService.class);
        
        log.info("发现 {} 个角色服务", roleServices.size());
        
        for (Map.Entry<String, BaseRoleService> entry : roleServices.entrySet()) {
            BaseRoleService<?, ?, ?, ?> service = entry.getValue();
            log.info("角色服务：{} -> {} ({})", 
                    service.getRoleType().getName(), 
                    service.getRoleType().getCode(),
                    entry.getKey());
        }
        
        log.info("角色服务注册完成");
    }
}
