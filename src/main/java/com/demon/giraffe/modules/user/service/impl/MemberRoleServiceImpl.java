package com.demon.giraffe.modules.user.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.common.util.CodeGeneratorUtil;
import com.demon.giraffe.modules.user.model.dto.query.MemberIdentityQueryRequest;
import com.demon.giraffe.modules.user.model.dto.request.MemberIdentityCreateRequest;
import com.demon.giraffe.modules.user.model.dto.response.MemberIdentityResponse;
import com.demon.giraffe.modules.user.model.enums.MemberLevelEnum;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import com.demon.giraffe.modules.user.model.enums.UserStatus;
import com.demon.giraffe.modules.user.model.po.MemberIdentityPo;
import com.demon.giraffe.modules.user.repository.MemberIdentityRepository;
import com.demon.giraffe.modules.user.service.base.AbstractRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;

/**
 * 会员角色服务实现
 * 基于统一角色管理架构，专门处理会员角色相关操作
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MemberRoleServiceImpl extends AbstractRoleService<
        MemberIdentityPo,
        MemberIdentityCreateRequest,
        MemberIdentityResponse,
        MemberIdentityQueryRequest> {

    private final MemberIdentityRepository memberRepository;
    private final CodeGeneratorUtil codeGeneratorUtil;

    // ========== 统一角色管理架构实现 ==========
    
    @Override
    public UserRole getRoleType() {
        return UserRole.CUSTOMER;
    }
    
    @Override
    protected Long doCreateRole(MemberIdentityCreateRequest request) {
        MemberIdentityPo po = MemberIdentityPo.builder()
                .appUserId(request.getAppUserId())
                .memberNo(codeGeneratorUtil.generateMemberCode(request.getAppUserId()))
                .level(MemberLevelEnum.NORMAL)
                .points(0)
                .totalPoints(0)
                .totalConsumed(BigDecimal.ZERO)
                .phone(request.getPhone())
                .realName(request.getRealName())
                .status(UserStatus.NORMAL)
                .build();
        
        memberRepository.save(po);
        return po.getId();
    }
    
    @Override
    protected Boolean doDeleteRole(Long userId) {
        return memberRepository.removeByAppUserId(userId);
    }
    
    @Override
    protected Long extractUserIdFromRequest(MemberIdentityCreateRequest request) {
        return request.getAppUserId();
    }
    
    @Override
    protected void validateCreateRequest(MemberIdentityCreateRequest request) {
        Assert.notNull(request.getAppUserId(), "用户ID不能为空");
        Assert.hasText(request.getPhone(), "手机号不能为空");
        Assert.isTrue(request.getPhone().length() == 11, "手机号长度必须为11位");
    }
    
    @Override
    protected void doRoleSpecificLogin(Long userId, MemberIdentityResponse roleInfo) {
        log.info("会员[{}]登录，等级：{}", roleInfo.getMemberNo(), roleInfo.getLevel());
        // 可以在这里记录登录积分、更新最后登录时间等
    }
    
    @Override
    protected void doAfterRoleCreated(Long roleId, MemberIdentityCreateRequest request) {
        log.info("会员身份创建成功，ID：{}，手机号：{}", roleId, request.getPhone());
        // 可以在这里发送欢迎消息、赠送新人积分等
    }
    
    // ========== 统一角色管理架构方法重写 ==========
    
    @Override
    public Boolean updateRole(MemberIdentityCreateRequest request) {
        MemberIdentityPo po = new MemberIdentityPo();
        BeanUtils.copyProperties(request, po);
        return memberRepository.updateById(po);
    }
    
    @Override
    public MemberIdentityResponse getRoleDetail(Long id) {
        MemberIdentityPo po = memberRepository.getById(id);
        Assert.notNull(po, "会员不存在");

        MemberIdentityResponse response = new MemberIdentityResponse();
        BeanUtils.copyProperties(po, response);
        return response;
    }
    
    @Override
    public MemberIdentityResponse getRoleByUserId(Long userId) {
        MemberIdentityPo po = memberRepository.getByAppUserId(userId);
        if (po == null) {
            return null;
        }
        
        MemberIdentityResponse response = new MemberIdentityResponse();
        BeanUtils.copyProperties(po, response);
        return response;
    }
    
    @Override
    public IPage<MemberIdentityResponse> queryRolePage(BasePageQuery<MemberIdentityQueryRequest> query) {
        query.init();
        Page<MemberIdentityPo> page = new Page<>(query.getPage(), query.getPerPage());
        // 这里需要实现具体的查询逻辑，暂时返回空页面
        IPage<MemberIdentityPo> poPage = new Page<>(query.getPage(), query.getPerPage());
        
        return poPage.convert(po -> {
            MemberIdentityResponse response = new MemberIdentityResponse();
            BeanUtils.copyProperties(po, response);
            return response;
        });
    }
    
    @Override
    public Boolean saveRole(MemberIdentityPo entity) {
        memberRepository.save(entity);
        return true;
    }
    
    @Override
    public Boolean existsById(Long id) {
        return id != null && memberRepository.getById(id) != null;
    }
    

    

    
    @Override
    public Boolean changeStatus(Long userId, Object newStatus) {
        if (newStatus instanceof UserStatus) {
            MemberIdentityResponse member = getRoleByUserId(userId);
            if (member != null) {
                return memberRepository.updateStatus(member.getId(), ((UserStatus) newStatus).ordinal());
            }
        }
        return false;
    }
    
    // ========== 业务特定方法 ==========
    
    /**
     * 增加积分
     */
    public Boolean addPoints(Long memberId, Integer points) {
        // 这里需要实现具体的积分增加逻辑
        // 暂时返回true，实际应该调用repository的方法
        log.info("为会员[{}]增加积分：{}", memberId, points);
        return true;
    }
    
    /**
     * 消费积分
     */
    public Boolean consumePoints(Long memberId, Integer points) {
        // 这里需要实现具体的积分消费逻辑
        log.info("会员[{}]消费积分：{}", memberId, points);
        return true;
    }
    
    /**
     * 升级会员等级
     */
    public Boolean upgradeLevel(Long memberId, MemberLevelEnum newLevel) {
        // 这里需要实现具体的等级升级逻辑
        log.info("会员[{}]升级到等级：{}", memberId, newLevel);
        return true;
    }
    
    /**
     * 增加消费金额
     */
    public Boolean addConsumption(Long memberId, BigDecimal amount) {
        // 这里需要实现具体的消费金额增加逻辑
        log.info("会员[{}]增加消费金额：{}", memberId, amount);
        return true;
    }
    
    /**
     * 根据消费金额自动升级会员等级
     */
    public Boolean autoUpgradeLevel(Long memberId) {
        MemberIdentityPo member = memberRepository.getById(memberId);
        if (member == null) {
            return false;
        }
        
        BigDecimal totalConsumed = member.getTotalConsumed();
        MemberLevelEnum currentLevel = member.getLevel();
        MemberLevelEnum newLevel = calculateLevelByConsumption(totalConsumed);
        
        if (newLevel.ordinal() > currentLevel.ordinal()) {
            return upgradeLevel(memberId, newLevel);
        }
        
        return true;
    }
    
    private MemberLevelEnum calculateLevelByConsumption(BigDecimal totalConsumed) {
        if (totalConsumed.compareTo(new BigDecimal("10000")) >= 0) {
            return MemberLevelEnum.DIAMOND;
        } else if (totalConsumed.compareTo(new BigDecimal("5000")) >= 0) {
            return MemberLevelEnum.GOLD;
        } else if (totalConsumed.compareTo(new BigDecimal("1000")) >= 0) {
            return MemberLevelEnum.SILVER;
        } else {
            return MemberLevelEnum.NORMAL;
        }
    }
}
