package com.demon.giraffe.modules.user.controller;

import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.qr.model.dto.request.QrCodeGenerateRequest;
import com.demon.giraffe.modules.qr.model.dto.response.QrCodeGenerateResponse;
import com.demon.giraffe.modules.qr.model.enums.QRBusinessType;
import com.demon.giraffe.modules.qr.service.QrCodeService;
import com.demon.giraffe.modules.qr.service.QrService;
import com.demon.giraffe.modules.user.model.dto.request.WechatLoginRequest;
import com.demon.giraffe.modules.user.model.dto.response.EnhancedLoginResponse;
import com.demon.giraffe.modules.user.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 统一微信登录控制器
 * 合并所有微信登录相关功能，包括基础登录、角色管理、二维码登录等
 */
@Slf4j
@RestController
@RequestMapping("/user/auth")
@RequiredArgsConstructor
@Tag(name = "统一微信登录", description = "集成所有微信登录功能的统一API")
public class UnifiedWechatController {

    private final IWechatService enhancedWechatService;
    private final UserService userService;
    private final QrCodeService qrCodeService;
    private final QrService qrService;


    @PostMapping("/wechat/login")
    @Operation(summary = "微信登录", description = "包含角色信息的微信登录")
    public ResultBean<EnhancedLoginResponse> enhancedLogin(@Valid @RequestBody WechatLoginRequest request) {
        try {
            EnhancedLoginResponse response = enhancedWechatService.enhancedLogin(request);
            response.setCurrentLoginTime();
            return ResultBean.success("登录成功", response);
        } catch (Exception e) {
            log.error("增强微信登录失败", e);
            return ResultBean.fail("登录失败：" + e.getMessage());
        }
    }


    @GetMapping("/simple/login/{openid}")
    @Operation(summary = "增强简单登录", description = "通过OpenID简单登录，包含角色信息")
    public ResultBean<EnhancedLoginResponse> enhancedSimpleLogin(
            @Parameter(description = "微信OpenID", required = true)
            @PathVariable String openid) {
        try {
            EnhancedLoginResponse response = enhancedWechatService.enhancedSimpleLogin(openid);
            response.setCurrentLoginTime();
            return ResultBean.success("登录成功", response);
        } catch (Exception e) {
            log.error("增强简单登录失败，OpenID：{}", openid, e);
            return ResultBean.fail("登录失败：" + e.getMessage());
        }
    }


    @PostMapping("/logout")
    @Operation(summary = "退出登录", description = "注销当前用户的登录状态")
    public ResultBean<Boolean> logout() {
        try {
            Boolean result = userService.logout();
            return ResultBean.success("退出登录成功", result);
        } catch (Exception e) {
            log.error("退出登录失败", e);
            return ResultBean.fail("退出登录失败：" + e.getMessage());
        }
    }

    // ================= 二维码登录功能 =================

    @GetMapping("/qrCode")
    @Operation(summary = "获取登录二维码", 
               description = "生成用于扫码登录的二维码，有效期5分钟")
    public ResultBean<QrCodeGenerateResponse> generateQrCode() {
        try {
            QrCodeGenerateResponse response = qrCodeService.generateQrCode(
                QrCodeGenerateRequest.builder()
                    .businessType(QRBusinessType.LOGIN)
                    .build()
            );
            return ResultBean.success("二维码生成成功", response);
        } catch (Exception e) {
            log.error("生成二维码失败", e);
            return ResultBean.fail("生成二维码失败：" + e.getMessage());
        }
    }
    @GetMapping("/poll/{businessId}")
    @Operation(summary = "轮询登录状态", 
               description = "轮询二维码登录状态，返回登录结果")
    public ResultBean<EnhancedLoginResponse> pollLoginStatus(
            @Parameter(name = "businessId", description = "二维码对应的业务ID", required = true, in = ParameterIn.PATH)
            @PathVariable Long businessId) {
        try {
            EnhancedLoginResponse response = qrService.pollLoginStatusByBusinessId(businessId);
            return ResultBean.success("轮询成功", response);
        } catch (Exception e) {
            log.error("轮询登录状态失败，业务ID：{}", businessId, e);
            return ResultBean.fail("轮询失败：" + e.getMessage());
        }
    }
}
