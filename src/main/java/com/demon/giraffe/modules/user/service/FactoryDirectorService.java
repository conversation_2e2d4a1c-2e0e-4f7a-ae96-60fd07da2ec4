package com.demon.giraffe.modules.user.service;

import com.demon.giraffe.modules.user.model.dto.query.FactoryDirectorQueryRequest;
import com.demon.giraffe.modules.user.model.dto.request.FactoryDirectorRequest;
import com.demon.giraffe.modules.user.model.dto.response.FactoryDirectorResponse;
import com.demon.giraffe.modules.user.model.enums.DirectorStatusEnum;
import com.demon.giraffe.modules.user.model.po.FactoryDirectorPo;
import com.demon.giraffe.modules.user.service.base.BaseRoleService;

/**
 * 工厂管理员服务接口
 * 继承基础角色服务，获得通用角色管理能力
 */
public interface FactoryDirectorService extends BaseRoleService<FactoryDirectorPo, FactoryDirectorRequest, FactoryDirectorResponse, FactoryDirectorQueryRequest> {

    /**
     * 变更管理员状态
     * @param id 管理员ID
     * @param status 新状态
     * @return 是否变更成功
     */
    Boolean changeDirectorStatus(Long id, DirectorStatusEnum status);

    /**
     * 检查工厂是否存在
     * @param factoryId 工厂ID
     * @return 是否存在
     */
    boolean existsFactoryById(Long factoryId);
}