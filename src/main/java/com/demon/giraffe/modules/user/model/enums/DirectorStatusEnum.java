package com.demon.giraffe.modules.user.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "厂长状态枚举")
public enum DirectorStatusEnum implements IEnum<Integer> {
    IN_OFFICE(0, "在任"),
    LEFT(1, "离任"),
    ON_LEAVE(2, "休假");
    @EnumValue
    private final Integer code;
    private final String desc;

    DirectorStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getValue() {
        return code;
    }
}