package com.demon.giraffe.modules.user.service.base;

import com.demon.giraffe.framework.satoken.util.SaTokenUtil;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import com.demon.giraffe.modules.user.model.po.UserPo;
import com.demon.giraffe.modules.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;

/**
 * 角色服务抽象基类
 * 提供通用的角色管理功能实现
 * 
 * @param <T> 角色实体类型
 * @param <R> 角色请求类型  
 * @param <S> 角色响应类型
 * @param <Q> 角色查询类型
 */
@Slf4j
public abstract class AbstractRoleService<T, R, S, Q extends Serializable> implements BaseRoleService<T, R, S, Q> {
    
    @Autowired
    protected UserService userService;
    
    /**
     * 统一登录处理
     * 1. 验证用户角色
     * 2. 获取角色信息
     * 3. 执行角色特定的登录逻辑
     */
    @Override
    public S login(Long userId) {
        log.info("用户[{}]尝试以[{}]角色登录", userId, getRoleType().getName());
        
        // 1. 验证用户存在且角色匹配
        UserPo user = userService.getUserInfo(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        if (!getRoleType().equals(user.getRole())) {
            throw new RuntimeException("用户角色不匹配，当前角色：" + user.getRole().getName() + 
                                     "，期望角色：" + getRoleType().getName());
        }
        
        // 2. 获取角色信息
        S roleInfo = getRoleByUserId(userId);
        if (roleInfo == null) {
            throw new RuntimeException("角色信息不存在");
        }
        
        // 3. 执行角色特定的登录逻辑
        doRoleSpecificLogin(userId, roleInfo);
        
        log.info("用户[{}]以[{}]角色登录成功", userId, getRoleType().getName());
        return roleInfo;
    }
    
    /**
     * 统一注销处理
     */
    @Override
    public Boolean logout(Long userId) {
        log.info("用户[{}]注销[{}]角色", userId, getRoleType().getName());
        
        try {
            // 执行角色特定的注销逻辑
            doRoleSpecificLogout(userId);
            
            // 清除token
            SaTokenUtil.invalidateTokenByUserId(userId);
            
            log.info("用户[{}][{}]角色注销成功", userId, getRoleType().getName());
            return true;
        } catch (Exception e) {
            log.error("用户[{}][{}]角色注销失败", userId, getRoleType().getName(), e);
            return false;
        }
    }
    
    /**
     * 统一创建角色
     */
    @Override
    public Long createRole(R request) {
        log.info("创建[{}]角色", getRoleType().getName());
        
        // 1. 参数验证
        validateCreateRequest(request);
        
        // 2. 业务逻辑验证
        validateBusinessRules(request);
        
        // 3. 创建角色
        Long roleId = doCreateRole(request);
        
        // 4. 更新用户角色
        Long userId = extractUserIdFromRequest(request);
        userService.updateUserRoles(userId, getRoleType());
        
        // 5. 执行角色特定的创建后处理
        doAfterRoleCreated(roleId, request);
        
        log.info("创建[{}]角色成功，角色ID：{}", getRoleType().getName(), roleId);
        return roleId;
    }
    
    /**
     * 统一删除角色
     */
    @Override
    public Boolean deleteByUserId(Long userId) {
        log.info("删除用户[{}]的[{}]角色", userId, getRoleType().getName());
        
        try {
            // 1. 执行角色特定的删除前处理
            doBeforeRoleDeleted(userId);
            
            // 2. 删除角色数据
            Boolean result = doDeleteRole(userId);
            
            // 3. 执行角色特定的删除后处理
            if (result) {
                doAfterRoleDeleted(userId);
            }
            
            log.info("删除用户[{}]的[{}]角色成功", userId, getRoleType().getName());
            return result;
        } catch (Exception e) {
            log.error("删除用户[{}]的[{}]角色失败", userId, getRoleType().getName(), e);
            return false;
        }
    }
    
    // ========== 抽象方法，子类必须实现 ==========
    
    /**
     * 执行具体的角色创建逻辑
     */
    protected abstract Long doCreateRole(R request);
    
    /**
     * 执行具体的角色删除逻辑
     */
    protected abstract Boolean doDeleteRole(Long userId);
    
    /**
     * 从请求中提取用户ID
     */
    protected abstract Long extractUserIdFromRequest(R request);
    
    // ========== 可选重写的方法 ==========
    
    /**
     * 验证创建请求参数
     */
    protected void validateCreateRequest(R request) {
        // 默认不做验证，子类可重写
    }
    
    /**
     * 验证业务规则
     */
    protected void validateBusinessRules(R request) {
        // 默认不做验证，子类可重写
    }
    
    /**
     * 角色特定的登录逻辑
     */
    protected void doRoleSpecificLogin(Long userId, S roleInfo) {
        // 默认不做处理，子类可重写
    }
    
    /**
     * 角色特定的注销逻辑
     */
    protected void doRoleSpecificLogout(Long userId) {
        // 默认不做处理，子类可重写
    }
    
    /**
     * 角色创建后的处理
     */
    protected void doAfterRoleCreated(Long roleId, R request) {
        // 默认不做处理，子类可重写
    }
    
    /**
     * 角色删除前的处理
     */
    protected void doBeforeRoleDeleted(Long userId) {
        // 默认不做处理，子类可重写
    }
    
    /**
     * 角色删除后的处理
     */
    protected void doAfterRoleDeleted(Long userId) {
        // 默认不做处理，子类可重写
    }
}
