
package com.demon.giraffe.modules.user.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.modules.user.model.dto.request.FactoryWorkerQueryRequest;
import com.demon.giraffe.modules.user.model.po.FactoryWorkerPo;
import com.demon.giraffe.modules.user.model.enums.FactoryPositionTypeEnum;

public interface FactoryWorkerRepository {

    /**
     * 保存员工信息
     */
    boolean save(FactoryWorkerPo po);

    /**
     * 根据ID更新员工信息
     */
    boolean updateById(FactoryWorkerPo po);

    /**
     * 根据ID查询员工信息
     */
    FactoryWorkerPo getById(Long id);

    /**
     * 分页查询员工信息
     */
    IPage<FactoryWorkerPo> queryPage(Page<FactoryWorkerPo> page, FactoryWorkerQueryRequest query);

    /**
     * 更新员工职位
     */
    boolean updatePosition(Long id, FactoryPositionTypeEnum positionType);

    /**
     * 根据员工编号查询
     */
    FactoryWorkerPo getByWorkerNo(String workerNo);

    /**
     * 根据用户ID查询员工信息
     */
    FactoryWorkerPo getByAppUserId(Long appUserId);

    boolean deleteByUserId(Long userId);
}