package com.demon.giraffe.modules.user.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.modules.user.model.dto.query.UserQueryRequest;
import com.demon.giraffe.modules.user.model.po.UserPo;

import java.util.List;

/**
 * 用户持久化接口
 * 定义所有对用户数据的操作接口
 */
public interface UserRepository {

    /**
     * 根据用户ID获取用户信息
     *
     * @param userId 用户ID
     * @return 用户实体
     */
    UserPo getById(Long userId);

    /**
     * 根据用户ID列表批量获取用户信息
     *
     * @param userIds 用户ID列表
     * @return 用户实体列表
     */
    List<UserPo> listByIds(List<Long> userIds);


    /**
     * 根据openid获取用户
     *
     * @param openid 微信openid
     * @return 用户实体
     */
    UserPo getByOpenid(String openid);


    /**
     * 保存用户信息
     *
     * @param userPo 用户实体
     * @return 是否保存成功
     */
    UserPo save(UserPo userPo);

    /**
     * 更新用户信息
     *
     * @param userPo 用户实体
     * @return 是否更新成功
     */
    boolean updateById(UserPo userPo);


    /**
     * 根据条件查询用户列表
     *
     * @param pageQuery 查询条件
     * @return 用户列表
     */
    IPage<UserPo> pageByQuery(BasePageQuery<UserQueryRequest> pageQuery);


    /**
     * 根据用户ID删除用户（逻辑删除）
     *
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean deleteById(Long userId);
}