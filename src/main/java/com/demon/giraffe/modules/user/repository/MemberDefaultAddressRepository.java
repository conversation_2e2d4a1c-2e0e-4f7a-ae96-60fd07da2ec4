package com.demon.giraffe.modules.user.repository;

import java.util.Optional;

/**
 * 会员默认地址存储接口
 * <p>
 * 提供会员默认地址的CRUD操作，包括设置、查询和删除默认地址
 * </p>
 */
public interface MemberDefaultAddressRepository {

    /**
     * 保存或更新会员的默认地址
     *
     * @param memberId 会员ID
     * @param addressId 地址ID
     */
    void saveOrUpdateDefaultAddress(Long memberId, Long addressId);

    /**
     * 获取会员的默认地址ID
     *
     * @param memberId 会员ID
     * @return 地址ID，如果不存在返回Optional.empty()
     */
    Optional<Long> getDefaultAddressId(Long memberId);

    /**
     * 删除会员的默认地址记录
     *
     * @param memberId 会员ID
     */
    void removeDefaultAddress(Long memberId);


    /**
     * 检查指定地址是否为会员的默认地址
     *
     * @param memberId 会员ID
     * @param addressId 地址ID
     * @return 是否为该会员的默认地址
     */
    boolean existsByMemberIdAndAddressId(Long memberId, Long addressId);
}