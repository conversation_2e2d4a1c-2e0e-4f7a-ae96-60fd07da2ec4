package com.demon.giraffe.modules.user.model.dto.response;

import com.demon.giraffe.modules.user.model.enums.DirectorStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "FactoryDirectorResponse", description = "厂长响应信息")
public class FactoryDirectorResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "厂长ID")
    private Long id;

    @Schema(description = "关联用户ID")
    private Long appUserId;

    @Schema(description = "厂长编号")
    private String directorNo;

    @Schema(description = "工厂ID")
    private Long factoryId;


    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}