package com.demon.giraffe.modules.user.model.dto.response;

import com.demon.giraffe.common.domain.dto.response.RegionResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "MemberAddressDetailResponse", description = "地址详情响应")
public class MemberAddressDetailResponse {

    @Schema(description = "地址ID")
    private Long id;

    @Schema(description = "地区响应")
    private RegionResponse region;

    @Schema(description = "详细地址")
    private String detailAddress;
    
    @Schema(description = "联系人姓名")
    private String contactName;
    
    @Schema(description = "联系电话")
    private String contactPhone;

    
    @Schema(description = "是否默认地址")
    private Boolean isDefault;
}