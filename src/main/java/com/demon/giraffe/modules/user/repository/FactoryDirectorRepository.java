package com.demon.giraffe.modules.user.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.modules.user.model.dto.query.FactoryDirectorQueryRequest;
import com.demon.giraffe.modules.user.model.enums.DirectorStatusEnum;
import com.demon.giraffe.modules.user.model.po.FactoryDirectorPo;

public interface FactoryDirectorRepository {

    boolean save(FactoryDirectorPo po);

    boolean updateById(FactoryDirectorPo po);

    FactoryDirectorPo getById(Long id);

    /**
     * 分页查询厂长信息
     * @param page 分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<FactoryDirectorPo> queryPage(Page<FactoryDirectorPo> page, FactoryDirectorQueryRequest query);

    boolean updateStatus(Long id, DirectorStatusEnum status);

    boolean deleteByUserId(Long userId);


    FactoryDirectorPo getByAppUserId( Long appUserId);
}