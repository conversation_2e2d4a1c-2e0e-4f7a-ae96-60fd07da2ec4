package com.demon.giraffe.modules.user.model.dto.query;

import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;

/**
 * 用户分页查询条件
 */
@Data
@EqualsAndHashCode(callSuper = false) // 注意修改为false
@Schema(
        name = "UserQueryRequest",
        description = "用户分页查询条件"
)
public class UserQueryRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(
            description = "用户角色类型",
            example = "ADMIN",
            allowableValues = {"ADMIN", "USER", "GUEST"}
    )
    private UserRole role;

    @Schema(
            description = "搜索关键词(支持用户ID)",
            example = "U202406000001"
    )
    private String keyword;


    @Schema(
            description = "注册时间范围-开始(yyyy-MM-dd HH:mm:ss)",
            example = "2024-01-01 00:00:00"
    )
    private String registerTimeStart;

    @Schema(
            description = "注册时间范围-结束(yyyy-MM-dd HH:mm:ss)",
            example = "2024-12-31 23:59:59"
    )
    private String registerTimeEnd;
}