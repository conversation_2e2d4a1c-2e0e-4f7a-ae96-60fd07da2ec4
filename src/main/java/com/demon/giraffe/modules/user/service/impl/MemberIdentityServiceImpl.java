package com.demon.giraffe.modules.user.service.impl;

import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.framework.satoken.util.SaTokenUtil;
import com.demon.giraffe.modules.user.model.dto.response.*;
import com.demon.giraffe.modules.user.model.po.MemberIdentityPo;
import com.demon.giraffe.modules.user.model.po.UserPo;
import com.demon.giraffe.modules.user.repository.MemberIdentityRepository;
import com.demon.giraffe.modules.user.repository.UserRepository;
import com.demon.giraffe.modules.user.service.MemberIdentityService;
import com.demon.giraffe.modules.user.service.helper.MemberIdentityConvertHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MemberIdentityServiceImpl implements MemberIdentityService {

    private final MemberIdentityRepository identityRepository;
    private final MemberIdentityConvertHelper memberAddressConvertHelper;
    private final UserRepository userRepository;

    @Override
    public MemberIdentityPo createMemberIdentity(UserPo userPo) {
        return identityRepository.save(memberAddressConvertHelper.initializeMember(userPo));
    }

    @Override
    public MemberIdentityDetailResponse getMemberIdentityDetail(Long userId) {
        MemberIdentityPo po = identityRepository.getByAppUserId(userId);
        if(Objects.isNull(po)){
            throw new BusinessException("会员身份不存在");
        }
        return memberAddressConvertHelper.convertToDetailResponse(po);
    }

    @Override
    @Transactional
    public Boolean deleteMyMemberIdentity() {
        UserPo userPo = SaTokenUtil.getUserPo();
        Long id = userPo.getId();
        identityRepository.removeByAppUserId(id);
        userRepository.deleteById(id);
        return true;
    }

    @Override
    public MemberIdentityDetailResponse getMyMemberIdentityDetail() {
        UserPo userPo = SaTokenUtil.getUserPo();
        return this.getMemberIdentityDetail(userPo.getId());
    }

    @Override
    public MemberIdentityPo getByUserId(Long userId) {
        return identityRepository.getByAppUserId(userId);
    }

    @Override
    public MemberIdentityPo getById(Long userId) {
        return identityRepository.getById(userId);
    }

    @Override
    public Map<Long, MemberIdentityDetailResponse> getMemberIdentitiesByUserIds(Set<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 批量查询会员身份信息
        List<MemberIdentityPo> memberIdentities = identityRepository.listByAppUserIds(userIds);

        // 转换为Map，key为用户ID，value为会员详细信息
        return memberIdentities.stream()
                .collect(Collectors.toMap(
                        MemberIdentityPo::getAppUserId,
                        memberAddressConvertHelper::convertToDetailResponse
                ));
    }

}