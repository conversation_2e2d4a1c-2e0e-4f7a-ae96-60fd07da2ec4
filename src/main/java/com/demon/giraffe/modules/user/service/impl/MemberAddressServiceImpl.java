package com.demon.giraffe.modules.user.service.impl;

import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.framework.satoken.util.SaTokenUtil;
import com.demon.giraffe.modules.cabinet.model.entity.SmartCabinet;
import com.demon.giraffe.modules.cabinet.service.SmartCabinetService;
import com.demon.giraffe.modules.common.model.entity.DistanceWrapper;
import com.demon.giraffe.modules.common.service.LocationService;
import com.demon.giraffe.modules.laundry.service.FactoryInfoService;
import com.demon.giraffe.modules.user.model.dto.request.*;
import com.demon.giraffe.modules.user.model.dto.response.*;
import com.demon.giraffe.modules.user.model.entity.MemberAddress;
import com.demon.giraffe.modules.user.model.po.*;
import com.demon.giraffe.modules.user.repository.*;
import com.demon.giraffe.modules.user.service.MemberAddressService;
import com.demon.giraffe.modules.user.service.RegionInvestorService;
import com.demon.giraffe.modules.user.service.helper.MemberAddressConvertHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员地址服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MemberAddressServiceImpl implements MemberAddressService {

    private final MemberAddressRepository addressRepository;
    private final MemberAddressConvertHelper convertHelper;
    private final MemberDefaultAddressRepository defaultAddressRepository;
    private final MemberIdentityRepository identityRepository;
    private final SmartCabinetService smartCabinetService;
    private final LocationService locationService;
    private final FactoryInfoService factoryInfoService;
    private final RegionInvestorService regionInvestorService;

    /**
     * 创建会员地址
     *
     * @param request 地址创建请求
     * @return 地址详情响应
     * @throws BusinessException 会员不存在时抛出异常
     */
    @Override
    @Transactional
    public MemberAddressDetailResponse createAddress(MemberAddressCreateRequest request) {
        // 获取当前用户
        UserPo userPo = SaTokenUtil.getUserPo();
        MemberIdentityPo memberIdentityPo = identityRepository.getByAppUserId(userPo.getId());
        if (Objects.isNull(memberIdentityPo)) {
            throw new BusinessException("未创建会员，请联系管理员");
        }

        // 转换并保存地址
        MemberAddressPo memberAddressPo = convertHelper.toPo(request, memberIdentityPo.getId());
        MemberAddressPo savedAddress = addressRepository.save(memberAddressPo);

        // 处理默认地址
        if (Boolean.TRUE.equals(request.getIsDefault())) {
            defaultAddressRepository.saveOrUpdateDefaultAddress(memberIdentityPo.getId(), savedAddress.getId());
        }

        return convertHelper.toResponse(savedAddress);
    }

    /**
     * 更新会员地址
     *
     * @param request 地址更新请求
     * @return 是否更新成功
     * @throws BusinessException 会员不存在或地址不存在时抛出异常
     */
    @Override
    @Transactional
    public Boolean updateAddress(MemberAddressUpdateRequest request) {
        // 获取当前用户
        UserPo userPo = SaTokenUtil.getUserPo();
        MemberIdentityPo memberIdentityPo = identityRepository.getByAppUserId(userPo.getId());
        if (Objects.isNull(memberIdentityPo)) {
            throw new BusinessException("未创建会员，请联系管理员");
        }

        // 检查地址是否存在且属于当前用户
        MemberAddressPo existingAddress = addressRepository.findByIdAndMemberId(request.getId(), memberIdentityPo.getId());
        if (Objects.isNull(existingAddress)) {
            throw new BusinessException("地址不存在或无权操作");
        }

        // 更新地址信息
        MemberAddressPo updatedPo = convertHelper.toPo(request, existingAddress);
        addressRepository.updateById(updatedPo);

        // 处理默认地址
        if (Boolean.TRUE.equals(request.getIsDefault())) {
            defaultAddressRepository.saveOrUpdateDefaultAddress(memberIdentityPo.getId(), request.getId());
        }

        return true;
    }

    /**
     * 删除会员地址
     *
     * @param addressId 地址ID
     * @return 是否删除成功
     * @throws BusinessException 会员不存在或地址不存在时抛出异常
     */
    @Override
    @Transactional
    public Boolean deleteAddress(Long addressId) {
        // 获取当前用户
        UserPo userPo = SaTokenUtil.getUserPo();
        MemberIdentityPo memberIdentityPo = identityRepository.getByAppUserId(userPo.getId());
        if (Objects.isNull(memberIdentityPo)) {
            throw new BusinessException("未创建会员，请联系管理员");
        }

        // 检查地址是否存在且属于当前用户
        MemberAddressPo existingAddress = addressRepository.findByIdAndMemberId(addressId, memberIdentityPo.getId());
        if (Objects.isNull(existingAddress)) {
            throw new BusinessException("地址不存在或无权操作");
        }

        // 如果是默认地址，先删除默认地址记录
        Optional<Long> optionalDefaultAddressId = defaultAddressRepository.getDefaultAddressId(memberIdentityPo.getId());
        if (optionalDefaultAddressId.isPresent() && addressId.equals(optionalDefaultAddressId.get())) {
            defaultAddressRepository.removeDefaultAddress(memberIdentityPo.getId());
        }

        // 删除地址
        return addressRepository.removeById(addressId);
    }

    /**
     * 设置默认地址
     *
     * @param addressId 地址ID
     * @return 是否设置成功
     * @throws BusinessException 会员不存在或地址不存在时抛出异常
     */
    @Override
    @Transactional
    public Boolean setDefaultAddress(Long addressId) {
        // 获取当前用户
        UserPo userPo = SaTokenUtil.getUserPo();
        MemberIdentityPo memberIdentityPo = identityRepository.getByAppUserId(userPo.getId());
        if (Objects.isNull(memberIdentityPo)) {
            throw new BusinessException("未创建会员，请联系管理员");
        }

        // 检查地址是否存在且属于当前用户
        if (!addressRepository.existsByIdAndMemberId(addressId, memberIdentityPo.getId())) {
            throw new BusinessException("地址不存在或无权操作");
        }

        // 设置默认地址
        defaultAddressRepository.saveOrUpdateDefaultAddress(memberIdentityPo.getId(), addressId);
        return true;
    }

    /**
     * 获取当前会员所有地址
     *
     * @return 地址列表
     * @throws BusinessException 会员不存在时抛出异常
     */
    @Override
    public List<MemberAddressDetailResponse> listAllAddresses() {
        UserPo userPo = SaTokenUtil.getUserPo();
        MemberIdentityPo memberIdentityPo = identityRepository.getByAppUserId(userPo.getId());
        if (Objects.isNull(memberIdentityPo)) {
            throw new BusinessException("未创建会员，请联系管理员");
        }

        List<MemberAddressPo> addressList = addressRepository.findAllByMemberId(memberIdentityPo.getId());
        return addressList.stream()
                .map(convertHelper::toResponse)
                .collect(Collectors.toList());
    }

    /**
     * 根据会员ID获取地址列表
     *
     * @param memberId 会员ID
     * @return 地址列表
     * @throws BusinessException 会员不存在时抛出异常
     */
    @Override
    public List<MemberAddressDetailResponse> listAddressesByMemberId(Long memberId) {

        List<MemberAddressPo> addressList = addressRepository.findAllByMemberId(memberId);
        return addressList.stream()
                .map(convertHelper::toResponse)
                .collect(Collectors.toList());
    }


    /**
     * 根据地址ID匹配最近的可用柜子
     *
     * @param addressId 地址ID
     * @return 最近的柜子信息
     * @throws BusinessException 地址不存在或没有可用柜子时抛出异常
     */
    @Override
    public SmartCabinet findNearestCabinetByAddress(Long addressId) {
        // 1. 获取地址信息
        MemberAddressPo address = addressRepository.getById(addressId);
        if (address == null) {
            throw new BusinessException("地址不存在");
        }

        // 2. 验证地址有经纬度信息
        if (address.getLongitude() == null || address.getLatitude() == null) {
            throw new BusinessException("该地址缺少经纬度信息，无法计算距离");
        }

        CountyEnum addressCode = address.getAddressCode();
        // 4. 获取该区域所有可用柜子
        List<SmartCabinet> cabinets = smartCabinetService.getAvailableCabinetsByRegion(addressCode);
        if (cabinets.isEmpty()) {
            log.error("该区域暂无可用柜子");
            return null;
        }

        // 5. 计算距离并排序
        List<DistanceWrapper<SmartCabinet>> distanceWrappers = locationService.calculateDistancesAndFilterGeneric(
                address.getLongitude(),
                address.getLatitude(),
                cabinets,
                SmartCabinet::getLongitude,
                SmartCabinet::getLatitude,
                null // 不限制最大距离
        );

        // 6. 返回最近的柜子
        return distanceWrappers.get(0).getPoint();
    }


    /**
     * 根据会员ID获取所有地址及预计取送时间
     *
     * @param memberId 会员ID
     * @return 包含预计取送时间的地址列表
     */
    @Override
    public List<MemberAddress> listAddressesWithDeliveryTime(Long memberId) {
        // 1. 获取会员所有地址
        List<MemberAddressPo> addressPos = addressRepository.findAllByMemberId(memberId);
        //fixme 优化这里  一次查出所有的id
        // 这样可以批量查询 地区对应的柜子，方便处理

        // 2. 转换为实体对象
        return addressPos.stream().map(po -> {
            MemberAddress address = convertHelper.convertPoToFullAddressEntity(po);

            // 3. 为每个地址计算预计取送时间
            calculateDeliveryTimeForAddress(address);

            return address;
        }).collect(Collectors.toList());
    }

    /**
     * 为单个地址计算预计取送时间
     *
     * @param address 地址实体
     */
    private void calculateDeliveryTimeForAddress(MemberAddress address) {
        try {
            // 1. 检查地址是否有经纬度
            if (address.getLongitude() == null || address.getLatitude() == null) {
                address.setEstimatedDeliveryTime(null);
                return;
            }

            // 2. 获取区域内可用柜子
            List<SmartCabinet> cabinets = smartCabinetService.getAvailableCabinetsByRegion(address.getCountyEnum());
            if (cabinets.isEmpty()) {
                address.setEstimatedDeliveryTime(null);
                return;
            }

            // 3. 找到最近的柜子
            SmartCabinet nearestCabinet = locationService.calculateDistancesAndFilterGeneric(
                    address.getLongitude(),
                    address.getLatitude(),
                    cabinets,
                    SmartCabinet::getLongitude,
                    SmartCabinet::getLatitude,
                    null
            ).get(0).getPoint();

            // 4. 计算距离（米）
            double distance = locationService.calculateDistance(
                    address.getLongitude(),
                    address.getLatitude(),
                    nearestCabinet.getLongitude(),
                    nearestCabinet.getLatitude()
            );

            // 5. 根据距离估算时间（分钟）：每公里按5分钟计算，最低30分钟
            address.setEstimatedDeliveryTime((int) Math.max(30, distance / 1000 * 5));

        } catch (Exception e) {
            log.error("计算地址[{}]的预计取送时间失败", address.getId(), e);
            address.setEstimatedDeliveryTime(null);
        }
    }

    /**
     * 根据地址ID和柜子ID计算取送时间
     *
     * @param addressId 地址ID
     * @param cabinetId 柜子ID
     * @return 取送时间（小时）
     */
    @Override
    public Integer calculateDeliveryTime(Long addressId, Long cabinetId) {
        // 1. 获取地址信息
        MemberAddressPo addressPo = addressRepository.getById(addressId);
        if (addressPo == null) {
            throw new BusinessException("地址不存在");
        }
        MemberAddress address = convertHelper.convertPoToFullAddressEntity(addressPo);
//
//        // 2. 获取柜子信息
//        SmartCabinet cabinet = smartCabinetService.getCabinetById(cabinetId);
//        if (cabinet == null) {
//            throw new BusinessException("柜子不存在");
//        }
//
//        // 3. 计算距离（米）
//        double distance = locationService.calculateDistance(
//                address.getLongitude(),
//                address.getLatitude(),
//                cabinet.getLongitude(),
//                cabinet.getLatitude()
//        );
        double distance = 0L;
        // 4. 返回预计时间（小时）：每公里按0.083小时（5分钟）计算，最低0.5小时
        return (int) Math.max(0.5, distance / 1000 * 0.083);
    }

    // 增加一个根据距离计算 时间的方法（大概的时间 ，单位是：小时 ）
    //
    //    最终结果算出直线距离就可以
    //    两种计算 直接去工厂 ->上门的时间
    //    柜子去工厂 -> 时间需要保存下（redis）
    //    柜子/会员地址   保存距离，保存时间

    {
//
////        根据柜子的投资人id获取工厂信息
//        List<SmartCabinet> availableCabinetsByRegion = smartCabinetService.getAvailableCabinetsByRegion(address.getCountyEnum());
//
//        RegionInvestorResponse investorDetail = regionInvestorService.getInvestorDetail();
////        根据工厂id
//        factoryInfoService.getFactoryDetail(investorDetail.getFactoryId())

    }
}