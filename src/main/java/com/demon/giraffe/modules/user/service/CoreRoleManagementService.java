package com.demon.giraffe.modules.user.service;

import com.demon.giraffe.modules.user.model.enums.UserRole;

/**
 * 核心角色管理服务接口
 * 定义所有角色管理的核心功能，避免重复定义
 */
public interface CoreRoleManagementService {

    /**
     * 分配角色
     * @param userId 用户ID
     * @param targetRole 目标角色
     * @param roleRequest 角色创建请求
     * @return 分配结果
     */
    Boolean assignRole(Long userId, UserRole targetRole, Object roleRequest);

    /**
     * 移除角色
     * @param userId 用户ID
     * @return 移除结果
     */
    Boolean removeRole(Long userId);

    /**
     * 获取用户角色信息
     * @param userId 用户ID
     * @return 角色信息
     */
    Object getUserRoleInfo(Long userId);

    /**
     * 获取用户角色信息（优化版本，避免重复查询用户）
     * @param user 已查询的用户对象
     * @return 角色信息
     */
    Object getUserRoleInfo(com.demon.giraffe.modules.user.model.po.UserPo user);

    /**
     * 获取用户当前角色
     * @param userId 用户ID
     * @return 用户角色
     */
    UserRole getUserRole(Long userId);


    /**
     * 统一角色注销
     * @param userId 用户ID
     * @return 注销结果
     */
    Boolean logout(Long userId);
}
