package com.demon.giraffe.modules.user.service;

import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import com.demon.giraffe.modules.user.model.po.UserPo;
import com.demon.giraffe.modules.user.service.base.BaseRoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 统一角色管理服务
 * 提供统一的角色管理入口，根据角色类型分发到对应的服务
 * 实现核心角色管理接口
 */
@Slf4j
@Service
public class UnifiedRoleManagementService implements CoreRoleManagementService {
    
    private final Map<UserRole, BaseRoleService<?, ?, ?, ?>> roleServiceMap = new HashMap<>();
    
    @Autowired
    private UserService userService;
    
    /**
     * 注册角色服务
     * 在Spring容器启动后自动注册所有角色服务
     */
    @Autowired
    public void registerRoleServices(List<BaseRoleService<?, ?, ?, ?>> roleServices) {
        for (BaseRoleService<?, ?, ?, ?> service : roleServices) {
            roleServiceMap.put(service.getRoleType(), service);
            log.info("注册角色服务：{} -> {}", service.getRoleType().getName(), service.getClass().getSimpleName());
        }
    }

    
    /**
     * 统一注销入口
     * @param userId 用户ID
     * @return 是否成功
     */
    @Override
    public Boolean logout(Long userId) {
        UserRole userRole = getInternalUserRole(userId);
        BaseRoleService<?, ?, ?, ?> roleService = getRoleService(userRole);
        return roleService.logout(userId);
    }
    
    /**
     * 统一角色分配
     * @param userId 用户ID
     * @param targetRole 目标角色
     * @param roleRequest 角色创建请求
     * @return 是否成功
     */
    @Override
    @Transactional
    @SuppressWarnings("unchecked")
    public Boolean assignRole(Long userId, UserRole targetRole, Object roleRequest) {
        log.info("为用户[{}]分配角色[{}]", userId, targetRole.getName());
        
        try {
            // 1. 清理旧角色
            cleanupOldRole(userId);
            
            // 2. 创建新角色
            BaseRoleService roleService = getRoleService(targetRole);
            Long roleId = roleService.createRole(roleRequest);
            
            log.info("用户[{}]角色分配成功，新角色：{}，角色ID：{}", userId, targetRole.getName(), roleId);
            return true;
        } catch (Exception e) {
            log.error("用户[{}]角色分配失败", userId, e);
            throw new BusinessException("角色分配失败：" + e.getMessage());
        }
    }
    
    /**
     * 统一角色删除
     * @param userId 用户ID
     * @return 是否成功
     */
    @Override
    @Transactional
    public Boolean removeRole(Long userId) {
        log.info("删除用户[{}]的角色", userId);
        
        try {
            UserRole userRole = getInternalUserRole(userId);
            BaseRoleService<?, ?, ?, ?> roleService = getRoleService(userRole);
            
            Boolean result = roleService.deleteByUserId(userId);
            
            if (result) {
                // 将用户角色重置为普通用户
                userService.updateUserRoles(userId, UserRole.CUSTOMER);
                log.info("用户[{}]角色删除成功", userId);
            }
            
            return result;
        } catch (Exception e) {
            log.error("用户[{}]角色删除失败", userId, e);
            return false;
        }
    }
    
    /**
     * 获取用户角色信息
     * @param userId 用户ID
     * @return 角色信息
     */
    @Override
    public Object getUserRoleInfo(Long userId) {
        UserRole userRole = getInternalUserRole(userId);
        BaseRoleService<?, ?, ?, ?> roleService = getRoleService(userRole);
        return roleService.getRoleByUserId(userId);
    }

    /**
     * 获取用户角色信息（优化版本，避免重复查询用户）
     * @param user 已查询的用户对象
     * @return 角色信息
     */
    @Override
    public Object getUserRoleInfo(UserPo user) {
        if (user == null) {
            return null;
        }

        UserRole userRole = user.getRole();
        BaseRoleService<?, ?, ?, ?> roleService = getRoleService(userRole);
        return roleService.getRoleByUserId(user.getId());
    }

    /**
     * 获取用户当前角色
     * @param userId 用户ID
     * @return 用户角色
     */
    @Override
    public UserRole getUserRole(Long userId) {
        return userService.getUserInfo(userId).getRole();
    }


    /**
     * 角色状态变更
     * @param userId 用户ID
     * @param newStatus 新状态
     * @return 是否成功
     */
    public Boolean changeRoleStatus(Long userId, Object newStatus) {
        UserRole userRole = getInternalUserRole(userId);
        BaseRoleService<?, ?, ?, ?> roleService = getRoleService(userRole);
        return roleService.changeStatus(userId, newStatus);
    }

    
    // ========== 私有方法 ==========
    
    /**
     * 获取用户角色（私有方法，供内部使用）
     */
    private UserRole getInternalUserRole(Long userId) {
        return userService.getUserInfo(userId).getRole();
    }
    
    /**
     * 获取角色服务
     */
    private BaseRoleService<?, ?, ?, ?> getRoleService(UserRole userRole) {
        BaseRoleService<?, ?, ?, ?> roleService = roleServiceMap.get(userRole);
        if (roleService == null) {
            throw new BusinessException("未找到角色[" + userRole.getName() + "]对应的服务");
        }
        return roleService;
    }
    
    /**
     * 清理旧角色数据
     */
    private void cleanupOldRole(Long userId) {
        try {
            UserRole currentRole = getInternalUserRole(userId);
            if (currentRole != UserRole.CUSTOMER) { // 普通用户无需清理
                BaseRoleService<?, ?, ?, ?> currentRoleService = getRoleService(currentRole);
                currentRoleService.deleteByUserId(userId);
                log.info("清理用户[{}]的旧角色[{}]成功", userId, currentRole.getName());
            }
        } catch (Exception e) {
            log.warn("清理用户[{}]的旧角色失败，继续执行", userId, e);
        }
    }
}
