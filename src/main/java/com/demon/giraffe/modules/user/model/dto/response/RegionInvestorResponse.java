package com.demon.giraffe.modules.user.model.dto.response;

import com.demon.giraffe.common.domain.dto.response.RegionResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "RegionInvestorResponse", description = "区域投资人响应信息")
public class RegionInvestorResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "投资人ID")
    private Long id;

    @Schema(description = "关联用户ID")
    private Long appUserId;

    @Schema(description = "投资人编号")
    private String investorNo;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "地区响应")
    private RegionResponse region;

    @Schema(description = "工厂id")
    private Long factoryId;
}