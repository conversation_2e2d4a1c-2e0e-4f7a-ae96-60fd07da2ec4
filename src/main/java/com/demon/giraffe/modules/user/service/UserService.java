package com.demon.giraffe.modules.user.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.modules.user.model.dto.query.UserQueryRequest;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import com.demon.giraffe.modules.user.model.po.UserPo;
import com.demon.giraffe.modules.user.model.vo.UserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 用户模块服务接口
 *
 * <AUTHOR>
 */
@Tag(name = "用户服务", description = "用户注册、登录、权限管理等接口")
public interface UserService {


    /**
     * 用户下线
     * @return 是否下线成功
     */
    @Operation(summary = "用户注销", description = "用户退出登录接口")
    Boolean logout();

    /**
     * 获取用户信息
     * @param userId 用户ID
     * @return 用户信息
     */
    @Operation(summary = "获取用户信息", description = "根据用户ID获取用户详细信息")
    UserPo getUserInfo(
            @Parameter(description = "用户ID", required = true)
            Long userId);


    /**
     * 修改用户权限,并失效之前的token
     * @param targetId 目标用户ID
     * @param role 新角色
     */
    @Operation(summary = "修改用户权限", description = "修改用户的角色权限")
    void updateUserRoles(
            @Parameter(description = "目标用户ID", required = true)
            Long targetId,
            @Parameter(description = "新的用户角色", required = true)
            UserRole role);

    /**
     * 根据openid获取用户
     * @param openId 微信openid
     * @return 用户信息
     */
    @Operation(summary = "通过openid获取用户", description = "根据微信openid查询用户信息")
    UserPo getUserByOpenId(
            @Parameter(description = "微信openid", required = true)
            String openId);
    /**
     * 用户分页查询
     * @param query 查询条件
     * @return 分页结果
     */
    @Operation(summary = "用户分页查询", description = "根据条件分页查询用户列表")
    IPage<UserVO> queryUserPage(
            @Parameter(description = "分页查询条件")
            BasePageQuery<UserQueryRequest> query);


}