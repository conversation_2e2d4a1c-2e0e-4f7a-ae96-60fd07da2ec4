package com.demon.giraffe.modules.user.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.modules.user.mapper.LogisticsDeliveryWorkerMapper;
import com.demon.giraffe.modules.user.model.dto.query.DeliveryWorkerQueryRequest;
import com.demon.giraffe.modules.user.model.enums.DeliveryWorkerStatusEnum;
import com.demon.giraffe.modules.user.model.po.LogisticsDeliveryWorkerPo;
import com.demon.giraffe.modules.user.repository.LogisticsDeliveryWorkerRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;

@Repository
@RequiredArgsConstructor
public class LogisticsDeliveryWorkerRepositoryImpl implements LogisticsDeliveryWorkerRepository {

    private final LogisticsDeliveryWorkerMapper workerMapper;

    @Override
    public boolean save(LogisticsDeliveryWorkerPo po) {
        return workerMapper.insert(po) > 0;
    }

    @Override
    public boolean updateById(LogisticsDeliveryWorkerPo po) {
        return workerMapper.updateById(po) > 0;
    }

    @Override
    public LogisticsDeliveryWorkerPo getById(Long id) {
        return workerMapper.selectById(id);
    }

    @Override
    public IPage<LogisticsDeliveryWorkerPo> queryPage(Page<LogisticsDeliveryWorkerPo> page, DeliveryWorkerQueryRequest query) {
        LambdaQueryWrapper<LogisticsDeliveryWorkerPo> wrapper = new LambdaQueryWrapper<>();
        if (query != null) {
            wrapper.eq(query.getAppUserId() != null, LogisticsDeliveryWorkerPo::getAppUserId, query.getAppUserId())
                  .eq(query.getRegionInvestorId() != null, LogisticsDeliveryWorkerPo::getRegionInvestorId, query.getRegionInvestorId())
                  .eq(query.getCurrentStatus() != null, LogisticsDeliveryWorkerPo::getCurrentStatus, query.getCurrentStatus())
//                  .like(query.getWorkerNo() != null, LogisticsDeliveryWorkerPo::getWorkerNo, query.getWorkerNo())
                  .like(query.getPhone() != null, LogisticsDeliveryWorkerPo::getPhone, query.getPhone())
                  .like(query.getRealName() != null, LogisticsDeliveryWorkerPo::getRealName, query.getRealName())
//                  .ge(query.getMinLongitude() != null, LogisticsDeliveryWorkerPo::getLongitude, query.getMinLongitude())
//                  .le(query.getMaxLongitude() != null, LogisticsDeliveryWorkerPo::getLongitude, query.getMaxLongitude())
//                  .ge(query.getMinLatitude() != null, LogisticsDeliveryWorkerPo::getLatitude, query.getMinLatitude())
//                  .le(query.getMaxLatitude() != null, LogisticsDeliveryWorkerPo::getLatitude, query.getMaxLatitude())
                  ;
        }
        return workerMapper.selectPage(page, wrapper);
    }

    @Override
    public boolean updateStatus(Long id, DeliveryWorkerStatusEnum status) {
        LogisticsDeliveryWorkerPo po = new LogisticsDeliveryWorkerPo();
        po.setId(id);
        po.setCurrentStatus(status);
        return workerMapper.updateById(po) > 0;
    }

    @Override
    public boolean updateLocation(Long id, BigDecimal longitude, BigDecimal latitude) {
        LogisticsDeliveryWorkerPo po = new LogisticsDeliveryWorkerPo();
        po.setId(id);
//        po.setLongitude(longitude);
//        po.setLatitude(latitude);
        return workerMapper.updateById(po) > 0;
    }

    @Override
    public LogisticsDeliveryWorkerPo getByAppUserId(Long appUserId) {
        LambdaQueryWrapper<LogisticsDeliveryWorkerPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LogisticsDeliveryWorkerPo::getAppUserId, appUserId);
        return workerMapper.selectOne(wrapper);
    }

    @Override
    public boolean deleteByUserId(Long userId) {
        LambdaUpdateWrapper<LogisticsDeliveryWorkerPo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(LogisticsDeliveryWorkerPo::getAppUserId, userId);
        return workerMapper.delete(wrapper) > 0;
    }
}