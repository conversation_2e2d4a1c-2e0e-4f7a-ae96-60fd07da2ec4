package com.demon.giraffe.modules.user.model.dto.response;

import com.demon.giraffe.modules.user.model.enums.SexEnum;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 增强的登录响应
 * 包含用户基础信息和角色信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "增强的登录响应")
public class EnhancedLoginResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "访问令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;

    @Schema(description = "角色权限", example = "ROOT")
    private UserRole userRole;

    @Schema(description = "用户编号，唯一业务ID", example = "如USR20230001")
    private String userCode;

    @Schema(description = "用户ID", example = "123456")
    private Long userId;

    @Schema(description = "用户昵称", example = "张三")
    private String nickName;

    @Schema(description = "用户头像URL", example = "https://example.com/avatar.jpg")
    private String avatarUrl;

    @Schema(description = "用户性别", example = "MALE")
    private SexEnum gender;

    @Schema(description = "角色详细信息")
    private Object roleInfo;


    @Schema(description = "登录时间戳")
    private Long loginTime;


    /**
     * 设置登录时间为当前时间
     */
    public void setCurrentLoginTime() {
        this.loginTime = System.currentTimeMillis();
    }


}
