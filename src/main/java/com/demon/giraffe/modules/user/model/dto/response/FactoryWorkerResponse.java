package com.demon.giraffe.modules.user.model.dto.response;

import com.demon.giraffe.modules.user.model.enums.FactoryPositionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "FactoryWorkerResponse", description = "工厂员工响应信息")
public class FactoryWorkerResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "员工ID")
    private Long id;

    @Schema(description = "关联用户ID")
    private Long appUserId;

    @Schema(description = "员工编号")
    private String workerNo;

    @Schema(description = "工厂ID")
    private Long factoryId;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "真实姓名")
    private String realName;

    @Schema(description = "身份证号")
    private String idCard;

    @Schema(description = "职位类型")
    private FactoryPositionTypeEnum positionType;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}