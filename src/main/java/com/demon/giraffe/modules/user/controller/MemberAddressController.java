package com.demon.giraffe.modules.user.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.demon.giraffe.common.annotation.ProcessRegion;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.user.model.dto.request.*;
import com.demon.giraffe.modules.user.model.dto.response.*;
import com.demon.giraffe.modules.user.service.MemberAddressService;
import io.swagger.v3.oas.annotations.*;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/user/member/address")
@Tag(name = "会员地址管理接口", description = "会员收发货地址管理")
@RequiredArgsConstructor
public class MemberAddressController {

    private final MemberAddressService memberAddressService;

    @PostMapping("create")
    @ProcessRegion
    @Operation(summary = "创建地址")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "地址创建请求",
            required = true,
            content = @Content(schema = @Schema(implementation = MemberAddressCreateRequest.class))
    )
    @SaCheckRole(value = {"CUSTOMER"}, mode = SaMode.OR)
    public ResultBean<MemberAddressDetailResponse> create(@Valid @RequestBody MemberAddressCreateRequest request) {
        try {
            MemberAddressDetailResponse response = memberAddressService.createAddress(request);
            return ResultBean.success("地址创建成功", response);
        } catch (Exception e) {
            log.error("创建地址失败", e);
            return ResultBean.fail("创建失败：" + e.getMessage());
        }
    }

    @PostMapping("/update")
    @ProcessRegion
    @Operation(summary = "更新地址")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "地址更新请求",
            required = true,
            content = @Content(schema = @Schema(implementation = MemberAddressUpdateRequest.class))
    )
    @SaCheckRole(value = {"CUSTOMER"}, mode = SaMode.OR)
    public ResultBean<Boolean> update(@Valid @RequestBody MemberAddressUpdateRequest request) {
        try {
            Boolean result = memberAddressService.updateAddress(request);
            return ResultBean.success("地址更新成功", result);
        } catch (Exception e) {
            log.error("更新地址失败", e);
            return ResultBean.fail("更新失败：" + e.getMessage());
        }
    }

    @PostMapping("/{addressId}")
    @Operation(summary = "删除地址")
    @Parameter(name = "addressId", description = "地址ID", required = true)
    @SaCheckRole(value = {"CUSTOMER"}, mode = SaMode.OR)
    public ResultBean<Boolean> delete(@PathVariable Long addressId) {
        Boolean result = memberAddressService.deleteAddress(addressId);
        return ResultBean.success(result);
    }




    @PostMapping("/default/{addressId}")
    @Operation(summary = "设置默认地址")
    @Parameters({
            @Parameter(name = "addressId", description = "地址ID", required = true)
    })
    @SaCheckRole(value = {"CUSTOMER"}, mode = SaMode.OR)
    public ResultBean<Boolean> setDefault(
            @PathVariable Long addressId) {
        Boolean result = memberAddressService.setDefaultAddress( addressId);
        return ResultBean.success(result);
    }


    @GetMapping("/list")
    @Operation(summary = "获取会员所有地址")
    @SaCheckRole(value = {"CUSTOMER"}, mode = SaMode.OR)
    public ResultBean<List<MemberAddressDetailResponse>> listAll() {
        List<MemberAddressDetailResponse> response = memberAddressService.listAllAddresses();
        return ResultBean.success(response);
    }
}