// FactoryWorkerServiceImpl.java
package com.demon.giraffe.modules.user.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.modules.user.model.dto.request.FactoryWorkerQueryRequest;
import com.demon.giraffe.modules.user.service.helper.FactoryWorkerConvertHelper;
import com.demon.giraffe.modules.user.model.dto.request.FactoryWorkerRequest;
import com.demon.giraffe.modules.user.model.dto.response.FactoryWorkerResponse;
import com.demon.giraffe.modules.user.model.enums.FactoryPositionTypeEnum;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import com.demon.giraffe.modules.user.model.po.FactoryWorkerPo;
import com.demon.giraffe.modules.user.repository.FactoryWorkerRepository;
import com.demon.giraffe.modules.user.service.FactoryWorkerService;
import com.demon.giraffe.modules.user.service.base.AbstractRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * 工厂员工服务实现
 * 基于统一角色管理架构重写
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FactoryWorkerServiceImpl extends AbstractRoleService<
        FactoryWorkerPo,
        FactoryWorkerRequest,
        FactoryWorkerResponse,
        FactoryWorkerQueryRequest> implements FactoryWorkerService {

    private final FactoryWorkerRepository workerRepository;
    private final FactoryWorkerConvertHelper convertHelper;

    // ========== 统一角色管理架构实现 ==========

    @Override
    public UserRole getRoleType() {
        return UserRole.CLEANER;
    }

    @Override
    protected Long doCreateRole(FactoryWorkerRequest request) {
        // 使用ConvertHelper统一处理编码生成和对象转换
        FactoryWorkerPo po = convertHelper.convertRequestToPoForCreate(request);

        // 检查生成的编号是否已存在（虽然概率很小）
        Assert.isTrue(!checkWorkerNoExist(po.getWorkerNo()), "员工编号已存在");

        workerRepository.save(po);
        return po.getId();
    }

    @Override
    protected Boolean doDeleteRole(Long userId) {
        return workerRepository.deleteByUserId(userId);
    }

    @Override
    protected Long extractUserIdFromRequest(FactoryWorkerRequest request) {
        return request.getAppUserId();
    }

    @Override
    protected void validateCreateRequest(FactoryWorkerRequest request) {
        Assert.notNull(request.getAppUserId(), "用户ID不能为空");
        Assert.notNull(request.getFactoryId(), "工厂ID不能为空");
        Assert.hasText(request.getRealName(), "姓名不能为空");
        Assert.hasText(request.getPhone(), "手机号不能为空");
        Assert.notNull(request.getPositionType(), "职位类型不能为空");
    }

    @Override
    protected void doRoleSpecificLogin(Long userId, FactoryWorkerResponse roleInfo) {
        log.info("工厂员工[{}]登录，职位：{}，工厂ID：{}",
                roleInfo.getWorkerNo(), roleInfo.getPositionType(), roleInfo.getFactoryId());
    }

    @Override
    protected void doAfterRoleCreated(Long roleId, FactoryWorkerRequest request) {
        log.info("工厂员工创建成功，ID：{}，工厂：{}，职位：{}",
                roleId, request.getFactoryId(), request.getPositionType());
    }

    // ========== 业务特定方法实现 ==========

    // ========== 统一角色管理架构方法重写 ==========

    @Override
    public Boolean updateRole(FactoryWorkerRequest request) {
        FactoryWorkerPo existPo = workerRepository.getById(request.getId());
        Assert.notNull(existPo, "员工不存在");

        // 使用ConvertHelper处理更新（不会重新生成编码）
        FactoryWorkerPo po = convertHelper.convertRequestToPoForUpdate(request);
        return workerRepository.updateById(po);
    }

    @Override
    public FactoryWorkerResponse getRoleDetail(Long id) {
        FactoryWorkerPo po = workerRepository.getById(id);
        Assert.notNull(po, "员工不存在");

        // 使用ConvertHelper统一转换
        return convertHelper.convertPoToResponse(po);
    }

    @Override
    public FactoryWorkerResponse getRoleByUserId(Long userId) {
        FactoryWorkerPo po = workerRepository.getByAppUserId(userId);
        if (po == null) {
            return null;
        }

        // 使用ConvertHelper统一转换
        return convertHelper.convertPoToResponse(po);
    }

    @Override
    public IPage<FactoryWorkerResponse> queryRolePage(BasePageQuery<FactoryWorkerQueryRequest> query) {
        query.init();

        Page<FactoryWorkerPo> page = new Page<>(query.getPage(), query.getPerPage());
        IPage<FactoryWorkerPo> poPage = workerRepository.queryPage(page, query.getQuery());

        // 使用ConvertHelper统一转换为Response对象
        return poPage.convert(convertHelper::convertPoToResponse);
    }

    @Override
    public Boolean saveRole(FactoryWorkerPo entity) {
        return workerRepository.save(entity);
    }

    @Override
    public Boolean existsById(Long id) {
        return id != null && workerRepository.getById(id) != null;
    }





    @Override
    public Boolean changeStatus(Long userId, Object newStatus) {
        if (newStatus instanceof FactoryPositionTypeEnum) {
            FactoryWorkerResponse worker = getRoleByUserId(userId);
            if (worker != null) {
                return changeWorkerPosition(worker.getId(), (FactoryPositionTypeEnum) newStatus);
            }
        }
        return false;
    }

    // ========== 业务特定方法实现 ==========

    @Override
    public Boolean changeWorkerPosition(Long id, FactoryPositionTypeEnum positionType) {
        FactoryWorkerPo po = workerRepository.getById(id);
        Assert.notNull(po, "员工不存在");
        return workerRepository.updatePosition(id, positionType);
    }

    @Override
    public Boolean checkWorkerNoExist(String workerNo) {
        return workerRepository.getByWorkerNo(workerNo) != null;
    }
}