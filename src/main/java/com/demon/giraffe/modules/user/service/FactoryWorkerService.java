package com.demon.giraffe.modules.user.service;

import com.demon.giraffe.modules.user.model.dto.request.FactoryWorkerQueryRequest;
import com.demon.giraffe.modules.user.model.dto.request.FactoryWorkerRequest;
import com.demon.giraffe.modules.user.model.dto.response.FactoryWorkerResponse;
import com.demon.giraffe.modules.user.model.enums.FactoryPositionTypeEnum;
import com.demon.giraffe.modules.user.model.po.FactoryWorkerPo;
import com.demon.giraffe.modules.user.service.base.BaseRoleService;

/**
 * 工厂员工服务接口
 * 继承基础角色服务，获得通用角色管理能力
 */
public interface FactoryWorkerService extends BaseRoleService<FactoryWorkerPo, FactoryWorkerRequest, FactoryWorkerResponse, FactoryWorkerQueryRequest> {

    /**
     * 变更员工职位
     * @param id 员工ID
     * @param positionType 新职位类型
     * @return 是否变更成功
     */
    Boolean changeWorkerPosition(Long id, FactoryPositionTypeEnum positionType);

    /**
     * 检查员工编号是否已存在
     * @param workerNo 员工编号
     * @return 是否存在
     */
    Boolean checkWorkerNoExist(String workerNo);
}