package com.demon.giraffe.modules.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.demon.giraffe.modules.user.model.po.RegionInvestorPo;
import org.apache.ibatis.annotations.*;

@Mapper
public interface RegionInvestorMapper extends BaseMapper<RegionInvestorPo> {

    /**
     * 根据用户ID查询投资人信息（包括已逻辑删除的记录）
     * @param appUserId 用户ID
     * @return 投资人信息
     */
    @Select("SELECT * FROM region_investor WHERE app_user_id = #{appUserId}")
    RegionInvestorPo selectByAppUserIdIgnoreDeleted(@Param("appUserId") Long appUserId);

    /**
     * 根据主键ID恢复已逻辑删除的投资人信息
     * @param id 主键ID
     * @return 影响行数
     */
    @Update("UPDATE region_investor SET deleted = 0 WHERE id = #{id} AND deleted = 1")
    int restoreById(@Param("id") Long id);

}