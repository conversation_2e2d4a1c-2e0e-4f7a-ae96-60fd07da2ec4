package com.demon.giraffe.modules.user.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 注册来源枚举
 * 对应数据库字段：register_source (1小程序/2公众号/3APP/4H5)
 */
@Getter
@AllArgsConstructor
@Schema(description = "注册来源枚举")
public enum RegisterSourceEnum implements IEnum<Integer> {

    MINI_PROGRAM(1, "小程序", "微信小程序注册"),
    OFFICIAL_ACCOUNT(2, "公众号", "微信公众号注册"),
    APP(3, "APP", "移动应用注册"),
    H5(4, "H5", "H5页面注册"),
    PC(5, "PC", "电脑网页注册"), // 可选的扩展
    UNKNOWN(0, "未知", "未知来源");

    @EnumValue
    private final Integer code;
    private final String name;
    private final String description;

    private static final Map<Integer, RegisterSourceEnum> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(RegisterSourceEnum::getCode, Function.identity()));

    /**
     * 根据code获取枚举
     */
    public static RegisterSourceEnum of(Integer code) {
        return CODE_MAP.getOrDefault(code, UNKNOWN);
    }

    /**
     * 判断是否是微信生态来源（小程序或公众号）
     */
    public boolean isWechatSource() {
        return this == MINI_PROGRAM || this == OFFICIAL_ACCOUNT;
    }

    /**
     * 判断是否是移动端来源
     */
    public boolean isMobileSource() {
        return this == MINI_PROGRAM || this == OFFICIAL_ACCOUNT || this == APP || this == H5;
    }

    @Override
    public Integer getValue() {
        return code;
    }
}