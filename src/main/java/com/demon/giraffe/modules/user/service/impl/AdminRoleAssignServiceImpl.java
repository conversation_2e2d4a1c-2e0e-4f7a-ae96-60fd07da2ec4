package com.demon.giraffe.modules.user.service.impl;

import com.demon.giraffe.common.domain.dto.request.RegionRequest;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.modules.user.model.dto.request.*;
import com.demon.giraffe.modules.user.model.enums.*;
import com.demon.giraffe.modules.user.model.po.UserPo;
import com.demon.giraffe.modules.user.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 管理员角色分配服务实现
 * 基于统一角色管理架构，专注于单个用户角色分配逻辑
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdminRoleAssignServiceImpl implements AdminRoleAssignService {

    private final FactoryDirectorService factoryDirectorService;
    private final RegionInvestorService regionInvestorService;
    private final CoreRoleManagementService coreRoleManagementService;
    private final UserService userService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean assignCleanerRole(AssignLaundryWorkerRequest request) {
        return assignRoleWithUser(
                request.getUserId(),
                "洗护工",
                () -> factoryDirectorService.existsFactoryById(request.getFactoryId()),
                (user) -> FactoryWorkerRequest.builder()
                        .appUserId(request.getUserId())
                        .factoryId(request.getFactoryId())
                        .phone(user.getPhone())
                        .realName(user.getNickname())
                        .positionType(FactoryPositionTypeEnum.WASHING)
                        .build(),
                UserRole.CLEANER
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean assignDeliveryRole(AssignDeliveryRequest request) {
        return assignRoleWithUser(
                request.getUserId(),
                "配送员",
                () -> regionInvestorService.getById(request.getRegionInvestorId()) != null,
                (user) -> DeliveryWorkerRequest.builder()
                        .appUserId(request.getUserId())
                        .regionInvestorId(request.getRegionInvestorId())
                        .phone(user.getPhone())
                        .realName(user.getNickname())
                        .build(),
                UserRole.DELIVERY
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean assignFactoryManagerRole(AssignFactoryManagerRequest request) {
        return assignRoleWithUser(
                request.getUserId(),
                "工厂管理员",
                () -> factoryDirectorService.existsFactoryById(request.getFactoryId()),
                (user) -> FactoryDirectorRequest.builder()
                        .appUserId(request.getUserId())
                        .factoryId(request.getFactoryId())
                        .build(),
                UserRole.FACTORY_MANAGER
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean assignInvestorRole(AssignInvestorRequest request) {
        return assignRoleWithUser(
                request.getUserId(),
                "区域投资人",
                () -> factoryDirectorService.existsFactoryById(request.getFactoryId()),
                (user) -> {
                    CountyEnum addressCode = request.getRegion().getAddressCode();
                    RegionInvestorRequest createRequest = RegionInvestorRequest.builder()
                            .region(new RegionRequest(addressCode))
                            .appUserId(request.getUserId())
                            .contactPerson(user.getNickname())
                            .contactPhone(user.getPhone())
                            .factoryId(request.getFactoryId())
                            .build();
                    createRequest.setAddressCode(addressCode);
                    return createRequest;
                },
                UserRole.INVESTOR
        );
    }

    /**
     * 通用角色分配处理方法（优化版本，避免重复查询用户）
     */
    private Boolean assignRoleWithUser(Long userId, String roleName,
                                       CheckedSupplier<Boolean> validator,
                                       CheckedFunction<UserPo, Object> requestSupplier,
                                       UserRole role) {
        try {
            if (userId == null) {
                log.error("用户不存在：{}", userId);
                return false;
            }
            log.info("开始为用户[{}]分配{}角色", userId, roleName);

            // 验证用户是否存在（只查询一次）
            UserPo userInfo = userService.getUserInfo(userId);
            if (userInfo == null) {
                log.error("用户不存在：{}", userId);
                return false;
            }

            // 验证关联实体存在（如工厂、投资人等）
            if (!validator.get()) {
                log.error("{}分配失败，关联实体校验不通过，用户ID：{}", roleName, userId);
                return false;
            }

            // 构建角色请求体（传入用户对象，避免重复查询）
            Object requestBody = requestSupplier.apply(userInfo);

            // 调用角色分配核心服务
            boolean result = coreRoleManagementService.assignRole(userId, role, requestBody);
            log.info("用户[{}]分配{}角色{}", userId, roleName, result ? "成功" : "失败");
            return result;
        } catch (Exception e) {
            log.error("分配{}角色失败，用户ID：{}", roleName, userId, e);
            return false;
        }
    }

    /**
     * 支持抛出异常的 Supplier 接口
     */
    @FunctionalInterface
    private interface CheckedSupplier<T> {
        T get() throws Exception;
    }

    /**
     * 支持抛出异常的 Function 接口
     */
    @FunctionalInterface
    private interface CheckedFunction<T, R> {
        R apply(T t) throws Exception;
    }
}
