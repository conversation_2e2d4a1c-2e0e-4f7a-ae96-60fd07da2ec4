package com.demon.giraffe.modules.user.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.modules.user.mapper.FactoryWorkerMapper;
import com.demon.giraffe.modules.user.model.dto.request.FactoryWorkerQueryRequest;
import com.demon.giraffe.modules.user.model.enums.FactoryPositionTypeEnum;
import com.demon.giraffe.modules.user.model.po.FactoryWorkerPo;
import com.demon.giraffe.modules.user.repository.FactoryWorkerRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class FactoryWorkerRepositoryImpl implements FactoryWorkerRepository {

    private final FactoryWorkerMapper workerMapper;

    @Override
    public boolean save(FactoryWorkerPo po) {
        return workerMapper.insert(po) > 0;
    }

    @Override
    public boolean updateById(FactoryWorkerPo po) {
        return workerMapper.updateById(po) > 0;
    }

    @Override
    public FactoryWorkerPo getById(Long id) {
        return workerMapper.selectById(id);
    }

    @Override
    public IPage<FactoryWorkerPo> queryPage(Page<FactoryWorkerPo> page, FactoryWorkerQueryRequest query) {
        LambdaQueryWrapper<FactoryWorkerPo> wrapper = new LambdaQueryWrapper<>();
        if (query != null) {
            wrapper.eq(query.getFactoryId() != null, FactoryWorkerPo::getFactoryId, query.getFactoryId())
                    .eq(query.getPositionType() != null, FactoryWorkerPo::getPositionType, query.getPositionType())
                    .like(query.getWorkerNo() != null, FactoryWorkerPo::getWorkerNo, query.getWorkerNo())
                    .like(query.getPhone() != null, FactoryWorkerPo::getPhone, query.getPhone())
                    .like(query.getRealName() != null, FactoryWorkerPo::getRealName, query.getRealName());
        }
        return workerMapper.selectPage(page, wrapper);
    }

    @Override
    public boolean updatePosition(Long id, FactoryPositionTypeEnum positionType) {
        FactoryWorkerPo po = new FactoryWorkerPo();
        po.setId(id);
        po.setPositionType(positionType);
        return workerMapper.updateById(po) > 0;
    }

    @Override
    public FactoryWorkerPo getByWorkerNo(String workerNo) {
        LambdaQueryWrapper<FactoryWorkerPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FactoryWorkerPo::getWorkerNo, workerNo);
        return workerMapper.selectOne(wrapper);
    }

    @Override
    public FactoryWorkerPo getByAppUserId(Long appUserId) {
        LambdaQueryWrapper<FactoryWorkerPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FactoryWorkerPo::getAppUserId, appUserId);
        return workerMapper.selectOne(wrapper);
    }

    @Override
    public boolean deleteByUserId(Long userId) {
        LambdaUpdateWrapper<FactoryWorkerPo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(FactoryWorkerPo::getAppUserId, userId);
        return workerMapper.delete(wrapper) > 0;
    }
}