package com.demon.giraffe.modules.user.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.modules.user.mapper.FactoryDirectorMapper;
import com.demon.giraffe.modules.user.model.dto.query.FactoryDirectorQueryRequest;
import com.demon.giraffe.modules.user.model.enums.DirectorStatusEnum;
import com.demon.giraffe.modules.user.model.po.FactoryDirectorPo;
import com.demon.giraffe.modules.user.repository.FactoryDirectorRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class FactoryDirectorRepositoryImpl implements FactoryDirectorRepository {

    private final FactoryDirectorMapper directorMapper;

    @Override
    public boolean save(FactoryDirectorPo po) {
        return directorMapper.insert(po) > 0;
    }

    @Override
    public boolean updateById(FactoryDirectorPo po) {
        return directorMapper.updateById(po) > 0;
    }

    @Override
    public FactoryDirectorPo getById(Long id) {
        return directorMapper.selectById(id);
    }

    @Override
    public IPage<FactoryDirectorPo> queryPage(Page<FactoryDirectorPo> page, FactoryDirectorQueryRequest query) {
        LambdaQueryWrapper<FactoryDirectorPo> wrapper = new LambdaQueryWrapper<>();
        if (query != null) {
            wrapper.eq(query.getAppUserId() != null, FactoryDirectorPo::getAppUserId, query.getAppUserId())
                    .eq(query.getFactoryId() != null, FactoryDirectorPo::getFactoryId, query.getFactoryId())
                    .eq(query.getStatus() != null, FactoryDirectorPo::getStatus, query.getStatus())
                    .like(query.getDirectorNo() != null, FactoryDirectorPo::getDirectorNo, query.getDirectorNo());
        }
        return directorMapper.selectPage(page, wrapper);
    }

    @Override
    public boolean updateStatus(Long id, DirectorStatusEnum status) {
        FactoryDirectorPo po = new FactoryDirectorPo();
        po.setId(id);
        po.setStatus(status);
        return directorMapper.updateById(po) > 0;
    }

    @Override
    public boolean deleteByUserId(Long userId) {
        LambdaUpdateWrapper<FactoryDirectorPo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(FactoryDirectorPo::getAppUserId, userId);
        return directorMapper.delete(wrapper) > 0;
    }

    @Override
    public FactoryDirectorPo getByAppUserId(Long appUserId) {
        LambdaQueryWrapper<FactoryDirectorPo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FactoryDirectorPo::getAppUserId, appUserId);
        return directorMapper.selectOne(wrapper);
    }
}