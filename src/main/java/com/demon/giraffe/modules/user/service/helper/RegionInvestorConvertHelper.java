package com.demon.giraffe.modules.user.service.helper;

import com.demon.giraffe.common.domain.dto.request.RegionRequest;
import com.demon.giraffe.common.domain.dto.response.RegionResponse;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.common.service.RegionProcessingService;
import com.demon.giraffe.common.util.CodeGeneratorUtil;
import com.demon.giraffe.modules.user.model.dto.request.RegionInvestorRequest;
import com.demon.giraffe.modules.user.model.dto.response.RegionInvestorResponse;
import com.demon.giraffe.modules.user.model.po.RegionInvestorPo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 区域投资人转换助手
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RegionInvestorConvertHelper {

    private final CodeGeneratorUtil codeGeneratorUtil;
    private final RegionProcessingService regionProcessingService;

    /**
     * 将请求对象转换为PO（用于创建）
     * @param request 请求对象
     * @return 转换后的PO对象
     */
    public RegionInvestorPo convertRequestToPo(RegionInvestorRequest request) {
        if (request == null) {
            return null;
        }

        // 使用统一区域处理服务解析区域编码
        CountyEnum countyEnum = request.getAddressCode();
        if (countyEnum == null && request.getRegion() != null) {
            countyEnum = request.getAddressCode();
        }

        String investorNo = codeGeneratorUtil.generatePartnerCode(countyEnum);

        RegionInvestorPo po = RegionInvestorPo.builder()
                .appUserId(request.getAppUserId())
                .investorNo(investorNo)
                .contactPerson(request.getContactPerson())
                .contactPhone(request.getContactPhone())
                .addressCode(countyEnum)
                .factoryId(request.getFactoryId())
                .build();

        // 只有在更新时才设置ID
        if (request.getId() != null) {
            po.setId(request.getId());
        }

        return po;
    }

    /**
     * 将PO转换为响应对象
     * @param po 持久化对象
     * @return 转换后的响应对象
     */
    public RegionInvestorResponse convertPoToResponse(RegionInvestorPo po) {
        if (po == null) {
            return null;
        }

        // 转换地区信息
        RegionResponse regionResponse = null;
        if (po.getAddressCode() != null) {
            regionResponse = new RegionResponse(po.getAddressCode());
        }

        return RegionInvestorResponse.builder()
                .id(po.getId())
                .appUserId(po.getAppUserId())
                .investorNo(po.getInvestorNo())
                .contactPerson(po.getContactPerson())
                .contactPhone(po.getContactPhone())
                .region(regionResponse)
                .factoryId(po.getFactoryId())
                .build();
    }

    /**
     * 更新PO对象属性
     * @param po 目标PO对象
     * @param request 来源请求对象
     */
    public void updatePoFromRequest(RegionInvestorPo po, RegionInvestorRequest request) {
        if (po == null || request == null) {
            return;
        }

        if (request.getAppUserId() != null) {
            po.setAppUserId(request.getAppUserId());
        }
        if (request.getContactPerson() != null) {
            po.setContactPerson(request.getContactPerson());
        }
        if (request.getContactPhone() != null) {
            po.setContactPhone(request.getContactPhone());
        }
        if (request.getFactoryId() != null) {
            po.setFactoryId(request.getFactoryId());
        }

        // 处理区域信息更新
        if (request.getRegion() != null) {
            CountyEnum countyEnum = request.getAddressCode();
            po.setAddressCode(countyEnum);
        }
    }

    /**
     * 将PO列表转换为响应对象列表
     * @param pos PO列表
     * @return 响应对象列表
     */
    public List<RegionInvestorResponse> convertPosToResponses(List<RegionInvestorPo> pos) {
        if (pos == null) {
            return null;
        }

        return pos.stream()
                .map(this::convertPoToResponse)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 统一转换方法（兼容性）
     */
    public RegionInvestorResponse convertToResponse(RegionInvestorPo po) {
        return convertPoToResponse(po);
    }

    /**
     * 将请求对象转换为PO（专门用于创建，不设置ID）
     * @param request 请求对象
     * @return 转换后的PO对象
     */
    public RegionInvestorPo convertRequestToPoForCreate(RegionInvestorRequest request) {
        if (request == null) {
            return null;
        }

        // 使用统一区域处理服务解析区域编码
        CountyEnum countyEnum = request.getAddressCode();
        if (countyEnum == null && request.getRegion() != null) {
            countyEnum = request.getAddressCode();
        }

        String investorNo = codeGeneratorUtil.generatePartnerCode(countyEnum);

        return RegionInvestorPo.builder()
                .appUserId(request.getAppUserId())
                .investorNo(investorNo)
                .contactPerson(request.getContactPerson())
                .contactPhone(request.getContactPhone())
                .addressCode(countyEnum)
                .factoryId(request.getFactoryId())
                .build();
    }

    /**
     * 将请求对象转换为PO（专门用于更新，包含ID）
     * @param request 请求对象
     * @return 转换后的PO对象
     */
    public RegionInvestorPo convertRequestToPoForUpdate(RegionInvestorRequest request) {
        if (request == null) {
            return null;
        }

        // 使用统一区域处理服务解析区域编码
        CountyEnum countyEnum = request.getAddressCode();
        if (countyEnum == null && request.getRegion() != null) {
            countyEnum = request.getAddressCode();
        }

        String investorNo = codeGeneratorUtil.generatePartnerCode(countyEnum);

        return RegionInvestorPo.builder()
                .appUserId(request.getAppUserId())
                .investorNo(investorNo)
                .contactPerson(request.getContactPerson())
                .contactPhone(request.getContactPhone())
                .addressCode(countyEnum)
                .factoryId(request.getFactoryId())
                .build();
    }

    /**
     * 统一转换方法（兼容性）
     */
    public RegionInvestorPo convertToEntity(RegionInvestorRequest request) {
        return convertRequestToPo(request);
    }
}