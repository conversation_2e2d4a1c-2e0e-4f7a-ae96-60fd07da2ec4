package com.demon.giraffe.modules.user.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@Schema(description = "会员等级枚举")
public enum MemberLevelEnum implements IEnum<Integer> {

    NORMAL(1, "普通会员"),
    SILVER(2, "银卡会员"),
    GOLD(3, "金卡会员"),
    DIAMOND(4, "钻石会员");
    @EnumValue
    private final Integer code;
    private final String description;

    public static MemberLevelEnum of(Integer code) {
        for (MemberLevelEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return NORMAL; // 默认返回普通会员
    }

    @Override
    public Integer getValue() {
        return code;
    }
}