package com.demon.giraffe.modules.user.model.dto.request;

import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.modules.user.model.enums.FactoryPositionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "FactoryWorkerQueryRequest", description = "工厂员工查询条件")
public class FactoryWorkerQueryRequest extends BasePageQuery<FactoryWorkerQueryRequest> implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "关联用户ID")
    private Long appUserId;

    @Schema(description = "工厂ID")
    private Long factoryId;

    @Schema(description = "员工编号")
    private String workerNo;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "真实姓名")
    private String realName;

    @Schema(description = "职位类型")
    private FactoryPositionTypeEnum positionType;
}