package com.demon.giraffe.modules.user.model.dto.response;

import com.demon.giraffe.common.domain.dto.response.RegionResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 添加会员地址请求体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "添加会员地址请求体")
public class MemberAddressResponse implements Serializable {

    @Schema(description = "地址ID", example = "20001")
    private Long id;


    @Schema(description = "关联会员ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    @NotNull(message = "会员ID不能为空")
    private Long memberId;

    @Schema(description = "地区响应")
    private RegionResponse region;

    @Schema(description = "详细地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "高新区中和新下街342号")
    @NotBlank(message = "详细地址不能为空")
    @Size(max = 200, message = "详细地址长度不能超过200个字符")
    private String detailAddress;

    @Schema(description = "联系人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotBlank(message = "联系人姓名不能为空")
    @Size(max = 30, message = "联系人姓名长度不能超过30个字符")
    private String contactName;

    @Schema(description = "联系电话", requiredMode = Schema.RequiredMode.REQUIRED, example = "13800000000")
    @NotBlank(message = "联系电话不能为空")
    @Size(min = 11, max = 11, message = "联系电话必须为11位")
    private String contactPhone;

    @Schema(description = "经度（最多3位整数，7位小数）", example = "104.0679230")
    @Digits(integer = 3, fraction = 7, message = "经度格式不正确")
    private BigDecimal longitude;

    @Schema(description = "纬度（最多3位整数，7位小数）", example = "30.6799430")
    @Digits(integer = 3, fraction = 7, message = "纬度格式不正确")
    private BigDecimal latitude;

    @Schema(description = "是否默认地址（0否 1是），默认0", example = "false")
    private Boolean isDefault ;
}
