package com.demon.giraffe.modules.user.model.dto.entity;

import lombok.Data;

/**
 * 微信用户解密后的信息
 */
@Data
public class WxUserInfoDto {

    /** 用户唯一标识 */
    private String openId;

    /** 昵称 */
    private String nickName;

    /** 性别（0-未知，1-男，2-女） */
    private Integer gender;

    /** 语言 */
    private String language;

    /** 城市 */
    private String city;

    /** 省份 */
    private String province;

    /** 国家 */
    private String country;

    /** 用户头像 */
    private String avatarUrl;

    /** 用户在开放平台的唯一标识符 */
    private String unionId;
}
