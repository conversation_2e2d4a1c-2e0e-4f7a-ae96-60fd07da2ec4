package com.demon.giraffe.modules.user.service.helper;

import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.common.util.CodeGeneratorUtil;
import com.demon.giraffe.modules.user.model.dto.request.DeliveryWorkerRequest;
import com.demon.giraffe.modules.user.model.dto.response.DeliveryWorkerResponse;
import com.demon.giraffe.modules.user.model.po.LogisticsDeliveryWorkerPo;
import com.demon.giraffe.modules.user.model.po.RegionInvestorPo;
import com.demon.giraffe.modules.user.repository.RegionInvestorRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 物流配送员转换助手
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LogisticsDeliveryWorkerConvertHelper {

    private final CodeGeneratorUtil codeGeneratorUtil;
    private final RegionInvestorRepository regionInvestorRepository;


    /**
     * 将请求对象转换为PO（用于创建）
     * @param request 请求对象
     * @return 转换后的PO对象
     */
    public LogisticsDeliveryWorkerPo convertRequestToPo(DeliveryWorkerRequest request) {
        if (request == null) {
            return null;
        }

        // 获取投资人信息以确定区域
        RegionInvestorPo investor = regionInvestorRepository.getById(request.getRegionInvestorId());
        if (investor == null) {
            throw new IllegalArgumentException("投资人不存在");
        }

        // 生成配送员编码
        String workerNo = codeGeneratorUtil.generateDeliveryWorkerCode(investor.getAddressCode());

        LogisticsDeliveryWorkerPo po = LogisticsDeliveryWorkerPo.builder()
                .appUserId(request.getAppUserId())
                .regionInvestorId(request.getRegionInvestorId())
                .workerNo(workerNo)
                .realName(request.getRealName())
                .phone(request.getPhone())
                .currentStatus(request.getCurrentStatus())
                .build();

        // 只有在更新时才设置ID
        if (request.getId() != null) {
            po.setId(request.getId());
        }

        return po;
    }

    /**
     * 将PO转换为响应对象
     * @param po 持久化对象
     * @return 转换后的响应对象
     */
    public DeliveryWorkerResponse convertPoToResponse(LogisticsDeliveryWorkerPo po) {
        if (po == null) {
            return null;
        }

        return DeliveryWorkerResponse.builder()
                .id(po.getId())
                .appUserId(po.getAppUserId())
                .realName(po.getRealName())
                .phone(po.getPhone())
                .build();
    }

    /**
     * 更新PO对象属性
     * @param po 目标PO对象
     * @param request 来源请求对象
     */
    public void updatePoFromRequest(LogisticsDeliveryWorkerPo po, DeliveryWorkerRequest request) {
        if (po == null || request == null) {
            return;
        }

        if (request.getAppUserId() != null) {
            po.setAppUserId(request.getAppUserId());
        }
        if (request.getRegionInvestorId() != null) {
            po.setRegionInvestorId(request.getRegionInvestorId());
        }
        if (request.getRealName() != null) {
            po.setRealName(request.getRealName());
        }
        if (request.getPhone() != null) {
            po.setPhone(request.getPhone());
        }
        if (request.getCurrentStatus() != null) {
            po.setCurrentStatus(request.getCurrentStatus());
        }

        // 注意：workerNo不允许更新，因为它是系统生成的唯一标识
    }

    /**
     * 将PO列表转换为响应对象列表
     * @param pos PO列表
     * @return 响应对象列表
     */
    public List<DeliveryWorkerResponse> convertPosToResponses(List<LogisticsDeliveryWorkerPo> pos) {
        if (pos == null) {
            return null;
        }

        return pos.stream()
                .map(this::convertPoToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 统一转换方法（兼容性）
     */
    public DeliveryWorkerResponse convertToResponse(LogisticsDeliveryWorkerPo po) {
        return convertPoToResponse(po);
    }

    /**
     * 将请求对象转换为PO（专门用于创建，不设置ID）
     * @param request 请求对象
     * @return 转换后的PO对象
     */
    public LogisticsDeliveryWorkerPo convertRequestToPoForCreate(DeliveryWorkerRequest request) {
        if (request == null) {
            return null;
        }

        // 获取投资人信息以确定区域
        RegionInvestorPo investor = regionInvestorRepository.getById(request.getRegionInvestorId());
        if (investor == null) {
            throw new IllegalArgumentException("投资人不存在");
        }

        // 生成配送员编码
        String workerNo = codeGeneratorUtil.generateDeliveryWorkerCode(investor.getAddressCode());

        return LogisticsDeliveryWorkerPo.builder()
                .appUserId(request.getAppUserId())
                .regionInvestorId(request.getRegionInvestorId())
                .workerNo(workerNo)
                .realName(request.getRealName())
                .phone(request.getPhone())
                .currentStatus(request.getCurrentStatus())
                .build();
    }

    /**
     * 将请求对象转换为PO（专门用于更新，包含ID）
     * @param request 请求对象
     * @return 转换后的PO对象
     */
    public LogisticsDeliveryWorkerPo convertRequestToPoForUpdate(DeliveryWorkerRequest request) {
        if (request == null) {
            return null;
        }

        return LogisticsDeliveryWorkerPo.builder()

                .appUserId(request.getAppUserId())
                .regionInvestorId(request.getRegionInvestorId())
                .realName(request.getRealName())
                .phone(request.getPhone())
                .currentStatus(request.getCurrentStatus())

                .build();
        // 注意：更新时不重新生成workerNo
    }

    /**
     * 统一转换方法（兼容性）
     */
    public LogisticsDeliveryWorkerPo convertToEntity(DeliveryWorkerRequest request) {
        return convertRequestToPo(request);
    }
}
