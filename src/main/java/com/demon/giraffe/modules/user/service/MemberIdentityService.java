package com.demon.giraffe.modules.user.service;

import com.demon.giraffe.modules.user.model.dto.response.MemberIdentityDetailResponse;
import com.demon.giraffe.modules.user.model.po.MemberIdentityPo;
import com.demon.giraffe.modules.user.model.po.UserPo;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 会员身份信息服务接口
 * <p>提供会员身份信息的创建、查询、更新等操作</p>
 */
public interface MemberIdentityService {

    /**
     * 创建会员身份信息
     *
     * @param userPo 登录用户信息
     * @return 会员身份实体
     */
    MemberIdentityPo createMemberIdentity(UserPo userPo);

    /**
     * 获取会员详细信息（面向业务展示）
     *
     * @param userId 关联用户ID
     * @return 会员详细信息响应对象
     */
    MemberIdentityDetailResponse getMemberIdentityDetail(@NotNull Long userId);

    /**
     * 删除当前登录用户的会员身份信息
     */
    Boolean deleteMyMemberIdentity();

    /**
     * 获取当前登录用户的会员身份详情
     */
    MemberIdentityDetailResponse getMyMemberIdentityDetail();


    /**
     * 根据用户ID获取实体对象
     *
     * @param userId 用户ID
     * @return 会员身份实体
     */
    MemberIdentityPo getByUserId(@NotNull Long userId);
    /**
     * 根据用户ID获取实体对象
     *
     * @param userId 用户ID
     * @return 会员身份实体
     */
    MemberIdentityPo getById(@NotNull Long userId);

    /**
     * 根据用户ID列表批量获取会员身份详细信息
     *
     * @param userIds 用户ID集合
     * @return 用户ID到会员详细信息的映射
     */
    Map<Long, MemberIdentityDetailResponse> getMemberIdentitiesByUserIds(@NotNull Set<Long> userIds);
}
