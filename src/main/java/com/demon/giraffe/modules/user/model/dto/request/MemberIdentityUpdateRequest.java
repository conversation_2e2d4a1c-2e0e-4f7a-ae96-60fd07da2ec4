package com.demon.giraffe.modules.user.model.dto.request;

import com.demon.giraffe.modules.user.model.enums.UserStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import com.demon.giraffe.modules.user.model.enums.MemberLevelEnum;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;



@Data
@Schema(name = "MemberIdentityUpdateRequest", description = "会员身份更新请求")
public class MemberIdentityUpdateRequest {

    @NotNull(message = "ID不能为空")
    @Schema(description = "会员ID", required = true)
    private Long id;

    @Schema(description = "会员等级（1普通/2银卡/3金卡/4钻石）")
    private MemberLevelEnum level;

    @Schema(description = "当前可用积分")
    private Integer points;

    @Schema(description = "累计获得积分")
    private Integer totalPoints;

    @Schema(description = "累计消费金额")
    private Double totalConsumed;

    @Size(min = 11, max = 11, message = "手机号长度必须为11位")
    @Schema(description = "绑定手机号")
    private String phone;

    @Schema(description = "实名认证姓名")
    private String realName;

    @Schema(description = "身份证号（加密存储）")
    private String idCard;

    private UserStatus status;
}