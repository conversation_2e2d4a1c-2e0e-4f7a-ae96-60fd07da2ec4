package com.demon.giraffe.modules.user.model.dto.response;

import com.demon.giraffe.modules.user.model.enums.UserStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import com.demon.giraffe.modules.user.model.enums.MemberLevelEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Schema(name = "MemberIdentityResponse", description = "会员身份基础响应")
public class MemberIdentityResponse implements Serializable {

    @Schema(description = "会员ID")
    private Long id;

    @Schema(description = "关联app_user用户ID")
    private Long appUserId;

    @Schema(description = "会员编号")
    private String memberNo;

    @Schema(description = "会员等级")
    private MemberLevelEnum level;

    @Schema(description = "当前可用积分")
    private Integer points;

    @Schema(description = "累计消费金额")
    private Double totalConsumed;

    @Schema(description = "绑定手机号")
    private String phone;

    @Schema(description = "状态（0正常/1冻结）")
    private UserStatus status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}