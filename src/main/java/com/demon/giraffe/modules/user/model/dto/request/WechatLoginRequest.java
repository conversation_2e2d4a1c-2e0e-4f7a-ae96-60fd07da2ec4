package com.demon.giraffe.modules.user.model.dto.request;

import com.demon.giraffe.common.domain.dto.request.RegionRequest;
import com.demon.giraffe.modules.user.model.enums.SexEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
@Schema(description = "微信登录请求参数")
public class WechatLoginRequest {
    @Schema(description = "微信临时登录凭证 微信小程序通过 wx.login() 获取的临时code", required = true, example = "081LYTkl2Yz8X34DLTml2Ytkl2LYTkld")
    @NotBlank(message = "微信code不能为空")
    private String code;

    @Schema(description = "微信用户基本信息 wx.getUserProfile() 获取的用户信息，首次登录建议传递（非必须）")
    private UserInfo userInfo;

    @Data
    @Schema(description = "微信用户信息")
    public static class UserInfo {

        @Schema(description = "手机号", example = "1111111")
        private String phoneCode;

        @Schema(description = "微信昵称", example = "微信用户")
        private String nickname;

        @Schema(description = "头像URL", example = "https://thirdwx.qlogo.cn/...")
        private String avatarUrl;

        @Schema(description = "性别 0-未知 1-男 2-女", example = "1")
        private SexEnum gender;

        @Schema(description = "城市", example = "深圳")
        private String city;

        @Schema(description = "省份", example = "广东")
        private String province;

        @Schema(description = "国家", example = "中国")
        private String country;

        /**
         * 转换为RegionRequest（如果有完整的省市信息）
         * 注意：微信用户信息通常不包含区县信息，此方法仅用于有完整信息的场景
         */
        public RegionRequest toRegionRequest(String district) {
            if (province != null && city != null && district != null) {
                return RegionRequest.builder()
                        .province(province.endsWith("省") || province.endsWith("市") ||
                                 province.endsWith("自治区") ? province : province + "省")
                        .city(city.endsWith("市") ? city : city + "市")
                        .district(district)
                        .build();
            }
            return null;
        }
    }
}