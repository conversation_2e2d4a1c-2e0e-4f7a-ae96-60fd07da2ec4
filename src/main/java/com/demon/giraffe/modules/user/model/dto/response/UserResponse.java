package com.demon.giraffe.modules.user.model.dto.response;

import com.demon.giraffe.modules.user.model.enums.SexEnum;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import com.demon.giraffe.modules.user.model.enums.UserStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户响应信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户响应信息")
public class UserResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户ID", example = "123456")
    private Long userId;

    @Schema(description = "用户编号(唯一业务ID)", example = "U202406000001")
    private String userCode;

    @Schema(description = "微信OpenID", example = "o7K2t5axxxxxxx")
    private String openid;

    @Schema(description = "用户昵称", example = "张三")
    private String nickName;

    @Schema(description = "用户头像URL", example = "https://example.com/avatar.jpg")
    private String avatarUrl;

    @Schema(description = "用户性别", example = "MALE")
    private SexEnum gender;

    @Schema(description = "手机号", example = "13800138000")
    private String phone;

    @Schema(description = "用户角色", example = "CUSTOMER")
    private UserRole userRole;

    @Schema(description = "用户状态", example = "NORMAL")
    private UserStatus status;

    @Schema(description = "城市", example = "深圳")
    private String city;

    @Schema(description = "省份", example = "广东")
    private String province;

    @Schema(description = "国家", example = "中国")
    private String country;

    @Schema(description = "注册时间", example = "2024-06-01T12:00:00")
    private LocalDateTime registerTime;

    @Schema(description = "最后登录时间", example = "2024-06-15T08:30:45")
    private LocalDateTime lastLoginTime;

    @Schema(description = "登录次数", example = "42")
    private Integer loginCount;

    /**
     * 获取角色名称
     */
    public String getRoleName() {
        return userRole != null ? userRole.getName() : "普通用户";
    }

    /**
     * 是否为投资人
     */
    public Boolean isInvestor() {
        return UserRole.INVESTOR.equals(userRole);
    }

    /**
     * 是否为厂长
     */
    public Boolean isFactoryManager() {
        return UserRole.FACTORY_MANAGER.equals(userRole);
    }

    /**
     * 是否为工厂员工
     */
    public Boolean isFactoryWorker() {
        return UserRole.CLEANER.equals(userRole);
    }

    /**
     * 是否为配送员
     */
    public Boolean isDeliveryWorker() {
        return UserRole.DELIVERY.equals(userRole);
    }

    /**
     * 是否为普通用户
     */
    public Boolean isCustomer() {
        return UserRole.CUSTOMER.equals(userRole);
    }

    /**
     * 是否有手机号
     */
    public Boolean hasPhone() {
        return phone != null && !phone.trim().isEmpty();
    }

    /**
     * 获取显示名称（优先昵称，其次用户编号）
     */
    public String getDisplayName() {
        if (nickName != null && !nickName.trim().isEmpty()) {
            return nickName;
        }
        return userCode != null ? userCode : "用户" + userId;
    }
}
