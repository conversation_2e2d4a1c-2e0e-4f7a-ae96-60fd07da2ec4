package com.demon.giraffe.modules.user.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.modules.user.model.dto.query.DeliveryWorkerQueryRequest;
import com.demon.giraffe.modules.user.model.enums.DeliveryWorkerStatusEnum;
import com.demon.giraffe.modules.user.model.po.LogisticsDeliveryWorkerPo;

import java.math.BigDecimal;

public interface LogisticsDeliveryWorkerRepository {

    boolean save(LogisticsDeliveryWorkerPo po);

    boolean updateById(LogisticsDeliveryWorkerPo po);

    LogisticsDeliveryWorkerPo getById(Long id);

    /**
     * 分页查询配送员信息
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<LogisticsDeliveryWorkerPo> queryPage(Page<LogisticsDeliveryWorkerPo> page, DeliveryWorkerQueryRequest query);

    boolean updateStatus(Long id, DeliveryWorkerStatusEnum status);

    boolean updateLocation(Long id, BigDecimal longitude, BigDecimal latitude);

    /**
     * 根据用户ID查询配送员信息
     */
    LogisticsDeliveryWorkerPo getByAppUserId(Long appUserId);

    boolean deleteByUserId(Long userId);
}