package com.demon.giraffe.modules.user.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "取送工状态枚举")
public enum DeliveryWorkerStatusEnum implements IEnum<Integer> {
    ONLINE(1, "在线"),
    OFFLINE(2, "离线"),
    BUSY(3, "忙碌"),
    REST(4, "休息");
    @EnumValue
    private final Integer code;
    private final String desc;

    DeliveryWorkerStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getValue() {
        return code;
    }
}