package com.demon.giraffe.modules.user.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.modules.user.model.dto.query.RegionInvestorQueryRequest;
import com.demon.giraffe.modules.user.model.dto.request.RegionInvestorRequest;
import com.demon.giraffe.modules.user.model.dto.response.RegionInvestorResponse;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import com.demon.giraffe.modules.user.model.po.RegionInvestorPo;
import com.demon.giraffe.modules.user.repository.RegionInvestorRepository;
import com.demon.giraffe.modules.user.service.RegionInvestorService;
import com.demon.giraffe.modules.user.service.base.AbstractRoleService;
import com.demon.giraffe.modules.user.service.helper.RegionInvestorConvertHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 区域投资人服务实现
 * 基于统一角色管理架构重写
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RegionInvestorServiceImpl extends AbstractRoleService<
        RegionInvestorPo,
        RegionInvestorRequest,
        RegionInvestorResponse,
        RegionInvestorQueryRequest> implements RegionInvestorService {

    private final RegionInvestorRepository investorRepository;
    private final RegionInvestorConvertHelper convertHelper;
    // ========== 统一角色管理架构实现 ==========

    @Override
    public UserRole getRoleType() {
        return UserRole.INVESTOR;
    }

    @Override
    protected Long doCreateRole(RegionInvestorRequest request) {
        RegionInvestorPo po = convertHelper.convertRequestToPo(request);
        investorRepository.saveOrUpdate(po);
        return po.getId();
    }

    @Override
    protected Boolean doDeleteRole(Long userId) {
        return investorRepository.deleteByUserId(userId);
    }

    @Override
    protected Long extractUserIdFromRequest(RegionInvestorRequest request) {
        return request.getAppUserId();
    }

    @Override
    protected void validateCreateRequest(RegionInvestorRequest request) {
        Assert.notNull(request.getAppUserId(), "用户ID不能为空");
        Assert.notNull(request.getRegion().getProvince(), "区域编码不能为空");
        Assert.notNull(request.getRegion().getCity(), "区域编码不能为空");
        Assert.notNull(request.getRegion().getDistrict(), "区域编码不能为空");
        Assert.notNull(request.getFactoryId(), "工厂ID不能为空");
        Assert.hasText(request.getContactPerson(), "联系人不能为空");
        Assert.hasText(request.getContactPhone(), "联系电话不能为空");
    }

    @Override
    protected void doRoleSpecificLogin(Long userId, RegionInvestorResponse roleInfo) {
        log.info("区域投资人[{}]登录，管理区域：{}", roleInfo.getInvestorNo(), roleInfo.getRegion());
        // 可以在这里记录登录日志、初始化缓存等
    }

    @Override
    protected void doAfterRoleCreated(Long roleId, RegionInvestorRequest request) {
        log.info("区域投资人创建成功，ID：{}", roleId);
        // 可以在这里发送通知、初始化相关数据等
    }

    @Override
    protected void doBeforeRoleDeleted(Long userId) {
        log.info("准备删除用户[{}]的投资人角色，检查关联数据", userId);
        // 删除前检查是否有关联的取送员、订单等
    }

    // ========== 业务特定方法实现 ==========

    // ========== 统一角色管理架构方法重写 ==========



    @Override
    public Boolean updateRole(RegionInvestorRequest request) {
        RegionInvestorPo existingPo = investorRepository.getById(request.getId());
        convertHelper.updatePoFromRequest(existingPo, request);
        return investorRepository.updateById(existingPo);
    }

    @Override
    public RegionInvestorResponse getRoleDetail(Long id) {
        RegionInvestorPo po = investorRepository.getById(id);
        Assert.notNull(po, "区域投资人不存在");
        return convertHelper.convertToResponse(po);
    }

    @Override
    public RegionInvestorResponse getRoleByUserId(Long userId) {
        RegionInvestorPo po = investorRepository.getByAppUserId(userId);
        return po != null ? convertHelper.convertToResponse(po) : null;
    }



    @Override
    public IPage<RegionInvestorResponse> queryRolePage(BasePageQuery<RegionInvestorQueryRequest> query) {
        query.init();
        Page<RegionInvestorPo> page = new Page<>(query.getPage(), query.getPerPage());
        IPage<RegionInvestorPo> poPage = investorRepository.queryPage(page, query.getQuery());
        return poPage.convert(convertHelper::convertToResponse);
    }


    @Override
    public Boolean saveRole(RegionInvestorPo entity) {
        return investorRepository.save(entity);
    }

    @Override
    public Boolean existsById(Long id) {
        if (id == null) {
            return false;
        }
        return Objects.nonNull(investorRepository.getById(id));
    }


    @Override
    public RegionInvestorResponse getById(Long id) {
        RegionInvestorPo po = investorRepository.getById(id);
        return convertHelper.convertToResponse(po);
    }

    @Override
    public RegionInvestorResponse getByAppUserId(Long appUserId) {
        RegionInvestorPo po = investorRepository.getByAppUserId(appUserId);
        return convertHelper.convertToResponse(po);
    }

    @Override
    public boolean deleteByAppUserId(Long appUserId) {
        return investorRepository.deleteByAppUserId(appUserId);
    }


    // ========== 业务特定方法 ==========

    @Override
    public Map<Long, RegionInvestorResponse> batchGetInvestors(List<Long> investorIds) {
        List<RegionInvestorPo> pos = investorRepository.listByIds(investorIds);
        return pos.stream()
                .collect(Collectors.toMap(
                        RegionInvestorPo::getId,
                        convertHelper::convertToResponse
                ));
    }

    @Override
    public List<RegionInvestorResponse> listInvestorsByIds(List<Long> investorIds) {
        if (investorIds == null || investorIds.isEmpty()) {
            return List.of();
        }

        List<RegionInvestorPo> investorPos = investorRepository.listByIds(investorIds);
        return investorPos.stream()
                .map(convertHelper::convertToResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeleteInvestors(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("请提供要删除的投资人ID列表");
        }

        // 校验投资人是否存在
        for (Long id : ids) {
            RegionInvestorPo investor = investorRepository.getById(id);
            if (investor == null) {
                log.warn("投资人ID[{}]不存在，跳过删除", id);
            }
        }

        return investorRepository.batchDelete(ids);
    }
}