package com.demon.giraffe.modules.user.repository;

import com.demon.giraffe.modules.user.model.po.MemberIdentityPo;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * 会员身份信息仓储服务
 */
public interface MemberIdentityRepository {

    /**
     * 保存会员身份信息
     *
     * @param po 会员身份信息实体
     * @return 保存后的实体（包含生成的主键ID）
     */
    MemberIdentityPo save(@Valid @NotNull MemberIdentityPo po);

    /**
     * 根据ID更新会员信息
     *
     * @param po 会员身份信息实体（必须包含ID）
     * @return 是否更新成功
     */
    boolean updateById(@Valid @NotNull MemberIdentityPo po);

    /**
     * 根据 appUserId 删除会员信息
     *
     * @param appUserId 关联用户ID
     * @return 是否删除成功
     */
    boolean removeByAppUserId(@NotNull Long appUserId);


    /**
     * 根据 appUserId 查询会员信息
     *
     * @param appUserId 关联用户ID
     * @return 会员身份信息实体
     */
    MemberIdentityPo getByAppUserId(@NotNull Long appUserId);

    /**
     * 根据ID查询会员信息
     *
     * @param id 会员ID
     * @return 会员身份信息实体
     */
    MemberIdentityPo getById(@NotNull Long id);

    /**
     * 根据用户ID列表批量查询会员信息
     *
     * @param appUserIds 用户ID集合
     * @return 会员身份信息列表
     */
    List<MemberIdentityPo> listByAppUserIds(@NotNull Set<Long> appUserIds);

    /**
     * 更新会员状态
     *
     * @param id     会员ID
     * @param status 新的状态值
     * @return 是否更新成功
     */
    boolean updateStatus(@NotNull Long id, @NotNull Integer status);
}