package com.demon.giraffe.modules.user.model.dto.entity;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class WxSessionDto {

    /** 微信 openid */
    private String openid;

    /** 微信 session_key，用于解密用户信息 */
    @SerializedName("session_key")  // 关键注解
    private String sessionKey;

    /** 微信 unionid（可选） */
    private String unionid;

    /** 错误码（若接口调用失败） */
    private Integer errcode;

    /** 错误信息（若接口调用失败） */
    private String errmsg;

}
