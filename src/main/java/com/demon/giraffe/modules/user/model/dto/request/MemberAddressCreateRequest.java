package com.demon.giraffe.modules.user.model.dto.request;

import com.demon.giraffe.common.domain.dto.request.RegionRequest;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 会员地址创建请求数据传输对象
 * <p>
 * 用于接收前端传递的地址创建信息，包含联系人、地址详情、联系方式等信息
 * </p>
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "MemberAddressCreateRequest", description = "地址创建请求")
public class MemberAddressCreateRequest {

    /**
     * 联系人姓名
     * <p>
     * 收货人或联系人的真实姓名，用于快递配送时确认身份
     * </p>
     */
    @NotBlank(message = "联系人姓名不能为空")
    @Size(max = 30, message = "联系人姓名长度不能超过30个字符")
    @Schema(description = "联系人姓名", required = true, example = "张三")
    private String contactName;

    /**
     * 区域信息（统一处理）
     */
    @Valid
    @Schema(description = "区域信息", required = true)
    private RegionRequest region;

    /**
     * 详细地址
     * <p>
     * 街道门牌号等具体地址信息，不包含省市区信息
     * </p>
     */
    @NotBlank(message = "详细地址不能为空")
    @Size(max = 200, message = "详细地址长度不能超过200个字符")
    @Schema(description = "详细地址", required = true, example = "科技园路123号腾讯大厦")
    private String detailAddress;

    /**
     * 联系电话
     * <p>
     * 收货人或联系人的手机号码，用于快递配送时联系
     * </p>
     */
    @NotBlank(message = "联系电话不能为空")
    @Size(min = 11, max = 11, message = "联系电话必须为11位")
    @Schema(description = "联系电话", required = true, example = "13800138000")
    private String contactPhone;

    /**
     * 是否默认地址
     * <p>
     * 标识该地址是否为用户的默认收货地址
     * 一个用户只能有一个默认地址
     * </p>
     */
    @Schema(description = "是否默认地址", defaultValue = "false")
    private Boolean isDefault;

    /**
     * 获取区域编码（兼容性方法）
     */
    public CountyEnum getAddressCode() {
        return region != null ? region.getAddressCode() : null;
    }

    /**
     * 设置区域编码（兼容性方法）
     */
    public void setAddressCode(CountyEnum addressCode) {
        if (region == null) {
            region = new RegionRequest();
        }
        region.setAddressCode(addressCode);
    }

    /**
     * 获取完整地址描述
     */
    public String getFullAddress() {
        return region != null ? region.getFullAddress() + detailAddress : detailAddress;
    }
}