package com.demon.giraffe.modules.user.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.demon.giraffe.common.annotation.ProcessRegion;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.common.domain.ResultBean;

import com.demon.giraffe.modules.user.model.dto.query.UserQueryRequest;
import com.demon.giraffe.modules.user.model.dto.request.*;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import com.demon.giraffe.modules.user.model.vo.UserVO;
import com.demon.giraffe.modules.user.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 管理员用户权限控制器
 * 重构后的版本，逻辑封装在AdminRoleAssignService中
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/user/admin")
@Tag(name = "管理员-用户权限", description = "管理员操作用户权限相关接口")
public class AdminUserController {

    private final UserService userService;
    private final AdminRoleAssignService adminRoleAssignService;


    @PostMapping("/assign-cleaner")
    @Operation(summary = "分配洗护工角色", description = "为单个用户分配洗护工角色")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<String> assignCleanerRole(@RequestBody @Valid AssignLaundryWorkerRequest request) {
        try {
            Boolean result = adminRoleAssignService.assignCleanerRole(request);
            return result ? ResultBean.success("洗护工角色分配成功") : ResultBean.fail("分配失败");
        } catch (Exception e) {
            return ResultBean.fail("分配失败：" + e.getMessage());
        }
    }

    @PostMapping("/assign-delivery")
    @Operation(summary = "分配配送员角色", description = "为单个用户分配配送员角色")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<String> assignDeliveryRole(@RequestBody @Valid AssignDeliveryRequest request) {
        try {
            Boolean result = adminRoleAssignService.assignDeliveryRole(request);
            return result ? ResultBean.success("配送员角色分配成功") : ResultBean.fail("分配失败");
        } catch (Exception e) {
            return ResultBean.fail("分配失败：" + e.getMessage());
        }
    }

    @PostMapping("/assign-factory-manager")
    @Operation(summary = "分配工厂管理员角色", description = "为单个用户分配工厂管理员角色")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<String> assignFactoryManagerRole(@RequestBody @Valid AssignFactoryManagerRequest request) {
        try {
            Boolean result = adminRoleAssignService.assignFactoryManagerRole(request);
            return result ? ResultBean.success("工厂管理员角色分配成功") : ResultBean.fail("分配失败");
        } catch (Exception e) {
            return ResultBean.fail("分配失败：" + e.getMessage());
        }
    }

    @PostMapping("/assign-investor")
    @ProcessRegion
    @Operation(summary = "分配区域投资人角色", description = "为单个用户分配区域投资人角色")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    public ResultBean<String> assignInvestorRole(@RequestBody @Valid AssignInvestorRequest request) {
        try {
            Boolean result = adminRoleAssignService.assignInvestorRole(request);
            return result ? ResultBean.success("区域投资人角色分配成功") : ResultBean.fail("分配失败");
        } catch (Exception e) {
            return ResultBean.fail("分配失败：" + e.getMessage());
        }
    }

    @Operation(
            summary = "分页查询用户列表",
            description = "支持按角色过滤查询",
            parameters = {
                    @Parameter(name = "role", description = "角色类型", schema = @Schema(implementation = UserRole.class)),
                    @Parameter(name = "keyword", description = "搜索关键词(用户ID/昵称/手机号)")
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "查询成功",
                            content = @Content(schema = @Schema(implementation = UserVO.class))
                    )
            }
    )
    @GetMapping("/page")
    public ResultBean<IPage<UserVO>> queryPage(
            @RequestBody @Valid BasePageQuery<UserQueryRequest> request) {
        request.init(); // 初始化分页参数
        return ResultBean.success(userService.queryUserPage(request));
    }
}