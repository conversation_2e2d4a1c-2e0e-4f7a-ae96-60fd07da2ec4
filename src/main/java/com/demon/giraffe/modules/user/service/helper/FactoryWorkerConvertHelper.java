package com.demon.giraffe.modules.user.service.helper;

import com.demon.giraffe.common.util.CodeGeneratorUtil;
import com.demon.giraffe.modules.user.model.dto.request.FactoryWorkerRequest;
import com.demon.giraffe.modules.user.model.dto.response.FactoryWorkerResponse;
import com.demon.giraffe.modules.user.model.po.FactoryWorkerPo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 工厂员工转换助手
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FactoryWorkerConvertHelper {

    private final CodeGeneratorUtil codeGeneratorUtil;

    /**
     * 将请求对象转换为PO（用于创建）
     * @param request 请求对象
     * @return 转换后的PO对象
     */
    public FactoryWorkerPo convertRequestToPo(FactoryWorkerRequest request) {
        if (request == null) {
            return null;
        }

        // 生成工厂员工编码
        String workerNo = codeGeneratorUtil.generateFactoryWorkerCode(request.getFactoryId());

        FactoryWorkerPo po = FactoryWorkerPo.builder()
                .appUserId(request.getAppUserId())
                .workerNo(workerNo)
                .factoryId(request.getFactoryId())
                .realName(request.getRealName())
                .phone(request.getPhone())
                .positionType(request.getPositionType())
                .build();

        // 只有在更新时才设置ID
        if (request.getId() != null) {
            po.setId(request.getId());
        }

        return po;
    }

    /**
     * 将PO转换为响应对象
     * @param po 持久化对象
     * @return 转换后的响应对象
     */
    public FactoryWorkerResponse convertPoToResponse(FactoryWorkerPo po) {
        if (po == null) {
            return null;
        }

        return FactoryWorkerResponse.builder()
                .id(po.getId())
                .appUserId(po.getAppUserId())
                .workerNo(po.getWorkerNo())
                .factoryId(po.getFactoryId())
                .realName(po.getRealName())
                .phone(po.getPhone())
                .positionType(po.getPositionType())
                .build();
    }

    /**
     * 更新PO对象属性
     * @param po 目标PO对象
     * @param request 来源请求对象
     */
    public void updatePoFromRequest(FactoryWorkerPo po, FactoryWorkerRequest request) {
        if (po == null || request == null) {
            return;
        }

        if (request.getAppUserId() != null) {
            po.setAppUserId(request.getAppUserId());
        }
        if (request.getFactoryId() != null) {
            po.setFactoryId(request.getFactoryId());
        }
        if (request.getRealName() != null) {
            po.setRealName(request.getRealName());
        }
        if (request.getPhone() != null) {
            po.setPhone(request.getPhone());
        }
        if (request.getPositionType() != null) {
            po.setPositionType(request.getPositionType());
        }
        // 注意：workerNo不允许更新，因为它是系统生成的唯一标识
    }

    /**
     * 将PO列表转换为响应对象列表
     * @param pos PO列表
     * @return 响应对象列表
     */
    public List<FactoryWorkerResponse> convertPosToResponses(List<FactoryWorkerPo> pos) {
        if (pos == null) {
            return null;
        }

        return pos.stream()
                .map(this::convertPoToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 统一转换方法（兼容性）
     */
    public FactoryWorkerResponse convertToResponse(FactoryWorkerPo po) {
        return convertPoToResponse(po);
    }

    /**
     * 将请求对象转换为PO（专门用于创建，不设置ID）
     * @param request 请求对象
     * @return 转换后的PO对象
     */
    public FactoryWorkerPo convertRequestToPoForCreate(FactoryWorkerRequest request) {
        if (request == null) {
            return null;
        }

        // 生成工厂员工编码
        String workerNo = codeGeneratorUtil.generateFactoryWorkerCode(request.getFactoryId());

        return FactoryWorkerPo.builder()
                .appUserId(request.getAppUserId())
                .workerNo(workerNo)
                .factoryId(request.getFactoryId())
                .realName(request.getRealName())
                .phone(request.getPhone())
                .positionType(request.getPositionType())
                .build();
    }

    /**
     * 将请求对象转换为PO（专门用于更新，包含ID）
     * @param request 请求对象
     * @return 转换后的PO对象
     */
    public FactoryWorkerPo convertRequestToPoForUpdate(FactoryWorkerRequest request) {
        if (request == null) {
            return null;
        }

        return FactoryWorkerPo.builder()
//                .id(request.getId())
                .appUserId(request.getAppUserId())
                .factoryId(request.getFactoryId())
                .realName(request.getRealName())
                .phone(request.getPhone())
                .positionType(request.getPositionType())
                .build();
        // 注意：更新时不重新生成workerNo
    }

    /**
     * 统一转换方法（兼容性）
     */
    public FactoryWorkerPo convertToEntity(FactoryWorkerRequest request) {
        return convertRequestToPo(request);
    }
}
