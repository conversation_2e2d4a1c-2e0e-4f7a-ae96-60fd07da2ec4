package com.demon.giraffe.modules.user.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.user.model.dto.query.RegionInvestorQueryRequest;
import com.demon.giraffe.modules.user.model.dto.response.RegionInvestorResponse;
import com.demon.giraffe.modules.user.service.RegionInvestorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/user/region/investor")
@RequiredArgsConstructor
@Tag(name = "区域投资人管理接口", description = "区域投资人相关操作")
public class RegionInvestorController {


    private final RegionInvestorService regionInvestorService;

    @GetMapping("/by-user/{userId}")
    @Operation(summary = "根据用户ID获取投资人信息", description = "根据用户ID获取投资人信息")
    public ResultBean<RegionInvestorResponse> getByUserId(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId) {
        try {
            RegionInvestorResponse result = regionInvestorService.getByAppUserId(userId);
            return ResultBean.success("获取成功", result);
        } catch (Exception e) {
            log.error("根据用户ID获取投资人信息失败", e);
            return ResultBean.fail("获取失败：" + e.getMessage());
        }
    }

    @PostMapping("/batch-get")
    @Operation(summary = "批量获取投资人信息", description = "根据投资人ID列表批量获取投资人信息")
    public ResultBean<Map<Long, RegionInvestorResponse>> batchGet(@RequestBody List<Long> investorIds) {
        try {
            Map<Long, RegionInvestorResponse> result = regionInvestorService.batchGetInvestors(investorIds);
            return ResultBean.success("获取成功", result);
        } catch (Exception e) {
            log.error("批量获取投资人信息失败", e);
            return ResultBean.fail("获取失败：" + e.getMessage());
        }
    }

    @PostMapping("/list-by-ids")
    @Operation(summary = "根据ID列表获取投资人列表", description = "根据投资人ID列表获取投资人基本信息列表")
    public ResultBean<List<RegionInvestorResponse>> listByIds(@RequestBody List<Long> investorIds) {
        try {
            List<RegionInvestorResponse> result = regionInvestorService.listInvestorsByIds(investorIds);
            return ResultBean.success("获取成功", result);
        } catch (Exception e) {
            log.error("根据ID列表获取投资人列表失败", e);
            return ResultBean.fail("获取失败：" + e.getMessage());
        }
    }

    @PostMapping("/page")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    @Operation(summary = "分页查询投资人信息", description = "ROOT权限：根据条件分页查询投资人信息列表")
    public ResultBean<IPage<RegionInvestorResponse>> queryInvestorPage(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "分页查询条件",
                    required = true,
                    content = @Content(schema = @Schema(implementation = BasePageQuery.class))
            )
            @RequestBody @Valid BasePageQuery<RegionInvestorQueryRequest> query) {
        try {
            IPage<RegionInvestorResponse> result = regionInvestorService.queryRolePage(query);
            return ResultBean.success("查询成功", result);
        } catch (Exception e) {
            log.error("分页查询投资人信息失败", e);
            return ResultBean.fail("查询失败：" + e.getMessage());
        }
    }

    @PostMapping("/batch-delete")
    @SaCheckRole(value = {"ROOT"}, mode = SaMode.OR)
    @Operation(summary = "批量删除投资人", description = "ROOT权限：根据ID列表批量删除投资人信息")
    public ResultBean<Boolean> batchDeleteInvestors(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "投资人ID列表",
                    required = true,
                    content = @Content(schema = @Schema(implementation = List.class))
            )
            @RequestBody List<Long> ids) {
        try {
            Boolean result = regionInvestorService.batchDeleteInvestors(ids);
            return ResultBean.success("删除成功", result);
        } catch (Exception e) {
            log.error("批量删除投资人失败", e);
            return ResultBean.fail("删除失败：" + e.getMessage());
        }
    }
}