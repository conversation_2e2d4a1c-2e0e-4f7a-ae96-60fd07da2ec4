package com.demon.giraffe.modules.user.model.dto.query;

import io.swagger.v3.oas.annotations.media.Schema;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(name = "MemberAddressQueryRequest", description = "地址查询条件")
public class MemberAddressQueryRequest implements Serializable {
    
    @Schema(description = "会员ID")
    private Long memberId;
    
    @Schema(description = "区域编码")
    private CountyEnum countyEnum;
    
    @Schema(description = "联系电话")
    private String contactPhone;
    
    @Schema(description = "是否默认地址")
    private Boolean isDefault;
}