package com.demon.giraffe.modules.user.service;

import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.modules.cabinet.model.entity.SmartCabinet;
import com.demon.giraffe.modules.user.model.dto.request.*;
import com.demon.giraffe.modules.user.model.dto.response.*;
import com.demon.giraffe.modules.user.model.entity.MemberAddress;

import java.util.List;

/**
 * 会员地址服务接口
 */
public interface MemberAddressService {

    /**
     * 创建会员地址
     *
     * @param request 地址创建请求
     * @return 地址详情响应
     */
    MemberAddressDetailResponse createAddress(MemberAddressCreateRequest request);

    /**
     * 更新会员地址
     *
     * @param request 地址更新请求
     * @return 是否更新成功
     */
    Boolean updateAddress(MemberAddressUpdateRequest request);

    /**
     * 删除会员地址
     *
     * @param addressId 地址ID
     * @return 是否删除成功
     */
    Boolean deleteAddress(Long addressId);

    /**
     * 设置默认地址
     *
     * @param addressId 地址ID
     * @return 是否设置成功
     */
    Boolean setDefaultAddress(Long addressId);

    /**
     * 获取当前会员所有地址
     *
     * @return 地址列表
     */
    List<MemberAddressDetailResponse> listAllAddresses();

    /**
     * 根据会员ID获取地址列表
     *
     * @param memberId 会员ID
     * @return 地址列表
     */
    List<MemberAddressDetailResponse> listAddressesByMemberId(Long memberId);


    /**
     * 根据地址ID和柜子地址计算取送时间
     *
     * @param addressIdd 地址ID
     * @param cabinetId  柜子ID
     * @return 取送时间（小时）
     */
    Integer calculateDeliveryTime(Long addressIdd, Long cabinetId);


    /**
     * 根据地址ID匹配最近的可用柜子
     *
     * @param addressId 地址ID
     * @return 最近的柜子信息
     * @throws BusinessException 地址不存在或没有可用柜子时抛出异常
     */
    SmartCabinet findNearestCabinetByAddress(Long addressId);


    List<MemberAddress> listAddressesWithDeliveryTime(Long memberId);

//    这里不对 ，应该是根据柜子 获取对应的工厂地址
//    然后计算时间
}