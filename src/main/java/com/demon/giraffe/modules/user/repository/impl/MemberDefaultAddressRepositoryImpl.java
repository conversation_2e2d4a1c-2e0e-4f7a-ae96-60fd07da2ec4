package com.demon.giraffe.modules.user.repository.impl;

import com.demon.giraffe.framework.redis.service.RedisService;
import com.demon.giraffe.modules.user.repository.MemberDefaultAddressRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 会员默认地址存储实现类（基于Redis）
 */
@Repository
public class MemberDefaultAddressRepositoryImpl implements MemberDefaultAddressRepository {

    private final RedisService redisService;
    private final static String DEFAULT_ADDRESS_PREFIX = "default_address";

    @Autowired
    public MemberDefaultAddressRepositoryImpl(RedisService redisService) {
        this.redisService = redisService;
    }

    @Override
    public void saveOrUpdateDefaultAddress(Long memberId, Long addressId) {
        String key = buildKey(memberId);
        redisService.set(key, addressId);
    }

    @Override
    public Optional<Long> getDefaultAddressId(Long memberId) {
        String key = buildKey(memberId);
        Long addressId = redisService.getValue(key, Long.class);
        return Optional.ofNullable(addressId);
    }

    @Override
    public void removeDefaultAddress(Long memberId) {
        String key = buildKey(memberId);
        redisService.deleteKey(key);
    }


    @Override
    public boolean existsByMemberIdAndAddressId(Long memberId, Long addressId) {
        String key = buildKey(memberId);
        Long storedAddressId = redisService.getValue(key, Long.class);
        return storedAddressId != null && storedAddressId.equals(addressId);
    }

    /**
     * 构建Redis键
     * @param memberId 会员ID
     * @return 完整的Redis键
     */
    private String buildKey(Long memberId) {
        return DEFAULT_ADDRESS_PREFIX + ":" + memberId;
    }
}