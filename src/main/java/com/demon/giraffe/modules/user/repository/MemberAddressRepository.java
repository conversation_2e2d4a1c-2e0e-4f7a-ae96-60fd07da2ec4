package com.demon.giraffe.modules.user.repository;

import com.demon.giraffe.modules.user.model.po.MemberAddressPo;

import java.util.List;

/**
 * 会员地址信息仓储接口
 * <p>
 * 提供会员地址信息的持久化操作，包括增删改查等基本操作
 * </p>
 */
public interface MemberAddressRepository {

    /**
     * 保存会员地址信息
     *
     * @param po 会员地址持久化对象
     * @return 保存后的会员地址信息
     */
    MemberAddressPo save(MemberAddressPo po);

    /**
     * 根据ID更新会员地址信息
     *
     * @param po 会员地址持久化对象
     * @return 是否更新成功
     */
    boolean updateById(MemberAddressPo po);

    /**
     * 根据ID查询会员地址信息
     *
     * @param id 地址ID
     * @return 会员地址信息
     */
    MemberAddressPo getById(Long id);

    boolean removeById(Long id);
    /**
     * 根据ID和会员ID查询会员地址信息
     *
     * @param id       地址ID
     * @param memberId 会员ID
     * @return 会员地址信息
     * @throws BusinessException 当地址不存在或不属于该会员时抛出
     */
    MemberAddressPo findByIdAndMemberId(Long id, Long memberId);

    /**
     * 检查地址是否存在且属于指定会员
     *
     * @param id       地址ID
     * @param memberId 会员ID
     * @return 是否存在且属于该会员
     */
    boolean existsByIdAndMemberId(Long id, Long memberId);

    List<MemberAddressPo> findAllByMemberId(Long memberId);
}