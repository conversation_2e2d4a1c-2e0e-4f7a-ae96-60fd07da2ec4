package com.demon.giraffe.modules.user.model.dto.query;

import com.demon.giraffe.common.domain.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "RegionInvestorQueryRequest", description = "区域投资人查询条件")
public class RegionInvestorQueryRequest extends BasePageQuery<RegionInvestorQueryRequest> implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "关联用户ID")
    private Long appUserId;

    @Schema(description = "工厂id")
    private Long factoryId;

    @Schema(description = "投资人编号")
    private String investorNo;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系电话")
    private String contactPhone;
}