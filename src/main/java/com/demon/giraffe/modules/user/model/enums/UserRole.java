package com.demon.giraffe.modules.user.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 用户角色枚举（无权限绑定版本）
 *
 * 角色说明：
 * - 超级管理员（ROOT）：系统唯一的管理角色，负责管理整个平台。
 * - 会员用户（CUSTOMER）：访客自行升级为会员。
 * - 区域投资人（INVESTOR）：拥有所属区域柜子与工厂的管理权。
 * - 取送员（DELIVERY）：归属区域投资人管理。
 * - 洗护工（CLEANER）、工厂管理员（FACTORY_MANAGER）：归属于工厂。
 *
 * 说明：
 * - 柜子与区域投资人绑定，由系统创建。
 * - 用户下单后自动匹配最近柜子，或选择上门服务。
 * - 投资人可绑定多个工厂，订单按规则分发到对应工厂。
 */

@Getter
public enum UserRole implements IEnum<String> {
    // 系统角色
    ROOT("root", "超级管理员", 999),
    // 业务角色
    CUSTOMER("customer", "会员用户", 100),
    DELIVERY("delivery", "取送员", 200),
    CLEANER("cleaner", "洗护工", 200),
    FACTORY_MANAGER("factory_manager", "工厂管理员", 300),
    INVESTOR("investor", "区域投资人", 400),
    ;

    @EnumValue
    private final String code;
    private final String name;
    private final int priority; // 用于角色优先级比较

    private static final Map<String, UserRole> CODE_MAP = Collections.unmodifiableMap(
            Stream.of(values()).collect(Collectors.toMap(UserRole::getCode, e -> e))
    );

    UserRole(String code, String name, int priority) {
        this.code = code;
        this.name = name;
        this.priority = priority;
    }

    public static UserRole of(String code) {
        return CODE_MAP.get(code);
    }

    @Override
    public String getValue() {
        return code;
    }

    /**
     * 判断是否包含目标角色（考虑优先级）
     * @param target 目标角色
     */
    public boolean includes(UserRole target) {
        return this.priority >= target.priority;
    }
}