package com.demon.giraffe.modules.user.util;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import com.demon.giraffe.common.config.WxConfig;
import com.demon.giraffe.common.util.GsonUtil;
import com.demon.giraffe.framework.redis.service.RedisService;
import com.demon.giraffe.modules.user.model.dto.entity.WxSessionDto;
import com.demon.giraffe.modules.user.model.dto.entity.WxUserInfoDto;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.security.AlgorithmParameters;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class WxApiUtil {
    private final WxConfig wxConfig;
    private final WxMaService wxMaService;
    private final RedisService redisService;

    public WxApiUtil(WxConfig wxConfig, WxMaService wxMaService, RedisService redisService) {
        this.wxConfig = wxConfig;
        this.wxMaService = wxMaService;
        this.redisService = redisService;
    }


    public WxSessionDto getWxSession(String code) {
        String url = String.format(
                "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
                wxConfig.getAppid(), wxConfig.getSecret(), code);

        try {
            // 使用 RedisService 中封装的 doGetJson 方法
            String response = redisService.doGetJson(url);
            WxSessionDto sessionDto = GsonUtil.fromJson(response, WxSessionDto.class);

            // 如果失败，记录日志
            if (sessionDto.getErrcode() != null) {
                log.warn("获取微信 session 失败: {}, {}", sessionDto.getErrcode(), sessionDto.getErrmsg());
                return null;
            }

            // 可选：缓存结果 5 分钟
            String redisKey = "wx:session:" + sessionDto.getOpenid();
            redisService.set(redisKey, sessionDto, 300); // 单位秒
            return sessionDto;

        } catch (Exception e) {
            log.error("调用微信 jscode2session 接口失败");
            return null;
        }
    }


    /**
     * 解密微信用户信息（返回结构化对象）
     *
     * @param encryptedData 加密数据
     * @param iv            初始向量
     * @param sessionKey    会话密钥
     * @return 微信用户信息 DTO
     */
    public WxUserInfoDto decryptUserInfo(String encryptedData, String iv, String sessionKey) {
        try {
            byte[] data = safeDecodeBase64(encryptedData);
            byte[] key = safeDecodeBase64(sessionKey);
            byte[] ivBytes = safeDecodeBase64(iv);

            if (data == null || key == null || ivBytes == null) {
                throw new IllegalArgumentException("Base64 解码失败，解密数据无效");
            }

            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            AlgorithmParameters params = AlgorithmParameters.getInstance("AES");
            params.init(new IvParameterSpec(ivBytes));
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(key, "AES"), params);

            byte[] result = cipher.doFinal(data);
            String decrypted = new String(result, StandardCharsets.UTF_8);
            log.debug("微信用户信息解密结果: {}", decrypted);

            return GsonUtil.fromJson(decrypted, WxUserInfoDto.class);
        } catch (Exception e) {
            log.error("解密微信用户信息失败");
            throw new RuntimeException("解密用户信息失败", e);
        }
    }

    public WxMaPhoneNumberInfo getPhoneNumber(String code) {
        try {
            return wxMaService.getUserService().getPhoneNoInfo(code);
        } catch (WxErrorException e) {
            log.error("获取手机号失败: {}", e.getMessage());
            return null;
        }
    }

    private byte[] safeDecodeBase64(String input) {
        try {
            return Base64.getDecoder().decode(input);
        } catch (IllegalArgumentException e) {
            log.error("Base64 解码失败: {}", input, e);
            return null;
        }
    }


    private static final String CHAR_POOL = "abcdefghijklmnopqrstuvwxyz0123456789";
    private static final int LENGTH = 7;

    private static final SecureRandom RANDOM = new SecureRandom();

    /**
     * 生成格式为7位随机小写字母+数字的字符串，如：xk93f0a
     */
    public static String generateRandomNickname() {
        StringBuilder sb = new StringBuilder(LENGTH);
        for (int i = 0; i < LENGTH; i++) {
            int index = RANDOM.nextInt(CHAR_POOL.length());
            sb.append(CHAR_POOL.charAt(index));
        }
        return sb.toString();
    }

}
