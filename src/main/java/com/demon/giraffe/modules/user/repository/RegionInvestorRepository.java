package com.demon.giraffe.modules.user.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.demon.giraffe.modules.user.model.dto.query.RegionInvestorQueryRequest;
import com.demon.giraffe.modules.user.model.po.RegionInvestorPo;

import java.util.List;
import java.util.Set;

public interface RegionInvestorRepository {

    RegionInvestorPo saveOrUpdate(RegionInvestorPo po);

    boolean save(RegionInvestorPo po);

    boolean updateById(RegionInvestorPo po);

    RegionInvestorPo getById(Long id);

    IPage<RegionInvestorPo> queryPage(Page<RegionInvestorPo> page, RegionInvestorQueryRequest query);

    boolean deleteByUserId(Long userId);

    RegionInvestorPo getByAppUserId(Long appUserId);

    boolean deleteByAppUserId(Long appUserId);

    List<RegionInvestorPo> listByIds(List<Long> ids);

    List<RegionInvestorPo> listByAppUserIds(Set<Long> appUserIds);

    boolean existsById(Long id);

    boolean existsByAppUserId(Long appUserId);

    /**
     * 根据区域和投资人ID查询投资人信息
     * @param addressCode 区域编码
     * @param regionInvestorId 投资人ID
     * @return 投资人信息
     */
    RegionInvestorPo getByRegionAndCabinet(CountyEnum addressCode, Long regionInvestorId);

    /**
     * 根据区域编码查询投资人列表
     * @param addressCode 区域编码
     * @return 投资人列表
     */
    List<RegionInvestorPo> listByAddressCode(CountyEnum addressCode);

    /**
     * 批量删除投资人
     * @param ids 投资人ID列表
     * @return 是否删除成功
     */
    boolean batchDelete(List<Long> ids);
}