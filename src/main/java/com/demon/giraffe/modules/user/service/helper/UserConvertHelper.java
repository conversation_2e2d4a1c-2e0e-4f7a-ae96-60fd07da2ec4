package com.demon.giraffe.modules.user.service.helper;

import com.demon.giraffe.common.util.CodeGeneratorUtil;
import com.demon.giraffe.modules.user.Constants;
import com.demon.giraffe.modules.user.model.dto.request.WechatLoginRequest;
import com.demon.giraffe.modules.user.model.dto.response.EnhancedLoginResponse;

import com.demon.giraffe.modules.user.model.dto.response.UserResponse;
import com.demon.giraffe.modules.user.model.enums.*;
import com.demon.giraffe.modules.user.model.po.UserPo;
import com.demon.giraffe.modules.user.model.vo.UserVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

import static com.demon.giraffe.modules.user.util.WxApiUtil.generateRandomNickname;

@Component
@RequiredArgsConstructor
public class UserConvertHelper {

    private final CodeGeneratorUtil codeGeneratorUtil;

    /**
     * 构建新用户
     */
    public UserPo fromWechatLoginRequest(WechatLoginRequest.UserInfo userInfo, String openid, String phoneNumber) {
        String nickname = generateRandomNickname();
        String avatarUrl = Constants.DEFAULT_AVATAR;
        SexEnum gender = SexEnum.UNKNOWN;
        String city = Constants.DEFAULT_CITY;
        String province = Constants.DEFAULT_PROVINCE;
        String country = Constants.DEFAULT_COUNTRY;

        if (userInfo != null) {
            if (StringUtils.isNotBlank(userInfo.getNickname())) {
                nickname = userInfo.getNickname();
            }
            if (StringUtils.isNotBlank(userInfo.getAvatarUrl())) {
                avatarUrl = userInfo.getAvatarUrl();
            }
            if (userInfo.getGender() != null) {
                gender = userInfo.getGender();
            }
            if (StringUtils.isNotBlank(userInfo.getCity())) {
                city = userInfo.getCity();
            }
            if (StringUtils.isNotBlank(userInfo.getProvince())) {
                province = userInfo.getProvince();
            }
            if (StringUtils.isNotBlank(userInfo.getCountry())) {
                country = userInfo.getCountry();
            }
        }

        return UserPo.builder()
                .openid(openid)
                .unionId(null)
                .userCode(codeGeneratorUtil.generateUserCode())
                .nickname(nickname)
                .avatarUrl(avatarUrl)
                .gender(gender)
                .city(city)
                .province(province)
                .country(country)
                .phone(phoneNumber)
                .language(Constants.DEFAULT_LANGUAGE)
                .registerSource(RegisterSourceEnum.MINI_PROGRAM)
                .registerTime(LocalDateTime.now())
                .lastLoginTime(LocalDateTime.now())
                .role(UserRole.CUSTOMER)
                .loginCount(1)
                .status(UserStatus.NORMAL)
                .deviceInfo(Constants.EMPTY_DEVICE_INFO)
                .remark(Constants.DEFAULT_REMARK)
                .build();
    }



    /**
     * 转换为EnhancedLoginResponse
     */
    public EnhancedLoginResponse toEnhancedLoginResponse(UserPo user, String token) {
        EnhancedLoginResponse response = EnhancedLoginResponse.builder()
                .token(token)
                .userRole(user.getRole())
                .userCode(user.getUserCode())
                .userId(user.getId())
                .nickName(user.getNickname())
                .avatarUrl(user.getAvatarUrl())
                .gender(user.getGender())
                .build();

        response.setCurrentLoginTime();
        return response;
    }

    public EnhancedLoginResponse toEnhancedLoginResponse(UserPo user) {
        return toEnhancedLoginResponse(user, null);
    }

    /**
     * 转换为UserResponse
     */
    public UserResponse toUserResponse(UserPo user) {
        if (user == null) {
            return null;
        }

        return UserResponse.builder()
                .userId(user.getId())
                .userCode(user.getUserCode())
                .openid(user.getOpenid())
                .nickName(user.getNickname())
                .avatarUrl(user.getAvatarUrl())
                .gender(user.getGender())
                .phone(user.getPhone())
                .userRole(user.getRole())
                .status(user.getStatus())
                .city(user.getCity())
                .province(user.getProvince())
                .country(user.getCountry())
                .registerTime(user.getRegisterTime())
                .lastLoginTime(user.getLastLoginTime())
                .loginCount(user.getLoginCount())
                .build();
    }

    public UserVO convertToVO(UserPo po) {
        return UserVO.builder()
                .userId(po.getId())
                .userCode(po.getUserCode())
                .nickName(po.getNickname())
                .avatarUrl(po.getAvatarUrl())
                .gender(po.getGender())
                .phone(po.getPhone())
                .userRole(po.getRole())
                .status(po.getStatus())
                .registerTime(po.getRegisterTime())
                .lastLoginTime(po.getLastLoginTime())
                .loginCount(po.getLoginCount())
                .build();
    }
}
