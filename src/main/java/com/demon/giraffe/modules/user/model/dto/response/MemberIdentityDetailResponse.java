package com.demon.giraffe.modules.user.model.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.demon.giraffe.modules.user.model.enums.MemberLevelEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(name = "MemberIdentityDetailResponse", description = "会员身份详情响应")
public class MemberIdentityDetailResponse {


    @Schema(description = "会员ID" )
    private Long memberId;

    @Schema(description = "关联app_user用户ID")
    private Long appUserId;

    @Schema(description = "会员编号")
    private String memberNo;

    @Schema(description = "会员等级")
    private MemberLevelEnum level;

    @Schema(description = "当前可用积分")
    private Integer points;

    @Schema(description = "累计获得积分")
    private Integer totalPoints;

    @Schema(description = "累计消费金额")
    private BigDecimal totalConsumed;

    @Schema(description = "绑定手机号")
    private String phone;

    @Schema(description = "实名认证姓名")
    private String realName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}