package com.demon.giraffe.modules.qr.model.dto.response;

import com.demon.giraffe.modules.qr.model.entity.QRData;
import com.demon.giraffe.modules.qr.model.enums.QRBusinessType;
import com.demon.giraffe.modules.qr.model.enums.QrCodeStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 二维码详情响应参数
 */
@Data
@Builder
@Schema(description = "二维码详情响应参数")
public class QrCodeDetailResponse {

    @Schema(description = "二维码ID")
    private Long qrCodeId;

    @Schema(description = "业务类型")
    private QRBusinessType businessType;

    @Schema(description = "业务数据")
    private QRData businessData;

    @Schema(description = "状态")
    private QrCodeStatusEnum status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "过期时间")
    private LocalDateTime expireTime;
}