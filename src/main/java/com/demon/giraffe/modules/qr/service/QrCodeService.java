package com.demon.giraffe.modules.qr.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.demon.giraffe.modules.qr.model.dto.request.QrCodeGenerateRequest;
import com.demon.giraffe.modules.qr.model.dto.response.QrCodeGenerateResponse;
import com.demon.giraffe.modules.qr.model.po.QrCodePo;

/**
 * 二维码服务接口
 */
public interface QrCodeService extends IService<QrCodePo> {

    /**
     * 生成二维码
     * @param request 生成请求参数
     * @return 生成的二维码信息
     */
    QrCodeGenerateResponse generateQrCode(QrCodeGenerateRequest request);

    /**
     * 根据加密数据获取有效二维码
     * @param encryptedData 加密数据
     * @return 有效的二维码信息
     * @throws com.demon.giraffe.common.exception.exception.BusinessException 二维码无效或不存在时抛出
     */
    QrCodePo getValidQrCode(String encryptedData);

    /**
     * 根据业务ID获取有效二维码
     * @param businessId 业务ID
     * @return 有效的二维码信息
     * @throws com.demon.giraffe.common.exception.exception.BusinessException 二维码无效或不存在时抛出
     */
    QrCodePo getValidQrCodeByBusinessId(Long businessId);

    /**
     * 使二维码失效
     * @param qrCodeId 二维码ID
     */
    void invalidateQrCode(Long qrCodeId);

    /**
     * 标记二维码为已使用状态
     * @param qrCodeId 二维码ID
     */
    void markQrCodeAsUsed(Long qrCodeId);

    /**
     * 通过业务ID使二维码失效
     * @param businessId 业务ID
     */
    void invalidateQrCodeByBusinessId(Long businessId);

    /**
     * 通过业务ID标记二维码为已使用状态
     * @param businessId 业务ID
     */
    void markQrCodeAsUsedByBusinessId(Long businessId);

    /**
     * 检查二维码是否过期
     * @param qrCodeId 二维码ID
     * @return true-已过期 false-未过期
     */
    boolean checkAndUpdateIfExpired(Long qrCodeId);

    /**
     * 根据业务ID检查二维码是否过期
     * @param businessId 业务ID
     * @return true-已过期 false-未过期
     */
    boolean checkAndUpdateIfExpiredByBusinessId(Long businessId);
}