package com.demon.giraffe.modules.qr.service.impl;

import com.demon.giraffe.framework.minio.domain.vo.MinioUploadVo;
import com.demon.giraffe.framework.minio.util.MinioUtil;
import com.demon.giraffe.modules.qr.model.config.QRCodeConfig;
import com.demon.giraffe.modules.qr.service.IQrImageService;
import com.google.zxing.*;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.*;

/**
 * 二维码图片生成服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QrImageServiceImpl implements IQrImageService {

    private final MinioUtil minioUtil;
    private final QRCodeConfig config = QRCodeConfig.defaultConfig();

    @Override
    public MinioUploadVo generateAndUploadQRCode(String bucketName, Object content) {
        byte[] qrCodeBytes = generateQRCodeImage(content.toString(), config.getWidth(), config.getHeight(),
                config.getForegroundColor(), config.getBackgroundColor());

        String fileName = "qrcode_" + UUID.randomUUID() + ".png";
        try (ByteArrayInputStream bis = new ByteArrayInputStream(qrCodeBytes)) {
            boolean success = minioUtil.putObject(bucketName, bis, fileName, "image/png");
            if (!success) throw new IllegalStateException("上传二维码到 MinIO 失败");

            return minioUtil.getUploadMinioVo(bucketName, fileName, "png", "image/png");
        } catch (IOException e) {
            log.error("二维码上传异常");
            throw new IllegalStateException("二维码生成上传失败", e);
        }
    }

    private byte[] generateQRCodeImage(String text, int width, int height, Color foreground, Color background) {
        Map<EncodeHintType, Object> hints = Map.of(
                EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H,
                EncodeHintType.CHARACTER_SET, "UTF-8",
                EncodeHintType.MARGIN, 1
        );

        try {
            BitMatrix bitMatrix = new QRCodeWriter().encode(text, BarcodeFormat.QR_CODE, width, height, hints);
            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D g = image.createGraphics();
            g.setColor(background);
            g.fillRect(0, 0, width, height);
            g.setColor(foreground);

            for (int x = 0; x < width; x++) {
                for (int y = 0; y < height; y++) {
                    if (bitMatrix.get(x, y)) g.fillRect(x, y, 1, 1);
                }
            }

            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, "png", baos);
            return baos.toByteArray();
        } catch (Exception e) {
            log.error("二维码生成失败: {}", text);
            throw new IllegalStateException("二维码生成异常", e);
        }
    }
}
