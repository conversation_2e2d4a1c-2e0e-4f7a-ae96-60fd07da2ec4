package com.demon.giraffe.modules.qr.service.impl;

import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.common.util.TimeUtil;
import com.demon.giraffe.framework.redis.service.RedisService;
import com.demon.giraffe.framework.satoken.util.SaTokenUtil;
import com.demon.giraffe.modules.qr.model.po.QrCodePo;
import com.demon.giraffe.modules.qr.service.QrCodeService;
import com.demon.giraffe.modules.qr.service.QrService;
import com.demon.giraffe.modules.user.model.dto.response.EnhancedLoginResponse;
import com.demon.giraffe.modules.user.model.po.UserPo;
import com.demon.giraffe.modules.user.service.helper.UserConvertHelper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class QrServiceImpl implements QrService {

    private static final String QR_PREFIX = "qr:login:";

    private final RedisService redisService;
    private final QrCodeService qrCodeService;
    private final UserConvertHelper userConvertHelper;

    public QrServiceImpl(RedisService redisService, QrCodeService qrCodeService, UserConvertHelper userConvertHelper) {
        this.redisService = redisService;
        this.qrCodeService = qrCodeService;
        this.userConvertHelper = userConvertHelper;
    }


    @Override
    public Boolean scanQrCode(Long businessId) {
        //校验 二维码是否过期
        QrCodePo validQrCodeByBusinessId = qrCodeService.getValidQrCodeByBusinessId(businessId);

        //获取当前用户信息
        UserPo userPo = SaTokenUtil.getUserPo();
        String fullToken = SaTokenUtil.getFullToken();
        EnhancedLoginResponse loginResponse = userConvertHelper.toEnhancedLoginResponse(userPo, fullToken);
        //存入redis
        String redisKey = QR_PREFIX + businessId;
        //失效二维码
        qrCodeService.invalidateQrCode(validQrCodeByBusinessId.getId());
        LocalDateTime expireTime = validQrCodeByBusinessId.getExpireTime();
        long l = TimeUtil.betweenSeconds(LocalDateTime.now(), expireTime);
        return redisService.set(redisKey, loginResponse, l);
    }

    /**
     * 轮询获取扫码登录状态及用户信息
     *
     * @param businessId 二维码业务ID
     * @return 登录响应信息(包含用户信息和token)
     */
    @Override
    public EnhancedLoginResponse pollLoginStatusByBusinessId(Long businessId) {
        //校验 二维码是否过期
        if (qrCodeService.checkAndUpdateIfExpiredByBusinessId(businessId)) {
            throw new BusinessException("二维码已经过期");
        }
        EnhancedLoginResponse value = redisService.getValue(QR_PREFIX + businessId, EnhancedLoginResponse.class);
        if (value == null) {
            qrCodeService.getValidQrCodeByBusinessId(businessId);
        }
        return value;
    }

}