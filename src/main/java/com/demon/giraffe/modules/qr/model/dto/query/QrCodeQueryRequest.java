package com.demon.giraffe.modules.qr.model.dto.query;

import com.demon.giraffe.common.domain.BasePageQuery;
import com.demon.giraffe.modules.qr.model.enums.QRBusinessType;
import com.demon.giraffe.modules.qr.model.enums.QrCodeStatusEnum;
import com.demon.giraffe.modules.qr.model.po.QrCodePo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 二维码查询请求参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "二维码查询请求参数")
public class QrCodeQueryRequest extends BasePageQuery<QrCodePo> {
    @Schema(description = "业务类型")
    private QRBusinessType businessType;

    @Schema(description = "状态")
    private QrCodeStatusEnum status;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;
}