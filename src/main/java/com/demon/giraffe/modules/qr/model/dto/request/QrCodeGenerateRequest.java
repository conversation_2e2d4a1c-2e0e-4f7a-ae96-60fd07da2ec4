package com.demon.giraffe.modules.qr.model.dto.request;

import com.demon.giraffe.modules.qr.model.enums.QRBusinessType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotNull;

/**
 * 二维码生成请求参数
 */
@Data
@Schema(description = "二维码生成请求参数")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QrCodeGenerateRequest {

    @NotNull
    @Schema(description = "业务类型", required = true)
    private QRBusinessType businessType;

}