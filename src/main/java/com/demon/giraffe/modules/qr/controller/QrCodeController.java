package com.demon.giraffe.modules.qr.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.qr.model.dto.request.QrCodeGenerateRequest;
import com.demon.giraffe.modules.qr.model.dto.query.QrCodeQueryRequest;
import com.demon.giraffe.modules.qr.model.dto.response.QrCodeDetailResponse;
import com.demon.giraffe.modules.qr.model.dto.response.QrCodeGenerateResponse;
import com.demon.giraffe.modules.qr.service.QrCodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 二维码管理控制器
 */
@RestController
@RequestMapping("/qr-code/manage")
@RequiredArgsConstructor
@Tag(name = "二维码管理接口", description = "二维码生成、解析和管理接口")
public class QrCodeController {

    private final QrCodeService qrCodeService;

    //提供前 生成二维码的能力

    @PostMapping("/generate")
    @Operation(summary = "生成二维码", description = "根据业务数据生成加密二维码")
    public ResultBean<QrCodeGenerateResponse> generateQrCode(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "二维码生成请求参数",
                    required = true,
                    content = @io.swagger.v3.oas.annotations.media.Content(
                            schema = @io.swagger.v3.oas.annotations.media.Schema(
                                    implementation = QrCodeGenerateRequest.class
                            )
                    )
            )
            @RequestBody @Valid QrCodeGenerateRequest request) {
        return ResultBean.success(qrCodeService.generateQrCode(request));
    }

    //提供前端 失效二维码的能力
    @PostMapping("/invalidate/{qrCodeId}")
    @Operation(summary = "使二维码失效", description = "使指定二维码失效")
    public ResultBean<Void> invalidateQrCode(
            @Parameter(description = "二维码ID", required = true)
            @PathVariable Long qrCodeId) {
        qrCodeService.invalidateQrCode(qrCodeId);
        return ResultBean.success();
    }
}