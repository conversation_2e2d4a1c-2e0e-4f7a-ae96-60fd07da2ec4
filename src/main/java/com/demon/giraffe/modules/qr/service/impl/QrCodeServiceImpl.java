package com.demon.giraffe.modules.qr.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.demon.giraffe.common.exception.exception.BusinessException;
import com.demon.giraffe.common.util.*;
import com.demon.giraffe.framework.minio.domain.enums.MinioBucketEnum;
import com.demon.giraffe.framework.minio.domain.vo.MinioUploadVo;
import com.demon.giraffe.modules.qr.mapper.QrCodeMapper;
import com.demon.giraffe.modules.qr.model.dto.request.QrCodeGenerateRequest;
import com.demon.giraffe.modules.qr.model.dto.response.QrCodeGenerateResponse;
import com.demon.giraffe.modules.qr.model.entity.QRData;
import com.demon.giraffe.modules.qr.model.enums.QRBusinessType;
import com.demon.giraffe.modules.qr.model.enums.QrCodeStatusEnum;
import com.demon.giraffe.modules.qr.model.po.QrCodePo;
import com.demon.giraffe.modules.qr.service.QrCodeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

import static com.demon.giraffe.common.util.TimeUtil.*;

/**
 * 二维码服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QrCodeServiceImpl extends ServiceImpl<QrCodeMapper, QrCodePo> implements QrCodeService {
    private final SecurityUtils securityUtils;
    private final QrImageServiceImpl qrImageService;
    private final CodeGeneratorUtil codeGeneratorUtil;

    @Override
    @Transactional
    public QrCodeGenerateResponse generateQrCode(QrCodeGenerateRequest request) {
        // 1. 生成二维码业务数据
        long timestamp = currentTimestamp();
        QRData qrData = generateQRDataByType(request.getBusinessType(), timestamp);

        // 2. 加密业务数据
        String encryptedData = securityUtils.encryptData(GsonUtil.toJson(qrData));

        // 3. 生成二维码图片并上传到MinIO
        MinioUploadVo uploadVo = qrImageService.generateAndUploadQRCode(
                MinioBucketEnum.TEMP_UPLOAD.getCode(),
                encryptedData
        );

        // 4. 计算过期时间
        LocalDateTime expireTime = fromTimestampMillis(timestamp)
                .plusSeconds(qrData.getExpiration());

        // 5. 构建并保存二维码记录
        QrCodePo qrCodePo = QrCodePo.builder()
                .qrType(request.getBusinessType())
                .businessId(qrData.getBusinessId())  // 设置业务ID
                .encryptedData(encryptedData)
                .status(QrCodeStatusEnum.VALID)
                .qrPath(uploadVo.getProxyUrl())
                .expireTime(expireTime)
                .build();

        save(qrCodePo);

        // 6. 构建响应
        return QrCodeGenerateResponse.builder()
                .qrCodeId(qrCodePo.getId())
                .businessId(qrData.getBusinessId())  // 返回业务ID
                .qrPath(uploadVo.getProxyUrl())
                .encryptedData(encryptedData)
                .expireTime(expireTime)
                .build();
    }

    @Override
    public QrCodePo getValidQrCode(String encryptedData) {
        QrCodePo qrCodePo = lambdaQuery()
                .eq(QrCodePo::getEncryptedData, encryptedData)
                .oneOpt()
                .orElseThrow(() -> new BusinessException("二维码不存在或已失效"));

        if (qrCodePo.getStatus() != QrCodeStatusEnum.VALID ||
                qrCodePo.getExpireTime().isBefore(LocalDateTime.now())) {
            throw new BusinessException("二维码已失效");
        }
        return qrCodePo;
    }

    @Override
    public QrCodePo getValidQrCodeByBusinessId(Long businessId) {
        QrCodePo qrCodePo = lambdaQuery()
                .eq(QrCodePo::getBusinessId, businessId)
                .oneOpt()
                .orElseThrow(() -> new BusinessException("二维码不存在或已失效"));

        if (qrCodePo.getStatus() != QrCodeStatusEnum.VALID ||
                qrCodePo.getExpireTime().isBefore(LocalDateTime.now())) {
            throw new BusinessException("二维码已失效");
        }
        return qrCodePo;
    }

    @Override
    @Transactional
    public void invalidateQrCode(Long qrCodeId) {
        lambdaUpdate()
                .eq(QrCodePo::getId, qrCodeId)
                .set(QrCodePo::getStatus, QrCodeStatusEnum.INVALID)
                .update();
    }

    @Override
    @Transactional
    public void markQrCodeAsUsed(Long qrCodeId) {
        lambdaUpdate()
                .eq(QrCodePo::getId, qrCodeId)
                .set(QrCodePo::getStatus, QrCodeStatusEnum.USED)
                .update();
    }

    @Override
    public void invalidateQrCodeByBusinessId(Long businessId) {

    }

    @Override
    public void markQrCodeAsUsedByBusinessId(Long businessId) {

    }
    @Override
    @Transactional
    public boolean checkAndUpdateIfExpired(Long qrCodeId) {
        QrCodePo qrCode = getById(qrCodeId);
        if (qrCode == null) {
            throw new BusinessException("二维码不存在");
        }

        boolean isExpired = LocalDateTime.now().isAfter(qrCode.getExpireTime());
        if (isExpired && qrCode.getStatus() != QrCodeStatusEnum.INVALID) {
            qrCode.setStatus(QrCodeStatusEnum.INVALID);
            updateById(qrCode);
        }
        return isExpired;
    }

    @Override
    @Transactional
    public boolean checkAndUpdateIfExpiredByBusinessId(Long businessId) {
        QrCodePo qrCode = lambdaQuery()
                .eq(QrCodePo::getBusinessId, businessId)
                .oneOpt()
                .orElseThrow(() -> new BusinessException("二维码不存在"));

        boolean isExpired = LocalDateTime.now().isAfter(qrCode.getExpireTime());
        if (isExpired && qrCode.getStatus() != QrCodeStatusEnum.INVALID) {
            qrCode.setStatus(QrCodeStatusEnum.INVALID);
            updateById(qrCode);
        }
        return isExpired;
    }

    private QRData generateQRDataByType(QRBusinessType businessType, Long timestamp) {
        long businessId = codeGeneratorUtil.generateQrBusinessId(businessType);
        QRData qrData = new QRData();
        qrData.setTimestamp(timestamp);
        qrData.setBusinessType(businessType.getCode());
        qrData.setBusinessId(businessId);
        qrData.setUrl("/qr/" + businessType.getCode() + "/{user_role}/" + businessId);
        qrData.setExpiration(businessType.getDefaultExpiration());
        log.info("生成二维码数据: {}", qrData);
        return qrData;
    }
}