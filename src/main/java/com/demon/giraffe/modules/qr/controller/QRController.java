package com.demon.giraffe.modules.qr.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.framework.satoken.util.SaTokenUtil;
import com.demon.giraffe.modules.qr.service.QrCodeService;
import com.demon.giraffe.modules.qr.service.QrService;
import com.demon.giraffe.modules.user.model.enums.UserRole;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 处理 SERVICE 类型二维码，角色为 admin
 * 请求路径：/qr/service/admin/{businessId}
 * 扫码相关业务
 */
@RestController
@RequestMapping("/qr-code/api")
@Tag(name = "二维码扫码接口", description = "处理不同类型二维码的扫码业务")
@Slf4j
public class QRController {

    private final QrCodeService qrCodeService;
    private final QrService qrService;

    public QRController(QrCodeService qrCodeService, QrService qrService) {
        this.qrCodeService = qrCodeService;
        this.qrService = qrService;
    }

    @GetMapping("/locker/admin/{businessId}")
    @Operation(summary = "处理储物柜管理员业务", description = "处理储物柜相关的管理员扫码业务")
    public ResultBean<String> handleServiceAdmin1(
            @Parameter(name = "businessId", description = "业务ID", required = true, in = ParameterIn.PATH)
            @PathVariable Long businessId) {
        return null;
    }

    @GetMapping("/service/admin/{businessId}")
    @Operation(summary = "处理服务管理员业务", description = "处理服务相关的管理员扫码业务")
    public ResultBean<String> handleServiceAdmin2(
            @Parameter(name = "businessId", description = "业务ID", required = true, in = ParameterIn.PATH)
            @PathVariable Long businessId) {
        return null;
    }

    //这里有要求，只有服务端有的角色才可以扫码登陆 ->权限限制
    @GetMapping("/longin/{role}/{businessId}")
    @Operation(summary = "扫码登录", description = "通过扫码进行登录，只有服务端有的角色才可以扫码登陆")
    @SaCheckRole(value = { "ROOT"}, mode = SaMode.OR)
    public ResultBean<Boolean> longin(
            @Parameter(name = "role", description = "用户角色", required = true, in = ParameterIn.PATH)
            @PathVariable UserRole role,
            @Parameter(name = "businessId", description = "业务ID", required = true, in = ParameterIn.PATH)
            @PathVariable Long businessId) {
        return ResultBean.success(qrService.scanQrCode(businessId));
    }
}