package com.demon.giraffe.modules.qr.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 二维码业务类型枚举
 */
@Getter
@AllArgsConstructor
@Schema(description = "二维码业务类型")
public enum QRBusinessType implements IEnum<String> {
    LOCKER_OPEN("locker_open", "开柜", "locker", 300L),    // 默认5分钟过期
    ORDER_SCAN("order_scan", "订单扫码", "order", 1800L),  // 默认30分钟过期
    LOGIN("login", "登录", "login", 60L),                // 默认1分钟过期
    PAYMENT("payment", "支付二维码", "payment", 300L),     // 默认5分钟过期

    // 工作流程相关二维码
    ORDER_PICKUP("order_pickup", "订单取件二维码", "order_pickup", 86400L),    // 24小时过期
    ORDER_DELIVERY("order_delivery", "订单配送二维码", "order_delivery", 86400L), // 24小时过期
    FACTORY_RECEIVE("factory_receive", "工厂接收二维码", "factory_receive", 86400L), // 24小时过期
    FACTORY_EXIT("factory_exit", "工厂出库二维码", "factory_exit", 86400L),    // 24小时过期
    CLOTHING_ITEM("clothing_item", "衣物单品二维码", "clothing_item", 2592000L), // 30天过期
    PROCESS_STAGE("process_stage", "工序阶段二维码", "process_stage", 86400L);   // 24小时过期

    @EnumValue
    @Schema(description = "业务编码")
    private final String code;

    @Schema(description = "描述")
    private final String description;

    @Schema(description = "用于controller路由及数据库qr_type字段值")
    private final String service;

    @Schema(description = "默认过期时间(秒)")
    private final Long defaultExpiration;

    /**
     * 指定 MyBatis-Plus 持久化使用的值
     */
    @Override
    public String getValue() {
        return this.service;
    }

    public static QRBusinessType of(String service) {
        for (QRBusinessType type : values()) {
            if (type.getService().equalsIgnoreCase(service)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的二维码类型: " + service);
    }
}
