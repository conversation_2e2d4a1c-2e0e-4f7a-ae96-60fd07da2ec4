package com.demon.giraffe.modules.qr.model.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 二维码生成响应参数
 */
@Data
@Builder
@Schema(description = "二维码生成响应参数")
public class QrCodeGenerateResponse {

    @Schema(description = "二维码ID")
    private Long qrCodeId;

    @Schema(description = "二维码地址")
    private String qrPath;

    @Schema(description = "业务ID")
    private Long businessId;

    @Schema(description = "加密的二维码数据")
    private String encryptedData;

    @Schema(description = "过期时间")
    private LocalDateTime expireTime;
}