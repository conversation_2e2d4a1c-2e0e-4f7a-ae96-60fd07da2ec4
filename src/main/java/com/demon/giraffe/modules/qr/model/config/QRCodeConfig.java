package com.demon.giraffe.modules.qr.model.config;

import lombok.Data;

import java.awt.*;

/**
 * 二维码生成配置类
 */
@Data
public class QRCodeConfig {
    private int width = 300;
    private int height = 300;
    private Color foregroundColor = Color.BLACK;
    private Color backgroundColor = Color.WHITE;

    /**
     * 构造方法 - 保留new创建方式
     */
    public QRCodeConfig() {
    }

    /**
     * 构造方法 - 支持自定义创建
     */
    public QRCodeConfig(int width, int height, Color foregroundColor, Color backgroundColor) {
        this.width = width;
        this.height = height;
        this.foregroundColor = foregroundColor;
        this.backgroundColor = backgroundColor;
    }

    /**
     * 静态方法 - 获取默认配置实例
     * @return 默认配置的QRCodeConfig实例
     */
    public static QRCodeConfig defaultConfig() {
        return new QRCodeConfig();
    }
}