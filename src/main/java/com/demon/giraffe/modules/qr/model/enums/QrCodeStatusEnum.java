package com.demon.giraffe.modules.qr.model.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 二维码状态枚举
 */
@Getter
@AllArgsConstructor
@Schema(description = "二维码状态枚举")
public enum QrCodeStatusEnum implements IEnum<Integer> {

    INVALID(0, "已失效"),
    VALID(1, "有效"),
    USED(2, "已使用");

    private final Integer code;
    private final String description;

    @Override
    public Integer getValue() {
        return this.code;
    }

    public static QrCodeStatusEnum of(Integer code) {
        for (QrCodeStatusEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return INVALID;
    }
}
