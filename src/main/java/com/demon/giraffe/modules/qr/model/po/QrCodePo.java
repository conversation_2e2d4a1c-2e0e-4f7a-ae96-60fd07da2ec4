package com.demon.giraffe.modules.qr.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.modules.qr.model.enums.QRBusinessType;
import com.demon.giraffe.modules.qr.model.enums.QrCodeStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.io.Serial;

@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("qr_code")
@Schema(description = "二维码信息表")
public class QrCodePo extends BasePo {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 二维码类型分类(对应QRBusinessType枚举的service字段) */
    @NotNull
    @Schema(description = "二维码类型分类(对应QRBusinessType枚举的service字段)")
    private QRBusinessType qrType;

    /** 业务唯一标识 */
    @NotNull(message = "业务ID不能为空")
    @Schema(description = "业务唯一标识", example = "123456789L")
    private Long businessId;

    /** 加密的业务数据JSON(包含businessType,businessId等) */
    @NotBlank
    @TableField("encrypted_data")
    @Schema(description = "加密的业务数据JSON(包含businessType,businessId等)")
    private String encryptedData;

    /** 状态：0-已失效，1-有效，2-已使用 */
    @NotNull
    @TableField(value = "status", fill = FieldFill.INSERT)
    @Schema(description = "状态：0-已失效，1-有效，2-已使用", defaultValue = "1")
    private QrCodeStatusEnum status;

    /** 过期时间 */
    @NotNull
    @TableField("expire_time")
    @Schema(description = "过期时间")
    private java.time.LocalDateTime expireTime;

    /** 加密的业务数据JSON打包为二维码 保存的图片路径 */
    @NotBlank
    @TableField("qr_path")
    @Schema(description = "加密的业务数据JSON打包为二维码 保存的图片路径")
    private String qrPath;
}