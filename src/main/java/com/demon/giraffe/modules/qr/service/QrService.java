package com.demon.giraffe.modules.qr.service;

import com.demon.giraffe.modules.user.model.dto.response.EnhancedLoginResponse;
import com.demon.giraffe.modules.user.model.enums.UserRole;

/**
 * 扫码登陆服务
 */
public interface QrService {


    /**
     * 此接口处理扫码登录逻辑，关键行为：
     * 1.校验二维码是否有效（未过期 + 状态有效）
     * 2.获取当前登录用户信息 + token
     * 3.构造 EnhancedLoginResponse，并存入 Redis
     * 4.使该二维码失效
     * 5.设置 Redis 过期时间（为二维码剩余时间）
     *
     * @param businessId 业务ID
     * @return 是否扫码成功
     */
    Boolean scanQrCode(Long businessId);


    /**
     * 轮询获取扫码登录状态及用户信息
     *
     * @param businessId 二维码业务ID
     * @return 登录响应信息(包含用户信息和token)
     */
    EnhancedLoginResponse pollLoginStatusByBusinessId(Long businessId);


}