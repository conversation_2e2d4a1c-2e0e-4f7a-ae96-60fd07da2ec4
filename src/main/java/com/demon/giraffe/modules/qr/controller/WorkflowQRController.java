package com.demon.giraffe.modules.qr.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.laundry.service.FactoryWorkflowService;
import com.demon.giraffe.modules.order.service.OrderTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工作流程二维码扫描控制器
 * 处理订单处理流程中的各种二维码扫描业务
 */
@Slf4j
@RestController
@RequestMapping("/api/qr/workflow")
@RequiredArgsConstructor
@Tag(name = "工作流程二维码扫描", description = "处理订单流程中的二维码扫描业务")
public class WorkflowQRController {

    private final OrderTaskService orderTaskService;
    private final FactoryWorkflowService factoryWorkflowService;

    @PostMapping("/factory/receive/{qrCodeId}")
    @Operation(summary = "工厂扫码接收订单", description = "工厂扫描订单二维码，确认接收订单")
    @SaCheckRole(value = {"ROOT", "FACTORY"}, mode = SaMode.OR)
    public ResultBean<Boolean> factoryReceiveOrder(
            @Parameter(description = "订单二维码ID", required = true)
            @PathVariable Long qrCodeId,
            @Parameter(description = "工厂ID", required = true)
            @RequestParam Long factoryId,
            @Parameter(description = "操作员ID", required = true)
            @RequestParam Long operatorId) {
        try {
            Boolean success = orderTaskService.factoryReceiveOrder(qrCodeId, factoryId, operatorId);
            return ResultBean.success(success);
        } catch (Exception e) {
            log.error("工厂接收订单失败，二维码ID：{}，工厂ID：{}，操作员ID：{}", qrCodeId, factoryId, operatorId, e);
            return ResultBean.fail("工厂接收订单失败：" + e.getMessage());
        }
    }

    @PostMapping("/factory/start-unpacking/{taskId}")
    @Operation(summary = "开始拆包", description = "工厂开始拆包订单")
    @SaCheckRole(value = {"ROOT", "FACTORY"}, mode = SaMode.OR)
    public ResultBean<Boolean> startUnpacking(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Long taskId,
            @Parameter(description = "操作员ID", required = true)
            @RequestParam Long operatorId) {
        try {
            Boolean success = orderTaskService.startUnpacking(taskId, operatorId);
            return ResultBean.success(success);
        } catch (Exception e) {
            log.error("开始拆包失败，任务ID：{}，操作员ID：{}", taskId, operatorId, e);
            return ResultBean.fail("开始拆包失败：" + e.getMessage());
        }
    }

    @PostMapping("/factory/complete-unpacking/{taskId}")
    @Operation(summary = "完成拆包并生成衣物二维码", description = "完成拆包，为每件衣物生成二维码")
    @SaCheckRole(value = {"ROOT", "FACTORY"}, mode = SaMode.OR)
    public ResultBean<List<Long>> completeUnpackingAndGenerateClothingQR(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Long taskId,
            @Parameter(description = "操作员ID", required = true)
            @RequestParam Long operatorId,
            @Parameter(description = "衣物清单", required = true)
            @RequestBody List<String> clothingItems) {
        try {
            List<Long> qrCodeIds = orderTaskService.completeUnpackingAndGenerateClothingQR(taskId, operatorId, clothingItems);
            return ResultBean.success(qrCodeIds);
        } catch (Exception e) {
            log.error("完成拆包失败，任务ID：{}，操作员ID：{}", taskId, operatorId, e);
            return ResultBean.fail("完成拆包失败：" + e.getMessage());
        }
    }

    @PostMapping("/clothing/start-process/{clothingQrCodeId}")
    @Operation(summary = "员工扫码开始处理衣物", description = "员工扫描衣物二维码，开始处理流程")
    @SaCheckRole(value = {"ROOT", "FACTORY"}, mode = SaMode.OR)
    public ResultBean<Boolean> startClothingProcess(
            @Parameter(description = "衣物二维码ID", required = true)
            @PathVariable Long clothingQrCodeId,
            @Parameter(description = "操作员ID", required = true)
            @RequestParam Long operatorId,
            @Parameter(description = "处理阶段：1-清洗 2-烘干 3-熨烫", required = true)
            @RequestParam Integer processStage) {
        try {
            Boolean success = factoryWorkflowService.startClothingProcess(clothingQrCodeId, operatorId, processStage);
            return ResultBean.success(success);
        } catch (Exception e) {
            log.error("开始处理衣物失败，衣物二维码ID：{}，操作员ID：{}，处理阶段：{}", clothingQrCodeId, operatorId, processStage, e);
            return ResultBean.fail("开始处理衣物失败：" + e.getMessage());
        }
    }

    @PostMapping("/clothing/complete-process/{clothingQrCodeId}")
    @Operation(summary = "员工扫码完成处理阶段", description = "员工扫描衣物二维码，完成当前处理阶段")
    @SaCheckRole(value = {"ROOT", "FACTORY"}, mode = SaMode.OR)
    public ResultBean<Boolean> completeClothingProcess(
            @Parameter(description = "衣物二维码ID", required = true)
            @PathVariable Long clothingQrCodeId,
            @Parameter(description = "操作员ID", required = true)
            @RequestParam Long operatorId,
            @Parameter(description = "处理阶段：1-清洗 2-烘干 3-熨烫", required = true)
            @RequestParam Integer processStage) {
        try {
            Boolean success = factoryWorkflowService.completeClothingProcess(clothingQrCodeId, operatorId, processStage);
            return ResultBean.success(success);
        } catch (Exception e) {
            log.error("完成处理阶段失败，衣物二维码ID：{}，操作员ID：{}，处理阶段：{}", clothingQrCodeId, operatorId, processStage, e);
            return ResultBean.fail("完成处理阶段失败：" + e.getMessage());
        }
    }

    @PostMapping("/factory/start-quality-check/{taskId}")
    @Operation(summary = "开始质检", description = "开始对订单进行质检")
    @SaCheckRole(value = {"ROOT", "FACTORY"}, mode = SaMode.OR)
    public ResultBean<Boolean> startQualityCheck(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Long taskId,
            @Parameter(description = "质检员ID", required = true)
            @RequestParam Long qualityCheckerId) {
        try {
            Boolean success = orderTaskService.startQualityCheck(taskId, qualityCheckerId);
            return ResultBean.success(success);
        } catch (Exception e) {
            log.error("开始质检失败，任务ID：{}，质检员ID：{}", taskId, qualityCheckerId, e);
            return ResultBean.fail("开始质检失败：" + e.getMessage());
        }
    }

    @PostMapping("/factory/complete-quality-check/{taskId}")
    @Operation(summary = "完成质检", description = "完成订单质检")
    @SaCheckRole(value = {"ROOT", "FACTORY"}, mode = SaMode.OR)
    public ResultBean<Boolean> completeQualityCheck(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Long taskId,
            @Parameter(description = "质检员ID", required = true)
            @RequestParam Long qualityCheckerId,
            @Parameter(description = "质检是否通过", required = true)
            @RequestParam Boolean qualityPassed,
            @Parameter(description = "质检备注")
            @RequestParam(required = false) String qualityNotes) {
        try {
            Boolean success = orderTaskService.completeQualityCheck(taskId, qualityCheckerId, qualityPassed, qualityNotes);
            return ResultBean.success(success);
        } catch (Exception e) {
            log.error("完成质检失败，任务ID：{}，质检员ID：{}", taskId, qualityCheckerId, e);
            return ResultBean.fail("完成质检失败：" + e.getMessage());
        }
    }

    @PostMapping("/factory/complete-packaging/{taskId}")
    @Operation(summary = "完成打包并生成出厂二维码", description = "完成订单打包，生成出厂二维码")
    @SaCheckRole(value = {"ROOT", "FACTORY"}, mode = SaMode.OR)
    public ResultBean<Long> completePackagingAndGenerateExitQR(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Long taskId,
            @Parameter(description = "操作员ID", required = true)
            @RequestParam Long operatorId) {
        try {
            Long qrCodeId = orderTaskService.completePackagingAndGenerateExitQR(taskId, operatorId);
            return ResultBean.success(qrCodeId);
        } catch (Exception e) {
            log.error("完成打包失败，任务ID：{}，操作员ID：{}", taskId, operatorId, e);
            return ResultBean.fail("完成打包失败：" + e.getMessage());
        }
    }

    @PostMapping("/delivery/pickup/{qrCodeId}")
    @Operation(summary = "送货员扫码取件", description = "送货员扫描出厂二维码，从工厂取件")
    @SaCheckRole(value = {"ROOT", "DELIVERY"}, mode = SaMode.OR)
    public ResultBean<Boolean> deliveryCourierPickup(
            @Parameter(description = "出厂二维码ID", required = true)
            @PathVariable Long qrCodeId,
            @Parameter(description = "快递员ID", required = true)
            @RequestParam Long courierId) {
        try {
            Boolean success = orderTaskService.deliveryCourierPickup(qrCodeId, courierId);
            return ResultBean.success(success);
        } catch (Exception e) {
            log.error("送货员取件失败，二维码ID：{}，快递员ID：{}", qrCodeId, courierId, e);
            return ResultBean.fail("送货员取件失败：" + e.getMessage());
        }
    }

    @PostMapping("/delivery/start/{taskId}")
    @Operation(summary = "开始配送", description = "快递员开始配送订单")
    @SaCheckRole(value = {"ROOT", "DELIVERY"}, mode = SaMode.OR)
    public ResultBean<Boolean> startDelivery(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Long taskId,
            @Parameter(description = "快递员ID", required = true)
            @RequestParam Long courierId) {
        try {
            Boolean success = orderTaskService.startDelivery(taskId, courierId);
            return ResultBean.success(success);
        } catch (Exception e) {
            log.error("开始配送失败，任务ID：{}，快递员ID：{}", taskId, courierId, e);
            return ResultBean.fail("开始配送失败：" + e.getMessage());
        }
    }

    @PostMapping("/delivery/complete/{taskId}")
    @Operation(summary = "完成配送", description = "快递员完成订单配送")
    @SaCheckRole(value = {"ROOT", "DELIVERY"}, mode = SaMode.OR)
    public ResultBean<Boolean> completeDelivery(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Long taskId,
            @Parameter(description = "快递员ID", required = true)
            @RequestParam Long courierId,
            @Parameter(description = "配送方式：1-上门 2-智能柜", required = true)
            @RequestParam Integer deliveryType,
            @Parameter(description = "智能柜ID（如果使用智能柜）")
            @RequestParam(required = false) Long cabinetId,
            @Parameter(description = "格口号（如果使用智能柜）")
            @RequestParam(required = false) String cellNo) {
        try {
            Boolean success = orderTaskService.completeDelivery(taskId, courierId, deliveryType, cabinetId, cellNo);
            return ResultBean.success(success);
        } catch (Exception e) {
            log.error("完成配送失败，任务ID：{}，快递员ID：{}", taskId, courierId, e);
            return ResultBean.fail("完成配送失败：" + e.getMessage());
        }
    }

    @PostMapping("/assign-delivery-courier/{taskId}")
    @Operation(summary = "分配送货快递员", description = "自动为完成的订单分配送货快递员")
    @SaCheckRole(value = {"ROOT", "ADMIN", "FACTORY"}, mode = SaMode.OR)
    public ResultBean<Boolean> assignDeliveryCourier(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Long taskId) {
        try {
            Boolean success = orderTaskService.assignDeliveryCourier(taskId);
            return ResultBean.success(success);
        } catch (Exception e) {
            log.error("分配送货快递员失败，任务ID：{}", taskId, e);
            return ResultBean.fail("分配送货快递员失败：" + e.getMessage());
        }
    }

    @PostMapping("/assign-delivery-courier/{taskId}/{courierId}")
    @Operation(summary = "手动分配送货快递员", description = "手动为订单分配指定的送货快递员")
    @SaCheckRole(value = {"ROOT", "ADMIN", "FACTORY"}, mode = SaMode.OR)
    public ResultBean<Boolean> assignDeliveryCourier(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Long taskId,
            @Parameter(description = "快递员ID", required = true)
            @PathVariable Long courierId) {
        try {
            Boolean success = orderTaskService.assignDeliveryCourier(taskId, courierId);
            return ResultBean.success(success);
        } catch (Exception e) {
            log.error("手动分配送货快递员失败，任务ID：{}，快递员ID：{}", taskId, courierId, e);
            return ResultBean.fail("手动分配送货快递员失败：" + e.getMessage());
        }
    }
}
