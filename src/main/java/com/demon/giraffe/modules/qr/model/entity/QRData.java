package com.demon.giraffe.modules.qr.model.entity;


import com.demon.giraffe.modules.qr.model.enums.QRBusinessType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * 二维码数据载体类
 *
 * <p>用于封装二维码携带的所有信息，支持泛型业务数据，包含完整的安全控制和版本管理</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-12-01
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QRData {

    /**
     * 业务类型分类
     * <p>
     * 示例: "LOCKER_OPEN"
     * @see QRBusinessType getservice
     */
    @NotNull(message = "业务类型不能为空")
    private String businessType;

    /**
     * 业务唯一标识
     * <p>
     * 示例: 123456789L
     */
    @NotNull(message = "业务ID不能为空")
    private Long businessId;

    /**
     * 二维码生成时间戳（UTC 秒）
     * <p>
     * 示例: 1672531200
     */
    @NotNull(message = "时间戳不能为空")
    @Positive(message = "时间戳必须为正数")
    private Long timestamp;

    /**
     * 有效期限（秒）
     * <p>
     * 默认: 300，示例: 600
     */
    private Long expiration ;

    /**
     * 请求参数: /qr/{businessType}/{user_role}/{businessId}
     */
    private String url;
}
