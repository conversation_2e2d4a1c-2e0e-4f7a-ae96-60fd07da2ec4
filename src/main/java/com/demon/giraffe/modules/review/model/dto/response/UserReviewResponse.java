package com.demon.giraffe.modules.review.model.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户评价响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户评价响应", example = """
    {
        "id": 1,
        "maskedUserName": "张*明",
        "userAvatarUrl": "/api-storage/inherent/profile_photo/customer/user_avatar (1).png",
        "imageUrls": ["https://example.com/img1.jpg", "https://example.com/img2.jpg"],
        "rating": 9,
        "content": "服务非常好，衣服洗得很干净，师傅态度也很好！",
        "isRepeatCustomer": true,
        "isAnonymous": false,
        "tagsString": "超级满意, 干净卫生, 服务态度好",
        "itemName": "洗护服务",
        "createTime": "2024-01-15T10:30:00"
    }
    """)
public class UserReviewResponse {

    @Schema(description = "评价ID", example = "1")
    private Long id;

    @Schema(description = "遮挡后的用户名称", example = "张*明")
    private String maskedUserName;

    @Schema(description = "用户头像URL", example = "/api-storage/inherent/profile_photo/customer/user_avatar (1).png")
    private String userAvatarUrl;

    @Schema(description = "配图集合", example = "[\"https://example.com/img1.jpg\", \"https://example.com/img2.jpg\"]")
    private List<String> imageUrls;

    @Schema(description = "评分（1-10分）", example = "9", minimum = "1", maximum = "10")
    private Integer rating;

    @Schema(description = "评价内容", example = "服务非常好，衣服洗得很干净，师傅态度也很好！")
    private String content;

    @Schema(description = "是否回头客", example = "true")
    private Boolean isRepeatCustomer;

    @Schema(description = "是否匿名评价", example = "false")
    private Boolean isAnonymous;

    @Schema(description = "标签字符串（逗号分隔）", example = "超级满意, 干净卫生, 服务态度好")
    private List<String> tagsString;

    @Schema(description = "评价对象名称", example = "洗护服务")
    private String itemName;

    @Schema(description = "评价时间", example = "2024-01-15T10:30:00")
    private LocalDateTime createTime;
}
