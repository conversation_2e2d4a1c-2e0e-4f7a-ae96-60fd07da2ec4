package com.demon.giraffe.modules.review.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.demon.giraffe.framework.mybatis.base.BasePo;
import com.demon.giraffe.framework.mybatis.typehandler.ReviewTagEnumListTypeHandler;

import com.demon.giraffe.framework.mybatis.typehandler.StringArrayListTypeHandler;
import com.demon.giraffe.modules.review.model.enums.ReviewTagEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 用户评价表PO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("user_review")
@Schema(description = "用户评价实体")
public class UserReviewPo extends BasePo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "会员ID不能为空")
    @TableField("member_id")
    @Schema(description = "会员ID（评论人）")
    private Long memberId;

    @NotNull(message = "订单ID不能为空")
    @TableField("order_id")
    @Schema(description = "关联订单ID")
    private Long orderId;

    @TableField("item_id")
    @Schema(description = "服务ID")
    private Long itemId;

    @TableField("item_name")
    @Schema(description = "评价对象名称，用于展示")
    private String itemName;


    @NotNull(message = "评分不能为空")
    @Min(value = 1, message = "评分最低为1分")
    @Max(value = 10, message = "评分最高为10分")
    @TableField("rating")
    @Schema(description = "评分（1-10分）")
    private Integer rating;

    @TableField("content")
    @Schema(description = "评价内容")
    private String content;



    @TableField(value = "image_urls", typeHandler = StringArrayListTypeHandler.class)
    @Schema(description = "配图列表")
    private List<String> imageUrls;

    @TableField(value = "tags", typeHandler = ReviewTagEnumListTypeHandler.class)
    @Schema(description = "评价标签")
    private List<ReviewTagEnum> tags;

    @TableField("is_repeat_customer")
    @Schema(description = "是否回头客（true-是，false-否）")
    private Boolean isRepeatCustomer;

    @TableField("is_anonymous")
    @Schema(description = "是否匿名评价（true-是，false-否）")
    private Boolean isAnonymous;
}
