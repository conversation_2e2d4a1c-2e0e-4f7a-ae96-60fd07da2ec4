package com.demon.giraffe.modules.review.service.impl;

import com.demon.giraffe.modules.review.model.dto.response.UserReviewResponse;

import com.demon.giraffe.modules.review.model.enums.ReviewTagEnum;
import com.demon.giraffe.modules.review.model.po.UserReviewPo;
import com.demon.giraffe.modules.review.repository.UserReviewRepository;
import com.demon.giraffe.modules.review.service.UserReviewService;
import com.demon.giraffe.modules.user.Constants;
import com.demon.giraffe.modules.user.model.dto.response.MemberIdentityDetailResponse;
import com.demon.giraffe.modules.user.model.po.UserPo;
import com.demon.giraffe.modules.user.repository.UserRepository;
import com.demon.giraffe.modules.user.service.MemberIdentityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户评价服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserReviewServiceImpl implements UserReviewService {

    private final UserReviewRepository userReviewRepository;
    private final UserRepository userRepository;
    private final MemberIdentityService memberIdentityService;

    @Override
    public List<UserReviewResponse> getReviewsByItemId(Long itemId, Integer limit) {
        List<UserReviewPo> reviews = userReviewRepository.findByItemId(itemId, limit);
        return convertToResponseBatch(reviews);
    }

    /**
     * 批量转换为响应DTO列表（优化版本，避免N+1查询）
     */
    private List<UserReviewResponse> convertToResponseBatch(List<UserReviewPo> reviews) {
        if (reviews == null || reviews.isEmpty()) {
            return Collections.emptyList();
        }

        // 1. 收集所有需要查询的会员ID
        Set<Long> memberIds = reviews.stream()
                .map(UserReviewPo::getMemberId)
                .collect(Collectors.toSet());

        // 2. 批量查询会员身份信息
        Map<Long, MemberIdentityDetailResponse> memberIdentityMap =
                memberIdentityService.getMemberIdentitiesByUserIds(memberIds);

        // 3. 收集所有需要查询的用户ID
        Set<Long> userIds = memberIdentityMap.values().stream()
                .map(MemberIdentityDetailResponse::getAppUserId)
                .collect(Collectors.toSet());

        // 4. 批量查询用户信息
        Map<Long, UserPo> userMap = userRepository.listByIds(new ArrayList<>(userIds))
                .stream()
                .collect(Collectors.toMap(UserPo::getId, user -> user));

        // 5. 批量转换
        return reviews.stream()
                .map(review -> convertToResponseWithCache(review, memberIdentityMap, userMap))
                .collect(Collectors.toList());
    }

    /**
     * 使用缓存数据转换单个评价为响应DTO
     */
    private UserReviewResponse convertToResponseWithCache(
            UserReviewPo review,
            Map<Long, MemberIdentityDetailResponse> memberIdentityMap,
            Map<Long, UserPo> userMap) {

        // 获取会员身份信息
        MemberIdentityDetailResponse memberIdentityDetail = memberIdentityMap.get(review.getMemberId());

        // 获取用户信息
        UserPo user = null;
        if (memberIdentityDetail != null) {
            user = userMap.get(memberIdentityDetail.getAppUserId());
        }

        // 处理用户名显示逻辑
        String displayName;
        String avatarUrl;

        if (user == null) {
            // 用户不存在，使用默认匿名信息
            displayName = Constants.DEFAULT_NICKNAME;
            avatarUrl = Constants.DEFAULT_AVATAR;
        } else if (Boolean.TRUE.equals(review.getIsAnonymous())) {
            // 匿名评价，直接显示匿名用户，不遮挡
            displayName = Constants.DEFAULT_NICKNAME;
            avatarUrl = Constants.DEFAULT_AVATAR;
        } else {
            // 非匿名评价，使用真实用户信息并遮挡用户名
            displayName = maskUserName(user.getNickname());
            avatarUrl = user.getAvatarUrl();
        }

        return UserReviewResponse.builder()
                .id(review.getId())
                .maskedUserName(displayName)
                .userAvatarUrl(avatarUrl)
                .imageUrls(review.getImageUrls())
                .rating(review.getRating())
                .content(review.getContent())
                .isRepeatCustomer(review.getIsRepeatCustomer())
                .isAnonymous(review.getIsAnonymous())
                .tagsString(convertTagsToString(review.getTags()))
                .itemName(review.getItemName())
                .createTime(review.getCreateTime())
                .build();
    }


    /**
     * 遮挡用户名称
     */
    private String maskUserName(String userName) {
        if (userName == null || userName.length() <= 1) {
            return userName;
        }

        if (userName.length() == 2) {
            return userName.charAt(0) + "*";
        }

        // 保留第一个字符，中间用*代替，保留最后一个字符
        StringBuilder masked = new StringBuilder();
        masked.append(userName.charAt(0));
        for (int i = 1; i < userName.length() - 1; i++) {
            masked.append("*");
        }
        masked.append(userName.charAt(userName.length() - 1));

        return masked.toString();
    }

    /**
     * 将标签枚举转换为字符串
     */
    /**
     * 将 tags 列表（可能是 Enum，也可能是 String）统一转换为中文描述
     */
    private List<String> convertTagsToString(List<?> tags) {
        if (tags == null || tags.isEmpty()) {
            return Collections.emptyList();
        }

        return tags.stream()
                .map(tag -> {
                    if (tag instanceof ReviewTagEnum enumVal) {
                        return enumVal.getDesc();
                    } else if (tag instanceof String strVal) {
                        try {
                            return ReviewTagEnum.valueOf(strVal).getDesc();
                        } catch (IllegalArgumentException e) {
                            return strVal; // 兜底返回原字符串
                        }
                    } else {
                        return String.valueOf(tag); // 再兜底
                    }
                })
                .toList();
    }



}
