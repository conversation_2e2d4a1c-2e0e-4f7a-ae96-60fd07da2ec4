package com.demon.giraffe.modules.review.controller;

import com.demon.giraffe.common.domain.ResultBean;
import com.demon.giraffe.modules.review.model.dto.response.UserReviewResponse;
import com.demon.giraffe.modules.review.service.UserReviewService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户评价控制器
 */
@RestController
@RequestMapping("/review")
@RequiredArgsConstructor
@Tag(name = "用户评价管理", description = "用户评价查询和管理接口")
public class UserReviewController {

    private final UserReviewService userReviewService;

    @GetMapping("/item/{itemId}")
    @Operation(
            summary = "根据服务ID获取评价列表",
            description = "根据指定的服务ID获取相关的用户评价列表"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "404", description = "评价项不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public ResultBean<List<UserReviewResponse>> getReviewsByItemId(
            @Parameter(description = "服务ID", example = "1") @PathVariable Long itemId,
            @Parameter(description = "返回数量限制", example = "10")
            @RequestParam(defaultValue = "10") Integer limit) {

        List<UserReviewResponse> reviews = userReviewService.getReviewsByItemId(itemId, limit);
        return ResultBean.success(reviews);
    }


}
