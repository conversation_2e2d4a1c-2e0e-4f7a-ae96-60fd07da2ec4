package com.demon.giraffe.modules.review.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * 评价标签枚举
 */
@Getter
@Schema(description = "评价标签枚举", example = "SUPER_SATISFIED")
public enum ReviewTagEnum implements IEnum<Integer> {
    
    @Schema(description = "超级满意")
    SUPER_SATISFIED(1, "超级满意"),
    
    @Schema(description = "洗的很快")
    WASH_FAST(2, "洗的很快"),
    
    @Schema(description = "值得推荐")
    WORTH_RECOMMEND(3, "值得推荐"),
    
    @Schema(description = "愿意回购")
    WILLING_REPURCHASE(4, "愿意回购"),
    
    @Schema(description = "干净卫生")
    CLEAN_HYGIENIC(5, "干净卫生"),
    
    @Schema(description = "服务态度好")
    GOOD_SERVICE(6, "服务态度好"),
    
    @Schema(description = "价格实惠")
    AFFORDABLE_PRICE(7, "价格实惠"),
    
    @Schema(description = "包装精美")
    BEAUTIFUL_PACKAGING(8, "包装精美"),
    
    @Schema(description = "送货及时")
    TIMELY_DELIVERY(9, "送货及时"),
    
    @Schema(description = "质量很好")
    GOOD_QUALITY(10, "质量很好"),
    
    @Schema(description = "专业技术")
    PROFESSIONAL_SKILL(11, "专业技术"),
    
    @Schema(description = "环保健康")
    ECO_FRIENDLY(12, "环保健康");

    @EnumValue
    private final Integer code;
    private final String desc;

    ReviewTagEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getValue() {
        return code;
    }

    /**
     * 根据代码获取枚举
     */
    public static ReviewTagEnum of(Integer code) {
        for (ReviewTagEnum tag : values()) {
            if (tag.code.equals(code)) {
                return tag;
            }
        }
        throw new IllegalArgumentException("无效的评价标签代码: " + code);
    }
}
