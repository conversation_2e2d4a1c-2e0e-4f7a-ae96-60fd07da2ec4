package com.demon.giraffe.modules.review.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.demon.giraffe.modules.review.mapper.UserReviewMapper;
import com.demon.giraffe.modules.review.model.po.UserReviewPo;
import com.demon.giraffe.modules.review.repository.UserReviewRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户评价Repository实现类（使用Wrapper实现）
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class UserReviewRepositoryImpl implements UserReviewRepository {

    private final UserReviewMapper userReviewMapper;


    @Override
    public List<UserReviewPo> findByItemId(Long itemId, Integer limit) {
        LambdaQueryWrapper<UserReviewPo> wrapper = Wrappers.lambdaQuery(UserReviewPo.class)
                .eq(UserReviewPo::getItemId, itemId)
                .orderByDesc(UserReviewPo::getCreateTime)
                .last(limit != null ? "LIMIT " + limit : "");
        return userReviewMapper.selectList(wrapper);
    }

}