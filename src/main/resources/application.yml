# 应用基础配置
server:
  port: ${SERVICE_PORT:8080}
  # 服务器域名（用于生成微信支付回调地址）
  domain: https://www.giraffewash.com
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
# Spring基础配置
spring:
  application:
    name: giraffe-wash
  profiles:
    active: dev-local
  main:
    allow-bean-definition-overriding: true
  # 数据源配置（MySQL）
  datasource:
    url: jdbc:mysql://${MYSQL_HOST}:3306/${MYSQL_DATABASE}?useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    username: ${MYSQL_USER}
    password: ${MYSQL_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=2000
  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST}
      port: 6379
      password: ${REDIS_PASSWORD}
      database: ${REDIS_DATABASE}
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 2
  # Spring Cache配置
  cache:
    type: redis
    redis:
      time-to-live: 1800000  # 默认过期时间30分钟（毫秒）
      cache-null-values: false  # 不缓存null值
      key-prefix: "giraffe:cache:"  # 缓存key前缀
      use-key-prefix: true

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mappers/**/*Mapper.xml
  type-aliases-package: com.giraffe.modules.*.model
  type-handlers-package: com.demon.giraffe.framework.mybatis.typehandler
  configuration:
#    default-executor-type: batch # 启用批量模式
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      id-type: auto
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui/index.html
    tags-sorter: alpha
    operations-sorter: alpha
knife4j:
  enable: true
  setting:
    language: zh-CN
  basic:
    enable: false  # ✅ 不启用访问密码保护
minio:
  endpoint: http://${MINIO_HOST}:9000
  access-key: ${MINIO_ROOT_USER}
  secret-key: ${MINIO_ROOT_PASSWORD}
  domain: ${MINIO_DOMAIN} # 可选：自定义访问域名（服务端配置用）
sa-token:
  token-style: jwt
  is-print: false
  is-log: false
  jwt-secret-key: giraffeSecretKey987 # 自定义你的密钥
  # token 有效期（单位：秒），默认30天，-1代表永不过期
  timeout: 28800
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
#  弃用
user:
  password:
    check:
      key:
        private: ${PRIVATE_KEY:PRIVATE_KEY}
        public: ${PUBLIC_KEY:PRIVATE_KEY}
#  数据库文件加密保存
security:
  aes:
    key: ${SECURITY_AES_KEY} # 16/24/32字节长度的密钥
    iv: ${SECURITY_AES_IV}  # 16字节长度的初始化向量
wechat:
  appid: ${WECHAT_APP_ID}
  secret: ${WECHAT_SECRET}
  #token: ${WECHAT_TOKEN}
  #aes-key: ${WECHAT_AES_KEY}
  # 微信支付商户号
  merchant-id: ${WECHAT_MERCHANT_ID}
  # 商户证书序列号（40位十六进制字符串）
  merchant-serial-number: ${WECHAT_MERCHANT_SERIAL_NUMBER}
  # 商户私钥文件路径
  private-key-path: ${WECHAT_PRIVATE_KEY_PATH}
  # API v3 密钥（32位字符串）
  api-v3-key: ${WECHAT_API_V3_KEY}
  # API v2 密钥（32位字符串）
  api-v2-key: ${WECHAT_API_V2_KEY}
  # p12文件路径
  key-path: ${WECHAT_KEY_PATH}
  # 公钥路径
  public-key-path: ${WECHAT_PUBLIC_KEY_PATH}
  #公钥ID
  public-key-id: ${WECHAT_PUBLIC_KEY_ID}
  #双向认证
  private-cert-path: ${WECHAT_PRIVATE_CERT_PATH}

# 自定义业务配置
giraffe:
  auth:
    token-expire: 7200 # 2小时
    sms:
      enable: true
      template-id: SMS_123456
  order:
    auto-cancel-minutes: 30
  cabinet:
    bluetooth:
      timeout: 10000 # 蓝牙操作超时(ms)
# 高德地图配置
amap:
  api-key: ${AMAP_API_KEY} # 默认值，建议从环境变量获取
#  腾讯短信服务
tencent:
  cloud:
    secretId: ${TENCENT_CLOUD_SECRET_ID}
    secretKey: ${TENCENT_CLOUD_SECRET_KEY}
    sms:
      region: ap-guangzhou
      sdkAppId: 1400000000  # 短信应用ID
      signName: 腾讯云      # 短信签名内容
