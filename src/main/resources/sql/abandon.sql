
--  地域价格差异表
CREATE TABLE `service_region_price`
(
    -- 主键
    `id`                 bigint        NOT NULL AUTO_INCREMENT COMMENT '定价主键ID',

    -- 业务字段
    `service_id`         bigint        NOT NULL COMMENT '服务项目ID',
    `address_code`       INT           NOT NULL COMMENT '区域编码',
    `region_investor_id` BIGINT                  DEFAULT 0 COMMENT '投资人ID(0为root)',
    `price`              decimal(8, 2) NOT NULL COMMENT '区域价格',
    `rush_fee`           decimal(8, 2)          DEFAULT 0.00 COMMENT '区域加急费',
    `effective_time`     datetime      NOT NULL COMMENT '生效时间',
    `expire_time`        datetime COMMENT '失效时间',
    `status`             tinyint       NOT NULL DEFAULT 0 COMMENT '状态(0正常/1停用)',

    -- 通用字段
    `creator`            bigint        NOT NULL COMMENT '创建者',
    `create_time`        datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`            bigint COMMENT '更新者',
    `update_time`        datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`            bit(1)        NOT NULL DEFAULT 0 COMMENT '删除标记(0正常/1已删除)',

    -- 主键索引
    PRIMARY KEY (`id`),

    -- 唯一索引（防止同一服务在同一区域、租户和生效时间的重复定价）
    UNIQUE KEY `uk_price_service_region` (`service_id`, `region_id`, `tenant_id`, `effective_time`),

    -- 普通索引
    KEY                  `idx_price_region` (`region_id`),
    KEY                  `idx_price_tenant` (`tenant_id`),
    KEY                  `idx_price_effective` (`effective_time`),

    -- 通用索引
    INDEX                `idx_create_time` (`create_time`),
    INDEX                `idx_update_time` (`update_time`),
    INDEX                `idx_deleted` (`deleted`),

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支持不同区域差异化定价';
CREATE TABLE `service_ticket`
(
    `id`                  bigint       NOT NULL AUTO_INCREMENT COMMENT '工单主键ID',

    -- 工单标识
    `ticket_no`           varchar(32)  NOT NULL COMMENT '工单号（规则：TK+年月日+6位序列）',
    `ticket_code`         varchar(20)           DEFAULT NULL COMMENT '工单分类编码（如：COMPLAIN_001）',

    -- 用户关联信息
    `member_id`           bigint                DEFAULT NULL COMMENT '用户ID',
    `member_name`         varchar(50)           DEFAULT NULL COMMENT '用户姓名（冗余存储）',
    `member_level`        tinyint               DEFAULT NULL COMMENT '用户等级（冗余）',

    -- 业务关联
    `order_id`            bigint                DEFAULT NULL COMMENT '关联订单ID',
    `order_no`            varchar(32)           DEFAULT NULL COMMENT '订单编号（冗余）',
    `related_ticket_id`   bigint                DEFAULT NULL COMMENT '关联工单ID',

    -- 工单分类
    `type`                tinyint      NOT NULL COMMENT '工单类型：1-投诉 2-建议 3-咨询 4-退款申请 5-异常处理 6-账户问题',
    `sub_type`            varchar(30)           DEFAULT NULL COMMENT '工单子类型（如：delivery_delay）',
    `priority`            tinyint      NOT NULL DEFAULT '2' COMMENT '优先级：1-低 2-中 3-高 4-紧急',
    `channel`             tinyint               DEFAULT '1' COMMENT '来源渠道：1-APP 2-电话 3-网页 4-邮件 5-社交媒体',

    -- 问题描述
    `title`               varchar(100) NOT NULL COMMENT '标题',
    `content`             text         NOT NULL COMMENT '内容描述',
    `images`              json                  DEFAULT NULL COMMENT '相关图片（[{"url":"","type":1}]）',
    `attachments`         json                  DEFAULT NULL COMMENT '附件（[{"name":"","url":"","size":100}]）',

    -- 联系信息
    `contact_info`        varchar(100)          DEFAULT NULL COMMENT '联系方式',
    `contact_type`        tinyint               DEFAULT NULL COMMENT '1-电话 2-邮箱 3-微信',
    `preferred_time`      varchar(50)           DEFAULT NULL COMMENT '偏好联系时间段',

    -- 处理状态
    `status`              tinyint      NOT NULL DEFAULT '0' COMMENT '状态：0-待处理 1-处理中 2-已解决 3-已关闭 4-已驳回 5-待反馈',
    `current_handler`     bigint                DEFAULT NULL COMMENT '当前处理人ID',
    `current_department`  tinyint               DEFAULT NULL COMMENT '当前处理部门',
    `first_response_time` datetime              DEFAULT NULL COMMENT '首次响应时间',
    `resolve_time`        datetime              DEFAULT NULL COMMENT '解决时间',
    `close_time`          datetime              DEFAULT NULL COMMENT '关闭时间',

    -- 质量评价
    `satisfaction`        tinyint               DEFAULT NULL COMMENT '满意度评分（1-5星）',
    `evaluation_comment`  varchar(200)          DEFAULT NULL COMMENT '评价内容',
    `response_rating`     tinyint               DEFAULT NULL COMMENT '响应速度评分',
    `solution_rating`     tinyint               DEFAULT NULL COMMENT '解决方案评分',

    -- 系统字段
    `creator`             bigint                DEFAULT NULL COMMENT '创建人ID（用户或客服）',
    `create_time`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version`             int          NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_ticket_no` (`ticket_no`) COMMENT '工单号唯一索引',
    KEY                   `idx_member` (`member_id`) COMMENT '用户工单查询',
    KEY                   `idx_order` (`order_id`) COMMENT '订单关联查询',
    KEY                   `idx_status` (`status`) COMMENT '状态筛选',
    KEY                   `idx_handler` (`current_handler`) COMMENT '处理人查询',
    KEY                   `idx_priority` (`priority`) COMMENT '优先级排序',
    KEY                   `idx_type` (`type`) COMMENT '工单类型查询',
    KEY                   `idx_create_time` (`create_time`) COMMENT '创建时间排序',
    KEY                   `idx_response_time` (`first_response_time`) COMMENT '响应时效分析',
    FULLTEXT KEY `idx_content_search` (`title`,`content`) COMMENT '内容全文检索'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服工单表（P3客户服务）';

CREATE TABLE `device_monitoring`
(
    -- 主键
    `id`              bigint      NOT NULL AUTO_INCREMENT COMMENT '监控主键ID',

    -- 设备信息
    `device_id`       bigint      NOT NULL COMMENT '设备ID',
    `device_type`     tinyint     NOT NULL DEFAULT 1 COMMENT '设备类型(1智能柜/2打印机)',

    -- 监控指标
    `metric_type`     varchar(20) NOT NULL COMMENT '指标类型',
    `metric_value`    varchar(50) NOT NULL COMMENT '指标值',
    `normal_range`    varchar(100) COMMENT '正常范围',
    `alert_threshold` varchar(100) COMMENT '告警阈值',

    -- 状态信息
    `status`          tinyint     NOT NULL DEFAULT 0 COMMENT '状态(0正常/1告警/2故障)',

    -- 时间信息
    `record_time`     datetime    NOT NULL COMMENT '记录时间',

    -- 通用字段
    `creator`         bigint      NOT NULL COMMENT '创建者',
    `create_time`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`         bigint COMMENT '更新者',
    `update_time`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`         bit(1)      NOT NULL DEFAULT 0 COMMENT '删除标记(0正常/1已删除)',

    -- 主键索引
    PRIMARY KEY (`id`),

    -- 业务索引
    KEY               `idx_monitor_device` (`device_id`, `device_type`),
    KEY               `idx_monitor_status` (`status`),
    KEY               `idx_monitor_time` (`record_time`),

    -- 通用索引
    INDEX             `idx_create_time` (`create_time`),
    INDEX             `idx_update_time` (`update_time`),
    INDEX             `idx_deleted` (`deleted`),

    -- 优化查询的复合索引
    KEY               `idx_device_metric_time` (`device_id`, `metric_type`, `record_time`),
    KEY               `idx_status_metric_time` (`status`, `metric_type`, `record_time`),
    KEY               `idx_device_status_time` (`device_id`, `status`, `record_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备状态监控';


CREATE TABLE `station_storage`
(
    `id`                   bigint      NOT NULL AUTO_INCREMENT COMMENT '存储记录主键ID',

    -- 驿站关联信息
    `station_id`           bigint      NOT NULL COMMENT '驿站ID(facility_id)',
    `station_name`         varchar(50)          DEFAULT NULL COMMENT '驿站名称（冗余存储）',
    `station_address`      varchar(200)         DEFAULT NULL COMMENT '驿站地址（冗余）',

    -- 业务关联信息
    `order_id`             bigint      NOT NULL COMMENT '订单ID',
    `order_no`             varchar(32)          DEFAULT NULL COMMENT '订单编号（冗余）',
    `storage_code`         varchar(20) NOT NULL COMMENT '存储码（规则：ST+6位日期+4位序列）',

    -- 存储物品信息
    `storage_type`         tinyint     NOT NULL COMMENT '存储类型：1-待取件 2-待送件 3-临时寄存',
    `member_id`            bigint      NOT NULL COMMENT '用户ID',
    `member_name`          varchar(30)          DEFAULT NULL COMMENT '用户姓名（冗余）',
    `contact_name`         varchar(30) NOT NULL COMMENT '联系人',
    `contact_phone`        varchar(20) NOT NULL COMMENT '联系电话（支持国际号码）',

    -- 物品详情
    `item_count`           int         NOT NULL DEFAULT '1' COMMENT '物品数量',
    `item_description`     varchar(100)         DEFAULT NULL COMMENT '物品描述',
    `item_value`           decimal(10, 2)       DEFAULT NULL COMMENT '物品估值（用于保价）',
    `item_images`          json                 DEFAULT NULL COMMENT '物品照片（[{url:"",type:1}]）',
    `special_requirements` varchar(200)         DEFAULT NULL COMMENT '特殊要求',

    -- 位置信息
    `storage_location`     varchar(50)          DEFAULT NULL COMMENT '存储位置（如：A区3排2号）',
    `storage_section`      varchar(20)          DEFAULT NULL COMMENT '存储区域（A/B/C区）',
    `storage_bin`          varchar(10)          DEFAULT NULL COMMENT '储物格编号',

    -- 时间信息
    `storage_time`         datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '存储时间',
    `expected_pickup_time` datetime             DEFAULT NULL COMMENT '预计取件时间',
    `pickup_deadline`      datetime             DEFAULT NULL COMMENT '最晚取件时间',
    `pickup_time`          datetime             DEFAULT NULL COMMENT '实际取件时间',

    -- 取件人信息
    `pickup_person`        varchar(30)          DEFAULT NULL COMMENT '取件人',
    `pickup_phone`         varchar(20)          DEFAULT NULL COMMENT '取件人电话',
    `pickup_relation`      varchar(20)          DEFAULT NULL COMMENT '与收件人关系',
    `pickup_credential`    varchar(50)          DEFAULT NULL COMMENT '取件凭证（身份证号/验证码）',

    -- 状态管理
    `status`               tinyint     NOT NULL DEFAULT '0' COMMENT '状态：0-存储中 1-已取件 2-超期未取 3-异常 4-已转寄',
    `timeout_remind_count` int         NOT NULL DEFAULT '0' COMMENT '超期提醒次数',
    `last_remind_time`     datetime             DEFAULT NULL COMMENT '最后提醒时间',
    `exception_code`       varchar(20)          DEFAULT NULL COMMENT '异常类型编码',
    `exception_desc`       varchar(200)         DEFAULT NULL COMMENT '异常描述',

    -- 系统字段
    `create_time`          datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`          datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `operator_id`          bigint               DEFAULT NULL COMMENT '操作员ID',
    `version`              int         NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_storage_code` (`storage_code`) COMMENT '存储码唯一索引',
    KEY                    `idx_station` (`station_id`) COMMENT '驿站查询',
    KEY                    `idx_order` (`order_id`) COMMENT '订单关联',
    KEY                    `idx_member` (`member_id`) COMMENT '用户查询',
    KEY                    `idx_status` (`status`) COMMENT '状态筛选',
    KEY                    `idx_time_range` (`storage_time`,`pickup_time`) COMMENT '时间范围查询',
    KEY                    `idx_storage_type` (`storage_type`) COMMENT '存储类型查询',
    KEY                    `idx_pickup_deadline` (`pickup_deadline`) COMMENT '最晚取件时间查询',
    KEY                    `idx_location` (`storage_section`,`storage_bin`) COMMENT '储物位置查询'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='驿站存储记录表（P2代收代管）';

CREATE TABLE `order_evaluation`
(
    `id`               bigint   NOT NULL AUTO_INCREMENT COMMENT '评价主键ID',

    -- 订单关联信息
    `order_id`         bigint   NOT NULL COMMENT '订单ID',
    `order_no`         varchar(32)       DEFAULT NULL COMMENT '订单编号（冗余存储）',
    `member_id`        bigint   NOT NULL COMMENT '评价用户ID',
    `member_name`      varchar(50)       DEFAULT NULL COMMENT '用户昵称（冗余存储）',

    -- 评分信息
    `overall_rating`   tinyint  NOT NULL DEFAULT '5' COMMENT '总体评分（1-5星）',
    `service_quality`  tinyint  NOT NULL DEFAULT '5' COMMENT '服务质量评分',
    `cleaning_effect`  tinyint  NOT NULL DEFAULT '5' COMMENT '清洗效果评分',
    `delivery_speed`   tinyint  NOT NULL DEFAULT '5' COMMENT '配送时效评分',
    `service_attitude` tinyint  NOT NULL DEFAULT '5' COMMENT '服务态度评分',
    `courier_rating`   tinyint           DEFAULT NULL COMMENT '取送员评分',
    `factory_rating`   tinyint           DEFAULT NULL COMMENT '工厂评分',
    `avg_rating`       decimal(3, 1)     DEFAULT NULL COMMENT '平均评分（自动计算）',

    -- 评价内容
    `content`          varchar(500)      DEFAULT NULL COMMENT '评价内容',
    `images`           json              DEFAULT NULL COMMENT '评价图片（[{"url":"","type":1}]）',
    `video_url`        varchar(255)      DEFAULT NULL COMMENT '评价视频URL',
    `tags`             json              DEFAULT NULL COMMENT '评价标签（[1,3]对应标签表）',

    -- 隐私设置
    `is_anonymous`     bit(1)   NOT NULL DEFAULT b'0' COMMENT '是否匿名',
    `show_username`    bit(1)            DEFAULT b'1' COMMENT '显示用户名',
    `show_location`    bit(1)            DEFAULT b'0' COMMENT '显示地理位置',

    -- 商家互动
    `reply_content`    varchar(300)      DEFAULT NULL COMMENT '商家回复',
    `reply_time`       datetime          DEFAULT NULL COMMENT '回复时间',
    `reply_user_id`    bigint            DEFAULT NULL COMMENT '回复人ID',
    `reply_user_role`  tinyint           DEFAULT NULL COMMENT '回复人角色（1-客服 2-店长）',

    -- 系统字段
    `create_time`      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`          bit(1)   NOT NULL DEFAULT b'0' COMMENT '删除标记',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order` (`order_id`) COMMENT '订单评价唯一索引',
    KEY                `idx_member` (`member_id`) COMMENT '用户评价查询',
    KEY                `idx_rating` (`overall_rating`) COMMENT '评分筛选',
    KEY                `idx_time` (`create_time`) COMMENT '时间排序',
    KEY                `idx_avg_rating` (`avg_rating`) COMMENT '平均分查询',
    KEY                `idx_factory_rating` (`factory_rating`) COMMENT '工厂评分查询',
    KEY                `idx_courier_rating` (`courier_rating`) COMMENT '配送员评分查询',
    FULLTEXT KEY `idx_content` (`content`) COMMENT '内容全文检索'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单评价表（P1用户反馈）';


-- 订单日志表
CREATE TABLE `order_status_log`
(
    `id`                bigint         NOT NULL AUTO_INCREMENT COMMENT '日志主键ID',
    `order_id`          bigint         NOT NULL COMMENT '关联订单ID',

    -- 状态变更信息
    `from_status`       tinyint                 DEFAULT NULL COMMENT '原状态（NULL表示初始状态）',
    `to_status`         tinyint        NOT NULL COMMENT '目标状态',
    `status_group`      varchar(20)             DEFAULT NULL COMMENT '状态分组（如：payment/delivery等）',

    -- 操作人信息
    `operator_id`       bigint                  DEFAULT NULL COMMENT '操作人ID',
    `operator_type`     tinyint        NOT NULL DEFAULT '0' COMMENT '操作人类型：0-系统 1-用户 2-取送员 3-工厂 4-客服 5-管理员',
    `operator_name`     varchar(30)             DEFAULT NULL COMMENT '操作人姓名（冗余存储）',
    `operator_role`     varchar(20)             DEFAULT NULL COMMENT '操作人角色（如：客服主管）',

    -- 操作上下文
    `operation_time`    datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    `operation_channel` tinyint                 DEFAULT NULL COMMENT '操作渠道：1-APP 2-PC 3-API 4-小程序',
    `operation_ip`      varchar(50)             DEFAULT NULL COMMENT '操作IP地址',

    -- 环境信息
    `location`          varchar(100)            DEFAULT NULL COMMENT '操作位置（GPS或地址）',
    `longitude`         DECIMAL(10, 7) NOT NULL COMMENT '经度(GCJ-02)',
    `latitude`          DECIMAL(10, 7) NOT NULL COMMENT '纬度(GCJ-02)',

    -- 附加信息
    `remark`            varchar(200)            DEFAULT NULL COMMENT '备注说明',
    `images`            json                    DEFAULT NULL COMMENT '相关图片（JSON数组格式）',
    `ext_data`          json                    DEFAULT NULL COMMENT '扩展数据（如：取消原因等）',

    PRIMARY KEY (`id`),
    KEY                 `idx_order` (`order_id`) COMMENT '订单查询优化',
    KEY                 `idx_operator` (`operator_id`, `operator_type`) COMMENT '操作人查询',
    KEY                 `idx_time` (`operation_time`) COMMENT '时间范围查询',
    KEY                 `idx_status_change` (`from_status`, `to_status`) COMMENT '状态流转分析',
    KEY                 `idx_channel` (`operation_channel`) COMMENT '渠道分析',
    SPATIAL KEY `idx_geo` (`longitude`, `latitude`) COMMENT '地理位置查询'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单状态流转日志表';
CREATE TABLE `laundry_factory`
(
    `id`                  bigint         NOT NULL AUTO_INCREMENT COMMENT '工厂主键ID',

    -- 基础信息
    `facility_id`         bigint         NOT NULL COMMENT '关联设施ID',
    `factory_no`          varchar(20)    NOT NULL COMMENT '工厂编号（规则：LF+6位序列）',
    `name`                varchar(50)    NOT NULL COMMENT '工厂名称',
    `short_name`          varchar(20)             DEFAULT NULL COMMENT '工厂简称',

    -- 联系信息
    `contact_person`      varchar(30)    NOT NULL COMMENT '联系人',
    `contact_phone`       varchar(20)    NOT NULL COMMENT '联系电话（支持分机号）',
    `emergency_contact`   varchar(20)             DEFAULT NULL COMMENT '紧急联系电话',
    `service_hotline`     varchar(20)             DEFAULT NULL COMMENT '服务热线',

    -- 地址信息
    `address`             varchar(200)   NOT NULL COMMENT '详细地址',
    `longitude`           DECIMAL(10, 7) NOT NULL COMMENT '经度(GCJ-02)',
    `latitude`            DECIMAL(10, 7) NOT NULL COMMENT '纬度(GCJ-02)',
    `coverage_areas`      json                    DEFAULT NULL COMMENT '服务覆盖区域（行政区划代码数组）',

    -- 资质信息
    `business_license`    varchar(50)             DEFAULT NULL COMMENT '营业执照号',
    `license_image`       varchar(255)            DEFAULT NULL COMMENT '营业执照照片URL',
    `qualification_certs` json                    DEFAULT NULL COMMENT '资质证书（[{type:1,no:'',image:''}]）',

    -- 服务能力
    `service_types`       json           NOT NULL COMMENT '可提供服务类型（[1,3,5]对应服务分类）',
    `special_equipment`   json                    DEFAULT NULL COMMENT '特殊设备（[{"name":"干洗机","count":2}]）',
    `daily_capacity`      int            NOT NULL DEFAULT '100' COMMENT '日处理能力（件）',
    `current_load`        int            NOT NULL DEFAULT '0' COMMENT '当前负荷（件）',
    `max_capacity`        int                     DEFAULT NULL COMMENT '最大弹性产能（件）',

    -- 质量指标
    `quality_level`       tinyint        NOT NULL DEFAULT '3' COMMENT '质量等级：1-5星',
    `avg_process_time`    decimal(5, 2)  NOT NULL DEFAULT '24.00' COMMENT '平均处理时长（小时）',
    `on_time_rate`        decimal(5, 2)           DEFAULT NULL COMMENT '准时完成率（%）',
    `rework_rate`         decimal(5, 2)           DEFAULT NULL COMMENT '返工率（%）',

    -- 运营信息
    `work_hours`          varchar(100)            DEFAULT NULL COMMENT '工作时间（如：9:00-18:00）',
    `holiday_schedule`    json                    DEFAULT NULL COMMENT '节假日安排',
    `status`              tinyint        NOT NULL DEFAULT '1' COMMENT '状态：1-营业中 2-暂停服务 3-已关闭',

    -- 系统字段
    `create_time`         datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_factory_no` (`factory_no`) COMMENT '工厂编号唯一索引',
    KEY                   `idx_facility` (`facility_id`) COMMENT '设施查询',
    KEY                   `idx_capacity` (`daily_capacity`,`current_load`) COMMENT '产能分析',
    KEY                   `idx_quality` (`quality_level`) COMMENT '质量筛选',
    KEY                   `idx_coverage` ((CAST(coverage_areas AS CHAR(50)))) COMMENT '服务区域查询',
    KEY                   `idx_status` (`status`) COMMENT '状态筛选',
    SPATIAL KEY `idx_location` (`longitude`,`latitude`) COMMENT '地理位置索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='洗护工厂表（P1核心资源表）';



CREATE TABLE `marketing_activity`
(
    `id`                BIGINT         NOT NULL AUTO_INCREMENT COMMENT '活动主键ID',

    -- 活动基础信息
    `activity_no`       varchar(32)    NOT NULL COMMENT '活动编号（规则：ACT+年月日+6位序列）',
    `name`              varchar(100)   NOT NULL COMMENT '活动名称',
    `type`              TINYINT        NOT NULL COMMENT '活动类型：1-限时特价 2-满减 3-首单优惠 4-分享得券 5-拼团 6-秒杀',
    `sub_type`          varchar(30)             DEFAULT NULL COMMENT '活动子类型（如：new_user_gift）',
    `description`       text                    DEFAULT NULL COMMENT '活动描述（富文本）',

    -- 展示配置
    `banner_image`      varchar(255)            DEFAULT NULL COMMENT '活动横幅URL',
    `detail_images`     json                    DEFAULT NULL COMMENT '详情页图片（["url1","url2"]）',
    `share_config`      json                    DEFAULT NULL COMMENT '分享配置（{"title":"","image":"","desc":""}）',

    -- 时间配置
    `start_time`        datetime       NOT NULL COMMENT '开始时间',
    `end_time`          datetime       NOT NULL COMMENT '结束时间',
    `preheat_time`      datetime                DEFAULT NULL COMMENT '预热开始时间',
    `apply_deadline`    datetime                DEFAULT NULL COMMENT '报名截止时间',

    -- 目标配置
    `target_regions`    json                    DEFAULT NULL COMMENT '目标区域（[110101,310115]行政区划代码）',
    `target_users`      json                    DEFAULT NULL COMMENT '目标用户条件（{"min_level":3,"tags":[1,2]}）',
    `exclude_users`     json                    DEFAULT NULL COMMENT '排除用户（{"blacklist":true}）',

    -- 规则配置
    `rules`             json           NOT NULL COMMENT '活动规则（{"full_100_minus_20":{"threshold":100,"discount":20}}）',
    `limit_rules`       json                    DEFAULT NULL COMMENT '限制规则（{"per_user_limit":1,"total_limit":1000}）',
    `coupon_ids`        json                    DEFAULT NULL COMMENT '关联优惠券ID列表',

    -- 数据统计
    `budget`            decimal(12, 2)          DEFAULT '0.00' COMMENT '活动预算',
    `current_cost`      decimal(12, 2) NOT NULL DEFAULT '0.00' COMMENT '当前成本',
    `participate_count` int            NOT NULL DEFAULT '0' COMMENT '参与人数',
    `success_count`     int            NOT NULL DEFAULT '0' COMMENT '成功转化数',
    `conversion_rate`   decimal(5, 2)           DEFAULT NULL COMMENT '转化率（自动计算）',

    -- 状态控制
    `status`            TINYINT        NOT NULL DEFAULT '0' COMMENT '状态：0-草稿 1-进行中 2-已结束 3-已暂停 4-未开始',
    `approval_status`   TINYINT                 DEFAULT '0' COMMENT '审核状态：0-待审核 1-已通过 2-已拒绝',
    `pause_reason`      varchar(200)            DEFAULT NULL COMMENT '暂停原因',

    -- 系统字段
    `creator`           BIGINT         NOT NULL COMMENT '创建人ID',
    `create_time`       datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`           BIGINT                  DEFAULT NULL COMMENT '更新人ID',
    `update_time`       datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version`           int            NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_activity_no` (`activity_no`) COMMENT '活动编号唯一索引',
    KEY                 `idx_time_range` (`start_time`,`end_time`) COMMENT '活动时间查询',
    KEY                 `idx_status` (`status`) COMMENT '状态筛选',
    KEY                 `idx_type` (`type`) COMMENT '活动类型查询',
    KEY                 `idx_approval` (`approval_status`) COMMENT '审核状态查询',
    KEY                 `idx_creator` (`creator`) COMMENT '创建人查询',
    KEY                 `idx_preheat` (`preheat_time`) COMMENT '预热活动查询',
    KEY                 `idx_conversion` (`conversion_rate`) COMMENT '转化率分析'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='营销活动表（P2营销核心表）';


-- 分享推广表
CREATE TABLE `share_promotion`
(
    `id`             BIGINT        NOT NULL AUTO_INCREMENT COMMENT '自增记录主键 ID',
    `sharer_id`      BIGINT        NOT NULL COMMENT '分享者 ID',
    `share_type`     TINYINT       NOT NULL COMMENT '分享类型 (1-邀请注册 / 2-分享订单 / 3-分享活动)',
    `share_code`     VARCHAR(20)   NOT NULL COMMENT '自动生成分享码',
    `share_url`      VARCHAR(500)           DEFAULT NULL COMMENT '分享链接',
    `invited_id`     BIGINT                 DEFAULT NULL COMMENT '被邀请人 ID',
    `order_id`       BIGINT                 DEFAULT NULL COMMENT '关联订单 ID',
    `activity_id`    BIGINT                 DEFAULT NULL COMMENT '关联活动 ID',
    `click_count`    INT           NOT NULL DEFAULT 0 COMMENT '点击次数',
    `register_count` INT           NOT NULL DEFAULT 0 COMMENT '注册转化数',
    `order_count`    INT           NOT NULL DEFAULT 0 COMMENT '下单转化数',
    `reward_type`    TINYINT                DEFAULT NULL COMMENT '奖励类型 (1-积分 / 2-优惠券 / 3-现金)',
    `reward_value`   DECIMAL(8, 2) NOT NULL DEFAULT 0.00 COMMENT '奖励值',
    `status`         TINYINT       NOT NULL DEFAULT 0 COMMENT '状态 (0-待确认 / 1-已奖励 / 2-已取消)',
    `confirm_time`   DATETIME               DEFAULT NULL COMMENT '确认时间',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_share_code` (`share_code`),
    KEY              `idx_share_sharer` (`sharer_id`),
    KEY              `idx_share_invited` (`invited_id`),
    KEY              `idx_share_type` (`share_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分享推广记录表';
