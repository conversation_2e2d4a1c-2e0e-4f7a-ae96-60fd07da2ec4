DROP TABLE IF EXISTS `app_user`;
-- 通用登陆表
CREATE TABLE `app_user`
(
    `id`              BIGINT      NOT NULL AUTO_INCREMENT COMMENT '用户主键ID',
    `openid`          VARCHAR(64) NOT NULL COMMENT '微信 openid',
    `unionid`         VARCHAR(64)          DEFAULT NULL COMMENT '微信 unionid',
    `phone`           VARCHAR(11)          DEFAULT NULL COMMENT '手机号',
    `user_code`       VARCHAR(18)          DEFAULT NULL COMMENT '用户编号，唯一业务ID（如USR20230001） ',
    `nickname`        VARCHAR(50)          DEFAULT NULL COMMENT '微信昵称',
    `avatar_url`      VARCHAR(255)         DEFAULT NULL COMMENT '微信头像 URL',
    `gender`          TINYINT              DEFAULT 0 COMMENT '性别 (0未知/1男/2女)',
    `language`        VARCHAR(10)          DEFAULT 'zh_CN' COMMENT '语言',
    `city`            VARCHAR(20)          DEFAULT NULL COMMENT '城市',
    `province`        VARCHAR(20)          DEFAULT NULL COMMENT '省份',
    `country`         VARCHAR(20)          DEFAULT NULL COMMENT '国家',
    `register_source` TINYINT     NOT NULL DEFAULT 1 COMMENT '注册来源 (1小程序/2公众号/3APP/4H5/5PC)',
    `register_time`   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
    `last_login_time` DATETIME             DEFAULT NULL COMMENT '最后登录时间',
    `role`            VARCHAR(32) NOT NULL DEFAULT 'customer' COMMENT '角色编码',
    `login_count`     INT         NOT NULL DEFAULT 0 COMMENT '登录次数',
    `status`          TINYINT     NOT NULL DEFAULT 0 COMMENT '状态 (0正常/1禁用/2注销/3冻结)',
    `device_info`     JSON                 DEFAULT NULL COMMENT '设备信息',
    `remark`          VARCHAR(200)         DEFAULT NULL COMMENT '备注',
    `creator`         BIGINT      NOT NULL COMMENT '创建者',
    `create_time`     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updater`         BIGINT               DEFAULT NULL COMMENT '更新者',
    `update_time`     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted`         TINYINT(1)     NOT NULL DEFAULT 0 COMMENT '删除标记',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_app_user_openid` (`openid`),
    UNIQUE KEY `uk_app_user_unionid` (`unionid`),
    UNIQUE KEY `uk_app_user_user_code` (`user_code`),
    UNIQUE KEY `uk_app_user_phone` (`phone`),
    KEY               `idx_app_user_status` (`status`),
    KEY               `idx_app_user_role` (`role`),
    KEY               `idx_app_user_register_source` (`register_source`),
    KEY               `idx_app_user_register_time` (`register_time`),
    KEY               `idx_create_time` (`create_time`),
    KEY               `idx_update_time` (`update_time`),
    KEY               `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信用户信息表';


-- 二维码表
DROP TABLE IF EXISTS `qr_code`;
CREATE TABLE `qr_code`
(
    `id`             BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `qr_type`        VARCHAR(32)  NOT NULL COMMENT '二维码类型分类(对应QRBusinessType枚举的service字段)',
    `business_id`    BIGINT(20) NOT NULL COMMENT '业务唯一标识',
    `encrypted_data` TEXT         NOT NULL COMMENT '加密的业务数据JSON(包含businessType,businessId等)',
    `status`         TINYINT      NOT NULL DEFAULT 1 COMMENT '状态枚举：0-已失效，1-有效，2-已使用',
    `expire_time`    DATETIME     NOT NULL COMMENT '过期时间',
    `qr_path`        VARCHAR(255) NOT NULL COMMENT '加密的业务数据JSON打包为二维码保存的图片路径',

    /* BasePo 基础字段 */
    `creator`        BIGINT       NOT NULL COMMENT '创建者',
    `create_time`    DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updater`        BIGINT                DEFAULT NULL COMMENT '更新者',
    `update_time`    DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted`        TINYINT(1)     NOT NULL DEFAULT 0 COMMENT '删除标记',

    /* 额外业务字段 */
    `creator_role`   VARCHAR(32)           DEFAULT NULL COMMENT '创建人角色',

    PRIMARY KEY (`id`),
    KEY              `idx_qr_type` (`qr_type`),
    KEY              `idx_business_id` (`business_id`),
    KEY              `idx_status_expire` (`status`, `expire_time`),
    KEY              `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='二维码信息表';

-- 厂长表 (新增)
DROP TABLE IF EXISTS `factory_director`;
CREATE TABLE factory_director
(
    `id`          BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '厂长ID',
    `app_user_id` BIGINT      NOT NULL COMMENT '关联用户ID',
    `director_no` VARCHAR(20) NOT NULL COMMENT '厂长编号',
    `factory_id`  BIGINT      NOT NULL COMMENT '所属工厂ID',
    -- 状态
    `status`      TINYINT     NOT NULL DEFAULT 0 COMMENT '状态(0在任/1离任/2休假)',
    -- 通用字段
    `creator`     BIGINT      NOT NULL COMMENT '创建者',
    `create_time` DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updater`     BIGINT               DEFAULT NULL COMMENT '更新者',
    `update_time` DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted`     TINYINT(1)     NOT NULL DEFAULT 0 COMMENT '删除标记',

    UNIQUE KEY uk_app_user (app_user_id),
    UNIQUE KEY uk_director_no (director_no),
    KEY           idx_factory (factory_id),
    KEY           idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='厂长信息表';

-- 工厂表
DROP TABLE IF EXISTS `factory_info`;
CREATE TABLE `factory_info`
(
    `id`             BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '工厂ID',
    `factory_code`   VARCHAR(20)    NOT NULL COMMENT '工厂编码(F+区域码+序号)',
    `factory_name`   VARCHAR(100)   NOT NULL COMMENT '工厂名称',
    `address_code`   INT            NOT NULL COMMENT '区域编码',
    `detail_address` VARCHAR(200)   NOT NULL COMMENT '详细地址',
    `longitude`      DECIMAL(10, 7) NOT NULL COMMENT '经度(GCJ-02)',
    `latitude`       DECIMAL(10, 7) NOT NULL COMMENT '纬度(GCJ-02)',
    `contact_person` VARCHAR(30)    NOT NULL COMMENT '联系人',
    `contact_phone`  VARCHAR(20)    NOT NULL COMMENT '联系电话',
    `status`         TINYINT        NOT NULL DEFAULT 0 COMMENT '状态(0正常/1维护中/2已关闭)',

    -- 通用字段
    `creator`        BIGINT         NOT NULL COMMENT '创建者',
    `create_time`    DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updater`        BIGINT                  DEFAULT NULL COMMENT '更新者',
    `update_time`    DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted`        TINYINT(1)     NOT NULL DEFAULT 0 COMMENT '删除标记',

    -- 索引
    UNIQUE KEY `uk_factory_code` (`factory_code`),
    UNIQUE KEY `uk_factory_phone` (`contact_phone`),
    KEY              `idx_address_code` (`address_code`),
    KEY              `idx_status_deleted` (`status`, `deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='洗衣工厂基础信息';
-- 投资人
DROP TABLE IF EXISTS `region_investor`;
CREATE TABLE `region_investor`
(
    `id`             BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `app_user_id`    BIGINT      NOT NULL COMMENT '关联用户ID',
    `investor_no`    VARCHAR(20) NOT NULL COMMENT '投资人编号',
    `contact_person` VARCHAR(30)          DEFAULT NULL COMMENT '联系人',
    `contact_phone`  VARCHAR(20)          DEFAULT NULL COMMENT '联系电话',
    `address_code`   INT         NOT NULL COMMENT '区域编码',

    `factory_id`     BIGINT      NOT NULL COMMENT '所属工厂ID', -- ✅ 新增字段

    -- 通用字段
    `creator`        BIGINT      NOT NULL COMMENT '创建者',
    `create_time`    DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updater`        BIGINT               DEFAULT NULL COMMENT '更新者',
    `update_time`    DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted`        TINYINT(1)     NOT NULL DEFAULT 0 COMMENT '删除标记',

    -- 索引
    UNIQUE KEY `uk_investor_user` (`app_user_id`),
    UNIQUE KEY `uk_investor_no` (`investor_no`),
    KEY              `idx_investor_phone` (`contact_phone`),
    KEY              `idx_address_code` (`address_code`),
    KEY              `idx_factory_id` (`factory_id`),
    KEY              `idx_investor_status` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='区域投资方表';
-- 取送工
DROP TABLE IF EXISTS `logistics_delivery_worker`;
CREATE TABLE `logistics_delivery_worker`
(
    `id`                 BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `app_user_id`        BIGINT      NOT NULL COMMENT '关联用户ID',
    `region_investor_id` BIGINT      NOT NULL COMMENT '所属投资人ID',
    `worker_no`          VARCHAR(20) NOT NULL COMMENT '员工编号',
    `current_status`     TINYINT(1)     NOT NULL DEFAULT 0 COMMENT '当前状态枚举：0在线，1离线，2忙碌，3休息',
    `phone`              VARCHAR(11)          DEFAULT NULL COMMENT '手机号',
    `real_name`          VARCHAR(30)          DEFAULT NULL COMMENT '真实姓名',

    -- 通用字段
    `creator`            BIGINT      NOT NULL COMMENT '创建者',
    `create_time`        DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updater`            BIGINT               DEFAULT NULL COMMENT '更新者',
    `update_time`        DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted`            TINYINT(1)     NOT NULL DEFAULT 0 COMMENT '删除标记',

    -- 索引
    UNIQUE KEY `uk_delivery_user` (`app_user_id`),
    UNIQUE KEY `uk_delivery_no` (`worker_no`),
    KEY                  `idx_delivery_status` (`current_status`, `deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='取送人员表';
-- 洗护工
DROP TABLE IF EXISTS `factory_worker`;
CREATE TABLE `factory_worker`
(
    `id`            BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',

    `app_user_id`   BIGINT      NOT NULL COMMENT '关联用户ID',
    `worker_no`     VARCHAR(20) NOT NULL COMMENT '员工编号',
    `factory_id`    BIGINT      NOT NULL COMMENT '所属工厂',

    `phone`         VARCHAR(11)          DEFAULT NULL COMMENT '绑定手机号',
    `real_name`     VARCHAR(30)          DEFAULT NULL COMMENT '实名认证姓名',
    `position_type` TINYINT(1)    DEFAULT 0,

    -- 通用字段 from BasePo
    `creator`       BIGINT      NOT NULL COMMENT '创建者',
    `create_time`   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updater`       BIGINT               DEFAULT NULL COMMENT '更新者',
    `update_time`   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted`       TINYINT(1)     NOT NULL DEFAULT 0 COMMENT '删除标记',

    -- 索引
    UNIQUE KEY `uk_worker_user` (`app_user_id`),
    UNIQUE KEY `uk_worker_no` (`worker_no`),
    KEY             `idx_worker_factory` (`factory_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工厂员工表';
-- 服务表
DROP TABLE IF EXISTS `service_category`;
CREATE TABLE `service_category`
(
    `id`          bigint       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name`        varchar(100) NOT NULL COMMENT '分类名称',
    `description` varchar(200)          DEFAULT NULL COMMENT '分类描述',
    `icon`        varchar(255)          DEFAULT NULL COMMENT '分类图标URL',
    `sort`        int          NOT NULL DEFAULT 0 COMMENT '排序值（越大越靠前）',
    `status`      tinyint      NOT NULL DEFAULT 0 COMMENT '状态：0-启用 1-禁用',

    -- 从 BasePo 继承的公共字段
    `creator`     BIGINT       NOT NULL COMMENT '创建者',
    `create_time` DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updater`     BIGINT                DEFAULT NULL COMMENT '更新者',
    `update_time` DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted`     TINYINT(1)     NOT NULL DEFAULT 0 COMMENT '删除标记',

    PRIMARY KEY (`id`),
    KEY           `idx_sort` (`sort`) COMMENT '排序查询优化',
    KEY           `idx_status` (`status`) COMMENT '状态筛选优化',
    KEY           `idx_deleted` (`deleted`) COMMENT '逻辑删除查询优化',
    KEY           `idx_create_time` (`create_time`) COMMENT '创建时间索引',
    KEY           `idx_update_time` (`update_time`) COMMENT '更新时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
  COMMENT='服务分类标签表（匹配ServiceCategoryPo实体）';

-- 服务表
DROP TABLE IF EXISTS `service_item`;
CREATE TABLE `service_item`
(
    `id`                  BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '服务主键ID',
    `category_ids`        TEXT          NOT NULL COMMENT '服务分类IDs(JSON数组)',
    `name`                VARCHAR(50)   NOT NULL COMMENT '服务名称',
    `description`         TEXT COMMENT '服务详细描述',
    `surface_plot`        VARCHAR(255)           DEFAULT NULL COMMENT '服务封面 URL',
    `compare_image`       VARCHAR(255)           DEFAULT NULL COMMENT '清洗前后对比图 URL',
    `base_price`          DECIMAL(8, 2) NOT NULL COMMENT '标准洗涤价格(单价加起来的价格，较贵)',
    `precise_price`       DECIMAL(8, 2) NOT NULL COMMENT '精洗额外费用',
    `premium_extra_hours` INT                    DEFAULT NULL COMMENT '精洗额外时间(小时)',
    `market_price`        DECIMAL(8, 2)          DEFAULT NULL COMMENT '服务定价',
    `process_hours`       INT           NOT NULL DEFAULT 1 COMMENT '标准处理时长(小时)',
    `rush_hours`          INT                    DEFAULT NULL COMMENT '加急处理时长(小时)',
    `rush_fee`            DECIMAL(8, 2)          DEFAULT NULL COMMENT '加急服务费用',
    `address_code`        INT                    DEFAULT NULL COMMENT '区域ID(县级枚举值)',
    `detail_config`       TEXT COMMENT '服务详情配置(JSON)',
    `status`              TINYINT       NOT NULL DEFAULT 0 COMMENT '状态(0正常/1停用)',
    `sort`                INT           NOT NULL DEFAULT 0 COMMENT '排序权重',
    `sales_count`         INT           NOT NULL DEFAULT 0 COMMENT '销售数量统计',
    -- 从 BasePo 继承的公共字段
    `creator`             BIGINT        NOT NULL COMMENT '创建者',
    `create_time`         DATETIME      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updater`             BIGINT                 DEFAULT NULL COMMENT '更新者',
    `update_time`         DATETIME      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted`             TINYINT(1)     NOT NULL DEFAULT 0 COMMENT '删除标记',

    -- 索引定义
    INDEX                 idx_status (`status`),
    INDEX                 idx_address_code (`address_code`),
    INDEX                 idx_sort (`sort`),
    INDEX                 idx_sales_count (`sales_count`),
    INDEX                 idx_name (`name`)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='核心服务项目表';


-- 轮播图配置表
DROP TABLE IF EXISTS `banner_config`;
CREATE TABLE `banner_config`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键 ID，自增',
    `title`            varchar(255)      DEFAULT NULL COMMENT '轮播图标题/名称（后台展示用）',
    `banners`          TEXT     NOT NULL COMMENT '服务图片(JSON数组)',
    `type`             varchar(20)       DEFAULT 'HOME' COMMENT '轮播图类型（如：HOME-首页，SERVICE-服务页）',
    `status`           int(1) DEFAULT 0 COMMENT '轮播图启用状态（0=启用，1=禁用）',
    `bind_target_json` text              DEFAULT NULL COMMENT '绑定目标Json',
    -- 从 BasePo 继承的公共字段
    `creator`          BIGINT   NOT NULL COMMENT '创建者',
    `create_time`      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updater`          BIGINT            DEFAULT NULL COMMENT '更新者',
    `update_time`      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted`          TINYINT(1)     NOT NULL DEFAULT 0 COMMENT '删除标记',
    PRIMARY KEY (`id`),
    KEY                `idx_type` (`type`),
    KEY                `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通用轮播图配置表';

-- 轮播图关系表
DROP TABLE IF EXISTS `banner_business_relation`;
CREATE TABLE `banner_business_relation`
(
    `id`            bigint      NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `banner_id`     bigint      NOT NULL COMMENT '轮播图ID',
    `business_type` varchar(20) NOT NULL COMMENT '业务类型(HOME/SERVICE)',
    `business_id`   bigint               DEFAULT NULL COMMENT '业务ID(服务ID，HOME类型为NULL)',
    -- 从 BasePo 继承的公共字段
    `creator`       BIGINT      NOT NULL COMMENT '创建者',
    `create_time`   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updater`       BIGINT               DEFAULT NULL COMMENT '更新者',
    `update_time`   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted`       TINYINT(1)     NOT NULL DEFAULT 0 COMMENT '删除标记',
    PRIMARY KEY (`id`),
    -- 索引：加速按轮播图ID查询
    KEY             `idx_banner` (`banner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='轮播图业务关系表';


-- 会员表
DROP TABLE IF EXISTS `member_identity`;

CREATE TABLE `member_identity`
(
    `id`             BIGINT      NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `app_user_id`    BIGINT      NOT NULL COMMENT '关联app_user用户ID',
    `member_no`      VARCHAR(32) NOT NULL COMMENT '会员编号（M+时间戳+随机数）',
    `level`          TINYINT     NOT NULL COMMENT '会员等级（1.普通/2.银卡/3.金卡/4.钻石）',
    `points`         INT                  DEFAULT 0 COMMENT '当前可用积分',
    `total_points`   INT                  DEFAULT 0 COMMENT '累计获得积分',
    `total_consumed` DECIMAL(10, 2)       DEFAULT 0.00 COMMENT '累计消费金额',
    `phone`          VARCHAR(11)          DEFAULT NULL COMMENT '绑定手机号',
    `real_name`      VARCHAR(32)          DEFAULT NULL COMMENT '实名认证姓名',
    `status`         TINYINT     NOT NULL DEFAULT 0 COMMENT '状态（0正常/1禁用/2注销/3冻结）',

    -- 从 BasePo 继承的公共字段
    `creator`        BIGINT      NOT NULL COMMENT '创建者',
    `create_time`    DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updater`        BIGINT               DEFAULT NULL COMMENT '更新者',
    `update_time`    DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted`        TINYINT(1)     NOT NULL DEFAULT 0 COMMENT '删除标记',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_member_no` (`member_no`),
    UNIQUE KEY `uk_member_identity_app_user_id` (`app_user_id`),
    UNIQUE KEY `uk_member_identity_phone` (`phone`),

    KEY              `idx_member_identity_level` (`level`),
    KEY              `idx_member_identity_status` (`status`),
    KEY              `idx_member_identity_create_time` (`create_time`),
    KEY              `idx_member_identity_update_time` (`update_time`),
    KEY              `idx_member_identity_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员身份管理表';


DROP TABLE IF EXISTS `member_address`;
-- 会员收发货地址表
CREATE TABLE `member_address`
(
    `id`             BIGINT       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `member_id`      BIGINT       NOT NULL COMMENT '关联会员ID',
    `address_code`   INT          NOT NULL COMMENT '区域编码(使用CountyEnum枚举)',
    `detail_address` VARCHAR(200) NOT NULL COMMENT '详细地址',
    `contact_name`   VARCHAR(30)  NOT NULL COMMENT '联系人姓名',
    `contact_phone`  VARCHAR(11)  NOT NULL COMMENT '联系电话',
    `longitude`      DECIMAL(10, 7) COMMENT '经度',
    `latitude`       DECIMAL(10, 7) COMMENT '纬度',
    -- 从 BasePo 继承的公共字段
    `creator`        BIGINT       NOT NULL COMMENT '创建者',
    `create_time`    DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updater`        BIGINT                DEFAULT NULL COMMENT '更新者',
    `update_time`    DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted`        TINYINT(1)     NOT NULL DEFAULT 0 COMMENT '删除标记',

    PRIMARY KEY (`id`),
    KEY              `idx_member_address_member_id` (`member_id`),
    KEY              `idx_member_address_address_code` (`address_code`),
    KEY              `idx_member_address_create_time` (`create_time`),
    KEY              `idx_member_address_update_time` (`update_time`),
    KEY              `idx_member_address_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员收发货地址表';

-- 智能柜表
DROP TABLE IF EXISTS `smart_cabinet`;
CREATE TABLE `smart_cabinet`
(
    `id`                 BIGINT         NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `region_investor_id` BIGINT                  DEFAULT 0 COMMENT '投资人ID(0为root)',
    `cabinet_no`         VARCHAR(20)    NOT NULL COMMENT '柜体编号（规则：SC+6位序列）',
    `name`               VARCHAR(50)    NOT NULL COMMENT '柜体名称（如：A栋1楼快递柜）',
    `address_code`       INT            NOT NULL COMMENT '安装区域编码',
    `detail_address`     VARCHAR(200)   NOT NULL COMMENT '详细安装地址',
    `longitude`          DECIMAL(10, 7) NOT NULL COMMENT '经度(GCJ-02)',
    `latitude`           DECIMAL(10, 7) NOT NULL COMMENT '纬度(GCJ-02)',
    /* 状态信息 */
    `online_status`      TINYINT        NOT NULL COMMENT '在线状态：0-离线 1-在线',
    `work_status`        TINYINT        NOT NULL COMMENT '工作状态：0-正常 1-故障 2-维护 3-已停用',
    -- 从 BasePo 继承的公共字段
    `creator`            BIGINT         NOT NULL COMMENT '创建者',
    `create_time`        DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updater`            BIGINT                  DEFAULT NULL COMMENT '更新者',
    `update_time`        DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted`            TINYINT(1)     NOT NULL DEFAULT 0 COMMENT '删除标记',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_cabinet_no` (`cabinet_no`),
    KEY                  `idx_address_code` (`address_code`),
    KEY                  `idx_status` (`online_status`, `work_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='智能柜主表';

DROP TABLE IF EXISTS `cabinet_cell`;
-- 格口实时状态表（记录被占用的格口）
CREATE TABLE `cabinet_cell`
(
    `id`          BIGINT   NOT NULL AUTO_INCREMENT,
    `cabinet_id`  BIGINT   NOT NULL COMMENT '柜体ID',
    `cell_type`   TINYINT  NOT NULL COMMENT '格口类型：1-A1 2-A2 3-A3 4-A4 5-A5',
    /* 状态信息 */
    `status`      TINYINT  NOT NULL DEFAULT 0 COMMENT '状态：0-空闲 1-占用 2-故障 3-维护',
    `lock_status` TINYINT  NOT NULL DEFAULT 0 COMMENT '锁状态：0-关闭 1-开启 2-异常',

    /* 使用信息 */
    `order_id`    BIGINT            DEFAULT NULL COMMENT '当前订单ID',
    `user_id`     BIGINT            DEFAULT NULL COMMENT '当前用户ID',
    `occupy_time` DATETIME          DEFAULT NULL COMMENT '占用开始时间',

    -- 从 BasePo 继承的公共字段
    `creator`     BIGINT   NOT NULL COMMENT '创建者',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updater`     BIGINT            DEFAULT NULL COMMENT '更新者',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted`     TINYINT(1)     NOT NULL DEFAULT 0 COMMENT '删除标记',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_cell_identity` (`cabinet_id`, `cell_type`),
    KEY           `idx_order` (`order_id`),
    KEY           `idx_user` (`user_id`),
    KEY           `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='柜体格口实时状态表';

-- 优惠券模板表
DROP TABLE IF EXISTS `coupon_template`;

CREATE TABLE `coupon_template`
(
    id             bigint auto_increment comment '主键ID'
        primary key,
    template_code  varchar(64)                              not null comment '模板编码（唯一业务标识）',
    name           varchar(100)                             not null comment '优惠券名称',
    type           tinyint        default 2                 not null comment '类型：1-满减券 2-折扣券（默认折扣券）',
    discount_type  tinyint        default 2                 not null comment '优惠类型：1-金额 2-比例（默认比例）',
    discount_value decimal(10, 2)                           not null comment '优惠值（金额或折扣率）',
    min_amount     decimal(10, 2) default 0.00 null comment '最低消费金额（默认0表示无最低消费）',
    max_discount   decimal(10, 2) null comment '最大优惠金额（折扣券用）',
    valid_days     int            default 30 null comment '领取后有效天数（默认30天）',
    total_count    int            default 1000              not null comment '总发放数量（默认1000张，0表示不限制）',
    issued_count   int            default 0                 not null comment '已发放数量（默认0）',
    used_count     int            default 0                 not null comment '已使用数量（默认0）',
    per_user_limit int            default 1                 not null comment '每人限领数量（默认1张）',
    icon_url       varchar(255) null comment '图标URL',
    description    varchar(500)   default '' null comment '使用说明（默认空字符串）',
    start_time     datetime       default CURRENT_TIMESTAMP not null comment '领取开始时间（默认当前时间）',
    end_time       datetime                                 not null comment '领取截止时间（默认5天后，由程序设置）',
    status         tinyint        default 0                 not null comment '模板状态：0-正常 1-停用（默认正常）',
    creator        bigint                                   not null comment '创建者',
    create_time    datetime       default CURRENT_TIMESTAMP not null comment '创建时间',
    updater        bigint null comment '更新者',
    update_time    datetime       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted        tinyint(1)     default 0                 not null comment '删除标记：0-未删除 1-已删除',
    constraint uk_template_code
        unique (template_code)
) comment '优惠券模板表（已移除地区限制、服务限制、商品项限制）';

CREATE INDEX idx_status ON coupon_template (status);

CREATE INDEX idx_time_range ON coupon_template (start_time, end_time);

-- 优惠券领取表
DROP TABLE IF EXISTS `member_coupon`;
CREATE TABLE `member_coupon`
(
    `id`           BIGINT   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `member_id`    BIGINT   NOT NULL COMMENT '会员ID',
    `template_id`  BIGINT   NOT NULL COMMENT '关联的优惠券模板ID',
    `order_id`     VARCHAR(32)       DEFAULT NULL COMMENT '使用订单ID（未使用时为空）',
    `receive_time` DATETIME NOT NULL COMMENT '领取时间',
    `start_time`   DATETIME NOT NULL COMMENT '生效时间',
    `expire_time`  DATETIME NOT NULL COMMENT '过期时间',
    `status`       TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '优惠券状态：0-未使用 1-已使用 2-已过期',
    `source`       TINYINT UNSIGNED NOT NULL COMMENT '来源：1-主动领取 2-系统赠送 3-活动获得 4-积分兑换',
    `use_time`     DATETIME          DEFAULT NULL COMMENT '使用时间（未使用时为空）',
    `creator`      BIGINT   NOT NULL COMMENT '创建者',
    `create_time`  DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updater`      BIGINT            DEFAULT NULL COMMENT '更新者',
    `update_time`  DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted`      TINYINT(1)      NOT NULL DEFAULT 0 COMMENT '删除标记',
    PRIMARY KEY (`id`),
    KEY            `idx_member_id` (`member_id`),
    KEY            `idx_template_id` (`template_id`),
    KEY            `idx_status_expire` (`status`, `expire_time`),
    KEY            `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户优惠券表';

DROP TABLE IF EXISTS `order_main`;
CREATE TABLE `order_main`
(
    `id`                      BIGINT         NOT NULL AUTO_INCREMENT COMMENT '订单主键ID',

    -- 基本信息
    `order_no`                VARCHAR(32)    NOT NULL COMMENT '订单号(唯一)',
    `member_id`               BIGINT         NOT NULL COMMENT '会员ID',

    -- 状态相关
    `order_status`            TINYINT        NOT NULL DEFAULT 10 COMMENT '订单状态(10待支付/20已支付/30待取件...)',
    `pay_status`              TINYINT        NOT NULL DEFAULT 0 COMMENT '支付状态(0未支付/1已支付/2已退款)',
    `cleaning_type`           TINYINT        NOT NULL COMMENT '清洗类型(1标洗/2精洗/...)',
    `is_urgent`               BOOLEAN        NOT NULL DEFAULT FALSE COMMENT '是否加急服务',

    -- 金额信息
    `total_amount`            DECIMAL(10, 2) NOT NULL DEFAULT 0.00 COMMENT '订单总金额',
    `discount_amount`         DECIMAL(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额',
    `coupon_amount`           DECIMAL(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠券金额',
    `pay_amount`              DECIMAL(10, 2) NOT NULL DEFAULT 0.00 COMMENT '实际支付金额',

    -- 服务与物流
    `pickup_type`             TINYINT        NOT NULL DEFAULT 1 COMMENT '取件方式(1智能柜/3上门)',
    `delivery_type`           TINYINT        NOT NULL DEFAULT 1 COMMENT '送件方式(1智能柜/3上门)',
    `pickup_address_id`       BIGINT                  Default NULL COMMENT '取件地址ID',
    `delivery_address_id`     BIGINT                  Default NULL COMMENT '送件地址ID',

    -- 时间信息
    `estimated_pickup_time`   DATETIME COMMENT '预计取件时间',
    `estimated_delivery_time` DATETIME COMMENT '预计送达时间',
    `actual_pickup_time`      DATETIME COMMENT '实际取件时间',
    `actual_delivery_time`    DATETIME COMMENT '实际送达时间',
    `cancel_time`             DATETIME COMMENT '取消时间',
    `complete_time`           DATETIME COMMENT '订单完成时间',

    -- 备注与扩展
    `remark`                  VARCHAR(500) COMMENT '用户备注',
    `cancel_reason`           VARCHAR(200) COMMENT '取消原因',
    `item_images`             TEXT COMMENT '衣物照片URL列表(JSON数组)',
    `description`             VARCHAR(255) COMMENT '系统订单描述',
    `order_detail`            TEXT           NOT NULL COMMENT '服务明细',

    -- 审计与状态
    `creator`                 BIGINT         NOT NULL COMMENT '创建者',
    `create_time`             DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`                 BIGINT COMMENT '更新者',
    `update_time`             DATETIME       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`                 BIT(1)         NOT NULL DEFAULT b'0' COMMENT '删除标记(0正常/1已删除)',

    -- 主键与唯一约束
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_no` (`order_no`),

    -- 业务索引
    KEY                       `idx_order_member` (`member_id`),
    KEY                       `idx_order_status` (`order_status`),
    KEY                       `idx_pay_status` (`pay_status`),
    KEY                       `idx_complete_time` (`complete_time`),
    KEY                       `idx_create_time` (`create_time`),
    KEY                       `idx_status_pay_status` (`order_status`, `pay_status`),
    KEY                       `idx_member_status` (`member_id`, `order_status`),

    -- 通用索引
    KEY                       `idx_update_time` (`update_time`),
    KEY                       `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单主表，记录订单核心信息（包括金额、服务、物流、状态等）';


-- 订单细节表
DROP TABLE IF EXISTS `order_detail`;
CREATE TABLE `order_detail`
(
    `id`                   BIGINT      NOT NULL AUTO_INCREMENT COMMENT '明细主键ID',
    `order_id`             BIGINT      NOT NULL COMMENT '关联订单主表ID',
--     一个订单可能是多个服务
    `service_ids`          text        NOT NULL COMMENT '关联服务项目集合',
-- 服务集合 json

--  价格
--     市场价格
--     订单价格
--     加急价格
--     优惠价格
--     支付价格

    -- 服务信息（冗余存储，避免服务变更影响历史订单）

    `service_name`         varchar(50) NOT NULL COMMENT '服务名称 集合（list）',
    `service_code`         varchar(20) NOT NULL COMMENT '服务编码 集合（list）',

    -- 价格信息
--     预计清洗时间
--     预计物流时间（增加物流算法：根据当前位置以及最终位置计算）
--     预计最终送达时间
--
    -- 服务执行
    `process_status`       TINYINT     NOT NULL DEFAULT '0' COMMENT '处理状态：0-待处理 1-处理中 2-已完成 3-已取消',
    `start_time`           datetime             DEFAULT NULL COMMENT '实际开始时间',
    `end_time`             datetime             DEFAULT NULL COMMENT '实际完成时间',
    `operator_id`          BIGINT               DEFAULT NULL COMMENT '操作人员ID',

    -- 物品信息
    `item_images`          text                 DEFAULT NULL COMMENT '物品照片（JSON数组格式）',
    `item_value`           decimal(10, 2)       DEFAULT NULL COMMENT '物品估值（用于保价）',
    `special_requirements` varchar(200)         DEFAULT NULL COMMENT '特殊要求',

    -- 通用字段
    `creator`              BIGINT      NOT NULL COMMENT '创建人ID',
    `create_time`          datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`              BIGINT               DEFAULT NULL COMMENT '更新人ID',
    `update_time`          datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`              bit(1)      NOT NULL DEFAULT b'0' COMMENT '删除标记：0-正常 1-已删除',

    PRIMARY KEY (`id`),
    KEY                    `idx_order` (`order_id`) COMMENT '订单查询优化',
    KEY                    `idx_service` (`service_id`) COMMENT '服务项目统计',
    KEY                    `idx_status` (`process_status`) COMMENT '状态筛选',
    KEY                    `idx_operator` (`operator_id`) COMMENT '操作人员查询',
    KEY                    `idx_create_time` (`create_time`) COMMENT '创建时间排序',
    KEY                    `idx_update_time` (`update_time`) COMMENT '更新时间追踪',
    KEY                    `idx_deleted` (`deleted`) COMMENT '删除状态筛选'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表（记录具体服务项目）';


-- 订单支付表
DROP TABLE IF EXISTS `order_payment`;
CREATE TABLE `order_payment`
(
    `id`                  BIGINT         NOT NULL AUTO_INCREMENT COMMENT '支付主键ID',
    `order_id`            BIGINT         NOT NULL COMMENT '关联订单ID',

    -- 支付基础信息
    `payment_no`          varchar(32)    NOT NULL COMMENT '支付单号（规则：PAY+年月日+8位序列）',
    `pay_method`          TINYINT        NOT NULL COMMENT '支付方式：1-微信 2-支付宝 3-余额 4-银行卡',
    `pay_amount`          decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '支付金额',

    -- 第三方支付信息
    `third_party_no`      varchar(64)             DEFAULT NULL COMMENT '第三方交易号',
    `third_party_account` varchar(50)             DEFAULT NULL COMMENT '第三方账户标识',

    -- 支付状态
    `pay_status`          TINYINT        NOT NULL DEFAULT '0' COMMENT '支付状态：0-待支付 1-成功 2-失败 3-已退款',
    `pay_time`            datetime                DEFAULT NULL COMMENT '支付成功时间',

    -- 退款信息
    `refund_amount`       decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '退款金额',
    `refund_time`         datetime                DEFAULT NULL COMMENT '退款时间',
    `refund_reason`       varchar(200)            DEFAULT NULL COMMENT '退款原因',

    -- 系统字段
    `create_time`         datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_payment_no` (`payment_no`) COMMENT '支付单号唯一索引',
    KEY                   `idx_order` (`order_id`) COMMENT '订单查询优化',
    KEY                   `idx_third_party` (`third_party_no`) COMMENT '第三方交易号查询',
    KEY                   `idx_status` (`pay_status`) COMMENT '状态筛选',
    KEY                   `idx_pay_time` (`pay_time`) COMMENT '支付时间统计'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单支付表';

DROP TABLE IF EXISTS `order_status`;
CREATE TABLE `order_status`
(
    `id`            bigint   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id`      bigint   NOT NULL COMMENT '订单ID',
    `task_status`   tinyint  NOT NULL COMMENT '订单状态(1-待分配,2-已分配待取件,3-取件完成待送厂,4-已到厂待清洗,5-工厂清洗中,6-清洗完成待取送,7-前往配送中,8-已送达,9-待取件(柜子),10-已完成,11-已取消,12-异常)',
    `business_data` json              DEFAULT NULL COMMENT '业务JSON数据',
    -- 通用字段
    `creator`       BIGINT   NOT NULL COMMENT '创建人ID',
    `create_time`   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`       BIGINT            DEFAULT NULL COMMENT '更新人ID',
    `update_time`   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`       bit(1)   NOT NULL DEFAULT b'0' COMMENT '删除标记：0-正常 1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_id` (`order_id`),
    KEY             `idx_task_status` (`task_status`),
    KEY             `idx_create_time` (`create_time`),
    KEY             `idx_update_time` (`update_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单状态主表';

DROP TABLE IF EXISTS `user_review`;
CREATE TABLE `user_review`
(
    `id`                 bigint   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `member_id`          bigint   NOT NULL COMMENT '会员ID（评论人）',
    `order_id`           bigint   NOT NULL COMMENT '关联订单ID',
    `item_id`            bigint            DEFAULT NULL COMMENT '服务ID（如商品/服务ID）',
    `item_name`          varchar(32)       DEFAULT NULL COMMENT '评价对象名称，用于展示',
    `rating`             int      NOT NULL COMMENT '评分（1-10分）',
    `content`            text COMMENT '评价内容',
    `image_urls`         text              DEFAULT NULL COMMENT '配图列表(JSON数组字符串)',
    `tags`               text              DEFAULT NULL COMMENT '评价标签(JSON数组字符串，如["服务好","包装好"])',
    `is_repeat_customer` tinyint(1) DEFAULT '0' COMMENT '是否回头客（1-是，0-否）',
    `is_anonymous`       tinyint(1) DEFAULT '0' COMMENT '是否匿名评价',
    `creator`            bigint   NOT NULL COMMENT '创建人ID',
    `create_time`        datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`            bigint            DEFAULT NULL COMMENT '更新人ID',
    `update_time`        datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`            tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记：0-正常，1-已删除',
    PRIMARY KEY (`id`),
    KEY                  `idx_member` (`member_id`),
    KEY                  `idx_order` (`order_id`),
    KEY                  `idx_member_item` (`member_id`, `item_id`) COMMENT '会员-商品联合索引',
    KEY                  `idx_rating_item` (`rating`, `item_id`) COMMENT '评分-商品联合索引',
    KEY                  `idx_create_time` (`create_time`) COMMENT '创建时间索引',
    KEY                  `idx_anonymous` (`is_anonymous`) COMMENT '匿名评价索引',
    KEY                  `idx_member_order` (`member_id`, `order_id`) COMMENT '会员-订单联合索引',
    KEY                  `idx_item_status` (`item_id`, `deleted`) COMMENT '商品-删除状态索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户评价表';


CREATE TABLE `payment_record`
(
    `id`             bigint         NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id`       bigint         NOT NULL COMMENT '关联订单ID',
    `order_no`       varchar(32)    NOT NULL COMMENT '订单号',
    `payment_no`     varchar(32)    NOT NULL COMMENT '支付单号',
    `member_id`      bigint         NOT NULL COMMENT '会员ID',
    `payment_type`   varchar(20)    NOT NULL COMMENT '支付方式',
    `payment_amount` decimal(10, 2) NOT NULL COMMENT '支付金额',
    `payment_status` varchar(20)    NOT NULL COMMENT '支付状态',
    `transaction_id` varchar(64)             DEFAULT NULL COMMENT '第三方交易号',
    `prepay_id`      varchar(64)             DEFAULT NULL COMMENT '预支付交易会话标识',
    `currency`       varchar(8)              DEFAULT NULL COMMENT '货币类型',
    `pay_time`       datetime                DEFAULT NULL COMMENT '支付完成时间',
    `expire_time`    datetime                DEFAULT NULL COMMENT '订单失效时间',
    `notify_data`    text COMMENT '支付回调原始数据',
    `creator`        varchar(64)             DEFAULT NULL COMMENT '创建人',
    `updater`        varchar(64)             DEFAULT NULL COMMENT '更新人',
    `create_time`    datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`        tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标记(0:未删除,1:已删除)',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_payment_no` (`payment_no`),
    KEY              `idx_order_id` (`order_id`),
    KEY              `idx_member_id` (`member_id`),
    KEY              `idx_payment_status` (`payment_status`),
    KEY              `idx_pay_time` (`pay_time`),
    KEY              `idx_prepay_id` (`prepay_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='支付记录表';