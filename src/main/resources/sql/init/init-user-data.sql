-- =====================================================
-- 用户数据初始化脚本
-- 包含各种角色的用户数据和对应的会员身份信息
-- =====================================================

-- =====================================================
-- 清空表数据并重置主键自增值
-- =====================================================
-- 禁用外键约束检查
SET FOREIGN_KEY_CHECKS = 0;

-- 清空用户评价表
TRUNCATE TABLE user_review;

-- 清空会员身份表
TRUNCATE TABLE member_identity;

-- 清空用户表
TRUNCATE TABLE app_user;

-- 重新启用外键约束检查
SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 1. 超级管理员 (ROOT)
-- =====================================================
INSERT INTO app_user (
    openid, unionid, user_code, phone, nickname, avatar_url, gender, language, 
    city, province, country, register_source, register_time, last_login_time, 
    role, login_count, status, device_info, remark, creator
) VALUES
-- 主管理员
('openid_root_admin', 'unionid_root_admin', 'USR20240001', '13800000001',
 '系统管理员', '/api-storage/inherent/profile_photo/root/admin_avatar (1).png', 0, 'zh_CN',
 '北京', '北京', '中国', 1, NOW(), NOW(),
 'root', 5, 0, '{"device":"admin_pc","version":"1.0"}', '系统主管理员账号', 0),

-- 备用管理员
('openid_root_backup', 'unionid_root_backup', 'USR20240002', '13800000002',
 '备用管理员', '/api-storage/inherent/profile_photo/root/admin_avatar (2).png', 1, 'zh_CN',
 '上海', '上海', '中国', 1, NOW(), DATE_SUB(NOW(), INTERVAL 1 DAY),
 'root', 2, 0, '{"device":"admin_mobile","version":"1.0"}', '系统备用管理员账号', 0);

-- =====================================================
-- 2. 会员用户 (CUSTOMER)
-- =====================================================
INSERT INTO app_user (
    openid, unionid, user_code, phone, nickname, avatar_url, gender, language, 
    city, province, country, register_source, register_time, last_login_time, 
    role, login_count, status, device_info, remark, creator
) VALUES
-- 普通会员
('openid_member_001', 'unionid_member_001', 'USR20240003', '13800001001',
 '张小明', '/api-storage/inherent/profile_photo/customer/user_avatar (1).png', 1, 'zh_CN',
 '北京', '北京', '中国', 1, NOW(), NOW(),
 'customer', 15, 0, '{"device":"iPhone","version":"8.0.33"}', '活跃用户', 1),

('openid_member_002', 'unionid_member_002', 'USR20240004', '13800001002',
 '李小红', '/api-storage/inherent/profile_photo/customer/user_avatar (2).png', 2, 'zh_CN',
 '上海', '上海', '中国', 1, DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 1 HOUR),
 'customer', 8, 0, '{"device":"Android","version":"8.0.33"}', '普通用户', 1),

('openid_member_003', 'unionid_member_003', 'USR20240005', '13800001003',
 '王大华', '/api-storage/inherent/profile_photo/customer/user_avatar (3).png', 1, 'zh_CN',
 '广州', '广东', '中国', 1, DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 3 HOUR),
 'customer', 25, 0, '{"device":"iPhone","version":"8.0.33"}', 'VIP用户', 1),

('openid_member_004', 'unionid_member_004', 'USR20240006', '13800001004',
 '赵小美', '/api-storage/inherent/profile_photo/customer/user_avatar (4).png', 2, 'zh_CN',
 '深圳', '广东', '中国', 1, DATE_SUB(NOW(), INTERVAL 10 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY),
 'customer', 3, 0, '{"device":"Android","version":"8.0.33"}', '新用户', 1),

('openid_member_005', 'unionid_member_005', 'USR20240007', '13800001005',
 '刘老板', '/api-storage/inherent/profile_photo/customer/user_avatar (5).png', 1, 'zh_CN',
 '杭州', '浙江', '中国', 1, DATE_SUB(NOW(), INTERVAL 30 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY),
 'customer', 50, 0, '{"device":"iPhone","version":"8.0.33"}', '钻石会员', 1);

-- =====================================================
-- 3. 区域投资人 (INVESTOR)
-- =====================================================
INSERT INTO app_user (
    openid, unionid, user_code, phone, nickname, avatar_url, gender, language, 
    city, province, country, register_source, register_time, last_login_time, 
    role, login_count, status, device_info, remark, creator
) VALUES

-- 3. 成都各区县区域管理员 (REGION_ADMIN)
-- =====================================================


INSERT INTO app_user (
    openid, unionid, user_code, phone, nickname, avatar_url, gender, language,
    city, province, country, register_source, register_time, last_login_time,
    role, login_count, status, device_info, remark, creator
) VALUES
-- 锦江区管理员
    ('openid_admin_510104', 'unionid_admin_510104', 'USR20230008', '13800002001',
    '锦江区管理员', '/api-storage/inherent/profile_photo/investor/investor_avatar (1).png', 1, 'zh_CN',
    '锦江区', '四川', '中国', 1, DATE_SUB(NOW(), INTERVAL 60 DAY), NOW(),
    'region_admin', 120, 0, '{"device":"iPad","version":"8.0.33"}', '锦江区管理员', 1),

-- 青羊区管理员
    ('openid_admin_510105', 'unionid_admin_510105', 'USR20230009', '13800002002',
    '青羊区管理员', '/api-storage/inherent/profile_photo/investor/investor_avatar (2).png', 2, 'zh_CN',
    '青羊区', '四川', '中国', 1, DATE_SUB(NOW(), INTERVAL 45 DAY), DATE_SUB(NOW(), INTERVAL 2 HOUR),
    'region_admin', 95, 0, '{"device":"iPhone","version":"8.0.33"}', '青羊区管理员', 1),

-- 金牛区管理员
    ('openid_admin_510106', 'unionid_admin_510106', 'USR20230010', '13800002003',
    '金牛区管理员', '/api-storage/inherent/profile_photo/investor/investor_avatar (3).png', 1, 'zh_CN',
    '金牛区', '四川', '中国', 1, DATE_SUB(NOW(), INTERVAL 90 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY),
    'region_admin', 200, 0, '{"device":"Android","version":"8.0.33"}', '金牛区管理员', 1),

-- 武侯区管理员
    ('openid_admin_510107', 'unionid_admin_510107', 'USR20230011', '13800002004',
    '武侯区管理员', '/api-storage/inherent/profile_photo/investor/investor_avatar (1).png', 1, 'zh_CN',
    '武侯区', '四川', '中国', 1, DATE_SUB(NOW(), INTERVAL 30 DAY), DATE_SUB(NOW(), INTERVAL 1 HOUR),
    'region_admin', 80, 0, '{"device":"Android","version":"8.0.33"}', '武侯区管理员', 1),

-- 成华区管理员
    ('openid_admin_510108', 'unionid_admin_510108', 'USR20230012', '13800002005',
    '成华区管理员', '/api-storage/inherent/profile_photo/investor/investor_avatar (2).png', 2, 'zh_CN',
    '成华区', '四川', '中国', 1, DATE_SUB(NOW(), INTERVAL 20 DAY), DATE_SUB(NOW(), INTERVAL 30 MINUTE),
    'region_admin', 65, 0, '{"device":"iPhone","version":"8.0.33"}', '成华区管理员', 1),

-- 龙泉驿区管理员
    ('openid_admin_510112', 'unionid_admin_510112', 'USR20230013', '13800002006',
    '龙泉驿区管理员', '/api-storage/inherent/profile_photo/investor/investor_avatar (3).png', 1, 'zh_CN',
    '龙泉驿区', '四川', '中国', 1, DATE_SUB(NOW(), INTERVAL 25 DAY), DATE_SUB(NOW(), INTERVAL 2 HOUR),
    'region_admin', 72, 0, '{"device":"Android","version":"8.0.33"}', '龙泉驿区管理员', 1),

-- 青白江区管理员
    ('openid_admin_510113', 'unionid_admin_510113', 'USR20230014', '13800002007',
    '青白江区管理员', '/api-storage/inherent/profile_photo/investor/investor_avatar (1).png', 1, 'zh_CN',
    '青白江区', '四川', '中国', 1, DATE_SUB(NOW(), INTERVAL 15 DAY), DATE_SUB(NOW(), INTERVAL 10 MINUTE),
    'region_admin', 45, 0, '{"device":"Android","version":"8.0.33"}', '青白江区管理员', 1),

-- 新都区管理员
    ('openid_admin_510114', 'unionid_admin_510114', 'USR20230015', '13800002008',
    '新都区管理员', '/api-storage/inherent/profile_photo/investor/investor_avatar (2).png', 1, 'zh_CN',
    '新都区', '四川', '中国', 1, DATE_SUB(NOW(), INTERVAL 12 DAY), DATE_SUB(NOW(), INTERVAL 20 MINUTE),
    'region_admin', 38, 0, '{"device":"Android","version":"8.0.33"}', '新都区管理员', 1),

-- 温江区管理员
    ('openid_admin_510115', 'unionid_admin_510115', 'USR20230016', '13800002009',
    '温江区管理员', '/api-storage/inherent/profile_photo/investor/investor_avatar (3).png', 1, 'zh_CN',
    '温江区', '四川', '中国', 1, DATE_SUB(NOW(), INTERVAL 18 DAY), DATE_SUB(NOW(), INTERVAL 5 MINUTE),
    'region_admin', 52, 0, '{"device":"Android","version":"8.0.33"}', '温江区管理员', 1),

-- 双流区管理员
    ('openid_admin_510116', 'unionid_admin_510116', 'USR20230017', '13800002010',
    '双流区管理员', '/api-storage/inherent/profile_photo/investor/investor_avatar (1).png', 1, 'zh_CN',
    '双流区', '四川', '中国', 1, DATE_SUB(NOW(), INTERVAL 8 DAY), DATE_SUB(NOW(), INTERVAL 15 MINUTE),
    'region_admin', 28, 0, '{"device":"Android","version":"8.0.33"}', '双流区管理员', 1),

-- 郫都区管理员
    ('openid_admin_510117', 'unionid_admin_510117', 'USR20230018', '13800002011',
    '郫都区管理员', '/api-storage/inherent/profile_photo/investor/investor_avatar (2).png', 1, 'zh_CN',
    '郫都区', '四川', '中国', 1, DATE_SUB(NOW(), INTERVAL 22 DAY), DATE_SUB(NOW(), INTERVAL 8 MINUTE),
    'region_admin', 60, 0, '{"device":"Android","version":"8.0.33"}', '郫都区管理员', 1),

-- 新津区管理员
    ('openid_admin_510118', 'unionid_admin_510118', 'USR20230019', '13800002012',
    '新津区管理员', '/api-storage/inherent/profile_photo/investor/investor_avatar (3).png', 1, 'zh_CN',
    '新津区', '四川', '中国', 1, DATE_SUB(NOW(), INTERVAL 10 DAY), DATE_SUB(NOW(), INTERVAL 5 MINUTE),
    'region_admin', 35, 0, '{"device":"Android","version":"8.0.33"}', '新津区管理员', 1),

-- 金堂县管理员
    ('openid_admin_510121', 'unionid_admin_510121', 'USR20230020', '13800002013',
    '金堂县管理员', '/api-storage/inherent/profile_photo/investor/investor_avatar (1).png', 1, 'zh_CN',
    '金堂县', '四川', '中国', 1, DATE_SUB(NOW(), INTERVAL 7 DAY), DATE_SUB(NOW(), INTERVAL 12 MINUTE),
    'region_admin', 28, 0, '{"device":"Android","version":"8.0.33"}', '金堂县管理员', 1),

-- 大邑县管理员
    ('openid_admin_510129', 'unionid_admin_510129', 'USR20230021', '13800002014',
    '大邑县管理员', '/api-storage/inherent/profile_photo/investor/investor_avatar (2).png', 2, 'zh_CN',
    '大邑县', '四川', '中国', 1, DATE_SUB(NOW(), INTERVAL 14 DAY), DATE_SUB(NOW(), INTERVAL 3 MINUTE),
    'region_admin', 42, 0, '{"device":"iPhone","version":"8.0.33"}', '大邑县管理员', 1),

-- 蒲江县管理员
    ('openid_admin_510131', 'unionid_admin_510131', 'USR20230022', '13800002015',
    '蒲江县管理员', '/api-storage/inherent/profile_photo/investor/investor_avatar (3).png', 1, 'zh_CN',
    '蒲江县', '四川', '中国', 1, DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 18 MINUTE),
    'region_admin', 22, 0, '{"device":"Android","version":"8.0.33"}', '蒲江县管理员', 1),

-- 都江堰市管理员
    ('openid_admin_510181', 'unionid_admin_510181', 'USR20230023', '13800002016',
    '都江堰市管理员', '/api-storage/inherent/profile_photo/investor/investor_avatar (1).png', 1, 'zh_CN',
    '都江堰市', '四川', '中国', 1, DATE_SUB(NOW(), INTERVAL 12 DAY), DATE_SUB(NOW(), INTERVAL 7 MINUTE),
    'region_admin', 38, 0, '{"device":"Android","version":"8.0.33"}', '都江堰市管理员', 1),

-- 彭州市管理员
    ('openid_admin_510182', 'unionid_admin_510182', 'USR20230024', '13800002017',
    '彭州市管理员', '/api-storage/inherent/profile_photo/investor/investor_avatar (2).png', 2, 'zh_CN',
    '彭州市', '四川', '中国', 1, DATE_SUB(NOW(), INTERVAL 9 DAY), DATE_SUB(NOW(), INTERVAL 25 MINUTE),
    'region_admin', 31, 0, '{"device":"iPhone","version":"8.0.33"}', '彭州市管理员', 1),

-- 邛崃市管理员
    ('openid_admin_510183', 'unionid_admin_510183', 'USR20230025', '13800002018',
    '邛崃市管理员', '/api-storage/inherent/profile_photo/investor/investor_avatar (3).png', 1, 'zh_CN',
    '邛崃市', '四川', '中国', 1, DATE_SUB(NOW(), INTERVAL 15 DAY), DATE_SUB(NOW(), INTERVAL 10 MINUTE),
    'region_admin', 45, 0, '{"device":"Android","version":"8.0.33"}', '邛崃市管理员', 1),

-- 崇州市管理员
    ('openid_admin_510184', 'unionid_admin_510184', 'USR20230026', '13800002019',
    '崇州市管理员', '/api-storage/inherent/profile_photo/investor/investor_avatar (1).png', 1, 'zh_CN',
    '崇州市', '四川', '中国', 1, DATE_SUB(NOW(), INTERVAL 12 DAY), DATE_SUB(NOW(), INTERVAL 20 MINUTE),
    'region_admin', 38, 0, '{"device":"Android","version":"8.0.33"}', '崇州市管理员', 1),

-- 简阳市管理员
    ('openid_admin_510185', 'unionid_admin_510185', 'USR20230027', '13800002020',
    '简阳市管理员', '/api-storage/inherent/profile_photo/investor/investor_avatar (2).png', 1, 'zh_CN',
    '简阳市', '四川', '中国', 1, DATE_SUB(NOW(), INTERVAL 18 DAY), DATE_SUB(NOW(), INTERVAL 5 MINUTE),
    'region_admin', 52, 0, '{"device":"Android","version":"8.0.33"}', '简阳市管理员', 1);
-- =====================================================
-- 4. 工厂管理员 (FACTORY_MANAGER)
-- =====================================================
INSERT INTO app_user (
    openid, unionid, user_code, phone, nickname, avatar_url, gender, language,
    city, province, country, register_source, register_time, last_login_time,
    role, login_count, status, device_info, remark, creator
) VALUES
-- 北京工厂管理员
('openid_factory_mgr_bj', 'unionid_factory_mgr_bj', 'USR20240011', '13800003001',
 '北京工厂长', '/api-storage/inherent/profile_photo/factory_manager/factory_mgr_avatar (1).png', 1, 'zh_CN',
 '北京', '北京', '中国', 1, DATE_SUB(NOW(), INTERVAL 30 DAY), DATE_SUB(NOW(), INTERVAL 1 HOUR),
 'factory_manager', 80, 0, '{"device":"Android","version":"8.0.33"}', '北京工厂管理员', 1),

-- 上海工厂管理员
('openid_factory_mgr_sh', 'unionid_factory_mgr_sh', 'USR20240012', '13800003002',
 '上海工厂长', '/api-storage/inherent/profile_photo/factory_manager/factory_mgr_avatar (2).png', 2, 'zh_CN',
 '上海', '上海', '中国', 1, DATE_SUB(NOW(), INTERVAL 20 DAY), DATE_SUB(NOW(), INTERVAL 30 MINUTE),
 'factory_manager', 65, 0, '{"device":"iPhone","version":"8.0.33"}', '上海工厂管理员', 1),

-- 广州工厂管理员
('openid_factory_mgr_gz', 'unionid_factory_mgr_gz', 'USR20240013', '13800003003',
 '广州工厂长', '/api-storage/inherent/profile_photo/factory_manager/factory_mgr_avatar (3).png', 1, 'zh_CN',
 '广州', '广东', '中国', 1, DATE_SUB(NOW(), INTERVAL 25 DAY), DATE_SUB(NOW(), INTERVAL 2 HOUR),
 'factory_manager', 72, 0, '{"device":"Android","version":"8.0.33"}', '广州工厂管理员', 1);

-- =====================================================
-- 5. 洗护工 (CLEANER)
-- =====================================================
INSERT INTO app_user (
    openid, unionid, user_code, phone, nickname, avatar_url, gender, language,
    city, province, country, register_source, register_time, last_login_time,
    role, login_count, status, device_info, remark, creator
) VALUES
-- 北京洗护工
('openid_cleaner_bj_01', 'unionid_cleaner_bj_01', 'USR20240014', '13800004001',
 '张师傅', '/api-storage/inherent/profile_photo/cleaner/cleaner_avatar (1).png', 1, 'zh_CN',
 '北京', '北京', '中国', 1, DATE_SUB(NOW(), INTERVAL 15 DAY), DATE_SUB(NOW(), INTERVAL 10 MINUTE),
 'cleaner', 45, 0, '{"device":"Android","version":"8.0.33"}', '北京工厂洗护工', 1),

('openid_cleaner_bj_02', 'unionid_cleaner_bj_02', 'USR20240015', '13800004002',
 '李师傅', '/api-storage/inherent/profile_photo/cleaner/cleaner_avatar (2).png', 1, 'zh_CN',
 '北京', '北京', '中国', 1, DATE_SUB(NOW(), INTERVAL 12 DAY), DATE_SUB(NOW(), INTERVAL 20 MINUTE),
 'cleaner', 38, 0, '{"device":"Android","version":"8.0.33"}', '北京工厂洗护工', 1),

-- 上海洗护工
('openid_cleaner_sh_01', 'unionid_cleaner_sh_01', 'USR20240016', '13800004003',
 '王师傅', '/api-storage/inherent/profile_photo/cleaner/cleaner_avatar (3).png', 1, 'zh_CN',
 '上海', '上海', '中国', 1, DATE_SUB(NOW(), INTERVAL 18 DAY), DATE_SUB(NOW(), INTERVAL 5 MINUTE),
 'cleaner', 52, 0, '{"device":"Android","version":"8.0.33"}', '上海工厂洗护工', 1),

('openid_cleaner_sh_02', 'unionid_cleaner_sh_02', 'USR20240017', '13800004004',
 '赵师傅', '/api-storage/inherent/profile_photo/cleaner/cleaner_avatar (4).png', 1, 'zh_CN',
 '上海', '上海', '中国', 1, DATE_SUB(NOW(), INTERVAL 8 DAY), DATE_SUB(NOW(), INTERVAL 15 MINUTE),
 'cleaner', 28, 0, '{"device":"Android","version":"8.0.33"}', '上海工厂洗护工', 1),

-- 广州洗护工
('openid_cleaner_gz_01', 'unionid_cleaner_gz_01', 'USR20240018', '13800004005',
 '刘师傅', '/api-storage/inherent/profile_photo/cleaner/cleaner_avatar (5).png', 1, 'zh_CN',
 '广州', '广东', '中国', 1, DATE_SUB(NOW(), INTERVAL 22 DAY), DATE_SUB(NOW(), INTERVAL 8 MINUTE),
 'cleaner', 60, 0, '{"device":"Android","version":"8.0.33"}', '广州工厂洗护工', 1);

-- =====================================================
-- 6. 取送员 (DELIVERY)
-- =====================================================
INSERT INTO app_user (
    openid, unionid, user_code, phone, nickname, avatar_url, gender, language,
    city, province, country, register_source, register_time, last_login_time,
    role, login_count, status, device_info, remark, creator
) VALUES
-- 北京取送员
('openid_delivery_bj_01', 'unionid_delivery_bj_01', 'USR20240019', '13800005001',
 '小王快递', '/api-storage/inherent/profile_photo/delivery/delivery_avatar (1).png', 1, 'zh_CN',
 '北京', '北京', '中国', 1, DATE_SUB(NOW(), INTERVAL 10 DAY), DATE_SUB(NOW(), INTERVAL 5 MINUTE),
 'delivery', 35, 0, '{"device":"Android","version":"8.0.33"}', '北京区域取送员', 1),

('openid_delivery_bj_02', 'unionid_delivery_bj_02', 'USR20240020', '13800005002',
 '小李快递', '/api-storage/inherent/profile_photo/delivery/delivery_avatar (2).png', 1, 'zh_CN',
 '北京', '北京', '中国', 1, DATE_SUB(NOW(), INTERVAL 7 DAY), DATE_SUB(NOW(), INTERVAL 12 MINUTE),
 'delivery', 28, 0, '{"device":"Android","version":"8.0.33"}', '北京区域取送员', 1),

-- 上海取送员
('openid_delivery_sh_01', 'unionid_delivery_sh_01', 'USR20240021', '13800005003',
 '小张快递', '/api-storage/inherent/profile_photo/delivery/delivery_avatar (3).png', 2, 'zh_CN',
 '上海', '上海', '中国', 1, DATE_SUB(NOW(), INTERVAL 14 DAY), DATE_SUB(NOW(), INTERVAL 3 MINUTE),
 'delivery', 42, 0, '{"device":"iPhone","version":"8.0.33"}', '上海区域取送员', 1),

('openid_delivery_sh_02', 'unionid_delivery_sh_02', 'USR20240022', '13800005004',
 '小赵快递', '/api-storage/inherent/profile_photo/delivery/delivery_avatar (4).png', 1, 'zh_CN',
 '上海', '上海', '中国', 1, DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 18 MINUTE),
 'delivery', 22, 0, '{"device":"Android","version":"8.0.33"}', '上海区域取送员', 1),

-- 广州取送员
('openid_delivery_gz_01', 'unionid_delivery_gz_01', 'USR20240023', '13800005005',
 '小刘快递', '/api-storage/inherent/profile_photo/delivery/delivery_avatar (5).png', 1, 'zh_CN',
 '广州', '广东', '中国', 1, DATE_SUB(NOW(), INTERVAL 12 DAY), DATE_SUB(NOW(), INTERVAL 7 MINUTE),
 'delivery', 38, 0, '{"device":"Android","version":"8.0.33"}', '广州区域取送员', 1),

('openid_delivery_gz_02', 'unionid_delivery_gz_02', 'USR20240024', '13800005006',
 '小陈快递', '/api-storage/inherent/profile_photo/delivery/delivery_avatar (6).png', 2, 'zh_CN',
 '广州', '广东', '中国', 1, DATE_SUB(NOW(), INTERVAL 9 DAY), DATE_SUB(NOW(), INTERVAL 25 MINUTE),
 'delivery', 31, 0, '{"device":"iPhone","version":"8.0.33"}', '广州区域取送员', 1);

-- =====================================================
-- 7. 会员身份数据 (MEMBER_IDENTITY)
-- =====================================================
-- 为所有 customer 角色的用户创建对应的会员身份
INSERT INTO member_identity (
    app_user_id, member_no, level, points, total_points, total_consumed,
    phone, real_name, status, creator
) VALUES
-- 对应 USR20240003 张小明 - 普通会员
(3, 'M20240003001', 1, 150, 500, 299.50, '13800001001', '张小明', 0, 1),

-- 对应 USR20240004 李小红 - 银卡会员
(4, 'M20240004001', 2, 280, 800, 599.80, '13800001002', '李小红', 0, 1),

-- 对应 USR20240005 王大华 - 金卡会员
(5, 'M20240005001', 3, 520, 1500, 1299.90, '13800001003', '王大华', 0, 1),

-- 对应 USR20240006 赵小美 - 普通会员
(6, 'M20240006001', 1, 50, 100, 89.90, '13800001004', '赵小美', 0, 1),

-- 对应 USR20240007 刘老板 - 钻石会员
(7, 'M20240007001', 4, 1200, 5000, 3999.99, '13800001005', '刘老板', 0, 1);



-- =====================================================
-- 9. 数据验证查询
-- =====================================================
-- 验证用户数据插入情况
SELECT '用户角色分布' AS info;
SELECT role, COUNT(*) as count FROM app_user WHERE deleted = 0 GROUP BY role;

-- 验证会员身份数据
SELECT '会员等级分布' AS info;
SELECT level, COUNT(*) as count FROM member_identity WHERE deleted = 0 GROUP BY level;

-- 验证用户总数
SELECT '数据插入汇总' AS info;
SELECT
    (SELECT COUNT(*) FROM app_user WHERE deleted = 0) AS total_users,
    (SELECT COUNT(*) FROM member_identity WHERE deleted = 0) AS total_members,
    (SELECT COUNT(*) FROM user_review WHERE deleted = 0) AS total_reviews;

-- =====================================================
-- 10. 数据说明
-- =====================================================
/*
用户角色分布：
- root: 2个 (系统管理员)
- customer: 5个 (会员用户，都有对应的member_identity记录)
- investor: 3个 (区域投资人：北京、上海、广州)
- factory_manager: 3个 (工厂管理员：北京、上海、广州)
- cleaner: 5个 (洗护工：北京2个、上海2个、广州1个)
- delivery: 6个 (取送员：每个城市2个)

会员等级分布：
- 普通会员(1): 2个
- 银卡会员(2): 1个
- 金卡会员(3): 1个
- 钻石会员(4): 1个

用户状态：
- 所有用户状态都是正常(0)
- 可以根据需要修改部分用户状态进行测试

设备信息：
- 包含iPhone、Android、iPad等不同设备类型
- 版本号统一为8.0.33

注册来源：
- 全部来自小程序(1)
- 可以根据需要修改部分用户的注册来源

联系方式：
- 手机号按角色分段：
  - 管理员: 138000000xx
  - 会员: 138000010xx
  - 投资人: 138000020xx
  - 工厂管理员: 138000030xx
  - 洗护工: 138000040xx
  - 取送员: 138000050xx

头像路径：
- 使用统一的存储路径格式
- 按角色分类命名

评价数据：
- 为5个会员用户各创建了1条评价记录
- 评分范围7-10分
- 包含不同的标签组合
- 部分用户设置为回头客
- 部分评价设置为匿名
*/
