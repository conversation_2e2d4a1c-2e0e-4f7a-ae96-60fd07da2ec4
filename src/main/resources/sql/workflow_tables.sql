-- 订单任务表
CREATE TABLE `order_task` (
    /* 主键字段 */
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID（自增）',
    
    /* 关联信息 */
    `order_id` bigint NOT NULL COMMENT '关联订单ID',
    `task_no` varchar(32) NOT NULL COMMENT '任务编号（规则：OT+年月日+6位序列）',
    
    /* 任务状态 */
    `task_status` int NOT NULL DEFAULT 10 COMMENT '任务状态：10-待分配取送员 20-已分配取送员 30-取送员前往取件 40-已取件打包 50-生成订单二维码 60-运输中 70-工厂已接收 80-工厂拆包中 90-生成衣物二维码 100-清洗处理中 110-质检中 120-清洗完成打包 130-生成出厂二维码 140-待分配送货员 150-已分配送货员 160-送货员已取件 170-配送中 180-已送达 190-订单完成 200-异常 210-已取消',
    
    /* 取件信息 */
    `pickup_courier_id` bigint DEFAULT NULL COMMENT '取件快递员ID',
    `pickup_start_time` datetime DEFAULT NULL COMMENT '取件开始时间',
    `pickup_completion_time` datetime DEFAULT NULL COMMENT '取件完成时间',
    `order_qr_code_id` bigint DEFAULT NULL COMMENT '订单二维码ID',
    
    /* 工厂处理信息 */
    `factory_id` bigint DEFAULT NULL COMMENT '处理工厂ID',
    `factory_receive_time` datetime DEFAULT NULL COMMENT '工厂接收时间',
    `factory_operator_id` bigint DEFAULT NULL COMMENT '工厂操作员ID',
    `unpacking_time` datetime DEFAULT NULL COMMENT '拆包时间',
    `clothing_qr_generated_time` datetime DEFAULT NULL COMMENT '衣物二维码生成时间',
    `processing_start_time` datetime DEFAULT NULL COMMENT '处理开始时间',
    `processing_completion_time` datetime DEFAULT NULL COMMENT '处理完成时间',
    `quality_check_time` datetime DEFAULT NULL COMMENT '质检时间',
    `quality_checker_id` bigint DEFAULT NULL COMMENT '质检员ID',
    `packaging_time` datetime DEFAULT NULL COMMENT '打包时间',
    `factory_exit_qr_code_id` bigint DEFAULT NULL COMMENT '出厂二维码ID',
    
    /* 配送信息 */
    `delivery_courier_id` bigint DEFAULT NULL COMMENT '送货快递员ID',
    `delivery_pickup_time` datetime DEFAULT NULL COMMENT '送货员取件时间',
    `delivery_start_time` datetime DEFAULT NULL COMMENT '配送开始时间',
    `delivery_completion_time` datetime DEFAULT NULL COMMENT '配送完成时间',
    `cabinet_id` bigint DEFAULT NULL COMMENT '智能柜ID（如果使用智能柜配送）',
    `cabinet_cell_no` varchar(20) DEFAULT NULL COMMENT '智能柜格口号',
    
    /* 异常信息 */
    `exception_reason` varchar(500) DEFAULT NULL COMMENT '异常原因',
    `exception_code` varchar(20) DEFAULT NULL COMMENT '异常类型编码',
    
    /* 时间信息 */
    `estimated_completion_time` datetime DEFAULT NULL COMMENT '预计完成时间',
    `actual_completion_time` datetime DEFAULT NULL COMMENT '实际完成时间',
    
    /* 其他信息 */
    `process_notes` varchar(1000) DEFAULT NULL COMMENT '处理备注',
    `total_clothing_items` int DEFAULT 0 COMMENT '衣物总件数',
    `processed_clothing_items` int DEFAULT 0 COMMENT '已处理衣物件数',
    
    /* 系统字段 */
    `creator` bigint NOT NULL COMMENT '创建人ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version` int NOT NULL DEFAULT 1 COMMENT '乐观锁版本号',
    `deleted` tinyint NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_id` (`order_id`),
    UNIQUE KEY `uk_task_no` (`task_no`),
    KEY `idx_task_status` (`task_status`),
    KEY `idx_pickup_courier` (`pickup_courier_id`),
    KEY `idx_delivery_courier` (`delivery_courier_id`),
    KEY `idx_factory` (`factory_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单任务表';

-- 衣物单品表
CREATE TABLE `clothing_item` (
    /* 主键字段 */
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID（自增）',
    
    /* 关联信息 */
    `order_id` bigint NOT NULL COMMENT '关联订单ID',
    `order_task_id` bigint NOT NULL COMMENT '关联订单任务ID',
    `factory_order_id` bigint NOT NULL COMMENT '关联工厂订单ID',
    
    /* 衣物基本信息 */
    `item_no` varchar(32) NOT NULL COMMENT '衣物编号（规则：CI+年月日+8位序列）',
    `item_name` varchar(100) NOT NULL COMMENT '衣物名称',
    `item_type` int NOT NULL COMMENT '衣物类型：1-上衣 2-裤子 3-裙子 4-外套 5-内衣 6-其他',
    `brand` varchar(50) DEFAULT NULL COMMENT '品牌',
    `color` varchar(50) DEFAULT NULL COMMENT '颜色',
    `size` varchar(20) DEFAULT NULL COMMENT '尺码',
    `material` varchar(50) DEFAULT NULL COMMENT '材质',
    `cleaning_type` int NOT NULL COMMENT '清洗类型：0-标洗 1-精洗',
    
    /* 二维码信息 */
    `qr_code_id` bigint DEFAULT NULL COMMENT '衣物二维码ID',
    
    /* 处理状态 */
    `process_status` int NOT NULL DEFAULT 0 COMMENT '处理状态：0-待处理 1-清洗中 2-烘干中 3-熨烫中 4-质检中 5-已完成 6-异常',
    
    /* 处理时间 */
    `process_start_time` datetime DEFAULT NULL COMMENT '处理开始时间',
    `process_end_time` datetime DEFAULT NULL COMMENT '处理结束时间',
    
    /* 清洗阶段 */
    `washing_start_time` datetime DEFAULT NULL COMMENT '清洗开始时间',
    `washing_end_time` datetime DEFAULT NULL COMMENT '清洗结束时间',
    `washing_operator_id` bigint DEFAULT NULL COMMENT '清洗操作员ID',
    
    /* 烘干阶段 */
    `drying_start_time` datetime DEFAULT NULL COMMENT '烘干开始时间',
    `drying_end_time` datetime DEFAULT NULL COMMENT '烘干结束时间',
    `drying_operator_id` bigint DEFAULT NULL COMMENT '烘干操作员ID',
    
    /* 熨烫阶段 */
    `ironing_start_time` datetime DEFAULT NULL COMMENT '熨烫开始时间',
    `ironing_end_time` datetime DEFAULT NULL COMMENT '熨烫结束时间',
    `ironing_operator_id` bigint DEFAULT NULL COMMENT '熨烫操作员ID',
    
    /* 质检信息 */
    `quality_check_time` datetime DEFAULT NULL COMMENT '质检时间',
    `quality_checker_id` bigint DEFAULT NULL COMMENT '质检员ID',
    `quality_score` int DEFAULT NULL COMMENT '质量评分（1-5星）',
    `quality_passed` tinyint DEFAULT NULL COMMENT '质检是否通过',
    `quality_notes` varchar(500) DEFAULT NULL COMMENT '质检备注',
    `defect_description` varchar(500) DEFAULT NULL COMMENT '瑕疵描述',
    
    /* 其他信息 */
    `process_images` text DEFAULT NULL COMMENT '处理过程照片（JSON格式）',
    `special_instructions` varchar(500) DEFAULT NULL COMMENT '特殊处理说明',
    `reprocess_count` int DEFAULT 0 COMMENT '重新处理次数',
    `exception_reason` varchar(500) DEFAULT NULL COMMENT '异常原因',
    
    /* 系统字段 */
    `creator` bigint NOT NULL COMMENT '创建人ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version` int NOT NULL DEFAULT 1 COMMENT '乐观锁版本号',
    `deleted` tinyint NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_item_no` (`item_no`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_order_task_id` (`order_task_id`),
    KEY `idx_factory_order_id` (`factory_order_id`),
    KEY `idx_process_status` (`process_status`),
    KEY `idx_qr_code_id` (`qr_code_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='衣物单品表';

-- 工厂处理阶段表
CREATE TABLE `factory_process_stage` (
    /* 主键字段 */
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID（自增）',
    
    /* 关联信息 */
    `clothing_item_id` bigint NOT NULL COMMENT '关联衣物ID',
    `factory_order_id` bigint NOT NULL COMMENT '关联工厂订单ID',
    
    /* 阶段信息 */
    `stage_type` int NOT NULL COMMENT '阶段类型：1-清洗 2-烘干 3-熨烫 4-质检',
    `stage_name` varchar(50) NOT NULL COMMENT '阶段名称',
    `stage_status` int NOT NULL DEFAULT 0 COMMENT '阶段状态：0-待开始 1-进行中 2-已完成 3-异常',
    
    /* 操作员信息 */
    `operator_id` bigint DEFAULT NULL COMMENT '操作员ID',
    `operator_name` varchar(50) DEFAULT NULL COMMENT '操作员姓名',
    
    /* 时间信息 */
    `start_time` datetime DEFAULT NULL COMMENT '开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '结束时间',
    `estimated_duration` int DEFAULT NULL COMMENT '预计耗时（分钟）',
    `actual_duration` int DEFAULT NULL COMMENT '实际耗时（分钟）',
    
    /* 质量信息 */
    `quality_score` int DEFAULT NULL COMMENT '质量评分（1-5星）',
    `quality_notes` varchar(500) DEFAULT NULL COMMENT '质量备注',
    
    /* 其他信息 */
    `process_notes` varchar(500) DEFAULT NULL COMMENT '处理备注',
    `exception_reason` varchar(500) DEFAULT NULL COMMENT '异常原因',
    `process_images` text DEFAULT NULL COMMENT '处理过程照片（JSON格式）',
    
    /* 系统字段 */
    `creator` bigint NOT NULL COMMENT '创建人ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version` int NOT NULL DEFAULT 1 COMMENT '乐观锁版本号',
    `deleted` tinyint NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
    
    PRIMARY KEY (`id`),
    KEY `idx_clothing_item_id` (`clothing_item_id`),
    KEY `idx_factory_order_id` (`factory_order_id`),
    KEY `idx_stage_type` (`stage_type`),
    KEY `idx_stage_status` (`stage_status`),
    KEY `idx_operator_id` (`operator_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工厂处理阶段表';

-- 添加索引优化
ALTER TABLE `order_task` ADD INDEX `idx_status_factory` (`task_status`, `factory_id`);
ALTER TABLE `order_task` ADD INDEX `idx_status_courier` (`task_status`, `pickup_courier_id`);
ALTER TABLE `order_task` ADD INDEX `idx_status_delivery` (`task_status`, `delivery_courier_id`);

ALTER TABLE `clothing_item` ADD INDEX `idx_status_operator` (`process_status`, `washing_operator_id`);
ALTER TABLE `clothing_item` ADD INDEX `idx_quality_check` (`quality_passed`, `quality_checker_id`);

-- 添加外键约束（可选，根据实际需要）
-- ALTER TABLE `order_task` ADD CONSTRAINT `fk_order_task_order` FOREIGN KEY (`order_id`) REFERENCES `order_main` (`id`);
-- ALTER TABLE `clothing_item` ADD CONSTRAINT `fk_clothing_item_order` FOREIGN KEY (`order_id`) REFERENCES `order_main` (`id`);
-- ALTER TABLE `clothing_item` ADD CONSTRAINT `fk_clothing_item_task` FOREIGN KEY (`order_task_id`) REFERENCES `order_task` (`id`);
-- ALTER TABLE `factory_process_stage` ADD CONSTRAINT `fk_process_stage_clothing` FOREIGN KEY (`clothing_item_id`) REFERENCES `clothing_item` (`id`);
