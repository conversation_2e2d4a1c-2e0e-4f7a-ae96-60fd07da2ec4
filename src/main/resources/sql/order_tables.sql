-- =====================================================
-- 订单相关数据库表创建脚本
-- 创建时间: 2025-07-13
-- 描述: 洗衣服务平台订单系统相关表结构
-- =====================================================

-- 订单配送信息表
CREATE TABLE `order_delivery_info`
(
    /* 主键字段 */
    `id`                     bigint      NOT NULL AUTO_INCREMENT COMMENT '主键ID（自增）',

    /* 关联信息 */
    `order_id`               bigint      NOT NULL COMMENT '关联订单ID（引用order_main表）',

    /* 取件信息 */
    `pickup_address_id`      bigint      NOT NULL COMMENT '取件地址ID（引用member_address表）',
    `pickup_contact_name`    varchar(30) NOT NULL COMMENT '取件联系人姓名',
    `pickup_contact_phone`   varchar(11) NOT NULL COMMENT '取件联系人电话',
    `pickup_detail_address`  varchar(200) NOT NULL COMMENT '取件详细地址',
    `pickup_longitude`       decimal(10, 7) DEFAULT NULL COMMENT '取件地址经度',
    `pickup_latitude`        decimal(10, 7) DEFAULT NULL COMMENT '取件地址纬度',

    /* 送件信息 */
    `delivery_address_id`    bigint      NOT NULL COMMENT '送件地址ID（引用member_address表）',
    `delivery_contact_name`  varchar(30) NOT NULL COMMENT '送件联系人姓名',
    `delivery_contact_phone` varchar(11) NOT NULL COMMENT '送件联系人电话',
    `delivery_detail_address` varchar(200) NOT NULL COMMENT '送件详细地址',
    `delivery_longitude`     decimal(10, 7) DEFAULT NULL COMMENT '送件地址经度',
    `delivery_latitude`      decimal(10, 7) DEFAULT NULL COMMENT '送件地址纬度',

    /* 配送时间信息 */
    `estimated_pickup_time`  datetime(3) DEFAULT NULL COMMENT '预计取件时间',
    `estimated_delivery_time` datetime(3) DEFAULT NULL COMMENT '预计送达时间',
    `actual_pickup_time`     datetime(3) DEFAULT NULL COMMENT '实际取件时间',
    `actual_delivery_time`   datetime(3) DEFAULT NULL COMMENT '实际送达时间',

    /* 配送选项 */
    `is_urgent`              bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否加急：0-普通 1-加急',
    `urgent_fee`             decimal(8, 2) DEFAULT 0.00 COMMENT '加急费用',
    `delivery_fee`           decimal(8, 2) DEFAULT 0.00 COMMENT '配送费用',

    /* 系统字段 */
    `create_time`            datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `update_time`            datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `creator`                bigint      DEFAULT NULL COMMENT '创建人ID',
    `updater`                bigint      DEFAULT NULL COMMENT '更新人ID',
    `deleted`                tinyint(1)  NOT NULL DEFAULT 0 COMMENT '逻辑删除标记：0-正常 1-已删除',

    /* 主键与索引 */
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_id` (`order_id`) COMMENT '订单ID唯一索引',
    KEY `idx_pickup_address` (`pickup_address_id`) COMMENT '取件地址索引',
    KEY `idx_delivery_address` (`delivery_address_id`) COMMENT '送件地址索引',
    KEY `idx_pickup_time` (`estimated_pickup_time`) COMMENT '预计取件时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单配送信息表';

-- 订单服务项目表
CREATE TABLE `order_service_item`
(
    /* 主键字段 */
    `id`                bigint         NOT NULL AUTO_INCREMENT COMMENT '主键ID（自增）',

    /* 关联信息 */
    `order_id`          bigint         NOT NULL COMMENT '关联订单ID（引用order_main表）',
    `service_id`        bigint         NOT NULL COMMENT '服务项目ID（引用service_info表）',

    /* 服务信息 */
    `service_name`      varchar(100)   NOT NULL COMMENT '服务名称',
    `service_type`      tinyint        NOT NULL COMMENT '清洗类型：0-标准洗 1-精洗',
    `category_id`       bigint         DEFAULT NULL COMMENT '衣物分类ID',
    `category_name`     varchar(50)    DEFAULT NULL COMMENT '衣物分类名称',

    /* 数量和价格 */
    `quantity`          int            NOT NULL DEFAULT 1 COMMENT '数量',
    `unit_price`        decimal(8, 2)  NOT NULL COMMENT '单价',
    `total_price`       decimal(8, 2)  NOT NULL COMMENT '小计金额',

    /* 服务选项 */
    `is_premium`        bit(1)         NOT NULL DEFAULT b'0' COMMENT '是否精洗：0-普通 1-精洗',
    `premium_fee`       decimal(8, 2)  DEFAULT 0.00 COMMENT '精洗费用',
    `special_requirements` text        DEFAULT NULL COMMENT '特殊要求',

    /* 处理状态 */
    `process_status`    tinyint        NOT NULL DEFAULT 1 COMMENT '处理状态：1-待处理 2-处理中 3-已完成',
    `process_notes`     text           DEFAULT NULL COMMENT '处理备注',

    /* 系统字段 */
    `create_time`       datetime(3)    NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `update_time`       datetime(3)    NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `creator`           bigint         DEFAULT NULL COMMENT '创建人ID',
    `updater`           bigint         DEFAULT NULL COMMENT '更新人ID',
    `deleted`           tinyint(1)     NOT NULL DEFAULT 0 COMMENT '逻辑删除标记：0-正常 1-已删除',

    /* 主键与索引 */
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`) COMMENT '订单ID索引',
    KEY `idx_service_id` (`service_id`) COMMENT '服务ID索引',
    KEY `idx_category` (`category_id`) COMMENT '分类索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单服务项目表';

-- 订单优惠券使用记录表
CREATE TABLE `order_coupon_usage`
(
    /* 主键字段 */
    `id`                bigint         NOT NULL AUTO_INCREMENT COMMENT '主键ID（自增）',

    /* 关联信息 */
    `order_id`          bigint         NOT NULL COMMENT '关联订单ID（引用order_main表）',
    `coupon_id`         bigint         NOT NULL COMMENT '优惠券ID（引用coupon表）',
    `member_id`         bigint         NOT NULL COMMENT '会员ID（引用member表）',

    /* 优惠券信息 */
    `coupon_name`       varchar(100)   NOT NULL COMMENT '优惠券名称',
    `coupon_type`       tinyint        NOT NULL COMMENT '优惠券类型：1-满减券 2-折扣券',
    `discount_amount`   decimal(8, 2)  NOT NULL COMMENT '优惠金额',
    `min_amount`        decimal(8, 2)  DEFAULT 0.00 COMMENT '最低消费金额',

    /* 使用状态 */
    `usage_status`      tinyint        NOT NULL DEFAULT 1 COMMENT '使用状态：1-已使用 2-已退回',
    `usage_time`        datetime(3)    NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '使用时间',
    `return_time`       datetime(3)    DEFAULT NULL COMMENT '退回时间',

    /* 系统字段 */
    `create_time`       datetime(3)    NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `update_time`       datetime(3)    NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `creator`           bigint         DEFAULT NULL COMMENT '创建人ID',
    `updater`           bigint         DEFAULT NULL COMMENT '更新人ID',
    `deleted`           tinyint(1)     NOT NULL DEFAULT 0 COMMENT '逻辑删除标记：0-正常 1-已删除',

    /* 主键与索引 */
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`) COMMENT '订单ID索引',
    KEY `idx_coupon_id` (`coupon_id`) COMMENT '优惠券ID索引',
    KEY `idx_member_id` (`member_id`) COMMENT '会员ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单优惠券使用记录表';

-- 订单退款记录表
CREATE TABLE `order_refund`
(
    /* 主键字段 */
    `id`                bigint         NOT NULL AUTO_INCREMENT COMMENT '主键ID（自增）',

    /* 关联信息 */
    `order_id`          bigint         NOT NULL COMMENT '关联订单ID（引用order_main表）',
    `payment_id`        bigint         DEFAULT NULL COMMENT '关联支付记录ID（引用order_payment表）',

    /* 退款基本信息 */
    `refund_no`         varchar(32)    NOT NULL COMMENT '退款单号（规则：REF+年月日+8位序列）',
    `refund_amount`     decimal(8, 2)  NOT NULL COMMENT '退款金额',
    `refund_reason`     varchar(200)   NOT NULL COMMENT '退款原因',
    `refund_type`       tinyint        NOT NULL COMMENT '退款类型：1-用户申请 2-系统自动 3-客服处理',

    /* 退款状态 */
    `refund_status`     tinyint        NOT NULL DEFAULT 1 COMMENT '退款状态：1-申请中 2-审核通过 3-退款中 4-退款成功 5-退款失败 6-已拒绝',
    `apply_time`        datetime(3)    NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '申请时间',
    `approve_time`      datetime(3)    DEFAULT NULL COMMENT '审核时间',
    `refund_time`       datetime(3)    DEFAULT NULL COMMENT '退款完成时间',

    /* 第三方信息 */
    `third_party_refund_no` varchar(64) DEFAULT NULL COMMENT '第三方退款单号',
    `refund_method`     tinyint        DEFAULT NULL COMMENT '退款方式：1-原路退回 2-余额退款',

    /* 审核信息 */
    `approver_id`       bigint         DEFAULT NULL COMMENT '审核人ID',
    `approve_notes`     text           DEFAULT NULL COMMENT '审核备注',
    `reject_reason`     varchar(200)   DEFAULT NULL COMMENT '拒绝原因',

    /* 系统字段 */
    `create_time`       datetime(3)    NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `update_time`       datetime(3)    NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `creator`           bigint         DEFAULT NULL COMMENT '创建人ID',
    `updater`           bigint         DEFAULT NULL COMMENT '更新人ID',
    `deleted`           tinyint(1)     NOT NULL DEFAULT 0 COMMENT '逻辑删除标记：0-正常 1-已删除',

    /* 主键与索引 */
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_refund_no` (`refund_no`) COMMENT '退款单号唯一索引',
    KEY `idx_order_id` (`order_id`) COMMENT '订单ID索引',
    KEY `idx_payment_id` (`payment_id`) COMMENT '支付ID索引',
    KEY `idx_status` (`refund_status`) COMMENT '退款状态索引',
    KEY `idx_apply_time` (`apply_time`) COMMENT '申请时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单退款记录表';

-- 订单配送路径表
CREATE TABLE `order_delivery_route`
(
    /* 主键字段 */
    `id`                bigint         NOT NULL AUTO_INCREMENT COMMENT '主键ID（自增）',

    /* 关联信息 */
    `order_id`          bigint         NOT NULL COMMENT '关联订单ID（引用order_main表）',

    /* 路径节点信息 */
    `route_step`        tinyint        NOT NULL COMMENT '路径步骤：1-用户地址到柜子 2-柜子到工厂 3-工厂到柜子 4-柜子到用户地址',
    `from_type`         tinyint        NOT NULL COMMENT '起点类型：1-用户地址 2-智能柜 3-洗衣工厂',
    `from_id`           bigint         NOT NULL COMMENT '起点ID（根据类型关联不同表）',
    `from_address`      varchar(200)   NOT NULL COMMENT '起点地址',
    `from_longitude`    decimal(10, 7) DEFAULT NULL COMMENT '起点经度',
    `from_latitude`     decimal(10, 7) DEFAULT NULL COMMENT '起点纬度',

    `to_type`           tinyint        NOT NULL COMMENT '终点类型：1-用户地址 2-智能柜 3-洗衣工厂',
    `to_id`             bigint         NOT NULL COMMENT '终点ID（根据类型关联不同表）',
    `to_address`        varchar(200)   NOT NULL COMMENT '终点地址',
    `to_longitude`      decimal(10, 7) DEFAULT NULL COMMENT '终点经度',
    `to_latitude`       decimal(10, 7) DEFAULT NULL COMMENT '终点纬度',

    /* 距离和时间 */
    `distance`          decimal(8, 2)  DEFAULT NULL COMMENT '距离（公里）',
    `estimated_duration` int           DEFAULT NULL COMMENT '预计耗时（分钟）',
    `actual_duration`   int            DEFAULT NULL COMMENT '实际耗时（分钟）',

    /* 配送员信息 */
    `courier_id`        bigint         DEFAULT NULL COMMENT '配送员ID',
    `courier_name`      varchar(30)    DEFAULT NULL COMMENT '配送员姓名',
    `courier_phone`     varchar(11)    DEFAULT NULL COMMENT '配送员电话',

    /* 状态信息 */
    `route_status`      tinyint        NOT NULL DEFAULT 1 COMMENT '路径状态：1-待执行 2-执行中 3-已完成 4-异常',
    `start_time`        datetime(3)    DEFAULT NULL COMMENT '开始时间',
    `complete_time`     datetime(3)    DEFAULT NULL COMMENT '完成时间',

    /* 系统字段 */
    `create_time`       datetime(3)    NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `update_time`       datetime(3)    NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `creator`           bigint         DEFAULT NULL COMMENT '创建人ID',
    `updater`           bigint         DEFAULT NULL COMMENT '更新人ID',
    `deleted`           tinyint(1)     NOT NULL DEFAULT 0 COMMENT '逻辑删除标记：0-正常 1-已删除',

    /* 主键与索引 */
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`) COMMENT '订单ID索引',
    KEY `idx_route_step` (`route_step`) COMMENT '路径步骤索引',
    KEY `idx_courier` (`courier_id`) COMMENT '配送员索引',
    KEY `idx_status` (`route_status`) COMMENT '状态索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单配送路径表';

-- 支付记录表
CREATE TABLE `payment_record`
(
    /* 主键字段 */
    `id`                bigint         NOT NULL AUTO_INCREMENT COMMENT '主键ID（自增）',

    /* 关联信息 */
    `order_id`          bigint         NOT NULL COMMENT '关联订单ID（引用order_main表）',
    `order_no`          varchar(32)    NOT NULL COMMENT '订单号',
    `payment_no`        varchar(32)    NOT NULL COMMENT '支付单号（规则：PAY+年月日+8位序列）',
    `member_id`         bigint         NOT NULL COMMENT '会员ID',

    /* 支付信息 */
    `payment_type`      tinyint        NOT NULL DEFAULT 1 COMMENT '支付方式：1-微信支付',
    `payment_amount`    decimal(8, 2)  NOT NULL COMMENT '支付金额',
    `payment_status`    varchar(16)    NOT NULL COMMENT '支付状态：NOTPAY-支付中 SUCCESS-支付成功 REFUND-转入退款 CLOSED-已关闭',

    /* 第三方支付信息 */
    `transaction_id`    varchar(64)    DEFAULT NULL COMMENT '第三方交易号',
    `prepay_id`         varchar(64)    DEFAULT NULL COMMENT '预支付交易会话标识',
    `code_url`          varchar(512)   DEFAULT NULL COMMENT '二维码链接（Native支付）',
    `openid`            varchar(128)   DEFAULT NULL COMMENT '用户openid',
    `bank_type`         varchar(32)    DEFAULT NULL COMMENT '银行类型',
    `currency`          varchar(8)     DEFAULT 'CNY' COMMENT '货币类型',

    /* 时间信息 */
    `pay_time`          datetime(3)    DEFAULT NULL COMMENT '支付完成时间',
    `expire_time`       datetime(3)    DEFAULT NULL COMMENT '订单失效时间',

    /* 回调数据 */
    `notify_data`       text           DEFAULT NULL COMMENT '支付回调原始数据',
    `remark`            varchar(500)   DEFAULT NULL COMMENT '备注',

    /* 系统字段 */
    `create_time`       datetime(3)    NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `update_time`       datetime(3)    NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `creator`           bigint         DEFAULT NULL COMMENT '创建人ID',
    `updater`           bigint         DEFAULT NULL COMMENT '更新人ID',
    `deleted`           tinyint(1)     NOT NULL DEFAULT 0 COMMENT '逻辑删除标记：0-正常 1-已删除',

    /* 主键与索引 */
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_payment_no` (`payment_no`) COMMENT '支付单号唯一索引',
    KEY `idx_order_id` (`order_id`) COMMENT '订单ID索引',
    KEY `idx_order_no` (`order_no`) COMMENT '订单号索引',
    KEY `idx_member_id` (`member_id`) COMMENT '会员ID索引',
    KEY `idx_transaction_id` (`transaction_id`) COMMENT '第三方交易号索引',
    KEY `idx_payment_status` (`payment_status`) COMMENT '支付状态索引',
    KEY `idx_pay_time` (`pay_time`) COMMENT '支付时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付记录表';
