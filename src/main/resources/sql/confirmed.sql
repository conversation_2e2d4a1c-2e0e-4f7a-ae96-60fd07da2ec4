-- 柜子
-- 心跳存redis
-- 绑定关联设施：后面再做扩充
-- 加入柜子属于哪个投资人



-- 柜子使用记录
CREATE TABLE `cabinet_usage_log`
(
    /* 主键字段 */
    `id`                bigint  NOT NULL AUTO_INCREMENT COMMENT '主键ID（自增）',

    /* 设备关联信息 */
    `cabinet_id`        bigint  NOT NULL COMMENT '关联柜体ID（引用cabinet表）',
    `cell_id`           bigint  NOT NULL COMMENT '关联格口ID（引用cabinet_cell表）',

    /* 业务关联信息 */
    `order_id`          bigint           DEFAULT NULL COMMENT '关联订单ID（引用order表，可为空）',
    `user_id`           bigint           DEFAULT NULL COMMENT '操作用户ID（引用user表，可为空）',

    /* 操作信息 */
    `operation_type`    tinyint NOT NULL COMMENT '操作类型：1-存入 2-取出 3-维护 4-检查',
    `operator_type`     tinyint NOT NULL DEFAULT '1' COMMENT '操作人类型：1-用户 2-取送员 3-系统 4-管理员 5-运维人员',

    /* 安全验证 */
    `verification_code` varchar(20)      DEFAULT NULL COMMENT '验证码（加密存储，6位数字或字母）',
    `open_method`       tinyint NOT NULL COMMENT '开启方式：1-扫码 2-蓝牙 3-密码 4-远程 5-指纹 6-IC卡',

    /* 时间记录 */
    `open_time`         datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '开门时间（精确到毫秒）',
    `close_time`        datetime(3) DEFAULT NULL COMMENT '关门时间（精确到毫秒）',
    `duration`          int UNSIGNED DEFAULT NULL COMMENT '使用时长（秒，无符号整数）',
    `is_timeout`        bit(1)  NOT NULL DEFAULT b'0' COMMENT '是否超时：0-未超时 1-已超时',

    /* 凭证信息 */
    `images`            json             DEFAULT NULL COMMENT '操作凭证照片（JSON数组格式，如["url1","url2"]）',

    /* 系统字段 */
    `create_time`       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间（精确到毫秒）',
    `update_time`       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '记录更新时间（精确到毫秒）',
    `creator`           bigint           DEFAULT NULL COMMENT '创建人ID（引用user表）',
    `updater`           bigint           DEFAULT NULL COMMENT '更新人ID（引用user表）',
    `deleted`           tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标记：0-正常 1-已删除',

    /* 主键与索引 */
    PRIMARY KEY (`id`),
    KEY                 `idx_cabinet` (`cabinet_id`) COMMENT '柜体查询索引',
    KEY                 `idx_cell` (`cell_id`) COMMENT '格口查询索引',
    KEY                 `idx_order` (`order_id`) COMMENT '订单追踪索引',
    KEY                 `idx_user` (`user_id`) COMMENT '用户行为分析索引',
    KEY                 `idx_open_time` (`open_time`) COMMENT '时间范围查询索引',
    KEY                 `idx_operation` (`operation_type`,`operator_type`) COMMENT '操作类型分析联合索引',
    KEY                 `idx_timeout` (`is_timeout`) COMMENT '超时记录筛选索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='柜体使用记录表（安全审计/操作日志）';
-- 二维码表
CREATE TABLE `qr_code`
(
    `id`             BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `qr_type`        VARCHAR(32)  NOT NULL COMMENT '二维码类型分类(对应QRBusinessType枚举的service字段)',
    `encrypted_data` TEXT         NOT NULL COMMENT '加密的业务数据JSON(包含businessType,businessId等)',
    `status`         TINYINT      NOT NULL DEFAULT 1 COMMENT '状态枚举：0-已失效，1-有效，2-已使用',
    `expire_time`    DATETIME     NOT NULL COMMENT '过期时间',
    `qr_path`        VARCHAR(255) NOT NULL COMMENT '加密的业务数据JSON打包为二维码保存的图片路径',

    /* BasePo 基础字段 */
    `create_time`    DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator`        BIGINT(20) DEFAULT NULL COMMENT '创建人ID',
    `updater`        BIGINT(20) DEFAULT NULL COMMENT '更新人ID',
    `deleted`        TINYINT(1) DEFAULT 0 COMMENT '是否逻辑删除(0-未删除，1-已删除)',

    /* 额外业务字段 */
    `creator_role`   VARCHAR(32)           DEFAULT NULL COMMENT '创建人角色',

    PRIMARY KEY (`id`),
    KEY              `idx_qr_type` (`qr_type`),
    KEY              `idx_status_expire` (`status`, `expire_time`),
    KEY              `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='二维码信息表';
