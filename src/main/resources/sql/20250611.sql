


-- 去送任务表
CREATE TABLE `delivery_task`
(
    `id`                   BIGINT      NOT NULL AUTO_INCREMENT COMMENT '任务主键ID',

    -- 任务基础信息
    `task_no`              varchar(32) NOT NULL COMMENT '任务编号（规则：DT+年月日+6位序列）',
    `courier_id`           BIGINT      NOT NULL COMMENT '取送员ID',
    `task_type`            TINYINT     NOT NULL COMMENT '任务类型：1-取件 2-送件 3-取送一体',
    `task_date`            date        NOT NULL COMMENT '任务执行日期',

    -- 时间计划
    `estimated_start_time` datetime             DEFAULT NULL COMMENT '预计开始时间（系统计算）',
    `estimated_end_time`   datetime             DEFAULT NULL COMMENT '预计结束时间（系统计算）',
    `courier_adjust_time`  datetime             DEFAULT NULL COMMENT '取送员调整时间',

    -- 实际执行
    `actual_start_time`    datetime             DEFAULT NULL COMMENT '实际开始时间',
    `actual_end_time`      datetime             DEFAULT NULL COMMENT '实际完成时间',

    -- 任务状态
    `status`               TINYINT     NOT NULL DEFAULT '0' COMMENT '状态：0-待接单 1-已接单 2-进行中 3-已完成 4-异常 5-已取消',
    `exception_code`       varchar(20)          DEFAULT NULL COMMENT '异常类型编码',

    -- 订单统计
    `total_orders`         int         NOT NULL DEFAULT '0' COMMENT '总订单数',
    `completed_orders`     int         NOT NULL DEFAULT '0' COMMENT '已完成订单数',
    `urgent_orders`        int                  DEFAULT '0' COMMENT '加急订单数',

    -- 路线规划
    `route_info`           json                 DEFAULT NULL COMMENT '路线信息（含坐标点、距离、预估时长）',
    `distance`             decimal(8, 2)        DEFAULT NULL COMMENT '总距离（公里）',
    `estimated_duration`   int                  DEFAULT NULL COMMENT '预估时长（分钟）',

    -- 系统字段
    `create_time`          datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`          datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version`              int         NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_task_no` (`task_no`) COMMENT '任务编号唯一索引',
    KEY                    `idx_courier` (`courier_id`) COMMENT '取送员查询优化',
    KEY                    `idx_date_type` (`task_date`,`task_type`) COMMENT '日期类型统计',
    KEY                    `idx_status` (`status`) COMMENT '状态筛选',
    KEY                    `idx_estimated_time` (`estimated_start_time`,`estimated_end_time`) COMMENT '计划时间查询'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='取送任务主表（P1核心业务表）';

-- 去送任务明细表
CREATE TABLE `delivery_task_detail`
(
    `id`                BIGINT      NOT NULL AUTO_INCREMENT COMMENT '明细主键ID',

    -- 任务关联信息
    `task_id`           BIGINT      NOT NULL COMMENT '关联任务ID',
    `order_id`          BIGINT      NOT NULL COMMENT '关联订单ID',
    `sequence`          int         NOT NULL DEFAULT '1' COMMENT '执行顺序',

    -- 地址信息
    `address_type`      TINYINT     NOT NULL COMMENT '地址类型：1-取件地址 2-送件地址',
    `address_id`        BIGINT      NOT NULL COMMENT '关联地址ID（避免数据冗余）',
    `address_snapshot`  json                 DEFAULT NULL COMMENT '地址快照（JSON格式）',

    -- 联系人信息
    `contact_name`      varchar(30) NOT NULL COMMENT '联系人',
    `contact_phone`     varchar(20) NOT NULL COMMENT '联系电话（支持国际号码）',
    `contact_secondary` varchar(20)          DEFAULT NULL COMMENT '备用联系电话',

    -- 时间信息
    `estimated_time`    datetime             DEFAULT NULL COMMENT '预计到达时间（系统计算）',
    `adjusted_time`     datetime             DEFAULT NULL COMMENT '取送员调整时间',
    `arrival_time`      datetime             DEFAULT NULL COMMENT '实际到达时间',
    `completion_time`   datetime             DEFAULT NULL COMMENT '完成时间',

    -- 执行状态
    `status`            TINYINT     NOT NULL DEFAULT '0' COMMENT '状态：0-待执行 1-前往中 2-已到达 3-已完成 4-异常',
    `sub_status`        TINYINT              DEFAULT NULL COMMENT '子状态：1-客户拒收 2-联系不上...',
    `exception_code`    varchar(20)          DEFAULT NULL COMMENT '异常类型编码',
    `exception_reason`  varchar(200)         DEFAULT NULL COMMENT '异常原因详情',

    -- 验证信息
    `verification_code` varchar(10)          DEFAULT NULL COMMENT '6位动态验证码',
    `code_expire_time`  datetime             DEFAULT NULL COMMENT '验证码过期时间',

    -- 位置信息
    `longitude`         decimal(10, 7)       DEFAULT NULL COMMENT '执行位置经度',
    `latitude`          decimal(10, 7)       DEFAULT NULL COMMENT '执行位置纬度',
    `location_accuracy` decimal(5, 2)        DEFAULT NULL COMMENT '定位精度（米）',

    -- 凭证信息
    `images`            json                 DEFAULT NULL COMMENT '执行凭证照片（[{url:"",type:1}]）',
    `signature`         varchar(255)         DEFAULT NULL COMMENT '电子签名URL',

    -- 系统字段
    `create_time`       datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_task_order_type` (`task_id`,`order_id`,`address_type`) COMMENT '防止重复记录',
    KEY                 `idx_task` (`task_id`) COMMENT '任务查询优化',
    KEY                 `idx_order` (`order_id`) COMMENT '订单追踪',
    KEY                 `idx_status` (`status`) COMMENT '状态筛选',
    KEY                 `idx_estimated_time` (`estimated_time`) COMMENT '计划时间查询',
    KEY                 `idx_geo` (`longitude`,`latitude`) COMMENT '地理位置查询',
    KEY                 `idx_exception` (`exception_code`) COMMENT '异常分析'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='取送任务明细表（P1核心业务表）';

-- 工厂订单表
CREATE TABLE `factory_order`
(
    `id`                    BIGINT      NOT NULL AUTO_INCREMENT COMMENT '工厂订单主键ID',

    -- 订单关联信息
    `order_id`              BIGINT      NOT NULL COMMENT '原订单ID',
    `factory_id`            BIGINT      NOT NULL COMMENT '工厂ID',
    `batch_no`              varchar(32) NOT NULL COMMENT '批次号（规则：BATCH+年月日+4位序列）',

    -- 物品信息
    `item_count`            int         NOT NULL DEFAULT '0' COMMENT '物品总数',
    `item_details`          json                 DEFAULT NULL COMMENT '物品详情（[{id:1,name:"",type:1}]）',
    `special_requirements`  varchar(500)         DEFAULT NULL COMMENT '特殊要求',

    -- 时间节点
    `receive_time`          datetime             DEFAULT NULL COMMENT '工厂接收时间',
    `start_time`            datetime             DEFAULT NULL COMMENT '开始处理时间',
    `estimated_finish_time` datetime             DEFAULT NULL COMMENT '预计完成时间',
    `actual_finish_time`    datetime             DEFAULT NULL COMMENT '实际完成时间',
    `delivery_time`         datetime             DEFAULT NULL COMMENT '出库时间',

    -- 处理信息
    `operator_id`           BIGINT               DEFAULT NULL COMMENT '主要操作员ID',
    `operator_team`         varchar(50)          DEFAULT NULL COMMENT '处理班组',
    `status`                TINYINT     NOT NULL DEFAULT '0' COMMENT '状态：0-待接收 1-已接收 2-处理中 3-质检中 4-已完成 5-异常 6-已取消',
    `current_stage`         TINYINT              DEFAULT NULL COMMENT '当前阶段（关联工序表）',

    -- 质量信息
    `quality_score`         TINYINT              DEFAULT NULL COMMENT '质量评分（1-5星）',
    `qc_operator`           BIGINT               DEFAULT NULL COMMENT '质检员ID',
    `qc_time`               datetime             DEFAULT NULL COMMENT '质检时间',
    `defect_items`          json                 DEFAULT NULL COMMENT '瑕疵品记录（[{item_id:1,defect_type:3}]）',

    -- 凭证信息
    `process_images`        json                 DEFAULT NULL COMMENT '处理过程照片（[{time:"",url:"",type:1}]）',
    `qc_report`             varchar(255)         DEFAULT NULL COMMENT '质检报告URL',

    -- 系统字段
    `creator`               BIGINT      NOT NULL COMMENT '创建人ID',
    `create_time`           datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`               BIGINT               DEFAULT NULL COMMENT '更新人ID',
    `update_time`           datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version`               int         NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
    `deleted`               bit(1)      NOT NULL DEFAULT b'0' COMMENT '删除标记',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order` (`order_id`) COMMENT '原订单唯一索引',
    KEY                     `idx_factory` (`factory_id`) COMMENT '工厂查询',
    KEY                     `idx_batch` (`batch_no`) COMMENT '批次追踪',
    KEY                     `idx_status` (`status`) COMMENT '状态筛选',
    KEY                     `idx_time_range` (`receive_time`,`actual_finish_time`) COMMENT '时效分析',
    KEY                     `idx_quality` (`quality_score`) COMMENT '质量分析',
    KEY                     `idx_operator` (`operator_id`) COMMENT '操作员统计',
    KEY                     `idx_stage` (`current_stage`) COMMENT '工序阶段查询'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工厂订单表（P1生产管理表）';

-- 工序日志表
CREATE TABLE `laundry_process_log`
(
    `id`               BIGINT      NOT NULL AUTO_INCREMENT COMMENT '记录主键ID',

    -- 订单关联信息
    `factory_order_id` BIGINT      NOT NULL COMMENT '工厂订单ID',
    `order_detail_id`  BIGINT      NOT NULL COMMENT '订单明细ID',
    `batch_no`         varchar(32)          DEFAULT NULL COMMENT '批次号（冗余存储）',

    -- 流程步骤信息
    `process_step`     TINYINT     NOT NULL COMMENT '处理步骤：1-接收 2-预处理 3-清洗 4-烘干 5-质检 6-包装 7-发货',
    `step_name`        varchar(20) NOT NULL COMMENT '步骤名称',
    `step_status`      TINYINT     NOT NULL DEFAULT '0' COMMENT '步骤状态：0-待开始 1-进行中 2-已完成 3-异常 4-已跳过',
    `step_sequence`    TINYINT              DEFAULT NULL COMMENT '步骤顺序（用于自定义流程）',

    -- 操作信息
    `operator_id`      BIGINT               DEFAULT NULL COMMENT '操作员ID',
    `operator_name`    varchar(30)          DEFAULT NULL COMMENT '操作员姓名（冗余存储）',
    `operator_role`    varchar(20)          DEFAULT NULL COMMENT '操作员角色',
    `workstation_id`   varchar(20)          DEFAULT NULL COMMENT '工位/设备编号',

    -- 时间信息
    `start_time`       datetime             DEFAULT NULL COMMENT '开始时间',
    `finish_time`      datetime             DEFAULT NULL COMMENT '完成时间',
    `duration`         int                  DEFAULT NULL COMMENT '处理时长（分钟）',
    `time_standard`    int                  DEFAULT NULL COMMENT '标准工时（分钟）',

    -- 质量信息
    `quality_check`    TINYINT              DEFAULT NULL COMMENT '质检结果：1-优秀 2-良好 3-合格 4-不合格',
    `quality_metrics`  json                 DEFAULT NULL COMMENT '质检指标（{"color":4,"texture":3}）',
    `issues_found`     varchar(200)         DEFAULT NULL COMMENT '发现的问题',
    `defect_types`     json                 DEFAULT NULL COMMENT '缺陷类型编码（[101,203]）',

    -- 凭证信息
    `before_images`    json                 DEFAULT NULL COMMENT '处理前照片（[{url:"",type:1}]）',
    `after_images`     json                 DEFAULT NULL COMMENT '处理后照片（[{url:"",type:1}]）',
    `process_video`    varchar(255)         DEFAULT NULL COMMENT '处理视频URL',

    -- 系统字段
    `creator`          BIGINT      NOT NULL COMMENT '创建人ID',
    `create_time`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`          BIGINT               DEFAULT NULL COMMENT '更新人ID',
    `update_time`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`          bit(1)      NOT NULL DEFAULT b'0' COMMENT '删除标记',

    PRIMARY KEY (`id`),
    KEY                `idx_factory_order` (`factory_order_id`) COMMENT '工厂订单查询',
    KEY                `idx_order_detail` (`order_detail_id`) COMMENT '订单明细追踪',
    KEY                `idx_step_status` (`process_step`,`step_status`) COMMENT '步骤状态分析',
    KEY                `idx_operator` (`operator_id`) COMMENT '操作员统计',
    KEY                `idx_time_range` (`start_time`,`finish_time`) COMMENT '时效分析',
    KEY                `idx_quality` (`quality_check`) COMMENT '质量查询',
    KEY                `idx_batch` (`batch_no`) COMMENT '批次追踪',
    KEY                `idx_workstation` (`workstation_id`) COMMENT '工位效率分析'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='洗护流程记录表（P1质量追溯表）';

-- 消息模板表
CREATE TABLE `message_template`
(
    `id`               BIGINT       NOT NULL AUTO_INCREMENT COMMENT '模板主键ID',

    -- 模板基础信息
    `template_code`    varchar(50)  NOT NULL COMMENT '模板编码（业务唯一标识）',
    `template_name`    varchar(100) NOT NULL COMMENT '模板名称',
    `template_type`    TINYINT      NOT NULL COMMENT '模板类型：1-订单通知 2-营销推广 3-系统通知 4-区域开放 5-账户安全',

    -- 渠道配置
    `channel_type`     TINYINT      NOT NULL DEFAULT '1' COMMENT '推送渠道：1-小程序模板 2-小程序订阅 3-微信客服 4-短信 5-APP推送 6-邮件',
    `wx_template_id`   varchar(100)          DEFAULT NULL COMMENT '微信模板ID',
    `sms_template_id`  varchar(50)           DEFAULT NULL COMMENT '短信模板ID',
    `channel_config`   json                  DEFAULT NULL COMMENT '渠道特殊配置',

    -- 内容配置
    `title`            varchar(100) NOT NULL COMMENT '消息标题',
    `content_template` text         NOT NULL COMMENT '内容模板（支持{{变量}}语法）',
    `content_sample`   text                  DEFAULT NULL COMMENT '渲染示例（自动生成）',
    `language`         varchar(10)           DEFAULT 'zh-CN' COMMENT '语言版本',

    -- 交互配置
    `jump_page`        varchar(200)          DEFAULT NULL COMMENT '跳转页面（path?param=value）',
    `jump_type`        TINYINT               DEFAULT NULL COMMENT '跳转类型：1-小程序页 2-Web页 3-APP页',
    `button_config`    json                  DEFAULT NULL COMMENT '按钮配置（[{text:"",action:""}]）',

    -- 变量配置
    `keyword_mapping`  json                  DEFAULT NULL COMMENT '变量映射（{"orderNo":"order_no"}）',
    `variable_rules`   json                  DEFAULT NULL COMMENT '变量校验规则（{"phone":"^1\\d{10}$"}）',

    -- 发送控制
    `send_condition`   json                  DEFAULT NULL COMMENT '发送条件（{"time_range":[9,18],"user_tags":[1,3]}）',
    `priority`         TINYINT      NOT NULL DEFAULT '3' COMMENT '优先级：1-5（数字越大优先级越高）',
    `status`           TINYINT      NOT NULL DEFAULT '0' COMMENT '状态：0-正常 1-停用 2-测试中',

    -- 限流配置
    `daily_limit`      int          NOT NULL DEFAULT '1000' COMMENT '每日发送上限',
    `user_limit`       int          NOT NULL DEFAULT '10' COMMENT '每用户每日上限',
    `rate_limit`       int                   DEFAULT NULL COMMENT '每分钟发送上限',
    `blacklist_config` json                  DEFAULT NULL COMMENT '黑名单配置',

    -- 系统字段
    `creator`          BIGINT       NOT NULL COMMENT '创建人ID',
    `create_time`      datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`          BIGINT                DEFAULT NULL COMMENT '更新人ID',
    `update_time`      datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`          bit(1)       NOT NULL DEFAULT b'0' COMMENT '删除标记',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_template_code` (`template_code`) COMMENT '模板编码唯一索引',
    KEY                `idx_type` (`template_type`) COMMENT '类型查询',
    KEY                `idx_channel` (`channel_type`) COMMENT '渠道查询',
    KEY                `idx_status` (`status`) COMMENT '状态筛选',
    KEY                `idx_priority` (`priority`) COMMENT '优先级排序',
    KEY                `idx_language` (`language`) COMMENT '多语言支持',
    FULLTEXT KEY `idx_content` (`title`,`content_template`) COMMENT '内容检索'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息推送模板表（P1消息中心）';

-- 消息推送记录表
CREATE TABLE `message_push_log`
(
    `id`                BIGINT       NOT NULL AUTO_INCREMENT COMMENT '推送记录主键ID',

    -- 推送标识信息
    `push_no`           varchar(32)  NOT NULL COMMENT '推送编号（规则：MSG+年月日+8位序列）',
    `batch_no`          varchar(32)           DEFAULT NULL COMMENT '批次号（用于批量推送）',

    -- 模板关联信息
    `template_id`       BIGINT       NOT NULL COMMENT '消息模板ID',
    `template_code`     varchar(50)  NOT NULL COMMENT '模板编码（冗余存储）',
    `template_version`  int                   DEFAULT '1' COMMENT '模板版本号',

    -- 接收人信息
    `user_id`           BIGINT       NOT NULL COMMENT '目标用户ID',
    `member_id`         BIGINT                DEFAULT NULL COMMENT '会员ID（关联业务）',
    `user_type`         TINYINT               DEFAULT '1' COMMENT '用户类型：1-普通用户 2-商家 3-配送员',
    `device_id`         varchar(64)           DEFAULT NULL COMMENT '设备标识',

    -- 渠道与业务信息
    `channel_type`      TINYINT      NOT NULL COMMENT '推送渠道：1-小程序模板 2-小程序订阅 3-微信客服 4-短信 5-APP推送 6-邮件',
    `business_type`     TINYINT      NOT NULL COMMENT '业务类型：1-订单 2-营销 3-系统 4-区域开放 5-账户安全',
    `business_id`       BIGINT                DEFAULT NULL COMMENT '关联业务ID',
    `scenario_code`     varchar(30)           DEFAULT NULL COMMENT '场景编码（如：order_paid）',

    -- 消息内容
    `title`             varchar(100) NOT NULL COMMENT '消息标题',
    `content`           text         NOT NULL COMMENT '实际发送内容',
    `content_variables` json                  DEFAULT NULL COMMENT '渲染变量（{"orderNo":"123"}）',
    `raw_template`      text                  DEFAULT NULL COMMENT '原始模板内容（冗余存储）',

    -- 交互配置
    `extra_data`        json                  DEFAULT NULL COMMENT '额外数据（跳转参数/埋点数据等）',
    `deep_link`         varchar(255)          DEFAULT NULL COMMENT '深度链接',
    `page_path`         varchar(200)          DEFAULT NULL COMMENT '小程序页面路径',

    -- 推送状态
    `send_time`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
    `schedule_time`     datetime              DEFAULT NULL COMMENT '预定发送时间',
    `send_status`       TINYINT      NOT NULL DEFAULT '0' COMMENT '发送状态：0-待发送 1-成功 2-失败 3-用户拒收 4-已取消',
    `third_msg_id`      varchar(64)           DEFAULT NULL COMMENT '第三方消息ID',
    `failure_reason`    varchar(500)          DEFAULT NULL COMMENT '失败详情',
    `retry_count`       TINYINT      NOT NULL DEFAULT '0' COMMENT '重试次数',
    `final_send_time`   datetime              DEFAULT NULL COMMENT '最终发送时间',

    -- 用户行为
    `click_time`        datetime              DEFAULT NULL COMMENT '首次点击时间',
    `is_clicked`        bit(1)       NOT NULL DEFAULT b'0' COMMENT '是否已点击',
    `click_count`       int          NOT NULL DEFAULT '0' COMMENT '点击次数',
    `last_click_time`   datetime              DEFAULT NULL COMMENT '最后点击时间',
    `conversion_data`   json                  DEFAULT NULL COMMENT '转化数据（如：下单ID）',

    -- 系统字段
    `creator`           BIGINT       NOT NULL COMMENT '创建人ID',
    `create_time`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`           BIGINT                DEFAULT NULL COMMENT '更新人ID',
    `update_time`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`           bit(1)       NOT NULL DEFAULT b'0' COMMENT '删除标记',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_push_no` (`push_no`) COMMENT '推送编号唯一索引',
    KEY                 `idx_user` (`user_id`) COMMENT '用户行为分析',
    KEY                 `idx_template` (`template_id`) COMMENT '模板效果分析',
    KEY                 `idx_business` (`business_type`,`business_id`) COMMENT '业务追踪',
    KEY                 `idx_send_time` (`send_time`) COMMENT '发送时间统计',
    KEY                 `idx_status` (`send_status`) COMMENT '状态筛选',
    KEY                 `idx_channel` (`channel_type`) COMMENT '渠道分析',
    KEY                 `idx_click` (`is_clicked`,`click_time`) COMMENT '点击行为分析',
    KEY                 `idx_scenario` (`scenario_code`) COMMENT '场景分析',
    KEY                 `idx_batch` (`batch_no`) COMMENT '批次查询',
    KEY                 `idx_schedule` (`schedule_time`) COMMENT '定时发送查询'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息推送记录表（P1消息追踪表）';

-- 用户消息偏好表
CREATE TABLE `user_message_preference`
(
    `id`               BIGINT   NOT NULL AUTO_INCREMENT COMMENT '偏好设置主键ID',

    -- 用户标识
    `user_id`          BIGINT   NOT NULL COMMENT '用户ID',
    `member_id`        BIGINT            DEFAULT NULL COMMENT '会员ID（冗余存储）',
    `device_id`        varchar(64)       DEFAULT NULL COMMENT '设备标识（多端同步用）',

    -- 消息类型配置
    `message_type`     TINYINT  NOT NULL COMMENT '消息类型：1-订单通知 2-营销推广 3-系统通知 4-区域开放 5-账户安全',
    `message_subtype`  varchar(30)       DEFAULT NULL COMMENT '消息子类型（如：order_paid）',
    `channel_type`     TINYINT  NOT NULL COMMENT '渠道类型：1-小程序 2-短信 3-APP推送 4-邮件 5-微信客服',

    -- 偏好设置
    `is_enabled`       bit(1)   NOT NULL DEFAULT b'1' COMMENT '是否启用：0-关闭 1-开启',
    `time_range_start` time              DEFAULT '09:00:00' COMMENT '允许推送开始时间',
    `time_range_end`   time              DEFAULT '21:00:00' COMMENT '允许推送结束时间',
    `frequency_limit`  int      NOT NULL DEFAULT '10' COMMENT '每日接收频次限制',
    `priority_filter`  TINYINT           DEFAULT NULL COMMENT '优先级过滤（仅接收该级别以上）',

    -- 行为统计
    `last_push_time`   datetime          DEFAULT NULL COMMENT '最后推送时间',
    `total_received`   int      NOT NULL DEFAULT '0' COMMENT '累计接收数量',
    `total_clicked`    int      NOT NULL DEFAULT '0' COMMENT '累计点击数量',
    `click_rate`       decimal(5, 2)     DEFAULT NULL COMMENT '点击率（自动计算）',

    -- 屏蔽控制
    `block_until`      datetime          DEFAULT NULL COMMENT '临时屏蔽截止时间',
    `block_reason`     varchar(50)       DEFAULT NULL COMMENT '屏蔽原因（用户设置/系统判定）',
    `global_block`     bit(1)            DEFAULT b'0' COMMENT '全局屏蔽（所有消息类型）',

    -- 系统字段
    `creator`          BIGINT   NOT NULL COMMENT '创建人ID（系统用户为0）',
    `create_time`      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`          BIGINT            DEFAULT NULL COMMENT '更新人ID',
    `update_time`      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`          bit(1)   NOT NULL DEFAULT b'0' COMMENT '删除标记',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_preference` (`user_id`,`message_type`,`channel_type`) COMMENT '用户+类型+渠道唯一索引',
    KEY                `idx_enabled` (`is_enabled`) COMMENT '启用状态筛选',
    KEY                `idx_block` (`block_until`) COMMENT '屏蔽状态查询',
    KEY                `idx_message_type` (`message_type`) COMMENT '消息类型分析',
    KEY                `idx_channel` (`channel_type`) COMMENT '渠道偏好分析',
    KEY                `idx_user_global` (`user_id`,`global_block`) COMMENT '全局屏蔽查询',
    KEY                `idx_device` (`device_id`) COMMENT '设备偏好同步'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户消息偏好表（P2用户体验优化）';

-- 消息推送队列表
CREATE TABLE `message_push_queue`
(
    `id`               BIGINT      NOT NULL AUTO_INCREMENT COMMENT '队列任务主键ID',

    -- 任务标识
    `task_no`          varchar(32) NOT NULL COMMENT '任务编号（规则：TASK+年月日+6位序列）',
    `batch_no`         varchar(32)          DEFAULT NULL COMMENT '批次号（用于关联任务）',

    -- 消息内容
    `template_id`      BIGINT      NOT NULL COMMENT '消息模板ID',
    `template_code`    varchar(50)          DEFAULT NULL COMMENT '模板编码（冗余存储）',
    `content_data`     json        NOT NULL COMMENT '内容变量数据（{"orderNo":"123"}）',
    `message_title`    varchar(100)         DEFAULT NULL COMMENT '消息标题（冗余存储）',

    -- 接收人配置
    `user_ids`         json        NOT NULL COMMENT '目标用户ID列表（[1001,1002]）',
    `user_filter`      json                 DEFAULT NULL COMMENT '用户筛选条件（{"tags":[1,3],"vip":true}）',
    `exclude_users`    json                 DEFAULT NULL COMMENT '排除用户ID列表',
    `total_count`      int         NOT NULL DEFAULT '0' COMMENT '目标用户总数',

    -- 业务关联
    `business_type`    TINYINT     NOT NULL COMMENT '业务类型：1-订单 2-营销 3-系统 4-区域开放',
    `business_id`      BIGINT               DEFAULT NULL COMMENT '关联业务ID',
    `scenario_code`    varchar(30)          DEFAULT NULL COMMENT '场景编码（如：order_paid）',

    -- 调度控制
    `scheduled_time`   datetime             DEFAULT NULL COMMENT '预定发送时间（NULL表示立即发送）',
    `priority`         TINYINT     NOT NULL DEFAULT '3' COMMENT '任务优先级：1-5（数字越大优先级越高）',
    `expire_time`      datetime             DEFAULT NULL COMMENT '任务过期时间',
    `retry_policy`     json                 DEFAULT NULL COMMENT '重试策略（{"max_attempts":3,"interval":300}）',

    -- 执行状态
    `status`           TINYINT     NOT NULL DEFAULT '0' COMMENT '任务状态：0-待处理 1-处理中 2-已完成 3-已失败 4-已取消',
    `current_progress` int                  DEFAULT '0' COMMENT '当前进度（已处理数）',
    `success_count`    int         NOT NULL DEFAULT '0' COMMENT '发送成功数',
    `failure_count`    int         NOT NULL DEFAULT '0' COMMENT '发送失败数',
    `failure_users`    json                 DEFAULT NULL COMMENT '失败用户记录',

    -- 执行记录
    `start_time`       datetime             DEFAULT NULL COMMENT '开始处理时间',
    `finish_time`      datetime             DEFAULT NULL COMMENT '完成时间',
    `executor_node`    varchar(50)          DEFAULT NULL COMMENT '执行节点标识',
    `failure_reason`   text                 DEFAULT NULL COMMENT '失败原因详情',

    -- 系统字段
    `creator`          BIGINT      NOT NULL COMMENT '创建人ID',
    `create_time`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`          BIGINT               DEFAULT NULL COMMENT '更新人ID',
    `update_time`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version`          int         NOT NULL DEFAULT '0' COMMENT '乐观锁版本号',
    `deleted`          bit(1)      NOT NULL DEFAULT b'0' COMMENT '删除标记',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_task_no` (`task_no`) COMMENT '任务编号唯一索引',
    KEY                `idx_status` (`status`) COMMENT '状态筛选',
    KEY                `idx_scheduled` (`scheduled_time`) COMMENT '定时任务查询',
    KEY                `idx_priority` (`priority` DESC) COMMENT '优先级排序',
    KEY                `idx_business` (`business_type`,`business_id`) COMMENT '业务追踪',
    KEY                `idx_progress` (`current_progress`) COMMENT '进度监控',
    KEY                `idx_template` (`template_id`) COMMENT '模板分析',
    KEY                `idx_scenario` (`scenario_code`) COMMENT '场景分析',
    KEY                `idx_create_time` (`create_time`) COMMENT '创建时间排序'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推送任务队列表（P2异步处理）';



-- 会员积分表
CREATE TABLE `member_points_log`
(
    -- 主键
    `id`          BIGINT       NOT NULL AUTO_INCREMENT COMMENT '记录主键ID',

    -- 会员信息
    `member_id`   BIGINT       NOT NULL COMMENT '会员ID',

    -- 积分信息
    `type`        TINYINT      NOT NULL COMMENT '积分类型(1获得/2消费/3过期/4退回)',
    `points`      int          NOT NULL DEFAULT 0 COMMENT '积分数量(正负数)',
    `balance`     int          NOT NULL DEFAULT 0 COMMENT '变动后余额',

    -- 来源信息
    `source_type` TINYINT      NOT NULL COMMENT '来源类型(1下单/2分享/3签到/4消费/5系统赠送)',
    `source_id`   BIGINT COMMENT '来源关联ID',

    -- 描述信息
    `description` varchar(100) NOT NULL COMMENT '变动说明',

    -- 时间信息
    `expire_time` datetime COMMENT '过期时间',

    -- 通用字段
    `creator`     BIGINT       NOT NULL COMMENT '创建者',
    `create_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`     BIGINT COMMENT '更新者',
    `update_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`     bit(1)       NOT NULL DEFAULT 0 COMMENT '删除标记(0正常/1已删除)',

    -- 主键索引
    PRIMARY KEY (`id`),

    -- 业务索引
    KEY           `idx_points_member` (`member_id`),
    KEY           `idx_points_type` (`type`),
    KEY           `idx_points_source` (`source_type`, `source_id`),
    KEY           `idx_points_expire` (`expire_time`),

    -- 通用索引
    INDEX         `idx_create_time` (`create_time`),
    INDEX         `idx_update_time` (`update_time`),
    INDEX         `idx_deleted` (`deleted`),

    -- 优化查询的复合索引
    KEY           `idx_member_balance` (`member_id`, `balance`),
    KEY           `idx_member_create_time` (`member_id`, `create_time`),
    KEY           `idx_member_type_time` (`member_id`, `type`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分变动记录，用户激励机制';


CREATE TABLE `partner_settlement`
(
    -- 主键
    `id`                BIGINT         NOT NULL AUTO_INCREMENT COMMENT '结算主键ID',
    -- 基础信息
    `tenant_id`         BIGINT         NOT NULL COMMENT '租户ID',
    `settlement_no`     varchar(32)    NOT NULL COMMENT '结算单号(自动生成)',
    -- 结算周期
    `period_start`      date           NOT NULL COMMENT '结算周期开始',
    `period_end`        date           NOT NULL COMMENT '结算周期结束',

    -- 订单统计
    `total_orders`      int            NOT NULL DEFAULT 0 COMMENT '订单总数',

    -- 金额信息
    `total_amount`      decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '总营业额',
    `platform_fee_rate` decimal(5, 4)  NOT NULL COMMENT '平台费率',
    `platform_fee`      decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '平台费用',
    `commission_rate`   decimal(5, 4)  NOT NULL COMMENT '佣金比例',
    `commission_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '佣金金额',
    `deduction_amount`  decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '扣除金额',
    `final_amount`      decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最终结算金额',

    -- 状态与时间
    `status`            TINYINT        NOT NULL DEFAULT 0 COMMENT '状态(0待确认/1已确认/2已支付/3已驳回)',
    `confirm_time`      datetime COMMENT '确认时间',
    `pay_time`          datetime COMMENT '支付时间',

    -- 备注
    `remark`            varchar(200) COMMENT '备注',

    -- 通用字段
    `creator`           BIGINT         NOT NULL COMMENT '创建者',
    `create_time`       datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`           BIGINT COMMENT '更新者',
    `update_time`       datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`           bit(1)         NOT NULL DEFAULT 0 COMMENT '删除标记(0正常/1已删除)',

    -- 主键索引
    PRIMARY KEY (`id`),

    -- 唯一索引
    UNIQUE KEY `uk_settlement_no` (`settlement_no`),

    -- 业务索引
    KEY                 `idx_settlement_tenant` (`tenant_id`),
    KEY                 `idx_settlement_period` (`period_start`, `period_end`),
    KEY                 `idx_settlement_status` (`status`),

    -- 通用索引
    INDEX               `idx_create_time` (`create_time`),
    INDEX               `idx_update_time` (`update_time`),
    INDEX               `idx_deleted` (`deleted`),

    -- 优化查询的复合索引
    KEY                 `idx_tenant_period_status` (`tenant_id`, `period_end`, `status`),
    KEY                 `idx_status_create_time` (`status`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合伙人分润结算';

-- 快递员结算表
CREATE TABLE `courier_settlement`
(
    `id`                BIGINT        NOT NULL AUTO_INCREMENT COMMENT '自增结算主键 ID',
    `staff_id`          BIGINT        NOT NULL COMMENT '人员 ID',
    `settlement_no`     VARCHAR(32)   NOT NULL COMMENT '自动生成结算单号',
    `period_start`      DATE          NOT NULL COMMENT '结算周期开始',
    `period_end`        DATE          NOT NULL COMMENT '结算周期结束',
    `work_days`         INT           NOT NULL DEFAULT 0 COMMENT '工作天数',
    `completed_tasks`   INT           NOT NULL DEFAULT 0 COMMENT '完成任务数',
    `completed_orders`  INT           NOT NULL DEFAULT 0 COMMENT '完成订单数',
    `base_salary`       DECIMAL(8, 2) NOT NULL DEFAULT 0.00 COMMENT '基础工资',
    `task_commission`   DECIMAL(8, 2) NOT NULL DEFAULT 0.00 COMMENT '任务提成',
    `performance_bonus` DECIMAL(8, 2) NOT NULL DEFAULT 0.00 COMMENT '绩效奖金',
    `deduction`         DECIMAL(8, 2) NOT NULL DEFAULT 0.00 COMMENT '扣除金额',
    `final_amount`      DECIMAL(8, 2) NOT NULL DEFAULT 0.00 COMMENT '最终金额',
    `status`            TINYINT       NOT NULL DEFAULT 0 COMMENT '状态 (0-待确认 / 1-已确认 / 2-已支付)',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_courier_settlement_no` (`settlement_no`),
    KEY                 `idx_courier_settlement_staff` (`staff_id`),
    KEY                 `idx_courier_settlement_period` (`period_start`, `period_end`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='取送员结算表';
