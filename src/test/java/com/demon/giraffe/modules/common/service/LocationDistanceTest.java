package com.demon.giraffe.modules.common.service;

import com.demon.giraffe.modules.cabinet.model.po.SmartCabinetPo;
import com.demon.giraffe.modules.common.model.entity.DistanceWrapper;
import com.demon.giraffe.modules.common.service.impl.LocationServiceImpl;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class LocationDistanceTest {

    public static void main(String[] args) {
        // 1. 准备测试数据
        List<CommercialSquare> squares = new ArrayList<>();

        // 添加融城理想广场数据
        squares.add(new CommercialSquare(
                1L,
                new BigDecimal("104.0618090"), new BigDecimal("30.5961680")

        ));

//        // 添加旺旺商业广场数据
//        squares.add(new CommercialSquare(
//                2L,
//                new BigDecimal("104.0696530"), new BigDecimal("30.5880220")
//        ));

        // 2. 设置中心点坐标（例如：成都天府广场）
        BigDecimal centerLon = new BigDecimal("104.0696530"); // 经度
        BigDecimal centerLat = new BigDecimal("30.5880220");  // 纬度

        // 3. 创建LocationService实例（这里用模拟实现）
        LocationService locationService = new LocationServiceImpl(null);

        // 4. 调用泛型方法计算距离并排序
        List<DistanceWrapper<CommercialSquare>> sortedSquares = locationService.calculateDistancesAndFilterGeneric(
                centerLon, centerLat,
                squares,
                CommercialSquare::getLongitude,
                CommercialSquare::getLatitude,
                null // 不设距离限制
        );

        // 5. 打印结果
        System.out.println("商业广场到天府广场的距离排序：");
        sortedSquares.forEach(square -> {
            double distance = locationService.calculateDistance(
                    centerLon, centerLat,
                    square.getPoint().getLongitude(), square.getPoint().getLatitude()
            );
            System.out.printf("%s - 距离: %.2f米%n",
                    square.getPoint().getId(), distance);
        });
    }

    @Getter
    @AllArgsConstructor
    static class CommercialSquare {
        private Long id;

        private BigDecimal longitude;
        private BigDecimal latitude;
    }

}