package com.demon.giraffe.modules.review.model.po;

import com.demon.giraffe.modules.review.mapper.UserReviewMapper;
import com.demon.giraffe.modules.review.model.enums.ReviewTagEnum;
import com.google.gson.Gson;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.security.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;
@SpringBootTest
public class UserReviewPoTest {
    private static final Gson gson = new Gson();

    private static final List<String> ITEM_NAMES = List.of("高端护理服务", "洗衣服务", "干洗西装", "奢侈品护理", "地毯清洁");
    private static final List<String> CONTENT_TEMPLATES = List.of("服务非常好，速度也快。", "洗得很干净，满意！", "包装精致，推荐！", "体验不错，态度很好", "一般般，时间稍长");

    private static final long MEMBER_ID_MIN = 10000L;
    private static final long MEMBER_ID_MAX = 20000L;

    private static final long ORDER_ID_MIN = 30000L;
    private static final long ORDER_ID_MAX = 40000L;

    private static final long ITEM_ID_MIN = 50000L;
    private static final long ITEM_ID_MAX = 60000L;

    private static final long CREATOR_ID = 9999L;
    @Resource
    private UserReviewMapper userReviewMapper;

    @Test
    public void testBuilder() {
        for (int i = 0; i < 300; i++) {
            UserReviewPo po = generateRandomReview(); // 每次新的对象
            userReviewMapper.insert(po);
            System.out.println("插入成功，生成ID：" + po.getId());
        }
    }



    public static UserReviewPo generateRandomReview() {
        ThreadLocalRandom rand = ThreadLocalRandom.current();

        UserReviewPo po = UserReviewPo.builder()
                .memberId(randLong(1, 4))
                .orderId(randLong(ORDER_ID_MIN, ORDER_ID_MAX))
                .itemId(randLong(1, 11))
                .itemName(randItem(ITEM_NAMES))
                .rating(rand.nextInt(1, 11))
                .content(randItem(CONTENT_TEMPLATES))
                .imageUrls(generateRandomImageUrls())
                .tags(generateRandomTags())
                .isRepeatCustomer(rand.nextBoolean())
                .isAnonymous(rand.nextBoolean()).build();
        po.setCreateTime(LocalDateTime.now());
        po.setUpdateTime(LocalDateTime.now());
        po.setCreator(0L);
        po.setUpdater(0L);
        return po;
    }



    private static int boolToInt(Boolean bool) {
        return (bool != null && bool) ? 1 : 0;
    }

    private static String escape(String str) {
        return str.replace("'", "''");
    }

    private static long randLong(long min, long max) {
        return ThreadLocalRandom.current().nextLong(min, max);
    }

    private static <T> T randItem(List<T> list) {
        return list.get(ThreadLocalRandom.current().nextInt(list.size()));
    }

    private static List<String> generateRandomImageUrls() {
        int count = ThreadLocalRandom.current().nextInt(0, 4); // 0~3 个图
        List<String> urls = new ArrayList<>();
        for (int i = 1; i <= count; i++) {
            urls.add("/api-storage/inherent/positive_review/imag (" + ThreadLocalRandom.current().nextInt(1, 21) + ").png");
        }
        return urls;
    }

    private static List<ReviewTagEnum> generateRandomTags() {
        ReviewTagEnum[] values = ReviewTagEnum.values();
        int count = ThreadLocalRandom.current().nextInt(1, Math.min(4, values.length + 1)); // 最多选3个
        List<ReviewTagEnum> list = new ArrayList<>(List.of(values));
        Collections.shuffle(list);
        return list.subList(0, count);
    }

}