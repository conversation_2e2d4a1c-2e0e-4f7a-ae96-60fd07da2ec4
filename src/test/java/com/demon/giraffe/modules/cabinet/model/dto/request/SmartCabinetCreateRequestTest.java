package com.demon.giraffe.modules.cabinet.model.dto.request;

import com.demon.giraffe.common.domain.dto.request.RegionRequest;
import com.demon.giraffe.common.domain.enums.CountyEnum;
import com.google.gson.Gson;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

public class SmartCabinetCreateRequestTest {

    @Test
    public void testReadFromCsvFile() throws IOException {
        // 1. 读取CSV文件
        String filePath = "src/main/resources/temp/request.json";
        List<String> lines = Files.readAllLines(Paths.get(filePath));

        // 2. 跳过表头，处理数据行
        List<SmartCabinetCreateRequest> requests = lines.stream()
                .map(this::parseCsvLine)
                .collect(Collectors.toList());

        // 3. 验证数据


        System.out.println(new Gson().toJson( requests));

    }

    private SmartCabinetCreateRequest parseCsvLine(String line) {
        String[] parts = line.split(",");

        SmartCabinetCreateRequest request = new SmartCabinetCreateRequest();
        request.setRegionInvestorId(Long.parseLong(parts[0]));
        request.setName(parts[1]);

        RegionRequest region = new RegionRequest();
        region.setProvince(parts[2]);
        region.setCity(parts[3]);
        region.setDistrict(parts[4]);
        request.setRegion(region);

        request.setDetailAddress(parts[7]);

        return request;
    }

    @Test
    public void testBuilderPattern() {
        // 使用builder模式创建请求对象
        SmartCabinetCreateRequest request = SmartCabinetCreateRequest.builder()
                .regionInvestorId(47L)
                .name("锦江区测试柜")
                .region(RegionRequest.builder()
                        .province("四川省")
                        .city("成都市")
                        .district("锦江区")
                        .addressCode(CountyEnum.C510104)
                        .build())
                .detailAddress("春熙路123号")
                .build();

        // 验证builder创建的对象
        assertNotNull(request);
        assertEquals(47L, request.getRegionInvestorId());
        assertEquals("锦江区测试柜", request.getName());
        assertEquals("四川省", request.getRegion().getProvince());
        assertEquals("成都市", request.getRegion().getCity());
        assertEquals("锦江区", request.getRegion().getDistrict());
        assertEquals(CountyEnum.C510104, request.getAddressCode());
        assertEquals("春熙路123号", request.getDetailAddress());
    }
}