package com.demon.giraffe.modules.marketing.model.po;

import com.demon.giraffe.common.util.GsonUtil;
import com.demon.giraffe.modules.marketing.model.dto.request.CouponTemplateCreateRequest;
import com.demon.giraffe.modules.marketing.model.enums.*;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 优惠券模板PO测试类
 * <p>测试优惠券模板PO类的相关功能</p>
 */
public class CouponTemplatePoTest {

    /**
     * 测试创建优惠券模板PO对象
     * <p>
     * 测试步骤：
     * 1. 创建10种不同类型的优惠券模板（5种折扣券，5种满减券）
     * 2. 将每个优惠券模板转换为JSON格式并打印输出
     * 3. 验证生成的JSON数据是否符合预期格式
     * </p>
     */
    @Test
    public void testToCouponTemplatePo() {
        // 准备测试数据 - 创建10种不同类型的优惠券模板
        List<CouponTemplateCreateRequest> couponTemplates = new ArrayList<>();

        // 添加5种折扣券
        couponTemplates.add(createDiscount("新用户9折券", 50, 0.90, "/api-storage/inherent/discount_coupon/Y (1).png"));
        couponTemplates.add(createDiscount("满150享8折券", 150, 0.80, "/api-storage/inherent/discount_coupon/Y (3).png"));
        couponTemplates.add(createDiscount("满80享8.5折券", 80, 0.85, "/api-storage/inherent/discount_coupon/Y (5).png"));
        couponTemplates.add(createDiscount("满200享7.5折券", 200, 0.75, "/api-storage/inherent/discount_coupon/Y (7).png"));
        couponTemplates.add(createDiscount("满100享8.8折券", 100, 0.88, "/api-storage/inherent/discount_coupon/Y (9).png"));

        // 添加5种满减券
        couponTemplates.add(createFullReduction("满200减50券", 200, 50, "/api-storage/inherent/discount_coupon/Y (2).png"));
        couponTemplates.add(createFullReduction("满300减80券", 300, 80, "/api-storage/inherent/discount_coupon/Y (4).png"));
        couponTemplates.add(createFullReduction("满500减150券", 500, 150, "/api-storage/inherent/discount_coupon/Y (6).png"));
        couponTemplates.add(createFullReduction("满80减15券", 80, 15, "/api-storage/inherent/discount_coupon/Y (8).png"));
        couponTemplates.add(createFullReduction("满100减20券", 100, 20, "/api-storage/inherent/discount_coupon/Y (10).png"));

        // 将每个优惠券模板转换为JSON格式并打印输出
        couponTemplates.forEach(template -> {
            String json = GsonUtil.toJson(template);
            System.out.println(json);
            // 验证JSON数据不为空
            assertNotNull(json);
            // 验证JSON包含必要字段
            assertTrue(json.contains("\"name\""));
            assertTrue(json.contains("\"type\""));
            assertTrue(json.contains("\"discountValue\""));
        });
    }

    /**
     * 创建折扣券模板请求
     *
     * @param name 优惠券名称
     * @param minAmount 最低消费金额
     * @param discountRate 折扣率（0-1之间的小数）
     * @param iconUrl 图标URL
     * @return 配置好的折扣券模板请求对象
     */
    private CouponTemplateCreateRequest createDiscount(String name, double minAmount, double discountRate, String iconUrl) {
        CouponTemplateCreateRequest request = new CouponTemplateCreateRequest();
        request.setName(name);
        request.setType(CouponTypeEnum.DISCOUNT);
        request.setDiscountType(DiscountTypeEnum.PERCENTAGE);
        BigDecimal discountValue = BigDecimal.valueOf(discountRate);
        request.setDiscountValue(discountValue);
        BigDecimal minAmountValue = BigDecimal.valueOf(minAmount);
        request.setMinAmount(minAmountValue);

        // 计算理论优惠金额 = 最低消费金额 × (1 - 折扣率)
        BigDecimal theoreticalDiscount = minAmountValue.multiply(BigDecimal.ONE.subtract(discountValue));

        // 设置最大优惠金额，确保不小于理论优惠金额
        // 如果业务允许，可以设置更大的值，但这里我们默认使用理论值
        request.setMaxDiscount(theoreticalDiscount);

        request.setValidDays(30); // 有效期为30天
        request.setTotalCount(1000); // 总发放数量1000张
        request.setPerUserLimit(1); // 每人限领1张
        request.setIconUrl(iconUrl);
        request.setStartTime(System.currentTimeMillis()); // 立即生效
        request.setEndTime(System.currentTimeMillis() + 5L * 24 * 60 * 60 * 1000); // 5天后失效
        return request;
    }

    /**
     * 创建满减券模板请求
     *
     * @param name 优惠券名称
     * @param minAmount 最低消费金额
     * @param reductionAmount 减免金额
     * @param iconUrl 图标URL
     * @return 配置好的满减券模板请求对象
     */
    private CouponTemplateCreateRequest createFullReduction(String name, double minAmount, double reductionAmount, String iconUrl) {
        CouponTemplateCreateRequest request = new CouponTemplateCreateRequest();
        request.setName(name);
        request.setType(CouponTypeEnum.FULL_REDUCTION);
        request.setDiscountType(DiscountTypeEnum.AMOUNT);
        BigDecimal discountValue = BigDecimal.valueOf(reductionAmount);
        request.setDiscountValue(discountValue);
        request.setMinAmount(BigDecimal.valueOf(minAmount));

        // 满减券不需要设置maxDiscount，因为优惠金额是固定的
        request.setMaxDiscount(null);

        request.setValidDays(30); // 有效期为30天
        request.setTotalCount(1000); // 总发放数量1000张
        request.setPerUserLimit(1); // 每人限领1张
        request.setIconUrl(iconUrl);
        request.setStartTime(System.currentTimeMillis()); // 立即生效
        request.setEndTime(System.currentTimeMillis() + 5L * 24 * 60 * 60 * 1000); // 5天后失效
        return request;
    }
}