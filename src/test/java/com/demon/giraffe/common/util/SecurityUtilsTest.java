package com.demon.giraffe.common.util;

import org.apache.commons.codec.binary.Base64;
import org.junit.jupiter.api.Test;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

/**
 * Test version of SecurityUtils with hardcoded test keys
 * WARNING: Only for testing - never use in production!
 */
public class SecurityUtilsTest {

    private static final String TEST_AES_KEY = "abcdef1234567890";
    private static final String TEST_AES_IV = "1234567890abcdef";
    private static final String AES_ALGORITHM = "AES/CBC/PKCS5Padding";


    @Test
    void testRoundTripEncryption() {
        String original = "Test message 123";
        String encrypted = SecurityUtilsTest.encryptData(original);
        String decrypted = SecurityUtilsTest.decryptData(encrypted);

        assertEquals(original, decrypted);
        assertNotEquals(original, encrypted);
    }

    @Test
    void testEmptyString() {
        String encrypted = "B6/1j3NcitODOZeCfN27V7Z/W62siAnJxdLhf5igQ3cheDwdyht29uHrFrRrpyYb2I7cUe29CiYT7VkLlFT3ZDoiE7OeqqaHs/nDFdpqHiDgi9e3Ig1ad1sUm0s0WCPj4y9vsO0Tcm2brp7Oq0i2N3DN+vip+45NNnfPbOt6LZ5S0/8U2F+Qvfm5DW+sSlMk";
        String decrypted = SecurityUtilsTest.decryptData(encrypted);

        System.out.println(decrypted);
    }

    @Test
    void testSpecialCharacters() {
        String original = "!@#$%^&*()_+{}|:\"<>?~`-=[]\\;',./";
        String encrypted = SecurityUtilsTest.encryptData(original);
        String decrypted = SecurityUtilsTest.decryptData(encrypted);

        assertEquals(original, decrypted);
    }

    @Test
    void testChineseCharacters() {
        String original = "这是一段中文测试文本";
        String encrypted = SecurityUtilsTest.encryptData(original);
        String decrypted = SecurityUtilsTest.decryptData(encrypted);

        assertEquals(original, decrypted);
    }

    @Test
    void testLongText() {
        String original = "Lorem ipsum dolor sit amet, consectetur adipiscing elit. " +
                "Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, " +
                "eget aliquam nisl nisl sit amet nisl. Nullam auctor, nisl eget ultricies " +
                "tincidunt, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.";

        String encrypted = SecurityUtilsTest.encryptData(original);
        String decrypted = SecurityUtilsTest.decryptData(encrypted);

        assertEquals(original, decrypted);
    }

    @Test
    void testEncryptionConsistency() {
        String original = "Consistent encryption test";
        String encrypted1 = SecurityUtilsTest.encryptData(original);
        String encrypted2 = SecurityUtilsTest.encryptData(original);

        // Same input should produce different outputs due to CBC mode
        assertNotEquals(encrypted1, encrypted2);

        // But both should decrypt to same value
        assertEquals(original, SecurityUtilsTest.decryptData(encrypted1));
        assertEquals(original, SecurityUtilsTest.decryptData(encrypted2));
    }


    public static String encryptData(String data) {
        try {
            Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
            SecretKeySpec keySpec = new SecretKeySpec(TEST_AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(TEST_AES_IV.getBytes(StandardCharsets.UTF_8));
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
            byte[] encrypted = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.encodeBase64String(encrypted);
        } catch (Exception e) {
            throw new RuntimeException("Test encryption failed", e);
        }
    }

    public static String decryptData(String encryptedData) {
        try {
            Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
            SecretKeySpec keySpec = new SecretKeySpec(TEST_AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(TEST_AES_IV.getBytes(StandardCharsets.UTF_8));
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            byte[] decoded = Base64.decodeBase64(encryptedData);
            byte[] decrypted = cipher.doFinal(decoded);
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("Test decryption failed", e);
        }
    }
}