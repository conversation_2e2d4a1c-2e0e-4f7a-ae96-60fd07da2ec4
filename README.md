# Giraffe-Wash 项目说明文档

一个用于自动化部署与管理的脚本化项目，旨在简化 `Giraffe-Wash` 项目在 Linux 主机中的安装与运维流程，基于 Docker 和 Docker Compose 实现容器化管理。

---

## 📚 目录

- [功能概述](#功能概述)
- [环境准备](#环境准备)
- [配置目录及文件](#配置目录及文件)
- [使用方法](#使用方法)
- [日志级别](#日志级别)
- [注意事项](#注意事项)
- [支持系统](#支持系统)
- [持久化路径](#持久化路径)
- [常见问题 FAQ](#常见问题-faq)
- [项目说明](#项目说明)
    - [项目架构](#项目架构)
    - [模块框架](#模块框架)

---

## 🧩 功能概述

`script/init.sh` 是一个用于初始化和管理 `Giraffe-Wash` 项目环境的 Bash 脚本，支持：

1. **环境初始化**：自动创建项目目录，注册 CLI 命令 `app`；
2. **软件安装与卸载**：自动安装或卸载 Docker 与 Docker Compose；
3. **容器管理**：构建 Docker 镜像、启动、重启、停止容器及查看状态；
4. **命令自动补全**：首次初始化后自动设置 CLI 命令补全。

---

## 🧾 环境准备

- 一台流畅的 Linux 主机；
- 建议普通用户执行（可通过 `sudo` 进行特权操作）；
- 确保网络连接正常；
- 支持 HTTP/HTTPS 代理。

---

## ⚙️ 配置目录及文件

项目核心配置集中在 `config/config.env` 文件中，示例如下：

| 变量名称              | 使用说明                                                     |
| --------------------- | ------------------------------------------------------------ |
| `PROJECT_HOME`        | 项目安装根目录，示例：`/opt/giraffe-wash`                    |
| `PROXY_IP`            | 代理地址，示例：`***********:7890`（自动导出为 http_proxy/https_proxy） |
| `DOCKER_HOME`         | Docker 安装目录（默认：`/opt/docker`）                       |
| `COMPOSE_HOME`        | Compose 安装目录（默认：`$DOCKER_HOME/cli-plugins`）         |
| `DOCKER_SERVICE_FILE` | Docker systemd 配置文件路径（固定，不支持修改）              |
| `COMPOSE_BIN_PATH`    | Compose 可执行文件路径（固定，不支持修改）                   |

模板文件存放在 `templates/` 目录中，初始化后会根据变量渲染生成最终配置文件，放置于 `$PROJECT_HOME/config`。

---

## 🚀 使用方法

### 1. 初始化项目环境

首次执行：

```bash
sudo bash script/init.sh init
```

建议使用普通用户执行，若使用 root 用户，需手动添加普通用户至 `docker` 用户组、重建命令软链。

---

### 2. 使用 `app` 命令

```bash
Usage: app [command] [options]
  init                    初始化环境
    ├─ ssl                渲染 ssl 证书
    ├─ delete             仅删除环境,不删除docker以及容器
    ├─ force              强制重新初始化
    ├─ sql                渲染 SQL 初始化文件
    └─ compose            渲染 compose 初始化文件
  install                 安装 Docker 和 Docker Compose
  uninstall               卸载 Docker 和 Docker Compose
  build                   构建镜像
    └─ giraffe-wash-java
  update                  更新容器
  start                   启动容器
  restart                 重启容器
  stop                    停止容器
  status                  显示容器状态
  package                 打包镜像
  vim                     修改 app 命令配置
  explain                 显示命令说明
  help                    显示此帮助信息
```

初始化后自动创建命令自动补全，首次使用需重启终端或重新登录。

### 3. app 用法

- app 命令创建之后，在项目路径（默认路径：/opt/giraffe-wash）下生成应用文件
- 安装应用依赖的服务 docker ，使用 `app install`
- 初始化服务文件，`app init compose && app init ssl && app init sql`
- 编译镜像，`app build`
- 启动应用，`app start`
- 更新服务，在重新编译了应用后，`app upodate [服务名]`

---

## 🪵 日志级别

脚本支持以下日志等级：

- `INFO`：普通信息（默认等级）
- `WARN`：警告信息
- `ERROR`：错误信息
- `DEBUG`：调试信息（适合问题排查）

日志级别可通过修改脚本中的 `LOG_LEVEL` 变量设置。

---

## ❗ 注意事项

- 部分操作需要 `sudo` 权限；
- 若配置了 `PROXY_IP`，将自动导出代理环境变量（http_proxy、https_proxy）；
- 模板目录下的配置将在初始化后自动复制并替换变量，生成于 `$PROJECT_HOME/config`。

---

## 💻 支持系统

建议使用以下系统环境：

- Ubuntu 20.04 / 22.04
- Debian 10+
- CentOS 7 / Rocky Linux / AlmaLinux
- Alpine（部分 systemd 功能可能不兼容）

---

## 🗃️ 持久化路径

以下容器挂载目录已配置持久化，位于 `$PROJECT_HOME/volumes` 目录下：

- MySQL: `$PROJECT_HOME/data/mysql`
- Redis: `$PROJECT_HOME/data/redis`
- MinIO: `$PROJECT_HOME/data/minio`

如需自定义数据卷挂载路径，可在模板文件中进行修改。

---

## ❓ 常见问题 FAQ

### Q1: `app` 命令无效？

请确认是否重新登录终端，或手动执行：

```bash
source ~/.bashrc
```

---

### Q2: Docker 安装失败？

- 确保系统网络通畅，若有代理，请正确配置 `PROXY_IP`；
- 查看实时安装日志

---

### Q3: 我在构建的时候，可以任意构建吗？

-   修改 app 脚本，在开头部分定义了一个字典，关于容器名和仓库地址以及dockerfile的关系映射
-   构建时需要注意主机是否有拉取仓库的权限

---

### Q4: 如何保护我的敏感数据？

- 在安装路径下找到 .env 文件，将敏感数据存储到该文件中，启动时会加载这个文件中的变量
- .env 的配置说明在 ENV_README.md 中详细解释

---

## 🏗️ 项目说明

### 📦 项目架构

```
giraffe-wash/                             
├── src/                                 
│   ├── main/                             
│   │   ├── java/                       
│   │   │   ├── com/                     
│   │   │   │   ├── giraffe/             
│   │   │   │   │   ├── common/           # 通用模块
│   │   │   │   │   │   ├── annotation/   # 自定义注解
│   │   │   │   │   │   ├── config/       # 全局配置
│   │   │   │   │   │   ├── constant/     # 全局常量
│   │   │   │   │   │   ├── domain/       # 全局实体
│   │   │   │   │   │   ├── enums/        # 全局枚举
│   │   │   │   │   │   ├── exception/    # 异常处理
│   │   │   │   │   │   └── util/         # 工具类库
│   │   │   │   │   ├── framework/        # 框架集成
│   │   │   │   │   │   ├── minio/        # 文件存储
│   │   │   │   │   │   ├── mybatis/      # MyBatis增强
│   │   │   │   │   │   ├── satoken/      # 权限框架
│   │   │   │   │   │   ├── swagger/      # API文档
│   │   │   │   │   │   ├── wechat/       # 微信生态
│   │   │   │   │   │   └── redis/        # Redis定制
│   │   │   │   │   ├── modules/          # 业务模块
│   │   │   │   │   │   ├── cabinet/      # 智能柜管理（设备格口、库存、状态）
│   │   │   │   │   │   ├── common/       # 公共模块（基础通用能力、枚举、常量等）
│   │   │   │   │   │   ├── delivery/     # 配送模块（配送员、派单、揽件、投件）
│   │   │   │   │   │   ├── device/       # 设备管理（智能设备、监控、维护）
│   │   │   │   │   │   ├── evaluation/   # 评价系统（用户/订单评价、评分）
│   │   │   │   │   │   ├── laundry/      # 洗护工厂（洗衣任务、分拣、物流）
│   │   │   │   │   │   ├── marketing/    # 营销活动（优惠券、促销、活动管理）
│   │   │   │   │   │   ├── message/      # 消息中心（短信、通知、推送）
│   │   │   │   │   │   ├── order/        # 订单模块（下单、订单流程、状态管理）
│   │   │   │   │   │   ├── payment/      # 支付模块（支付通道、账单、退款）
│   │   │   │   │   │   ├── promotion/    # 推广系统（邀请注册、分享裂变）
│   │   │   │   │   │   ├── qr/           # 二维码服务（生成、扫描、解析二维码对应的业务）
│   │   │   │   │   │   ├── service/      # 服务管理（服务项目、定价、服务范围）
│   │   │   │   │   │   ├── settlement/   # 结算管理（合伙人收益、佣金计算）
│   │   │   │   │   │   ├── ticket/       # 工单系统（客服处理、问题追踪）
│   │   │   │   │   │   └── user/         # 用户模块（登录注册、权限、资料管理）
│   │   │   │   │   ├── task/             # 定时任务
│   │   │   │   │   │   ├── order/        # 订单任务
│   │   │   │   │   │   ├── coupon/       # 优惠券任务
│   │   │   │   │   │   └── statistics/   # 数据统计
│   │   │   │   │   └── GiraffeApplication.java  # 启动类
│   │   ├── resources/                    # 资源文件
│   │   │   ├── application.yml           # 主配置
│   │   │   ├── application-dev.yml       # 开发配置
│   │   │   ├── application-prod.yml      # 生产配置
│   │   │   ├── mybatis/                  # MyBatis配置
│   │   │   │   ├── mapper/               # XML映射
│   ├── test/                             # 测试代码
├── sql/                                 # SQL脚本
│   ├── init/                            # 初始化
│   ├── update/                          # 升级脚本
│   └── data/                            # 模拟数据
├── docs/                                # 项目文档
│   ├── api/                             # API文档
│   ├── db/                              # 数据库设计
│   └── deploy/                          # 部署文档
└── pom.xml                              # Maven配置
```

---

### 🧱 模块框架

各业务模块目录结构如下：

```
modules/
├── {module_name}/                    # 模块名称（如user/order等）
│   ├── controller/                       # 控制器层
│   │   ├── {Xxx}Controller.java          # 业务控制器（按功能拆分）
│   │   └── {Yyy}Controller.java          # 示例：UserController.java
│   ├── repository/                        # mapper 的上层，持久化层，防止service对mapper直接操作
│   │   ├── {Xxx}Repository.java          # 持久化接口
│   │   ├── impl/                         # 持久化层实现类包
│   │   │   └── {Xxx}RepositoryImpl.java  # 持久化层实现类
│   ├── service/                      # 服务层
│   │   ├── {Xxx}Service.java          # 服务接口
│   │   ├── impl/                     # 实现类
│   │   │   ├── {Xxx}ServiceImpl.java  # 主服务实现
│   │   │   └── {Yyy}ServiceImpl.java  # 子服务实现
│   │   └── helper/                   # 服务辅助
│   │       ├── {Xxx}ConvertHelper.java # 转换工具
│   │       └── {Xxx}Validator.java    # 校验工具
│   │
│   ├── mapper/                       # 数据访问层
│   │   ├── {Xxx}Mapper.java           # MyBatis接口
│   │   └── {Yyy}Mapper.java           # 示例：UserMapper.java
│   │
│   ├── model/                        # 数据模型
│   │   ├── entity/                   # 数据库实体
│   │   │   ├── {Xxx}.java            # 实体类（与表对应）
│   │   │   └── {Yyy}.java            # 示例：AppUser.java
│   │   │
│   │   ├── dto/                      # 数据传输对象
│   │   │   ├── request/              # 请求DTO
│   │   │   │   ├── {Xxx}Req.java      # 创建/更新请求
│   │   │   │   └── {Xxx}QueryReq.java # 查询请求
│   │   │   ├── response/             # 响应DTO
│   │   │   │   ├── {Xxx}Resp.java     # 基础响应
│   │   │   │   └── {Xxx}DetailResp.java # 详情响应
│   │   │   └── query/                # 查询条件封装
│   │   │       └── {Xxx}Query.java    # 示例：UserQuery.java
│   │   │
│   │   ├── enums/                    # 模块枚举
│   │   │   └── {Xxx}StatusEnum.java   # 状态枚举
│   │   │
│   │   └── vo/                       # 视图对象
│   │       ├── {Xxx}VO.java           # 复杂视图
│   │       └── {Xxx}StatVO.java       # 统计视图
│   │
│   └── {Xxx}Constants.java           # 模块常量（如：UserConstants.java）
│
├── order/                            # 示例：订单模块
│   ├── controller/
│   │   ├── OrderController.java       # 主订单控制器
│   │   └── OrderItemController.java   # 子订单项控制器
│   ├── service/
│   │   ├── OrderService.java
│   │   ├── impl/
│   │   │   ├── OrderServiceImpl.java
│   │   │   └── OrderItemServiceImpl.java
│   │   └── helper/
│   │       ├── OrderNoGenerator.java  # 订单号生成
│   │       └── OrderCalculator.java   # 价格计算
│   └── ...                           # 其他相同结构
│
└── ...                               # 其他模块
```

---

如有建议或问题，欢迎在 Issues 区反馈。🚀