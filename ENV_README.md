## 📦 MySQL 容器配置

| 变量名                 | 说明                       | 示例值              |
| ---------------------- | -------------------------- | ------------------- |
| `MYSQL_CONTAINER_NAME` | MySQL 容器名称             | `mysql`             |
| `MYSQL_ROOT_PASSWORD`  | MySQL root 用户密码        | `example_root_pass` |
| `MYSQL_HOST`           | MySQL 服务主机名或容器名   | `mysql`             |
| `MYSQL_PORT`           | MySQL 端口号               | `3306`              |
| `MYSQL_USER`           | 应用连接使用的数据库用户名 | `appuser`           |
| `MYSQL_PASSWORD`           | 应用连接使用的数据库密码 | `apppasswd`           |
| `MYSQL_DATABASE`       | 应用所使用的数据库名       | `giraffe_db`        |



------

## 🚀 Redis 容器配置

| 变量名                 | 说明                           | 示例值             |
| ---------------------- | ------------------------------ | ------------------ |
| `REDIS_CONTAINER_NAME` | Redis 容器名称                 | `redis`            |
| `REDIS_HOST`           | Redis 服务主机名或容器名       | `redis`            |
| `REDIS_PORT`           | Redis 端口号                   | `6379`             |
| `REDIS_DATABASE`       | Redis 使用的数据库索引（0~15） | `11`               |
| `REDIS_PASSWORD`       | Redis 密码                     | `example_password` |



------

## 🗂️ MinIO 容器配置

| 变量名                | 说明                             | 示例值              |
| --------------------- | -------------------------------- | ------------------- |
| `MINIO_ROOT_USER`     | MinIO 管理用户名                 | `minioadmin`        |
| `MINIO_ROOT_PASSWORD` | MinIO 管理用户密码               | `minioadminpass`    |
| `MINIO_HOST`          | MinIO 服务主机名或容器名         | `minio`             |
| `MINIO_DOMAIN`        | MinIO 访问域名（如配置反向代理） | `minio.example.com` |



------

## 🦒 giraffe-wash 服务配置

> 注：若某些变量与中间件（如 MySQL、Redis、MinIO）配置一致，可直接复用，不必重复设置。

### 🔐 加密配置

| 变量名             | 说明                             | 示例值                           |
| ------------------ | -------------------------------- | -------------------------------- |
| `PRIVATE_KEY`      | 应用服务使用的私钥               | `-----BEGIN PRIVATE KEY-----...` |
| `SECURITY_AES_KEY` | AES 加密密钥（用于存储敏感信息） | `16字节或32字节密钥`             |
| `SECURITY_AES_IV`  | AES 初始化向量                   | `16字节 IV`                      |



------

### 💰 微信支付配置

| 变量名                          | 说明                 | 示例值                       |
| ------------------------------- | -------------------- | ---------------------------- |
| `WECHAT_MERCHANT_ID`            | 微信商户号           | `1234567890`                 |
| `WECHAT_MERCHANT_SERIAL_NUMBER` | 商户证书序列号       | `ABCDE12345`                 |
| `WECHAT_PRIVATE_KEY_PATH`       | 商户私钥文件路径     | `/path/to/apiclient_key.pem` |
| `WECHAT_API_V3_KEY`             | 微信支付 API v3 密钥 | `api_v3_secret_key`          |



------

### 📱 微信小程序配置

| 变量名          | 说明          | 示例值               |
| --------------- | ------------- | -------------------- |
| `WECHAT_APP_ID` | 小程序 App ID | `wx1234567890abcdef` |
| `WECHAT_SECRET` | 小程序密钥    | `secret_app_key`     |